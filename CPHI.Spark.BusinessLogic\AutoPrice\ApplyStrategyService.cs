﻿using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using log4net;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
//using System.Security.Policy;
using System.Threading.Tasks;

namespace CPHI.Spark.BusinessLogic.AutoPrice
{
   public static class ApplyStrategyService
   {

      public static decimal? DoesRuleSetRequireCalcOfPriceForDaysToSell(AdvertParamsForStrategyCalculator calcParams, StrategySelectionRuleSet ruleSet)
      {

         int ruleIndex = 0;
         bool advertPassesSpecificRule = false;

         //var ruleSet = ruleSetMapping.Value.StrategySelectionRuleSet;

         if (calcParams.VehicleReg == "LV69EGE")
         {
            { }
         }

         List<StrategySelectionRule> rules = ruleSet.StrategySelectionRules.ToList();

         while (ruleIndex < rules.Count && !advertPassesSpecificRule)
         {
            var rule = rules[ruleIndex];
            //loop over the rules, see if this rule applies to this advert if so, do it

            //check if rule applies
            bool passesRuleCriterias = true;
            int criteriaIndex = 0;
            var criterias = rule.StrategySelectionCriterias.ToList();
            while (passesRuleCriterias && criteriaIndex < criterias.Count)
            {
               var criteria = criterias[criteriaIndex];
               passesRuleCriterias = CheckIfRulePasses(
                   //advert, 
                   calcParams,
                   criteria.StrategyFieldName.ComparatorType, criteria.StrategyFieldName.FieldName, criteria.Value);
               criteriaIndex++;
            }

            if (passesRuleCriterias)
            {
               advertPassesSpecificRule = true;
               var daysToSell = rule.StrategyVersion.StrategyFactors.FirstOrDefault(x => x.Name == StrategyFactorName.DaysToSell);
               if (daysToSell != null)
               {
                  return daysToSell.StrategyFactorItems.First().Value;
               }
            }

            ruleIndex++;
         }

         //if we didn't pass a specific rule, use the default one (if it exists)
         if (!advertPassesSpecificRule && ruleSet.DefaultStrategyVersion != null)
         {
            var daysToSell = ruleSet.DefaultStrategyVersion.StrategyFactors.FirstOrDefault(x => x.Name == StrategyFactorName.DaysToSell);
            if (daysToSell != null)
            {
               return daysToSell.StrategyFactorItems.First().Value;
            }
         }

         return null;
      }

      public static bool DoWeNeedAParticularFactorsData(AdvertParamsForStrategyCalculator calcParams, StrategySelectionRuleSet ruleSet, StrategyFactorName targetFactorName)  //I am given advert and ruleset 
      {

         int ruleIndex = 0;
         bool weRequireLeavingData = false;
         bool wePassSpecificRule = false;

         List<StrategySelectionRule> rules = ruleSet.StrategySelectionRules.ToList();

         while (ruleIndex < rules.Count && !weRequireLeavingData)
         {
            var rule = rules[ruleIndex];
            //loop over the rules, see if this rule applies to this advert if so, do it

            //check if rule applies
            bool passesRuleCriterias = true;
            int criteriaIndex = 0;
            var criterias = rule.StrategySelectionCriterias.ToList();
            while (passesRuleCriterias && criteriaIndex < criterias.Count)
            {
               var criteria = criterias[criteriaIndex];
               passesRuleCriterias = CheckIfRulePasses(
                   //advert, 
                   calcParams,
                   criteria.StrategyFieldName.ComparatorType, criteria.StrategyFieldName.FieldName, criteria.Value);
               criteriaIndex++;
            }

            if (passesRuleCriterias)
            {
               wePassSpecificRule = true;

               if (rule.StrategyVersion.StrategyFactors.Any(x => x.Name == targetFactorName))  //StrategyFactorName.LeavingData
               {
                  weRequireLeavingData = true;
               }
            }

            ruleIndex++;
         }

         //if we didn't pass a specific rule, use the default one (if it exists)
         if (!wePassSpecificRule && ruleSet.DefaultStrategyVersion != null)
         {
            if (ruleSet.DefaultStrategyVersion.StrategyFactors.Any(x => x.Name == targetFactorName))
            {
               weRequireLeavingData = true;
            }
         }

         return weRequireLeavingData;
      }


      public static async Task ApplyRuleSet(
          DateTime runDate,
          AdvertParamsForStrategyCalculator calcParams,
          StrategySelectionRuleSet ruleSet,
          RetailerSite retailerSite,
          Site site, List<StrategyPriceBuildUpItem> factorImpacts,
          TokenResponse tokenResponse,
          AutoTraderCompetitorClient competitorClient,
          string autotraderBaseURL,
          ILog logger, decimal? preCalculatedDTSPrice,
          AutoTraderFutureValuationsClient atFutureValsClient,
          AutoTraderVehicleMetricsClient atMetricsClient,
          List<LeavingVehicle6mItem> leaving6mData,
          List<DailyValuationChange> dailyValuationChanges,
          List<TagDTO> tags)
      {
         int ruleIndex = 0;
         bool advertPassesSpecificRule = false;

         List<StrategySelectionRule> rules = ruleSet.StrategySelectionRules.ToList();

         if (calcParams.VehicleReg == "AK72ZMX")
         {
            { }
         }

         while (ruleIndex < rules.Count && !advertPassesSpecificRule)
         {
            var rule = rules[ruleIndex];
            //loop over the rules, see if this rule applies to this advert if so, do it

            //check if rule applies
            bool passesRuleCriterias = true;
            int criteriaIndex = 0;
            var criterias = rule.StrategySelectionCriterias.ToList();
            while (passesRuleCriterias && criteriaIndex < criterias.Count)
            {
               var criteria = criterias[criteriaIndex];
               passesRuleCriterias = CheckIfRulePasses(
                   //advert, 
                   calcParams,
                   criteria.StrategyFieldName.ComparatorType, criteria.StrategyFieldName.FieldName, criteria.Value);
               criteriaIndex++;
            }

            if (passesRuleCriterias)
            {
               advertPassesSpecificRule = true;
               await CalculateFactorImpacts(runDate, factorImpacts,
               competitorClient, tokenResponse, retailerSite, rule.StrategyVersion,
               calcParams, ruleSet, autotraderBaseURL, logger, preCalculatedDTSPrice,
                atFutureValsClient, atMetricsClient, leaving6mData, dailyValuationChanges, tags);
            }

            ruleIndex++;
         }

         //if we didn't pass a specific rule, use the default one (if it exists)
         if (!advertPassesSpecificRule && ruleSet.DefaultStrategyVersion != null)
         {
            await CalculateFactorImpacts(runDate, factorImpacts, competitorClient,
            tokenResponse, retailerSite, ruleSet.DefaultStrategyVersion, calcParams,
            ruleSet, autotraderBaseURL, logger, preCalculatedDTSPrice, atFutureValsClient,
             atMetricsClient, leaving6mData, dailyValuationChanges, tags);
         }

      }




      private static bool CheckIfRulePasses(AdvertParamsForStrategyCalculator advertParams, string ruleComparatorType, string ruleFieldName, string ruleValue)
      {
         if (ruleComparatorType == "text")
         {
            try
            {

               string adValue = (string)(ConstantMethodsService.GetPropertyValue(advertParams, ruleFieldName) ?? "?");
               bool passesRule = adValue.ToUpper() == ruleValue.ToUpper();
               return passesRule;
            }
            catch
            {
               return false;
            }
         }
         else if (ruleComparatorType == "bool")
         {
            try
            {

               bool adValue = (bool)(ConstantMethodsService.GetPropertyValue(advertParams, ruleFieldName) ?? false);
               bool ruleBoolVal = ruleValue.ToUpper() == "TRUE";
               bool passesRule = adValue == ruleBoolVal;
               return passesRule;
            }
            catch
            {
               return false;
            }
         }
         else if (ruleComparatorType == "int")
         {
            try
            {
               int adValue = (int)(ConstantMethodsService.GetPropertyValue(advertParams, ruleFieldName) ?? 0);
               bool passesRule = adValue == int.Parse(ruleValue);
               return passesRule;
            }
            catch
            {
               return false;
            }
         }
         else if (ruleComparatorType == "range")
         {
            try
            {
               object value = ConstantMethodsService.GetPropertyValue(advertParams, ruleFieldName);
               int adValue;

               if (value is int i)
               {
                  adValue = i;
               }
               else if (value is decimal d)
               {
                  adValue = (int)Math.Round(d, MidpointRounding.AwayFromZero);
               }
               else
               {
                  throw new InvalidCastException($"Unsupported type {value?.GetType().Name} for adValue.");
               }
               (int minValue, int maxValue) = ConstantMethodsService.ParseRange(ruleValue);
               bool passesRule = (adValue >= minValue) && (adValue <= maxValue);
               return passesRule;
            }
            catch
            {
               return false;
            }
         }
         throw new Exception("Unknown comparator type");
      }




      private static async Task CalculateFactorImpacts(
         DateTime runDate,
         List<StrategyPriceBuildUpItem> factorImpacts,
         AutoTraderCompetitorClient competitorClient,
         TokenResponse tokenResponse,
         RetailerSite retailerSite,
         //VehicleAdvertWithRating advertA,
         StrategyVersion strategyVersion,
         AdvertParamsForStrategyCalculator calcParams,
         StrategySelectionRuleSet ruleSet,
         string autotraderBaseURL,
         ILog logger,
         decimal? preCalculatedDTSPrice,
          AutoTraderFutureValuationsClient atFutureValsClient,
           AutoTraderVehicleMetricsClient atMetricsClient,
           List<LeavingVehicle6mItem> leaving6mData,
           List<DailyValuationChange> dailyValuationChanges,
           List<TagDTO> tags
           )
      {
         foreach (var factor in strategyVersion.StrategyFactors)
         {
            try
            {
               factor.StrategyVersion = strategyVersion;

               var impactItems = await CalculateStrategyImpactService.CalculateStrategyFactorImpact(
                   runDate,
                   competitorClient,
                   tokenResponse,
                   retailerSite,
                   factor,
                   calcParams,
                   autotraderBaseURL,
                   ruleSet,
                   preCalculatedDTSPrice,
                   atFutureValsClient,
                   atMetricsClient,
                   logger,
                   leaving6mData,
                   dailyValuationChanges,
                   tags
                   );

               if (impactItems.Where(x => x != null).ToList().Count >= 1)
               {
                  foreach (var impactItem in impactItems)
                  {
                     //do the new 'only apply if less than' type logic
                     if (factor.OnlyApplyIfImpactOverValue != null)
                     {
                        if (impactItem.Impact < factor.OnlyApplyIfImpactOverValue.Value)
                        {
                           //impact is too small.  Add note.
                           string prefix = impactItem.Impact > 0 ? "+" : string.Empty;
                           impactItem.ExtendedNotes += $", skipped as impact only {prefix}{impactItem.Impact.ToString("C0")}, less than minimum increase of +{factor.OnlyApplyIfImpactOverValue.Value.ToString("C0")}";
                           impactItem.Impact = 0;
                        }
                     }
                     if (factor.OnlyApplyIfImpactOverPercent != null && calcParams.valuationAdjusted != null)
                     {
                        decimal impactPct = impactItem.Impact / ((decimal)calcParams.valuationAdjusted);
                        if (impactPct < factor.OnlyApplyIfImpactOverPercent.Value)
                        {
                           //impact is too small.  Add note.
                           string prefix = impactItem.Impact > 0 ? "+" : string.Empty;
                           impactItem.ExtendedNotes += $", skipped as impact only {prefix}{Math.Round(impactPct * 10) / 10}%, less than minimum increase of +{Math.Round((decimal)factor.OnlyApplyIfImpactOverPercent.Value * 10) / 10}%";
                           impactItem.Impact = 0;
                        }
                     }

                     if (factor.OnlyApplyIfImpactUnderValue != null)
                     {
                        if (impactItem.Impact > factor.OnlyApplyIfImpactUnderValue.Value)
                        {
                           string prefix = impactItem.Impact > 0 ? "+" : string.Empty;
                           impactItem.ExtendedNotes += $", skipped as impact is {prefix}{impactItem.Impact.ToString("C0")}, which is not below the minimum reduction of {factor.OnlyApplyIfImpactUnderValue.Value.ToString("C0")}";
                           impactItem.Impact = 0;
                        }
                     }
                     if (factor.OnlyApplyIfImpactUnderPercent != null && calcParams.valuationAdjusted != null)
                     {
                        decimal impactPct = impactItem.Impact / ((decimal)calcParams.valuationAdjusted);
                        if (impactPct > factor.OnlyApplyIfImpactUnderPercent.Value)
                        {
                           //impact is too big.  Add note.
                           string prefix = impactItem.Impact > 0 ? "+" : string.Empty;
                           impactItem.ExtendedNotes += $", skipped as impact is {prefix}{Math.Round(impactPct * 10) / 10}%, which is not below minimum reduction of {Math.Round((decimal)factor.OnlyApplyIfImpactUnderPercent.Value * 10) / 10}%";
                           impactItem.Impact = 0;
                        }
                     }
                  }

                  factorImpacts.AddRange(impactItems.Where(x => x.Impact != 0));
                  try
                  {
                     calcParams.currentStrategyPrice += impactItems.Select(x => x.Impact).Sum();
                  }
                  catch (Exception ex)
                  {
                     { }
                  }
               }



            }
            catch (Exception ex)
            {
               if (ex.Message == "No Retail Rating")
               {
                  if (logger != null)
                  {
                     logger.Warn($"No retail rating for adId #{calcParams.AdvertId}, skipping factor");
                  }
               }
               else
               {
                  if (logger != null)
                  {
                     logger.Error($"Failed on factor {factor.Name} on adId #{calcParams.AdvertId}, {ex.Message}", ex);
                  }
               }
            }
         }
      }

   }
}
