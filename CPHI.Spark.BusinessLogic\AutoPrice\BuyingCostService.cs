﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace CPHI.Spark.BusinessLogic.AutoPrice
{
   public static class BuyingCostService
   {

      public enum BuyingCostType // They are also used in BuyingCostsSets table.
      {
         Warranty,
         Margin,
         AddtionalMech,
         Auctionfee,
         Paint,
         Delivery,
         Other,
      }


      public static decimal GetBuyingCostForValuation(int retailerSiteId, int valuationAmount, DateTime? firstRegistered, string make, string model, ICollection<BuyingCostsSet> buyingCostsSets, BuyingCostType buyingCostType, BulkUploadPredefinedTemplateType? templateType)
      {
         DateTime today = DateTime.Today;
         RegexOptions regexOptions = RegexOptions.IgnoreCase;
         int? ageInMonths = null;

         if (firstRegistered.HasValue)
         {
            ageInMonths = (today.Year - firstRegistered.Value.Year) * 12 + today.Month - firstRegistered.Value.Month;
         }
         // Attempt to match by the following:
         // 1. CostType, RetailerSite.. .. failing that:
         // 2. CostType ..

         var marginVM = buyingCostsSets
            .Where(x => x.CostType == buyingCostType.ToString()) //main
            .Where(x => x.RetailerSite_Id == retailerSiteId)
            .Where(x => Regex.IsMatch(make, x.MakePattern ?? ".*", regexOptions) && Regex.IsMatch(model, x.ModelPattern ?? ".*", regexOptions))
            .Where(x => x.TemplateType.HasValue ? x.TemplateType == templateType : 1 == 1)
            .Where(x => x.ValuationUpTo.HasValue ? x.ValuationUpTo >= valuationAmount : 1 == 1)
            .Where(x => x.ValuationUpTo.HasValue ? x.ValuationUpTo >= valuationAmount : 1 == 1)
            .Where(x => x.FromMonths.HasValue ? x.FromMonths <= ageInMonths : 1 == 1)
            .OrderByDescending(x => x.FromMonths)
            .ThenBy(x => x.ValuationUpTo)
            .FirstOrDefault();

         if (marginVM != null)
         {
            return WhichMargin(marginVM, valuationAmount);
         }

         marginVM = buyingCostsSets
            .Where(x => x.CostType == buyingCostType.ToString()) //main
            .Where(x => x.RetailerSite_Id == null)
            .Where(x => x.TemplateType.HasValue ? x.TemplateType == templateType : 1 == 1)
            .Where(x => Regex.IsMatch(make, x.MakePattern ?? ".*") && Regex.IsMatch(model, x.ModelPattern ?? ".*", regexOptions))
            .Where(x => x.ValuationUpTo.HasValue ? x.ValuationUpTo >= valuationAmount : 1 == 1)
            .Where(x => x.FromMonths.HasValue ? x.FromMonths <= ageInMonths : 1 == 1)
            .OrderByDescending(x => x.FromMonths)
            .ThenBy(x => x.ValuationUpTo)
            .FirstOrDefault();

            return WhichMargin(marginVM, valuationAmount);

         
      }
      public static decimal WhichMargin(BuyingCostsSet buyingCostsSet, int valuationAmount)
      {
         if (buyingCostsSet != null && buyingCostsSet.IsPercent)
         {
            var x = ((decimal)valuationAmount * (buyingCostsSet.Amount / 100));
            return x;
         }
         else
         {
            return buyingCostsSet?.Amount ?? 0;
         }
      }



   }
}
