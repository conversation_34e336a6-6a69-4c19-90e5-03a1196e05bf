﻿using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using log4net;
using MoreLinq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

public static class CalculateStrategyImpactService
{
   //THIS IS THE MAIN METHOD
   public static async Task<List<StrategyPriceBuildUpItem>> CalculateStrategyFactorImpact(
       DateTime runDate,  //ok, just static item
       AutoTraderCompetitorClient competitorClient,
       TokenResponse tokenResponse, //ok, the token to use
       RetailerSite retailerSite,
       StrategyFactor factor,   //the factor
       AdvertParamsForStrategyCalculator parms, //the advert parameters.   THIS MUST INCLUDE EVERYTHING WE NEED
       string atBaseURL,
       StrategySelectionRuleSet ruleSet,
       decimal? preCalculatedDTSPrice,
       AutoTraderFutureValuationsClient atFutureValsClient,
       AutoTraderVehicleMetricsClient atMetricsClient,
       ILog logger,
       List<LeavingVehicle6mItem> leaving6mData,
       List<DailyValuationChange> dailyValuationChanges,
       List<TagDTO> tags)
   {

      if (parms.VehicleReg == "HT19RCZ")
      {
         { }
      }

      switch (factor.Name)
      {
         case StrategyFactorName.RetailRatingBand:
            return new List<StrategyPriceBuildUpItem>() { CalculateRetailRatingBandImpact(runDate, factor, parms, ruleSet) };

         case StrategyFactorName.DaysListed:
            return new List<StrategyPriceBuildUpItem>() { CalculateDaysListedImpact(factor, parms) };

         case StrategyFactorName.RetailRating:
            return new List<StrategyPriceBuildUpItem>() { CalculateRetailRatingImpact(factor, parms) };

         case StrategyFactorName.DaysInStock:
            return new List<StrategyPriceBuildUpItem>() { CalculateDaysInStockImpact(factor, parms) };

         case StrategyFactorName.RR_DL_Matrix:
            return new List<StrategyPriceBuildUpItem>() { CalculateRRDLMatrixImpact(factor, parms, ruleSet) };

         case StrategyFactorName.RR_DS_Matrix:
            return new List<StrategyPriceBuildUpItem>() { CalculateRRDSMatrix(factor, parms, ruleSet) };

         case StrategyFactorName.RR_DB_Matrix:
            return new List<StrategyPriceBuildUpItem>() { CalculateRRDBMatrix(factor, parms, ruleSet) };

         case StrategyFactorName.DTS_DL_Matrix:
            return new List<StrategyPriceBuildUpItem>() { CalculateDTSDLMatrix(factor, parms, ruleSet) };

         case StrategyFactorName.PY_DS_Matrix:
            return new List<StrategyPriceBuildUpItem>() { CalculatePYDSMatrix(factor, parms, ruleSet) };

         case StrategyFactorName.VB_DS_Matrix:
            return new List<StrategyPriceBuildUpItem>() { CalculateVBDSMatrix(factor, parms, ruleSet) };

         case StrategyFactorName.DaysListedBand:
            return new List<StrategyPriceBuildUpItem>() { CalculateDaysListedBandImpact(runDate, factor, parms, ruleSet) };

         case StrategyFactorName.DaysInStockBand:
            {
               int value = ((int)parms.DaysInStock);
               List<string> notes = new List<string>();
               notes.Add($"Days In Stock Band of {parms.RetailRating}");
               var impactItem = ApplyFactorAndCreateFactorRatings(runDate, factor, value, parms.currentStrategyPrice, parms.snapshotId, ruleSet, notes);
               return new List<StrategyPriceBuildUpItem>() { impactItem };
            }

         case StrategyFactorName.RetailerName:
            return new List<StrategyPriceBuildUpItem>() { CalculateRetailerNameImpact(retailerSite, factor, parms, ruleSet) };

         case StrategyFactorName.Brand:
            return new List<StrategyPriceBuildUpItem>() { CalculateBrandImpact(retailerSite, factor, parms, ruleSet) };

         case StrategyFactorName.ModelName:
            return new List<StrategyPriceBuildUpItem>() { CalculateModelNameImpact(retailerSite, factor, parms, ruleSet) };

         case StrategyFactorName.MatchCheapestCompetitor:
            return new List<StrategyPriceBuildUpItem>() { await CalculateCheapestCompetitorImpact(competitorClient, retailerSite, factor, parms, ruleSet) };

         case StrategyFactorName.TrackMarketPosition:
            return new List<StrategyPriceBuildUpItem>() { await CalculateTrackMarketPositionImpact(competitorClient, retailerSite, parms, factor, ruleSet) };

         case StrategyFactorName.AchieveMarketPositionScore:
            return new List<StrategyPriceBuildUpItem>() { await CalculateMarketPositionImpact(competitorClient, retailerSite, factor, parms, ruleSet) };

         case StrategyFactorName.WholesaleAdjustment:
            return new List<StrategyPriceBuildUpItem>() { await CalculateWholesaleAdjustmentImpact(factor, parms, ruleSet) };

         case StrategyFactorName.Mileage:
            return new List<StrategyPriceBuildUpItem>() { CalculateMileageImpact(factor, parms) };

         case StrategyFactorName.LeavingData:
            return new List<StrategyPriceBuildUpItem>() { CalculateLeaving6mImpact(factor, parms, ruleSet, leaving6mData) };

         case StrategyFactorName.DaysToSell:
            {

               if (preCalculatedDTSPrice != null)
               {
                  //we already worked it out so just use it
                  if (preCalculatedDTSPrice == 0)
                  {
                     return new List<StrategyPriceBuildUpItem>() { EarlyReturnAsCannotDoDaysToSellStrategyPrice(runDate, (int)parms.valuationAdjusted, factor, parms.snapshotId, ruleSet) };
                  }
                  else
                  {
                     decimal impact = (decimal)preCalculatedDTSPrice - (decimal)parms.valuationAdjusted;
                     var daysTarget = factor.StrategyFactorItems.First().Value;

                     string extendedNote = $"Adjustment to valuation to achieve {daysTarget} days to sell";
                     if (parms.WeHadToUseFloorForDTS && preCalculatedDTSPrice != null)
                     {
                        extendedNote = $"Tried to achieve {Math.Ceiling(daysTarget)} days but lowest price hit at {((decimal)preCalculatedDTSPrice).ToString("C0")} achieving {Math.Ceiling(parms.RoughEstimateDTSLow)} days";
                     }
                     else if (parms.WeHadToUseCeilingForDTS)
                     {
                        extendedNote = $"Tried to achieve {Math.Ceiling(daysTarget)} days but highest price hit at {((decimal)preCalculatedDTSPrice).ToString("C0")} achieving {Math.Ceiling(parms.RoughEstimateDTSHigh)} days";
                     }
                     return new List<StrategyPriceBuildUpItem>() { ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
                                    factor,
                                    "DaysToSell",
                                    impact,
                                    extendedNote,
                                    parms.snapshotId,
                                    ruleSet) };
                  }
               }
               else
               {
                  //no pre-calcd, do it by using api calls
                  var impactItems = new List<StrategyPriceBuildUpItem>() { await WorkoutStrategyPriceBasedOnDaysToSellRequirementAndReturnImpact(
                      runDate,
                      atMetricsClient,
                      tokenResponse,
                      retailerSite,
                      factor,
                      parms,
                      atBaseURL, ruleSet, logger) };

                  return impactItems;
               }
            }

         case StrategyFactorName.OnBrandCheck:
            return new List<StrategyPriceBuildUpItem>() { CalculateOnBrandImpact(factor, parms, ruleSet) };

         case StrategyFactorName.MinimumProfit:
            return new List<StrategyPriceBuildUpItem>() { CalculateMinimumProfitBand(factor, parms, ruleSet) };

         case StrategyFactorName.MinimumPricePosition:
            return new List<StrategyPriceBuildUpItem>() { CalculateMinimumPricePosition(factor, parms, ruleSet) };
         case StrategyFactorName.MaximumPricePosition:
            return new List<StrategyPriceBuildUpItem>() { CalculateMaximumPricePosition(factor, parms, ruleSet) };

         case StrategyFactorName.RoundToNearestEnding:
         case StrategyFactorName.RoundUpToNearestEnding:
         case StrategyFactorName.RoundDownToNearestEnding:
            return new List<StrategyPriceBuildUpItem>() { CalculateRoundToNearestImpact(factor, parms, ruleSet) };

         case StrategyFactorName.RoundToNearestMultiple:
         case StrategyFactorName.RoundUpToNearestMultiple:
         case StrategyFactorName.RoundDownToNearestMultiple:
            return new List<StrategyPriceBuildUpItem>() { CalculateRoundToNearestMultipleImpact(factor, parms, ruleSet) };

         case StrategyFactorName.RoundToPriceBreak:
            return new List<StrategyPriceBuildUpItem>() { CalculateRoundToNearestPriceBreakImpact(factor, parms, ruleSet) };

         case StrategyFactorName.MakeModel:
            return new List<StrategyPriceBuildUpItem>() { CalculateMakeAndModelImpact(factor, parms, ruleSet) };

         case StrategyFactorName.MilesPerYear:
            return new List<StrategyPriceBuildUpItem>() { CalculateMilesPerYearImpact(factor, parms, ruleSet) };

         case StrategyFactorName.ValuationChangeUntilSell:
            {
               var impactItems = new List<StrategyPriceBuildUpItem>() { await WorkoutStrategyPriceBasedOnValuationChangeUntilSellAndReturnImpact(
                   runDate,
                   atFutureValsClient,
                   tokenResponse,
                   retailerSite,
                   factor,
                   parms,
                   atBaseURL, ruleSet) };
               return impactItems;
            }

         case StrategyFactorName.SetToAdvertisedPrice:
            return new List<StrategyPriceBuildUpItem>() { CalculateSetToAdvertisedPriceImpact(factor, parms, ruleSet) };

         case StrategyFactorName.SpecificColour:
            return new List<StrategyPriceBuildUpItem>() { CalculateSpecificColourImpact(factor, parms) };

         case StrategyFactorName.Colour:
            return new List<StrategyPriceBuildUpItem>() { CalculateColourImpact(factor, parms) };

         case StrategyFactorName.AgeAndOwners:
            return new List<StrategyPriceBuildUpItem>() { CalculateOwnersVsAgeImpact(factor, parms) };

         case StrategyFactorName.RetailRating10sBand:
            return new List<StrategyPriceBuildUpItem>() { CalculateRetailRatingBand10sImpact(runDate, factor, parms, ruleSet) };
         case StrategyFactorName.MakeFuelType:
            return new List<StrategyPriceBuildUpItem>() { CalculateMakeFuelTypeImpact(factor, parms, ruleSet) };
         case StrategyFactorName.MakeAgeBand:
            return new List<StrategyPriceBuildUpItem>() { CalculateMakeAgeBandImpact(factor, parms, ruleSet) };
         case StrategyFactorName.PerformanceRatingScore:
            return new List<StrategyPriceBuildUpItem>() { CalculatePerfRatingImpact(factor, parms) };
         case StrategyFactorName.FuelType:
            return new List<StrategyPriceBuildUpItem>() { CalculateFuelTypeImpact(factor, parms, ruleSet) };
         case StrategyFactorName.ValueBand:
            return new List<StrategyPriceBuildUpItem>() { CalculateValueBandImpact(factor, parms, ruleSet) };
         case StrategyFactorName.RegYear:
            return new List<StrategyPriceBuildUpItem>() { CalculateRegYearImpact(factor, parms, ruleSet) };
         case StrategyFactorName.LiveMarketCondition:
            return new List<StrategyPriceBuildUpItem>() { CalculateLiveMarketConditionImpact(factor, parms, ruleSet) };
         case StrategyFactorName.DateRange:
            return new List<StrategyPriceBuildUpItem>() { CalculateDateRangeImpact(factor, parms, ruleSet) };
         case StrategyFactorName.DailyValuationChange:
            return new List<StrategyPriceBuildUpItem>() { CalculateDailyValuationChangeImpact(factor, parms, ruleSet, dailyValuationChanges) };

         case StrategyFactorName.Tag:
            return CalculateTagImpact(factor, parms, ruleSet, tags);

         case StrategyFactorName.ApplyTagAdjustments:
            return CalculateApplyTagAdjustmentImpact(factor, parms, ruleSet, tags);

         default:
            throw new Exception("Unknown factor name");
      }
   }

   private static StrategyPriceBuildUpItem CalculateDailyValuationChangeImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet, List<DailyValuationChange> dailyValuationChanges)
   {
      if (parms.VehicleReg == "VF19PNN")
      {
         { }
      }
      var change = dailyValuationChanges.FirstOrDefault(x => x.AdvertId == parms.AdvertId);

      int impact = 0;
      string extendedNotes = string.Empty;
      if (change == null)
      {
         //perhaps is a new advert, so set strategy price to current price
         impact = (int)Math.Round(((decimal)parms.AdvertisedPrice)) - (int)parms.currentStrategyPrice;
         extendedNotes = "Did not see advert yesterday so adjustment to set strategy price equal to currently advertised price";
      }
      else
      {
         int moveToSetStrategyToYesterdayAdvertised = change.YesterdayAdvertised - (int)parms.currentStrategyPrice;
         int dailyValuationMove = change.TodayValuation - change.YesterdayValuation;
         impact = moveToSetStrategyToYesterdayAdvertised + dailyValuationMove;
         var gb = new CultureInfo("en-GB");
         extendedNotes = $"Yesterdays Advertised price {change.YesterdayAdvertised.ToString("C0", gb)}, daily valuation change {dailyValuationMove.ToString("C0", gb)}.";
      }
      StrategyPriceBuildUpItem buildUp = new StrategyPriceBuildUpItem();
      buildUp.VersionName = factor.StrategyVersion.Name;
      buildUp.FactorName = factor.Name.ToString();
      buildUp.FactorItemLabel = string.Empty;
      buildUp.FactorItemValue = 0;
      buildUp.SourceValue = string.Empty;
      buildUp.Impact = impact;
      buildUp.RuleSetComment = ruleSet.Comment;
      buildUp.ExtendedNotes = extendedNotes;
      buildUp.VehicleAdvertSnapshotId = parms.snapshotId;
      buildUp.StrategyFactorItemId = factor.StrategyFactorItems.First().Id;
      buildUp.StrategySelectionRuleSetId = ruleSet.Id;
      return buildUp;

   }

   private static StrategyPriceBuildUpItem CalculateLeaving6mImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet, List<LeavingVehicle6mItem> leaving6mData)
   {
      //find the leaving item, if we have one, generate impact
      var existingStrategyPrice = parms.currentStrategyPrice;
      if (parms.RetailRating == null)
      {
         return null;
      }
      var rrBand = BandingsService.ProvideRetailRatingBanding((decimal)parms.RetailRating);
      var leavingItem = leaving6mData.FirstOrDefault(x =>
         x.Model == parms.Model &&
         x.Make == parms.Make &&
         x.BodyType == parms.BodyType &&
         x.FuelType == parms.FuelType &&
         x.RetailRatingBand == rrBand
         );

      if (leavingItem == null)
      {
         return null;
      }

      var impact = existingStrategyPrice * (leavingItem.FinalPricePosition - 1);
      if (impact != 0)
      {
         try
         {

            string percentageString = (leavingItem.FinalPricePosition * 100).ToString("0.0") + "%";

            StrategyPriceBuildUpItem buildUp = new StrategyPriceBuildUpItem();
            buildUp.VersionName = factor.StrategyVersion.Name;
            buildUp.FactorName = factor.Name.ToString();
            buildUp.FactorItemLabel = string.Empty;
            buildUp.FactorItemValue = (int)Math.Round(impact, 0, MidpointRounding.AwayFromZero);
            buildUp.SourceValue = string.Empty;
            buildUp.Impact = impact;
            buildUp.RuleSetComment = ruleSet.Comment;
            buildUp.VehicleAdvertSnapshotId = parms.snapshotId;
            buildUp.ExtendedNotes = $"{parms.Make} {parms.Model} {parms.BodyType} {parms.FuelType} {rrBand} 6m @ {percentageString}";
            buildUp.StrategyFactorItemId = factor.StrategyFactorItems.First().Id;
            buildUp.StrategySelectionRuleSetId = ruleSet.Id;

            return buildUp;
         }
         catch (Exception ex)
         {
            return null;
         }
      }
      else
      {
         return null;
      }
   }

   private static StrategyPriceBuildUpItem CalculateRoundToNearestPriceBreakImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      var existingStrategyPrice = parms.currentStrategyPrice;
      var strategyFactorItem = factor.StrategyFactorItems.First();
      var roundWithin = strategyFactorItem.Value;
      string formattedRounding = roundWithin.ToString("C0", CultureInfo.GetCultureInfo("en-GB"));

      decimal newPrice = existingStrategyPrice;
      bool matchFound = false;
      int i = 0;
      while (i < AdvertStratifierService.ATPriceBreaks.Count && !matchFound)
      {
         var priceBreak = AdvertStratifierService.ATPriceBreaks[i];
         if (Math.Abs(existingStrategyPrice - priceBreak) <= roundWithin)
         {
            newPrice = priceBreak;
            matchFound = true; // Set the flag to true to break out of the loop
         }
         i++;
      }

      var impact = newPrice - existingStrategyPrice;

      if (impact != 0)
      {

         StrategyPriceBuildUpItem buildUp = new StrategyPriceBuildUpItem();
         buildUp.VersionName = factor.StrategyVersion.Name;
         buildUp.FactorName = factor.Name.ToString();
         buildUp.FactorItemLabel = string.Empty;
         buildUp.FactorItemValue = (int)Math.Round(impact, 0, MidpointRounding.AwayFromZero);
         buildUp.SourceValue = string.Empty;
         buildUp.Impact = impact;
         buildUp.RuleSetComment = ruleSet.Comment;
         buildUp.VehicleAdvertSnapshotId = parms.snapshotId;
         buildUp.ExtendedNotes = $"Round to price break as within {formattedRounding}";
         buildUp.StrategyFactorItemId = strategyFactorItem.Id;
         buildUp.StrategySelectionRuleSetId = ruleSet.Id;

         return buildUp;


      }
      else
      {
         return null;
      }
   }

   private static StrategyPriceBuildUpItem CalculateRoundToNearestImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      decimal existingStrategyPrice = parms.currentStrategyPrice;
      var strategyFactorItem = factor.StrategyFactorItems.First();


      decimal roundToNearest = strategyFactorItem.Value;

      RoundingMode mode = RoundingMode.RoundAwayFromZero;
      string roundingMessage = string.Empty;
      if (factor.Name == StrategyFactorName.RoundUpToNearestEnding)
      {
         mode = RoundingMode.OnlyRoundUp;
         roundingMessage = " up";
      }
      else if (factor.Name == StrategyFactorName.RoundDownToNearestEnding)
      {
         mode = RoundingMode.OnlyRoundDown;
         roundingMessage = " down";
      }

      decimal rounded = RoundToNearestEndingX(existingStrategyPrice, roundToNearest, mode);

      string formattedRounding = roundToNearest.ToString("C0", CultureInfo.GetCultureInfo("en-GB"));
      var impact = rounded - existingStrategyPrice;

      if (impact != 0)
      {
         var impactItem = new StrategyPriceBuildUpItem();
         impactItem.VersionName = factor.StrategyVersion.Name;
         impactItem.FactorName = factor.Name.ToString();
         impactItem.FactorItemLabel = strategyFactorItem.Label;
         impactItem.FactorItemValue = strategyFactorItem.Value;
         impactItem.Impact = impact;
         impactItem.RuleSetComment = ruleSet.Comment;
         impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
         impactItem.ExtendedNotes = $"Round{roundingMessage} to nearest price ending {formattedRounding}";
         impactItem.StrategyFactorItemId = strategyFactorItem.Id;
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;

         return impactItem;
      }
      else
      {
         return null;
      }
   }

   private static StrategyPriceBuildUpItem CalculateRoundToNearestMultipleImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      decimal existingStrategyPrice = parms.currentStrategyPrice;
      var strategyFactorItem = factor.StrategyFactorItems.First();

      decimal roundToNearest = strategyFactorItem.Value;

      RoundingMode mode = RoundingMode.RoundAwayFromZero;
      string roundingMessage = string.Empty;
      if (factor.Name == StrategyFactorName.RoundUpToNearestMultiple)
      {
         mode = RoundingMode.OnlyRoundUp;
         roundingMessage = " up";
      }
      else if (factor.Name == StrategyFactorName.RoundDownToNearestMultiple)
      {
         mode = RoundingMode.OnlyRoundDown;
         roundingMessage = " down";
      }

      decimal rounded = RoundToMultiple(existingStrategyPrice, roundToNearest, mode);

      string formattedRounding = roundToNearest.ToString("C0", CultureInfo.GetCultureInfo("en-GB"));
      var impact = rounded - existingStrategyPrice;

      if (impact != 0)
      {
         var impactItem = new StrategyPriceBuildUpItem();
         impactItem.VersionName = factor.StrategyVersion.Name;
         impactItem.FactorName = factor.Name.ToString();
         impactItem.FactorItemLabel = strategyFactorItem.Label;
         impactItem.FactorItemValue = strategyFactorItem.Value;
         impactItem.Impact = impact;
         impactItem.RuleSetComment = ruleSet.Comment;
         impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
         impactItem.ExtendedNotes = $"Round{roundingMessage} to multiple of {formattedRounding}";
         impactItem.StrategyFactorItemId = strategyFactorItem.Id;
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;

         //var impactItem = new StrategyFactorItemVehicleWebsiteRating(strategyFactorItem.Id, parms.snapshotId,  $"Round to nearest {formattedRounding}", impact, null, strategySelectionRuleSetId);
         return impactItem;
      }
      else
      {
         return null;
      }
   }

   private static async Task<StrategyPriceBuildUpItem> CalculateWholesaleAdjustmentImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      decimal existingStrategyPrice = parms.currentStrategyPrice;

      var adjustmentPct = factor.StrategyFactorItems.First(x => x.Label == "AdjustmentPct");
      var adjustmentAmount = factor.StrategyFactorItems.First(x => x.Label == "AdjustmentValue");

      decimal adjustedValue = existingStrategyPrice;

      if (adjustmentPct?.Value != null)
      {
         adjustedValue = (adjustedValue * (adjustmentPct.Value / 100));
      }

      if (adjustmentAmount?.Value != null)
      {
         adjustedValue = adjustedValue + adjustmentAmount.Value;
      }

      var impact = adjustedValue - existingStrategyPrice;

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = adjustmentPct.Label;
      impactItem.FactorItemValue = adjustmentPct.Value;
      impactItem.Impact = impact;
      impactItem.RuleSetComment = ruleSet.Comment;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = $"Adjust to {adjustmentPct.Value}% and by £{adjustmentAmount.Value}";
      impactItem.StrategyFactorItemId = adjustmentPct.Id;
      impactItem.StrategySelectionRuleSetId = ruleSet.Id;

      return impactItem;
   }


   private static decimal RoundToNearestEndingX(decimal price, decimal targetEnding, RoundingMode mode)
   {
      if (targetEnding < 0)
      { 
         throw new ArgumentOutOfRangeException(nameof(targetEnding), "Target ending must be non-negative.");
      }

      // Derive block size automatically
      decimal blockSize = (decimal)Math.Pow(10, Math.Ceiling(Math.Log10((double)(targetEnding + 1))));

      // ✅ If price already matches the target ending, just return it
      if ((price % blockSize) == targetEnding)
      {
         return price;
      }

      // Candidate at or before price
      decimal lowerResult = Math.Floor((price - targetEnding) / blockSize) * blockSize + targetEnding;

      // Candidate at or after price
      decimal upperResult = Math.Ceiling((price - targetEnding) / blockSize) * blockSize + targetEnding;

      switch (mode)
      {
         case RoundingMode.OnlyRoundUp:
            return upperResult;

         case RoundingMode.OnlyRoundDown:
            return lowerResult;

         case RoundingMode.RoundAwayFromZero:
         default:
            decimal diffLower = Math.Abs(price - lowerResult);
            decimal diffUpper = Math.Abs(upperResult - price);

            if (diffLower < diffUpper)
            {
               return lowerResult;
            }
            if (diffUpper < diffLower)
            {
               return upperResult;
            }
            return price >= 0 ? upperResult : lowerResult;
      }
   }



   private static decimal RoundToMultiple(decimal price, decimal multiple, RoundingMode mode)
   {
      if (multiple <= 0)
      { 
         throw new ArgumentOutOfRangeException(nameof(multiple), "Multiple must be greater than zero.");
      }

      switch (mode)
      {
         case RoundingMode.OnlyRoundUp:
            return Math.Ceiling(price / multiple) * multiple;

         case RoundingMode.OnlyRoundDown:
            return Math.Floor(price / multiple) * multiple;

         case RoundingMode.RoundAwayFromZero:
         default:
            // Divide, round away from zero, then scale back
            return Math.Round(price / multiple, 0, MidpointRounding.AwayFromZero) * multiple;
      }
   }





   private static StrategyPriceBuildUpItem CalculateMinimumProfitBand(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      var profitAtStrategyPrice = PricedProfitCalculatorService.CalculatePricedProfit((int)parms.currentStrategyPrice, parms.siv, parms.prepCost, parms.ownershipCondition, parms.isVatQ, (int)Math.Round(parms.originalPurchasePrice, 0, MidpointRounding.AwayFromZero));
      var minProfit = factor.StrategyFactorItems.First().Value;
      if (profitAtStrategyPrice < minProfit)
      {
         var newStrategyPrice = PricedProfitCalculatorService.CalculateRevisedSellingPrice(minProfit, parms.siv, parms.prepCost, parms.ownershipCondition, parms.isVatQ, (int)Math.Round(parms.originalPurchasePrice, 0, MidpointRounding.AwayFromZero));
         var impact = newStrategyPrice - parms.currentStrategyPrice;
         string formattedMinProfit = minProfit.ToString("C", CultureInfo.GetCultureInfo("en-GB"));
         var impactItem = new StrategyPriceBuildUpItem();
         impactItem.VersionName = factor.StrategyVersion.Name;
         impactItem.FactorName = factor.Name.ToString();
         impactItem.FactorItemLabel = factor.StrategyFactorItems.First().Label;
         impactItem.FactorItemValue = factor.StrategyFactorItems.First().Value;
         impactItem.Impact = impact;
         impactItem.RuleSetComment = ruleSet.Comment;
         impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
         impactItem.ExtendedNotes = $"Minimum Profit ({formattedMinProfit})";
         impactItem.StrategyFactorItemId = factor.StrategyFactorItems.First().Id;
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;

         return impactItem;
      }
      else
      {
         //profit is not below min profit level, that's ok then - no adjustment to make
         return null;
      }
   }

   private static StrategyPriceBuildUpItem CalculateMinimumPricePosition(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      // Check if valuation is available
      if (parms.valuationAdjusted == null || parms.valuationAdjusted <= 0)
      {
         // No valuation available, cannot calculate price position
         return null;
      }

      var minPricePositionPercent = factor.StrategyFactorItems.First().Value;
      var currentPricePosition = (parms.currentStrategyPrice / parms.valuationAdjusted) * 100;

      if (currentPricePosition < minPricePositionPercent)
      {
         // Calculate the impact needed to achieve the minimum price position
         // Formula: ((userPercentage / 100) - (currentStrategyPrice / valuation)) * valuation
         var impact = (((minPricePositionPercent / 100) - (parms.currentStrategyPrice / parms.valuationAdjusted)) * parms.valuationAdjusted) ?? 0;
         var impactToUse = Math.Max(impact, 0);

         var impactItem = new StrategyPriceBuildUpItem();
         impactItem.VersionName = factor.StrategyVersion.Name;
         impactItem.FactorName = factor.Name.ToString();
         impactItem.FactorItemLabel = factor.StrategyFactorItems.First().Label;
         impactItem.FactorItemValue = factor.StrategyFactorItems.First().Value;
         impactItem.Impact = impactToUse;
         impactItem.RuleSetComment = ruleSet.Comment;
         impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
         impactItem.ExtendedNotes = $"Minimum Price Position ({minPricePositionPercent:F1}%)";
         impactItem.StrategyFactorItemId = factor.StrategyFactorItems.First().Id;
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;

         return impactItem;
      }
      else
      {
         // Price position is already at or above minimum, no adjustment needed
         return null;
      }
   }
   private static StrategyPriceBuildUpItem CalculateMaximumPricePosition(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      // Check if valuation is available
      if (parms.valuationAdjusted == null || parms.valuationAdjusted <= 0)
      {
         // No valuation available, cannot calculate price position
         return null;
      }

      var maxPricePositionPercent = factor.StrategyFactorItems.First().Value;
      var currentPricePosition = (parms.currentStrategyPrice / parms.valuationAdjusted) * 100;

      if (currentPricePosition > maxPricePositionPercent)
      {
         // Calculate the impact needed to achieve the minimum price position
         // Formula: ((userPercentage / 100) - (currentStrategyPrice / valuation)) * valuation
         var impact = (
                        (
                           (maxPricePositionPercent / 100) - (parms.currentStrategyPrice / parms.valuationAdjusted)
                        ) * parms.valuationAdjusted
                       ) ?? 0;
         var impactToUse = Math.Min(impact, 0);

         var impactItem = new StrategyPriceBuildUpItem();
         impactItem.VersionName = factor.StrategyVersion.Name;
         impactItem.FactorName = factor.Name.ToString();
         impactItem.FactorItemLabel = factor.StrategyFactorItems.First().Label;
         impactItem.FactorItemValue = factor.StrategyFactorItems.First().Value;
         impactItem.Impact = impactToUse;
         impactItem.RuleSetComment = ruleSet.Comment;
         impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
         impactItem.ExtendedNotes = $"Maximum Price Position ({maxPricePositionPercent:F1}%)";
         impactItem.StrategyFactorItemId = factor.StrategyFactorItems.First().Id;
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;

         return impactItem;
      }
      else
      {
         // Price position is already at or above minimum, no adjustment needed
         return null;
      }
   }

   private static StrategyPriceBuildUpItem CalculateSetToAdvertisedPriceImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      // Check if advertised price is available
      if (parms.AdvertisedPrice == null || parms.AdvertisedPrice <= 0)
      {
         // No advertised price available, cannot calculate impact
         return null;
      }

      // Calculate the impact needed to set the strategy price to the advertised price
      var impact = parms.AdvertisedPrice - parms.currentStrategyPrice;

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = factor.StrategyFactorItems.First().Label;
      impactItem.FactorItemValue = factor.StrategyFactorItems.First().Value;
      impactItem.Impact = impact ?? 0;
      impactItem.RuleSetComment = ruleSet.Comment;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = $"Set To Advertised Price (£{parms.AdvertisedPrice:F0})";
      impactItem.StrategyFactorItemId = factor.StrategyFactorItems.First().Id;
      impactItem.StrategySelectionRuleSetId = ruleSet.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateOnBrandImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string thisItemLabel = parms.onBrand ? "OnBrand" : "OffBrand";
      //decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(thisItemLabel, parms.strategyPrice, factor, true);

      var factorItem = factor.StrategyFactorItems.FirstOrDefault(x => x.Label == thisItemLabel);
      if (factorItem == null)
      {
         throw new Exception($"Unknown label {thisItemLabel} when evaluating strategy for {factor.Name}");
      }
      decimal impact = parms.currentStrategyPrice * ((factorItem.Value / 100) - 1);
      List<string> notes = new List<string>();
      notes.Add($"{thisItemLabel}");
      var impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(factor, thisItemLabel, thisItemLabel, impact, parms.snapshotId, ruleSet, notes, false);
      return impactItem;
   }

   private static async Task<StrategyPriceBuildUpItem> CalculateCheapestCompetitorImpact(
       AutoTraderCompetitorClient competitorClient,
       RetailerSite retailerSite,
       StrategyFactor factor,
       AdvertParamsForStrategyCalculator parms,
       StrategySelectionRuleSet ruleSet)
   {
      //do competitor search
      return await WorkoutStrategyPriceForLocalCompetitor(
          competitorClient,
          retailerSite,
          parms,
          factor,
          ruleSet
          );
   }


   private static async Task<StrategyPriceBuildUpItem> CalculateMarketPositionImpact(
       AutoTraderCompetitorClient competitorClient,
       RetailerSite retailerSite,
       StrategyFactor factor,
       AdvertParamsForStrategyCalculator parms,
       StrategySelectionRuleSet ruleSet)
   {
      //do competitor search
      return await WorkoutStrategyPriceForMarketPositionScore(
          competitorClient,
          retailerSite,
          parms,
          factor,
          ruleSet
          );
   }

   private static StrategyPriceBuildUpItem CalculateRetailerNameImpact(RetailerSite retailerSite, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string value = retailerSite.Name;
      List<string> notes = new List<string>();
      notes.Add($"Site of {retailerSite.Name}");
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(value, parms.currentStrategyPrice, factor, false, notes);

      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          parms.RetailerName,
          value,
          impact,
          parms.snapshotId,
          ruleSet,
          notes, false
          );
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateBrandImpact(RetailerSite retailerSite, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string value = parms.Make;

      List<string> notes = new List<string>();
      notes.Add($"Make of {parms.Make}");
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(value, parms.currentStrategyPrice, factor, false, notes);

      if (impact == 0)
      {
         return null;
      }
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          value,
          value,
          impact,
          parms.snapshotId,
          ruleSet,
          notes, false
          );
      return impactItem;
   }
   private static StrategyPriceBuildUpItem CalculateModelNameImpact(RetailerSite retailerSite, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string value = parms.Model;
      List<string> notes = new List<string>();
      notes.Add($"Model of {parms.Model}");
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(value, parms.currentStrategyPrice, factor, false, notes);
      if (impact == 0)
      {
         return null;
      }
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          value,
          value,
          impact,
          parms.snapshotId,
          ruleSet,
          notes, false
          );
      return impactItem;
   }
   private static StrategyPriceBuildUpItem CalculateMakeFuelTypeImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string value = $"{parms.Make}|{parms.FuelType}";
      List<string> notes = new List<string>();
      notes.Add($"Make and FuelType of {parms.Make}, {parms.FuelType}");
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(value, parms.currentStrategyPrice, factor, false, notes);
      if (impact == 0)
      {
         return null;
      }
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          value,
          value,
          impact,
          parms.snapshotId,
          ruleSet,
          notes, false
          );
      return impactItem;
   }
   private static StrategyPriceBuildUpItem CalculateMakeAndModelImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string value = $"{parms.Make}|{parms.Model}";
      List<string> notes = new List<string>();
      notes.Add($"Make and Model of {parms.Make}, {parms.Model}");
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(value, parms.currentStrategyPrice, factor, false, notes);
      if (impact == 0)
      {
         return null;
      }
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          value,
          value,
          impact,
          parms.snapshotId,
          ruleSet,
          notes, false
          );
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateMilesPerYearImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.firstRegisteredDate == null)
      {
         throw new Exception("No first registered date");
      }

      if (parms.OdometerReading == null)
      {
         throw new Exception("No odometer reading");
      }

      int daysOld = DateTime.Now.Subtract(parms.firstRegisteredDate.Value).Days;
      int mileagePerAnnum = (int)parms.OdometerReading * 365 / daysOld;

      var matchingItem = factor.StrategyFactorItems.OrderBy(x => int.Parse(x.Label)).FirstOrDefault(x => int.Parse(x.Label) >= mileagePerAnnum);

      if (matchingItem == null)
      {
         //no matching item
         //throw new Exception("No matching item");
         return null;
      }

      if (matchingItem.Value == 100)
      {
         //throw new Exception("No impact");
         return null;
      }

      //we have an unusually low mileage vehicle, apply this factor's impact
      decimal impact = (matchingItem.Value / 100 - 1) * parms.currentStrategyPrice;
      if (impact == 0)
      {
         return null;
         //throw new Exception("No impact");
      }

      List<string> notes = new List<string>();
      notes.Add($"Mileage of {parms.OdometerReading:N0} which is {mileagePerAnnum:N0} per annum, triggering impact of {matchingItem.Value:F1}%");

      var res = new StrategyPriceBuildUpItem();
      res.VersionName = factor.StrategyVersion.Name;
      res.FactorName = factor.Name.ToString();
      res.FactorItemLabel = matchingItem.Label;
      res.FactorItemValue = matchingItem.Value;
      res.SourceValue = parms.OdometerReading.ToString();
      res.Impact = impact;
      res.VehicleAdvertSnapshotId = parms.snapshotId;
      res.ExtendedNotes = string.Join("", notes);
      res.StrategyFactorItemId = matchingItem.Id;

      if (ruleSet != null)
      {
         res.StrategySelectionRuleSetId = ruleSet.Id;
         res.RuleSetComment = ruleSet.Comment;
      }

      return res;

   }

   private static StrategyPriceBuildUpItem CalculateMakeAgeBandImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string value = $"{parms.Make}|{parms.AgeBand}";
      List<string> notes = new List<string>();
      notes.Add($"Make and AgeBand of {parms.Make}, {parms.AgeBand}");
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(value, parms.currentStrategyPrice, factor, false, notes);
      if (impact == 0)
      {
         return null;
      }
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          value,
          value,
          impact,
          parms.snapshotId,
          ruleSet,
          notes, false
          );
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateDaysListedBandImpact(DateTime runDate, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      int value = ((int)parms.DaysListed);
      List<string> notes = new List<string>();
      notes.Add($"Days Listed of {Math.Round(((decimal)parms.DaysListed))}");
      var impactItem = ApplyFactorAndCreateFactorRatings(runDate, factor, value, parms.currentStrategyPrice, parms.snapshotId, ruleSet, notes);
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateDTSDLMatrix(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.DaysToSell == null)
      {
         throw new Exception("No Days to Sell");
      }
      if (parms.DaysListed == null)
      {
         throw new Exception("No Days Listed");
      }

      int dts = ((int)parms.DaysToSell);
      int daysListed = ((int)parms.DaysListed);
      if (daysListed > 999)
      {
         daysListed = 999;
      }

      // Find the break point for retail rating
      List<int> dtsBreaks = factor.StrategyFactorItems
          .Select(x => ExtractDTSValue(x.Label))
          .OrderBy(x => x)
          .Distinct()
          .ToList();

      int dtsBreak;
      try
      {
         dtsBreak = dtsBreaks.First(x => x >= dts);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching retail rating found.");
      }

      // Filter items based on the retail rating break point
      var itemsForDTSBand = factor.StrategyFactorItems
          .Where(x => ExtractDTSValue(x.Label) == dtsBreak)
          .OrderBy(x => ExtractDLValue(x.Label))
          .ToList();

      StrategyFactorItem finalItem;
      try
      {
         // Find the final item based on days listed
         finalItem = itemsForDTSBand.First(x => ExtractDLValue(x.Label) >= daysListed);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching days listed found.");
      }

      decimal impact = parms.currentStrategyPrice * (finalItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Days Listed of {Math.Round(((decimal)parms.DaysListed))}, days to sell of {Math.Round(parms.DaysToSell)}");
      notes.Add($" which selects {finalItem.Label} giving impact of {finalItem.Value:F1}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = finalItem.Label;
      impactItem.FactorItemValue = finalItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = finalItem.Id;

      if (ruleSet != null)
      {
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;
         impactItem.RuleSetComment = ruleSet.Comment;
      }

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculatePYDSMatrix(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.firstRegisteredDate == null)
      {
         return null;
         //throw new Exception("No First Registered Date");
      }

      // Get plate year from first registered date
      int plateYear = ((DateTime)parms.firstRegisteredDate).Year;
      int daysInStock = parms.DaysInStock;

      // Find the break point for plate year
      List<int> pyBreaks = factor.StrategyFactorItems
          .Select(x => ExtractPYValue(x.Label))
          .OrderBy(x => x)
          .Distinct()
          .ToList();

      if (pyBreaks.Count == 0)
      {
         return null;
      }


      int lowestPyBreak = pyBreaks.FirstOrDefault(x => x >= plateYear);

      // Filter items based on the plate year break point
      var itemsForPlateYear = factor.StrategyFactorItems
          .Where(x => ExtractPYValue(x.Label) == lowestPyBreak)
          .OrderBy(x => ExtractDSValue(x.Label))
          .ToList();

      StrategyFactorItem finalItem;
      // Find the final item based on days in stock
      finalItem = itemsForPlateYear.OrderBy(x => ExtractPYValue(x.Label)).FirstOrDefault(x => ExtractDSValue(x.Label) >= daysInStock);
      if (finalItem == null)
      {
         return null;
      }

      decimal impact = parms.currentStrategyPrice * (finalItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Plate Year of {plateYear.ToString()}, days in stock of {parms.DaysInStock.ToString()}");
      ;
      notes.Add($" which selects {finalItem.Label} giving impact of {finalItem.Value:F1}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = finalItem.Label;
      impactItem.FactorItemValue = finalItem.Value;
      impactItem.SourceValue = string.Empty;
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = finalItem.Id;

      if (ruleSet != null)
      {
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;
         impactItem.RuleSetComment = ruleSet.Comment;
      }

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateVBDSMatrix(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.currentStrategyPrice == 0 || parms.DaysInStock == 0)
      {
         return null;
      }
      int priceBand = BandingsService.ProvideDetailedValueBand((int)parms.currentStrategyPrice);

      // Find the break point for days in stock

      List<int> priceBandBreaks = factor.StrategyFactorItems
          .Select(x => ExtractPriceBandValue(x.Label))
          .OrderBy(x => x)
          .Distinct()
          .ToList();

      int? priceBandBand = priceBandBreaks.FirstOrDefault(x => x >= priceBand);

      if (priceBandBand == null)
      {
         return null;
      }

      // Filter items based on the retail rating break point
      var itemsForRetailRating = factor.StrategyFactorItems
          .Where(x => ExtractPriceBandValue(x.Label) == priceBandBand)
          .OrderBy(x => ExtractDSValue(x.Label))
          .ToList();


      StrategyFactorItem finalItem = itemsForRetailRating.FirstOrDefault(x => ExtractDSValue(x.Label) >= parms.DaysInStock);
      if (finalItem == null)
      {
         return null;
      }


      decimal impact = parms.currentStrategyPrice * (finalItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Price Band of {priceBand.ToString("C0")}, days in stock of {parms.DaysInStock.ToString()}");
      notes.Add($" which selects {finalItem.Label} giving impact of {finalItem.Value:F1}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = finalItem.Label;
      impactItem.FactorItemValue = finalItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = finalItem.Id;

      if (ruleSet != null)
      {
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;
         impactItem.RuleSetComment = ruleSet.Comment;
      }

      return impactItem;

   }

   private static StrategyPriceBuildUpItem CalculateRRDSMatrix(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.RetailRating == null)
      {
         throw new Exception("No Retail Rating");
      }

      int rr = ((int)parms.RetailRating);
      int daysInStock = ((int)parms.DaysInStock);

      // Find the break point for retail rating
      List<int> rrBreaks = factor.StrategyFactorItems
          .Select(x => ExtractRRValue(x.Label))
          .OrderBy(x => x)
          .Distinct()
          .ToList();

      int rrBreak;
      try
      {
         rrBreak = rrBreaks.First(x => x >= rr);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching retail rating found.");
      }

      // Filter items based on the retail rating break point
      var itemsForRetailRating = factor.StrategyFactorItems
          .Where(x => ExtractRRValue(x.Label) == rrBreak)
          .OrderBy(x => ExtractDSValue(x.Label))
          .ToList();

      StrategyFactorItem finalItem;
      try
      {
         // Find the final item based on days in stock
         finalItem = itemsForRetailRating.First(x => ExtractDSValue(x.Label) >= daysInStock);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching days in stock found.");
      }

      decimal impact = parms.currentStrategyPrice * (finalItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Retail Rating of {Math.Round(((decimal)parms.RetailRating))}, days in stock of {parms.DaysInStock.ToString()}");
      notes.Add($" which selects {finalItem.Label} giving impact of {finalItem.Value:F1}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = finalItem.Label;
      impactItem.FactorItemValue = finalItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = finalItem.Id;

      if (ruleSet != null)
      {
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;
         impactItem.RuleSetComment = ruleSet.Comment;
      }

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateRRDBMatrix(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.RetailRating == null)
      {
         throw new Exception("No Retail Rating");
      }
      if (parms.DaysBookedIn == null)
      {
         throw new Exception("No Days Booked In");
      }

      int rr = ((int)parms.RetailRating);
      int daysBookedIn = ((int)parms.DaysBookedIn);

      // Find the break point for retail rating
      List<int> rrBreaks = factor.StrategyFactorItems
          .Select(x => ExtractRRValue(x.Label))
          .OrderBy(x => x)
          .Distinct()
          .ToList();

      int rrBreak;
      try
      {
         rrBreak = rrBreaks.First(x => x >= rr);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching retail rating found.");
      }

      // Filter items based on the retail rating break point
      var itemsForRetailRating = factor.StrategyFactorItems
          .Where(x => ExtractRRValue(x.Label) == rrBreak)
          .OrderBy(x => ExtractDBValue(x.Label))
          .ToList();

      StrategyFactorItem finalItem;
      try
      {
         // Find the final item based on days booked in
         finalItem = itemsForRetailRating.First(x => ExtractDBValue(x.Label) >= daysBookedIn);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching days booked in found.");
      }

      decimal impact = parms.currentStrategyPrice * (finalItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Retail Rating of {Math.Round(((decimal)parms.RetailRating))}, days booked in of {parms.DaysBookedIn.ToString()}");
      notes.Add($" which selects {finalItem.Label} giving impact of {finalItem.Value:F1}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = finalItem.Label;
      impactItem.FactorItemValue = finalItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = finalItem.Id;

      if (ruleSet != null)
      {
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;
         impactItem.RuleSetComment = ruleSet.Comment;
      }

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateRRDLMatrixImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.RetailRating == null)
      {
         throw new Exception("No Retail Rating");
      }
      if (parms.DaysListed == null)
      {
         throw new Exception("No Days Listed");
      }

      int rr = ((int)parms.RetailRating);
      int daysListed = ((int)parms.DaysListed);
      if (daysListed > 999)
      {
         daysListed = 999;
      }

      // Find the break point for retail rating
      List<int> rrBreaks = factor.StrategyFactorItems
          .Select(x => ExtractRRValue(x.Label))
          .OrderBy(x => x)
          .Distinct()
          .ToList();

      int rrBreak;
      try
      {
         rrBreak = rrBreaks.First(x => x >= rr);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching retail rating found.");
      }

      // Filter items based on the retail rating break point
      var itemsForRetailRating = factor.StrategyFactorItems
          .Where(x => ExtractRRValue(x.Label) == rrBreak)
          .OrderBy(x => ExtractDLValue(x.Label))
          .ToList();

      StrategyFactorItem finalItem;
      try
      {
         // Find the final item based on days listed
         finalItem = itemsForRetailRating.First(x => ExtractDLValue(x.Label) >= daysListed);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching days listed found.");
      }

      decimal impact = parms.currentStrategyPrice * (finalItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Retail Rating of {Math.Round(((decimal)parms.RetailRating))}, days listed of {parms.DaysListed.ToString()}");
      notes.Add($" which selects {finalItem.Label} giving impact of {finalItem.Value:F1}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = finalItem.Label;
      impactItem.FactorItemValue = finalItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = finalItem.Id;

      if (ruleSet != null)
      {
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;
         impactItem.RuleSetComment = ruleSet.Comment;
      }

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateDaysInStockImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.DaysInStock == null)
      {
         throw new Exception("No Days Listed");
      }

      int daysInStock = ((int)parms.DaysInStock);
      var orderedItems = factor.StrategyFactorItems.OrderBy(x => int.Parse(x.Label)).ToList();
      var matchingItem = orderedItems.FirstOrDefault(x => daysInStock <= int.Parse(x.Label));

      if (matchingItem == null)
      {
         throw new Exception($"Failed to finding matching strategy factor item for days in stock {daysInStock}");
      }
      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Days in stock of {parms.DaysInStock.ToString()}");
      notes.Add($" which selects {matchingItem.Label} giving impact of {matchingItem.Value}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateRetailRatingBandImpact(DateTime runDate, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.RetailRating == null)
      {
         throw new Exception($"No Retail Rating");
      }
      int value = ((int)parms.RetailRating);
      List<string> notes = new List<string>();
      notes.Add($"Retail Rating of {Math.Round(((decimal)(parms.RetailRating)))}");
      var impactItem = ApplyFactorAndCreateFactorRatings(runDate, factor, value, parms.currentStrategyPrice, parms.snapshotId, ruleSet, notes);
      return impactItem;
   }
   private static StrategyPriceBuildUpItem CalculateRetailRatingBand10sImpact(DateTime runDate, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.RetailRating == null)
      {
         throw new Exception($"No Retail Rating");
      }
      int value = ((int)parms.RetailRating);
      List<string> notes = new List<string>();
      notes.Add($"Retail Rating of {Math.Round(((decimal)parms.RetailRating))}");
      var impactItem = ApplyFactorAndCreateFactorRatings(runDate, factor, value, parms.currentStrategyPrice, parms.snapshotId, ruleSet, notes);
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculatePerformanceRatingScoreImpact(DateTime runDate, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.PerfRatingScore == null)
      {
         // Skip this factor if PerfRatingScore is null
         return null;
      }
      int value = ((int)parms.PerfRatingScore);
      List<string> notes = new List<string>();
      notes.Add($"Performance Rating of {parms.RetailRating}");
      var impactItem = ApplyFactorAndCreateFactorRatings(runDate, factor, value, parms.currentStrategyPrice, parms.snapshotId, ruleSet, notes);
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateDaysListedImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.DaysListed == null)
      {
         throw new Exception("No Days Listed");
      }

      int daysListed = ((int)parms.DaysListed);
      if (daysListed > 999)
      {
         daysListed = 999;
      }
      var orderedItems = factor.StrategyFactorItems.OrderBy(x => int.Parse(x.Label)).ToList();
      var matchingItem = orderedItems.FirstOrDefault(x => daysListed <= int.Parse(x.Label));

      if (matchingItem == null)
      {
         throw new Exception($"Failed to finding matching strategy factor item for days listed {daysListed}");
      }
      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);

      List<string> notes = new List<string>();
      notes.Add($"Days Listed of {parms.DaysListed}");
      notes.Add($" which selects {matchingItem.Label} giving impact of {matchingItem.Value}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateRetailRatingImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.RetailRating == null)
      {
         throw new Exception("No Retail Rating");
      }

      int retailRating = (int)Math.Floor(parms.RetailRating.Value);
      if (retailRating > 100)
      {
         retailRating = 100;
      }
      var orderedItems = factor.StrategyFactorItems.OrderBy(x => int.Parse(x.Label)).ToList();
      var matchingItem = orderedItems.FirstOrDefault(x => retailRating <= int.Parse(x.Label));

      if (matchingItem == null)
      {
         throw new Exception($"Failed to finding matching strategy factor item for retail rating {retailRating}");
      }
      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Retail rating of {parms.RetailRating}");
      notes.Add($" which selects {matchingItem.Label} giving impact of {matchingItem.Value}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = retailRating.ToString();
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculatePerfRatingImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.PerfRatingScore == null)
      {
         throw new Exception("No Performance Rating");
      }

      int perfRating = ((int)parms.PerfRatingScore);
      var orderedItems = factor.StrategyFactorItems.OrderBy(x => int.Parse(x.Label.Split("-")[0])).ToList();
      var matchingItem = orderedItems.FirstOrDefault(x => perfRating >= ExtractMinMaxValue(x.Label).Item1 && perfRating <= ExtractMinMaxValue(x.Label).Item2);

      if (matchingItem == null)
      {
         throw new Exception($"Failed to finding matching strategy factor item for days listed {perfRating}");
      }
      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Performance rating score of {parms.PerfRatingScore}");
      notes.Add($" which selects {matchingItem.Label} giving impact of {matchingItem.Value}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateMileageImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.OdometerReading == null)
      {
         return null;
         //throw new Exception("No Odometer Reading");
      }

      int odometerReading = ((int)parms.OdometerReading);

      var orderedItems = factor.StrategyFactorItems
         .Select(x => new StrategyFactorItem() { Id = x.Id, Label = x.Label.Replace("<", "").Replace(",", ""), Value = x.Value })
         .ToList();


      var matchingItem = orderedItems
         .Where(x => odometerReading < int.Parse(x.Label))
         .OrderBy(x => x.Label)
         .FirstOrDefault();

      if (matchingItem == null)
      {
         return null;
         //throw new Exception($"Failed to finding matching strategy factor item for odometer {odometerReading}");
      }
      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Odometer reading of {parms.OdometerReading}");
      notes.Add($" which selects {matchingItem.Label} giving impact of {matchingItem.Value}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateSpecificColourImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.SpecificColour == null)
      {
         return null;
      }


      string specificColour = parms.SpecificColour;
      var matchingItem = factor.StrategyFactorItems.FirstOrDefault(x => specificColour.StartsWith(x.Label, StringComparison.OrdinalIgnoreCase));

      if (matchingItem == null)
      {
         return null;
      }

      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Manufacturer colour of {parms.SpecificColour}");
      notes.Add($" which gives impact of {matchingItem.Value:F1}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = specificColour;// string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateColourImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.Colour == null)
      {
         return null;
      }


      var matchingItem = factor.StrategyFactorItems.FirstOrDefault(x => parms.Colour.StartsWith(x.Label, StringComparison.OrdinalIgnoreCase));

      if (matchingItem == null)
      {
         return null;
      }

      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Standard colour of {parms.Colour}");
      notes.Add($" which gives impact of {matchingItem.Value:F1}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = parms.Colour;
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateOwnersVsAgeImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.AgeAndOwners == null)
      {
         return null;
      }

      var matchingItem = factor.StrategyFactorItems.FirstOrDefault(x => x.Label == parms.AgeAndOwners);

      if (matchingItem == null)
      {
         return null;
      }

      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);
      List<string> notes = new List<string>();
      notes.Add($"Age and owners of {parms.AgeAndOwners}");
      notes.Add($" which gives impact of {matchingItem.Value:F1}%");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = parms.AgeAndOwners; // string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }
   private static List<StrategyPriceBuildUpItem> CalculateTagImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet, List<TagDTO> tags)
   {
      // This layer is applied if the vehicle has the tag that's on the strategy layer

      var today = DateTime.Now;
      List<StrategyPriceBuildUpItem> buildupItems = new List<StrategyPriceBuildUpItem>();

      var matchingItem = factor.StrategyFactorItems.FirstOrDefault();

      if (matchingItem == null)
      {
         return [];
      }

      var matchingItemTagId = Int32.Parse(matchingItem.Label);

      var tagsOnAdvert = parms.VehicleAdvertTagList?.Split("||")?.Select(x => Int32.Parse(x));

      if (tagsOnAdvert == null || !tagsOnAdvert.Contains(matchingItemTagId))
      {
         return [];
      }

      var tag = tags.FirstOrDefault(x => x.Id == matchingItemTagId);

      // First we do the % adjustment 

      if (matchingItem.Value != 100 && matchingItem.Value != null)
      {

         decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);

         var impactItem1 = new StrategyPriceBuildUpItem();
         impactItem1.VersionName = factor.StrategyVersion.Name;
         impactItem1.FactorName = factor.Name.ToString();
         impactItem1.FactorItemLabel = "Tag: " + tag.Label;
         impactItem1.FactorItemValue = matchingItem.Value;
         impactItem1.FactorItemValueAmount = 0;
         impactItem1.SourceValue = "Tag-Percent";
         impactItem1.Impact = impact;
         impactItem1.ExtendedNotes = "Tag: " + tag.Label + " applies impact of " + matchingItem.Value + "%";
         impactItem1.VehicleAdvertSnapshotId = parms.snapshotId;
         impactItem1.StrategyFactorItemId = matchingItem.Id;

         buildupItems.Add(impactItem1);
      }

      // Now we add the raw ££ adjustment

      if (matchingItem.ValueAmount != null && matchingItem.ValueAmount != 0)
      {
         var impactItem2 = new StrategyPriceBuildUpItem();

         impactItem2.VersionName = factor.StrategyVersion.Name;
         impactItem2.FactorName = factor.Name.ToString();
         impactItem2.FactorItemLabel = "Tag: " + tag.Label;
         impactItem2.FactorItemValue = 100;
         impactItem2.FactorItemValueAmount = matchingItem.ValueAmount.Value;
         impactItem2.SourceValue = "Tag-Currency";
         impactItem2.Impact = matchingItem.ValueAmount.Value;
         impactItem2.VehicleAdvertSnapshotId = parms.snapshotId;
         impactItem2.ExtendedNotes = "Tag: " + tag.Label;
         impactItem2.StrategyFactorItemId = matchingItem.Id;

         buildupItems.Add(impactItem2);
      }


      return buildupItems;
   }
   private static List<StrategyPriceBuildUpItem> CalculateApplyTagAdjustmentImpact(
      StrategyFactor factor,
      AdvertParamsForStrategyCalculator parms,
      StrategySelectionRuleSet ruleSet,
      List<TagDTO> dealerTags
      )
   {
      // This layer iterates each tag on the vehicle and applies adjustments on that tag

      var today = DateTime.Now;
      List<StrategyPriceBuildUpItem> buildupItems = new List<StrategyPriceBuildUpItem>();

      var matchingItem = factor.StrategyFactorItems.FirstOrDefault();

      if (matchingItem == null)
      {
         return [];
      }

      // Get the factors that have been applied to this vehicle
      var tagIds = parms.VehicleAdvertTagList?.Split("||").Select(x => Int32.Parse(x)).ToList();

      if (tagIds == null)
      {
         return [];
      }

      foreach (var tagId in tagIds)
      {
         var tag = dealerTags.FirstOrDefault(x => x.Id == tagId);

         if (tag == null)
         {
            continue;
         }

         // First we do the % adjustment 

         if (tag.StrategyImpactPct > 0)
         {

            decimal impact = parms.currentStrategyPrice * (tag.StrategyImpactPct / 100 - 1);

            var impactItem1 = new StrategyPriceBuildUpItem();
            impactItem1.VersionName = factor.StrategyVersion.Name;
            impactItem1.FactorName = factor.Name.ToString();
            impactItem1.FactorItemLabel = "Tag: " + tag.Label;
            impactItem1.FactorItemValue = tag.StrategyImpactPct;
            impactItem1.FactorItemValueAmount = 0;
            impactItem1.SourceValue = "ApplyTags-Percent";
            impactItem1.Impact = impact;
            impactItem1.VehicleAdvertSnapshotId = parms.snapshotId;
            impactItem1.ExtendedNotes = string.Empty;
            impactItem1.ExtendedNotes = "Applying Tag: " + tag.Label + " wth impact of " + matchingItem.Value + "%";
            impactItem1.StrategyFactorItemId = matchingItem.Id;

            buildupItems.Add(impactItem1);
         }

         // Now we add the raw ££ adjustment

         if (tag.StrategyImpactAmount != 0)
         {
            var impactItem2 = new StrategyPriceBuildUpItem();
            impactItem2.VersionName = factor.StrategyVersion.Name;
            impactItem2.FactorName = factor.Name.ToString();
            impactItem2.FactorItemLabel = "Tag: " + tag.Label;
            impactItem2.FactorItemValue = 100;
            impactItem2.FactorItemValueAmount = tag.StrategyImpactAmount;
            impactItem2.SourceValue = "ApplyTags-Currency";
            impactItem2.Impact = tag.StrategyImpactAmount;
            impactItem2.VehicleAdvertSnapshotId = parms.snapshotId;
            impactItem2.ExtendedNotes = "Applying Tag: " + tag.Label;
            impactItem2.StrategyFactorItemId = matchingItem.Id;

            buildupItems.Add(impactItem2);
         }


      }
      return buildupItems;
   }

   private static StrategyPriceBuildUpItem CalculateDateRangeImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      var today = DateTime.Now;
      //List<StrategyPriceBuildUpItem> buildupItems = new List<StrategyPriceBuildUpItem>();

      var matchingItem = factor.StrategyFactorItems.FirstOrDefault();

      if (matchingItem == null)
      {
         return null;
      }

      var dateParts = matchingItem.Label.Split("|");

      if (dateParts.Count() != 2)
      {
         return null;
      }

      var fromOK = DateTime.TryParse(dateParts[0], out DateTime fromDate);
      var toOK = DateTime.TryParse(dateParts[1], out DateTime toDate);

      if (!fromOK || !toOK)
      {
         return null;
      }

      toDate = toDate.Date.AddDays(1).AddTicks(-1);

      if (toDate < DateTime.Now || fromDate > DateTime.Now)
      {
         return null;
      }

      // First we do the % adjustment 

      //var percentFactor = matchingItem.Value / 100M;
      string percentNote = string.Empty;
      if (matchingItem.Value != 100)
      {
         percentNote = $"{matchingItem.Value}% factor";
      }
      string valueNote = string.Empty;
      if (matchingItem.ValueAmount != null)
      {
         decimal amount = matchingItem.ValueAmount.Value;
         valueNote = $"{(amount >= 0 ? "+" : "-")}{Math.Abs(amount)}";
      }

      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100M - 1) + matchingItem.ValueAmount.Value;
      List<string> notes = new List<string>();
      notes.Add($"Today is {DateTime.Now.ToString("dd MMM yyyy")}, selecting {percentNote} {valueNote}");
      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.FactorItemValueAmount = 0;
      impactItem.SourceValue = "DateRange";
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Join("", notes);
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;

      // Now we add the raw ££ adjustment

      //if (matchingItem.ValueAmount != null)
      //{
      //   var impactItem2 = new StrategyPriceBuildUpItem();
      //   impactItem2.VersionName = factor.StrategyVersion.Name;
      //   impactItem2.FactorName = factor.Name.ToString();
      //   impactItem2.FactorItemLabel = matchingItem.Label;
      //   impactItem2.FactorItemValue = 100;
      //   impactItem2.FactorItemValueAmount = matchingItem.ValueAmount.Value;
      //   impactItem2.SourceValue = "DateRange-Currency";
      //   impactItem2.Impact = matchingItem.ValueAmount.Value;
      //   impactItem2.VehicleAdvertSnapshotId = parms.snapshotId;
      //   impactItem2.ExtendedNotes = string.Empty;
      //   impactItem2.StrategyFactorItemId = matchingItem.Id;

      //   buildupItems.Add(impactItem2);
      //}


   }



   public static int ExtractRRValue(string input)
   {
      // Regular expression to match "RR" followed by digits before the "|"
      Regex regex = new Regex(@"RR(\d+)\|");
      Match match = regex.Match(input);

      if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
      {
         return result;
      }

      throw new Exception("Failure extracting Retail Rating");
   }

   public static int ExtractPriceBandValue(string input)
   {
      // Regular expression to match "VB" followed by digits before the "|"
      Regex regex = new Regex(@"VB(\d+)\|");
      Match match = regex.Match(input);

      if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
      {
         return result;
      }

      throw new Exception("Failure extracting Price Band");
   }

   public static int ExtractDTSValue(string input)
   {
      // Regular expression to match "DTS" followed by digits before the "|"
      Regex regex = new Regex(@"DTS(\d+)\|");
      Match match = regex.Match(input);

      if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
      {
         return result;
      }

      throw new Exception("Failure extracting Days to Sell");


   }

   public static (int, int) ExtractMinMaxValue(string input)

   {
      if (string.IsNullOrEmpty(input))
      {
         throw new ArgumentException("Input string cannot be null or empty.", nameof(input));
      }

      var parts = input.Split('-');

      if (parts.Length != 2)
      {
         throw new ArgumentException("Input string must be in the format 'start-end'.", nameof(input));
      }

      if (!int.TryParse(parts[0], out int start) || !int.TryParse(parts[1], out int end))
      {
         throw new ArgumentException("Both parts of the input string must be valid integers.", nameof(input));
      }

      return (start, end);
   }

   private static int ExtractDLValue(string input)
   {
      // Regular expression to match "DL" followed by digits
      Regex regex = new Regex(@"\|DL(\d+)");
      Match match = regex.Match(input);

      if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
      {
         return result;
      }

      throw new Exception("Failure extracting Days Listed");
   }

   private static int ExtractDSValue(string input)
   {
      // Regular expression to match "DS" followed by digits
      Regex regex = new Regex(@"\|DS(\d+)");
      Match match = regex.Match(input);

      if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
      {
         return result;
      }

      throw new Exception("Failure extracting Days In Stock");
   }

   public static int ExtractPYValue(string input)
   {
      // Regular expression to match "PY" followed by digits before the "|"
      Regex regex = new Regex(@"PY(\d+)\|");
      Match match = regex.Match(input);

      if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
      {
         return result;
      }

      throw new Exception("Failure extracting Plate Year");
   }

   private static int ExtractDBValue(string input)
   {
      // Regular expression to match "DB" followed by digits
      Regex regex = new Regex(@"\|DB(\d+)");
      Match match = regex.Match(input);

      if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
      {
         return result;
      }

      throw new Exception("Failure extracting Days Booked In");
   }


   private static StrategyPriceBuildUpItem ApplyFactorAndCreateFactorRatings(DateTime runDate, StrategyFactor factor, int value, decimal strategyPrice, int snapshotId, StrategySelectionRuleSet ruleSet, List<string> notes)
   {
      string thisItemLabel = AdvertStratifierService.Stratify(value, factor.Name);
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactorThatRequiresStratification(thisItemLabel, factor, strategyPrice);
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          thisItemLabel,
          (value).ToString(),
          impact,
          snapshotId,
          ruleSet,
          notes, true
          );
      return impactItem;
   }


   private static StrategyPriceBuildUpItem EarlyReturnAsCannotDoDaysToSellStrategyPrice(DateTime runDate, int? valuationToUse, StrategyFactor factor, int snapshotId, StrategySelectionRuleSet ruleSet)
   {
      string note = $"Unable to calculate strategy price as days to sell cannot be measured";
      StrategyPriceBuildUpItem buildUpItem = ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
                  factor,
                  "DaysToSell",
                  0,
                  note,
                  snapshotId,
                  ruleSet);

      return buildUpItem;

   }




   public static async Task<decimal?> FindPricePoint(
       AutoTraderVehicleMetricsClient atMetricsClient,
       TokenResponse tokenResponse,
       RetailerSite retailerSite,
       int valuationToUse,
       decimal targetDays,
       int mileageToUse,
       decimal lowPP,
       decimal highPP,
       string derivativeId, string portalOptions, bool hasOptionsSpecified, DateTime? firstRegisteredDate, string atBaseUrl, ILog logger)
   {
      int lowPrice = (int)Math.Ceiling(valuationToUse * lowPP);
      int highPrice = (int)Math.Floor(valuationToUse * highPP);


      try
      {

         Task<decimal> taskLow = ProvideDaysToSellTask(retailerSite, mileageToUse,
         lowPrice, atMetricsClient, tokenResponse.AccessToken, derivativeId, portalOptions,
          hasOptionsSpecified, firstRegisteredDate, atBaseUrl, valuationToUse, logger)();


         Task<decimal> taskHigh = ProvideDaysToSellTask(retailerSite, mileageToUse,
          highPrice, atMetricsClient, tokenResponse.AccessToken, derivativeId, portalOptions,
           hasOptionsSpecified, firstRegisteredDate, atBaseUrl, valuationToUse, logger)();

         await Task.WhenAll(taskLow, taskHigh);


         decimal daysToSellAtHigh = taskHigh.Result;
         decimal daysToSellAtLow = taskLow.Result;
         if (daysToSellAtHigh == 0 || daysToSellAtLow == 0)
         {
            return null;
         }

         return WorkoutPricePointFromDaysToSellResults(targetDays, lowPP, highPP, daysToSellAtHigh, daysToSellAtLow);

      }
      catch (Exception ex)
      {
         throw new Exception(ex.Message, ex);
      }
   }

   public static decimal WorkoutPricePointFromDaysToSellResults(decimal targetDays, decimal lowPP, decimal highPP, decimal daysToSellAtHigh, decimal daysToSellAtLow)
   {
      var daysRange = daysToSellAtHigh - daysToSellAtLow;
      var resultingPricePoint = ((targetDays - daysToSellAtLow) / daysRange * (highPP - lowPP)) + lowPP;
      return resultingPricePoint;
   }




   public static Func<Task<decimal>> ProvideDaysToSellTask(
       RetailerSite retailerSite,
       int mileageToUse,
       int amount,
       AutoTraderVehicleMetricsClient atMetricsClient,
       string token,
       string derivativeId,
       string portalOptions,
       bool hasOptionsSpecified,
       DateTime? firstRegisteredDate, string atBaseURL, decimal valuationToUse, ILog? logger)
   {
      return async () =>
      {
         try
         {

            GetAdvertPriceAdjustedDaysToSellParams parms = new GetAdvertPriceAdjustedDaysToSellParams()
            {
               AutotraderBaseURL = atBaseURL,
               AdvertiserId = retailerSite.RetailerId,
               DerivativeId = derivativeId,
               FirstRegistrationDate = firstRegisteredDate,
               OdometerReadingMiles = mileageToUse,
               Amount = amount,
               UseSpecificOptions = hasOptionsSpecified,
               SpecificOptionNames = hasOptionsSpecified ? portalOptions.Split(',') : new List<string>(),

               AverageValuation = valuationToUse,
               AdjustedValuation = valuationToUse
            };

            return await atMetricsClient.GetAdvertPriceAdjustedDaysToSell(parms, token, logger);
         }
         catch (Exception ex)
         {
            return 0;
         }
      };
   }


   private static async Task<StrategyPriceBuildUpItem> WorkoutStrategyPriceBasedOnValuationChangeUntilSellAndReturnImpact(
      DateTime runDate,
      AutoTraderFutureValuationsClient atFutureValsClient,
      TokenResponse tokenResponse,
      RetailerSite retailerSite,
      StrategyFactor factor,
      AdvertParamsForStrategyCalculator parms
      , string atBaseURL, StrategySelectionRuleSet ruleSet)
   {
      //we already know the days to sell (check what this would be based on for a newly valued vehicle)
      //we just need to get a future valuation at that point
      //diff between valuation then and valuation now is this layer i guess.


      //throw new NotImplementedException();

      List<DateTime> futureValnPoints = new List<DateTime>();//
      DateTime daysToSell = DateTime.Now.AddDays((int)Math.Ceiling(parms.DaysToSell));
      futureValnPoints.Add(daysToSell);


      GetFutureValuationsParams getFutureValuationsParams = new GetFutureValuationsParams()
      {
         futureValuationPoints = futureValnPoints,
         derivativeId = parms.derivativeId,
         firstRegistrationDate = parms.firstRegisteredDate.Value,
         odometerReading = parms.OdometerReading.Value,
         retailerId = retailerSite.RetailerId,
         currentValuation = parms.valuationAverage.Value,
         currentAdjustedValuation = parms.valuationAdjusted.Value
      };
      var futureValuations = await atFutureValsClient.GetFutureValuation(getFutureValuationsParams,
      tokenResponse, null);


      var valuationForLastDay = futureValuations.Last().RetailValueExcludingVat ?? futureValuations.Last().RetailValue;
      var impact = (decimal)(valuationForLastDay - parms.valuationAdjusted);

      List<string> notes = new List<string>();
      notes.Add($"Valuation now is {((decimal)parms.valuationAdjusted).ToString("C0")} and will be {valuationForLastDay.ToString("C0")}");



      if (impact != 0)
      {
         StrategyPriceBuildUpItem buildUp = new StrategyPriceBuildUpItem();
         buildUp.VersionName = factor.StrategyVersion.Name;
         buildUp.FactorName = factor.Name.ToString();
         buildUp.FactorItemLabel = string.Empty;
         buildUp.FactorItemValue = (int)Math.Round(impact, 0, MidpointRounding.AwayFromZero);
         buildUp.SourceValue = string.Empty;
         buildUp.Impact = impact;
         buildUp.RuleSetComment = ruleSet.Comment;
         buildUp.VehicleAdvertSnapshotId = parms.snapshotId;
         buildUp.StrategyFactorItemId = factor.StrategyFactorItems.First().Id;
         buildUp.ExtendedNotes = string.Join("", notes);
         buildUp.StrategySelectionRuleSetId = ruleSet.Id;

         return buildUp;
      }
      else
      {
         return null;
      }




   }



   private static async Task<StrategyPriceBuildUpItem> WorkoutStrategyPriceBasedOnDaysToSellRequirementAndReturnImpact(
       DateTime runDate,
       AutoTraderVehicleMetricsClient atMetricsClient,
       TokenResponse tokenResponse,
       RetailerSite retailerSite,
       StrategyFactor factor,
       AdvertParamsForStrategyCalculator parms
       , string atBaseURL, StrategySelectionRuleSet ruleSet, ILog logger)
   {
      decimal factorItemValue = factor.StrategyFactorItems.First().Value;
      var targetDays = factorItemValue;

      //check must have at least one of these
      if (parms.dateOnForecourt == null && parms.OdometerReading == null)
      {
         var returnItem = EarlyReturnAsCannotDoDaysToSellStrategyPrice(runDate, (int)parms.valuationAdjusted, factor, parms.snapshotId, ruleSet);
         return returnItem;
      }

      int mileageToUse = WorkoutMileageToUse(parms.OdometerReading, parms.firstRegisteredDate, parms.dateOnForecourt);

      //firstly find out what the very lowest price is we can submit to the portal

      //we send in the adjusted val.  but the AT endpoint will limit us based on the average val.  FFS
      decimal lowestPPNew = Workout90PctPrice(parms.valuationAdjusted, parms.valuationAverage);

      //get a rough estimate of the ideal price point using 90% and 100%
      decimal highPP = 1M;
      decimal? resultingPricePoint = null;
      try
      {
         decimal? pricePointResult = await FindPricePoint(
             atMetricsClient,
             tokenResponse,
             retailerSite,
             (int)parms.valuationAdjusted,
             targetDays,
             mileageToUse,
             lowestPPNew,
             highPP,
             parms.derivativeId,
             parms.portalOptions,
             parms.hasOptionsSpecified,
             parms.firstRegisteredDate,
             atBaseURL, logger);
         resultingPricePoint = pricePointResult;
      }
      catch (Exception ex)
      {
         StrategyPriceBuildUpItem returnItem = EarlyReturnAsCannotDoDaysToSellStrategyPrice(runDate, (int)parms.valuationAdjusted, factor, parms.snapshotId, ruleSet);
         return returnItem;
      }

      if (resultingPricePoint == null || resultingPricePoint == 0)
      {
         //early return if either of the days to sell are 0 due to some error e.g. unusual vehicle
         StrategyPriceBuildUpItem returnItem = EarlyReturnAsCannotDoDaysToSellStrategyPrice(runDate, (int)parms.valuationAdjusted, factor, parms.snapshotId, ruleSet);
         return returnItem;
      }
      else
      {
         //further increase the accuracy of the price point if we are able to
         decimal highPercent = Math.Ceiling((decimal)resultingPricePoint * 100) / 100;
         decimal lowPercent = highPercent - 0.01M;
         if (lowPercent > lowestPPNew && highPercent < 1.1M)
         {
            try
            {
               var pricePointRes = await FindPricePoint(
                   atMetricsClient,
                   tokenResponse,
                   retailerSite,
                   (int)parms.valuationAdjusted,
                   targetDays,
                   mileageToUse,
                   lowPercent,
                   highPercent,
                   parms.derivativeId,
                   parms.portalOptions,
                   parms.hasOptionsSpecified,
                   parms.firstRegisteredDate, atBaseURL, logger);
               resultingPricePoint = pricePointRes;

            }
            catch (Exception ex)
            {
               //Guess it couldn't do the fine tuning
            }
            decimal impact = ((decimal)resultingPricePoint - 1M) * (decimal)parms.valuationAdjusted;
            string extendedNote = $"Adjustment to valuation to achieve {targetDays} days to sell";
            StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
                        factor,
                        "DaysToSell",
                        impact,
                        extendedNote,
                        parms.snapshotId, ruleSet);
            return impactItem;
         }
         else
         {
            //failed to further finnesse it, use what we have
            decimal impact = ((decimal)resultingPricePoint - 1M) * (decimal)parms.valuationAdjusted;
            string extendedNote = $"Adjustment to valuation to achieve {targetDays} days to sell";
            StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
                        factor,
                        "DaysToSell",
                        impact,
                        extendedNote,
                        parms.snapshotId,
                        ruleSet);
            return impactItem;
         }

      }
   }

   public static decimal Workout90PctPrice(int? valuationAvg, int? valuationAdj)
   {
      decimal lowestPPNew = 0.9M;
      //if (valuationAvg > valuationAdj)
      //{
      //   lowestPPNew = 0.92M * (int)valuationAvg / (int)valuationAdj;
      //}

      return lowestPPNew;
   }

   public static int WorkoutMileageToUse(int? odometerReading, DateTime? firstRegDate, DateTime? dateOnForecourt)
   {
      int mileageToUse = 0;
      if (odometerReading != null)
      {
         mileageToUse = (int)odometerReading;
      }
      else
      {
         DateTime adFirstRegdDate = firstRegDate != null ? (DateTime)firstRegDate : DateTime.Now;
         double vehicleAgeInMonths = ((DateTime)dateOnForecourt - adFirstRegdDate).Days / (365.25 / 12);
         mileageToUse = odometerReading ?? (int)Math.Round((vehicleAgeInMonths * 1000));
      }

      return mileageToUse;
   }

   private static async Task<StrategyPriceBuildUpItem> WorkoutStrategyPriceForLocalCompetitor(
       AutoTraderCompetitorClient competitorClient,
       RetailerSite retailerSite,
       AdvertParamsForStrategyCalculator parms,
       StrategyFactor factor,
       StrategySelectionRuleSet ruleSet
       )
   {

      int radius = (int)factor.StrategyFactorItems.First(s => s.Label == "Radius").Value;
      int competitorRank = (int)factor.StrategyFactorItems.First(s => s.Label == "Ranking").Value;
      int plateRange = (int)factor.StrategyFactorItems.First(s => s.Label == "PlateSteps").Value;
      int mileageSteps = (int)factor.StrategyFactorItems.First(s => s.Label == "MileageSteps").Value;
      var competitorTypesToUse = factor.StrategyFactorItems
          .Where(s => s.Value == 1 && (s.Label == "Independent" || s.Label == "Franchise" || s.Label == "Supermarket" || s.Label == "Private"))
          .Select(s => s.Label)
          .ToList();

      CompetitorSearchParams searchParams = new CompetitorSearchParams(parms, radius);
      VehicleListing competitorAnalysis = await competitorClient.GetCompetitorNew(searchParams, null);

      if (parms.valuationAdjusted == null)
      {
         return null;
      }

      int valuationToUse = (int)parms.valuationAdjusted;

      CompetitorSummary competitorSummary = new CompetitorSummary(competitorAnalysis, decimal.Parse(retailerSite.Site.Longitude), decimal.Parse(retailerSite.Site.Latitude), valuationToUse, valuationToUse, radius, competitorTypesToUse, true, retailerSite.Postcode);


      int mileageToUse = parms.OdometerReading ?? 0;
      var minMileage = Math.Max(1000, (mileageToUse - mileageSteps));
      var maxMileage = mileageToUse + mileageSteps;
      var relevantCompetitor = competitorSummary.CompetitorVehicles
          .Where(x => x.CompetitorName != null && x.CompetitorName != string.Empty && x.Mileage >= minMileage && x.Mileage <= maxMileage) //no privates, similar mileage
          .Where(x => x.PricePosition > 0)
          .OrderBy(x => x.PricePosition)
          .Skip(competitorRank - 1)
          .FirstOrDefault();
      //var tst = competitorSummary.CompetitorVehicles.OrderBy(x => x.PricePosition).Take(20).ToList();
      if (relevantCompetitor != null)
      {
         //var ourAdPricePosition = parms.currentStrategyPrice / (decimal)valuationToUse;
         //if (relevantCompetitor.PricePosition < ourAdPricePosition)
         //{
         decimal targetPP = (Math.Round(relevantCompetitor.PricePosition * 100, 1, MidpointRounding.AwayFromZero) - 0.1M) / 100;
         decimal priceNeedsToBe = (decimal)(targetPP * valuationToUse);
         decimal priceMatchImpact = priceNeedsToBe - parms.currentStrategyPrice;

         string lowestPPString = (relevantCompetitor.PricePosition * 100).ToString("0.0\\%");
         string extendedNote = $"Matched to {relevantCompetitor.VehicleReg} at {relevantCompetitor.CompetitorName} having PP of {lowestPPString}";
         StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
             factor,
             "Radius",
             priceMatchImpact,
             extendedNote,
             parms.snapshotId,
             ruleSet);
         return impactItem;

      }
      else
      {
         return null;
      }
   }


   private static async Task<StrategyPriceBuildUpItem> CalculateTrackMarketPositionImpact(
    AutoTraderCompetitorClient competitorClient,
    RetailerSite retailerSite,
    AdvertParamsForStrategyCalculator parms,
    StrategyFactor factor,
    StrategySelectionRuleSet ruleSet
    )
   {
      int radius = (int)factor.StrategyFactorItems.First(s => s.Label == "Radius").Value;
      int ppVariance = (int)factor.StrategyFactorItems.First(s => s.Label == "PP%Variance").Value;
      int plateRange = (int)factor.StrategyFactorItems.First(s => s.Label == "PlateSteps").Value;
      int mileageSteps = (int)factor.StrategyFactorItems.First(s => s.Label == "MileageSteps").Value;
      var competitorTypesToUse = factor.StrategyFactorItems
          .Where(s => s.Value == 1 && (s.Label == "Independent" || s.Label == "Franchise" || s.Label == "Supermarket" || s.Label == "Private"))
          .Select(s => s.Label)
          .ToList();

      CompetitorSearchParams searchParams = new CompetitorSearchParams(parms, radius);
      VehicleListing competitorAnalysis = await competitorClient.GetCompetitorNew(searchParams, null);

      if (parms.valuationAdjusted == null)
      {
         return null;
      }

      int valuationToUse = (int)parms.valuationAdjusted;

      CompetitorSummary competitorSummary = new CompetitorSummary(competitorAnalysis, decimal.Parse(retailerSite.Site.Longitude), decimal.Parse(retailerSite.Site.Latitude), valuationToUse, valuationToUse, radius, competitorTypesToUse, true, retailerSite.Postcode);


      int mileageToUse = parms.OdometerReading ?? 0;
      var minMileage = Math.Max(1000, (mileageToUse - mileageSteps));
      var maxMileage = mileageToUse + mileageSteps;
      int skipToGetHalfway = competitorSummary.CompetitorVehicles.Count / 2;
      var relevantCompetitor = competitorSummary.CompetitorVehicles
          .Where(x => x.CompetitorName != null && x.CompetitorName != string.Empty && x.Mileage >= minMileage && x.Mileage <= maxMileage) //no privates, similar mileage
          .Where(x => x.PricePosition > 0)
          .OrderBy(x => x.PricePosition)
          .Skip(skipToGetHalfway - 1)
          .FirstOrDefault();

      if (relevantCompetitor != null)
      {
         decimal targetPP = (Math.Round(relevantCompetitor.PricePosition * 100, 1, MidpointRounding.AwayFromZero) - 0.1M) / 100 + (ppVariance / 100);
         decimal priceNeedsToBe = (decimal)(targetPP * valuationToUse);
         decimal priceMatchImpact = priceNeedsToBe - parms.currentStrategyPrice;

         string lowestPPString = (relevantCompetitor.PricePosition * 100).ToString("0.0\\%");
         string extendedNote = $"Matched to {relevantCompetitor.VehicleReg} at {relevantCompetitor.CompetitorName} having PP of {lowestPPString}";
         StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
             factor,
             "Radius",
             priceMatchImpact,
             extendedNote,
             parms.snapshotId,
             ruleSet);
         return impactItem;

      }
      else
      {
         return null;
      }
   }



   private static async Task<StrategyPriceBuildUpItem> WorkoutStrategyPriceForMarketPositionScore(
       AutoTraderCompetitorClient competitorClient,
       RetailerSite retailerSite,
       AdvertParamsForStrategyCalculator parms,
       StrategyFactor factor,
       StrategySelectionRuleSet ruleSet
       )
   {
      //// Copy of WorkoutStrategyPriceForLocalCompetitor, with few changes ///


      int radius = (int)factor.StrategyFactorItems.First(s => s.Label == "Radius").Value;
      int desiredMarketPositionScore = (int)factor.StrategyFactorItems.First(s => s.Label == "Ranking").Value;
      int plateRange = (int)factor.StrategyFactorItems.First(s => s.Label == "PlateSteps").Value;
      int mileageSteps = (int)factor.StrategyFactorItems.First(s => s.Label == "MileageSteps").Value;
      var competitorTypesToUse = factor.StrategyFactorItems
          .Where(s => s.Value == 1 && (s.Label == "Independent" || s.Label == "Franchise" || s.Label == "Supermarket" || s.Label == "Private"))
          .Select(s => s.Label)
          .ToList();

      CompetitorSearchParams searchParams = new CompetitorSearchParams(parms, radius);
      VehicleListing competitorAnalysis = await competitorClient.GetCompetitorNew(searchParams, null);

      if (parms.valuationAdjusted == null)
      {
         return null;
      }

      int valuationToUse = (int)parms.valuationAdjusted;

      CompetitorSummary competitorSummary = new CompetitorSummary(
         competitorAnalysis,
          decimal.Parse(retailerSite.Site.Longitude),
          decimal.Parse(retailerSite.Site.Latitude),
          valuationToUse,
          valuationToUse,
          radius,
          competitorTypesToUse,
          true,
          retailerSite.Postcode
          );

      int mileageToUse = parms.OdometerReading ?? 0;
      var minMileage = Math.Max(1000, (mileageToUse - mileageSteps));
      var maxMileage = mileageToUse + mileageSteps;
      var relevantCompetitors = competitorSummary.CompetitorVehicles
          .Where(x => x.CompetitorName != null && x.CompetitorName != string.Empty && x.Mileage >= minMileage && x.Mileage <= maxMileage) //no privates, similar mileage
          .OrderBy(x => x.PricePosition);

      int competitorIndex = Math.Max(1, (100 - desiredMarketPositionScore) * relevantCompetitors.Count() / 100);


      var relevantCompetitor = relevantCompetitors
          .Skip(competitorIndex - 1)
          .FirstOrDefault();

      if (relevantCompetitor != null)
      {
         decimal targetPP = (Math.Round(relevantCompetitor.PricePosition * 100, 1, MidpointRounding.AwayFromZero) - 0.1M) / 100;
         decimal priceNeedsToBe = (decimal)(targetPP * valuationToUse);
         decimal priceMatchImpact = priceNeedsToBe - parms.currentStrategyPrice;

         string lowestPPString = (relevantCompetitor.PricePosition * 100).ToString("0.0\\%");
         string extendedNote = $"Matched to {relevantCompetitor.VehicleReg} at {relevantCompetitor.CompetitorName} having PP of {lowestPPString}";
         StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
             factor,
             "Radius",
             priceMatchImpact,
             extendedNote,
             parms.snapshotId,
             ruleSet);
         return impactItem;

      }
      else
      {
         return null;
      }
   }

   private static StrategyPriceBuildUpItem CalculateFuelTypeImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string value = parms.FuelType;
      List<string> notes = new List<string>();
      notes.Add($"FuelType of {parms.FuelType}");
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(value, parms.currentStrategyPrice, factor, false, notes);
      if (impact == 0)
      {
         return null;
      }
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          value,
          value,
          impact,
          parms.snapshotId,
          ruleSet,
          notes, false
          );
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateValueBandImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      // Determine value band based on current strategy price
      string valueBand = DetermineValueBand(parms.currentStrategyPrice);
      List<string> notes = new List<string>();
      notes.Add($"ValueBand of {valueBand}");
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(valueBand, parms.currentStrategyPrice, factor, false, notes);
      if (impact == 0)
      {
         return null;
      }
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          valueBand,
          valueBand,
          impact,
          parms.snapshotId,
          ruleSet,
          notes, false
          );
      return impactItem;
   }

   private static string DetermineValueBand(decimal price)
   {
      if (price < 5000)
         return "£5k-£10k";
      if (price < 10000)
         return "£5k-£10k";
      if (price < 15000)
         return "£10k-£15k";
      if (price < 20000)
         return "£15k-£20k";
      if (price < 30000)
         return "£20k-£30k";
      if (price < 40000)
         return "£30k-£40k";
      if (price < 50000)
         return "£40k-£50k";
      return ">£50k";
   }

   private static StrategyPriceBuildUpItem CalculateRegYearImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      // Extract year from firstRegisteredDate
      string regYear = parms.firstRegisteredDate?.Year.ToString() ?? DateTime.Now.Year.ToString();
      List<string> notes = new List<string>();
      notes.Add($"Reg Year of {regYear}");
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(regYear, parms.currentStrategyPrice, factor, false, notes);
      if (impact == 0)
      {
         return null;
      }
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          regYear,
          regYear,
          impact,
          parms.snapshotId,
          ruleSet,
          notes, false
          );
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateLiveMarketConditionImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      decimal liveMarketCondition = parms.LiveMarketCondition;

      // Convert to percentage and round to nearest integer for matching
      int marketConditionPercent = (int)Math.Round(liveMarketCondition * 100);

      // Find the matching "up to" item (similar to DaysListed logic)
      var orderedItems = factor.StrategyFactorItems.OrderBy(x => int.Parse(x.Label)).ToList();
      var matchingItem = orderedItems.FirstOrDefault(x => marketConditionPercent <= int.Parse(x.Label));

      if (matchingItem == null)
      {
         // If no matching item found, use the highest threshold
         matchingItem = orderedItems.LastOrDefault();
      }

      if (matchingItem == null)
      {
         return null;
      }

      List<string> notes = new List<string>();
      notes.Add($"{factor.Name} of {matchingItem.Label}");
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(matchingItem.Label, parms.currentStrategyPrice, factor, false, notes);
      if (impact == 0)
      {
         return null;
      }
      string liveMarketValue = marketConditionPercent >= 0 ? $"+{marketConditionPercent}%" : $"{marketConditionPercent}%";

      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          matchingItem.Label,
          liveMarketValue,
          impact,
          parms.snapshotId,
          ruleSet,
          notes, true
          );
      return impactItem;
   }
}