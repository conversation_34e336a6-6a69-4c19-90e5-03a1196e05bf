﻿using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using log4net;
using System.Diagnostics;
using CPHI.Spark.Model.Services;
using log4net.Repository.Hierarchy;
using CPHI.Spark.Model.Fcst;
//using Datadog.Trace;

namespace CPHI.Spark.BusinessLogic.AutoPrice
{
   public class CalculateStrategyPricesService
   {
      public readonly string connString;
      private readonly HttpClient httpClient;
      private readonly IHttpClientFactory _httpClientFactory;

      public CalculateStrategyPricesService(string connStringIn, IHttpClientFactory httpClientFactory)
      {
         this.connString = connStringIn;
         this.httpClient = httpClientFactory.CreateClient();
         _httpClientFactory = httpClientFactory;
      }

      public async Task CalculateStrategyPricesForTodayAdverts(DealerGroupName dealerGroup, string atApiKey, string atApiSecret, string atBaseURL, ILog logger, bool useTestStrategy, Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingsDict)
      {


         DateTime runDate = DateTime.Now;

         //initiate the DA level objects
         RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(connString);
         VehicleAdvertSnapshotsDataAccess vehicleAdvertSnapshotsDataAccess = new VehicleAdvertSnapshotsDataAccess(connString);
         VehicleAdvertsService vehicleAdvertsService = new VehicleAdvertsService(connString);
         var atConfig = new AutoTraderConfig() { AutotraderApiKey = atApiKey, AutotraderApiSecret = atApiSecret, AutotraderBaseURL = atBaseURL };
         DaysToSellService daysToSellService = new DaysToSellService(connString, atConfig, _httpClientFactory);
         LeavingPricesDataAccess leavingPricesDataAccess = new LeavingPricesDataAccess(connString);
         TagDataAccess tagDataAccess = new TagDataAccess(connString);

         ILookup<int, VehicleAdvertWithRating> advertsByRetailerSiteId = null;

         var allTags = await tagDataAccess.SearchTags(new TagSearchDTO() { DealerGroupName = dealerGroup, IsActive = true });

         advertsByRetailerSiteId = await GetTodaysAdvertsByRetailerSiteId(dealerGroup, runDate, retailerSitesDataAccess, vehicleAdvertsService);

         List<int> advertIds = new List<int>();

         //Fetch the competitorLInks
         foreach (var retailerCollection in advertsByRetailerSiteId)
         {
            foreach (var ad in retailerCollection)
            {
               advertIds.Add(ad.AdId);
            }
         }
         var competitorLinks = await vehicleAdvertsService.GetAdvertsAndCompetitorLinks(dealerGroup, advertIds);
         var linksLookup = competitorLinks.ToDictionary(x => x.AdvertId, x => x.CompetitorLink);


         //Fetch the strategies
         List<RetailerSiteWithStrategy> retailersWithStrategies;
         if (useTestStrategy)
         {
            List<RetailerSite> retailerSitesWithStrategySelectionRuleSets = await retailerSitesDataAccess.GetRetailerSitesWithTestStrategies(runDate, dealerGroup);
            retailersWithStrategies = retailerSitesWithStrategySelectionRuleSets.Select(x => new RetailerSiteWithStrategy(x, true)).ToList();
         }
         else
         {
            List<RetailerSite> retailerSitesWithStrategySelectionRuleSets = await retailerSitesDataAccess.GetRetailerSitesWithStrategies(runDate, dealerGroup);
            retailersWithStrategies = retailerSitesWithStrategySelectionRuleSets.Select(x => new RetailerSiteWithStrategy(x, false)).ToList();
         }



         //loop through each site, use strategy to update the snapshots with the strategy price
         List<StrategyPriceBuildUpItem> buildUpItems = new List<StrategyPriceBuildUpItem>();
         List<VehicleAdvertWithRating> updatedAdverts = new List<VehicleAdvertWithRating>();
         AutoTraderVehicleMetricsClient atMetricsClient = new AutoTraderVehicleMetricsClient(_httpClientFactory,
         atApiKey, atApiSecret, atBaseURL);
         AutoTraderFutureValuationsClient atFutureValsClient = new AutoTraderFutureValuationsClient(_httpClientFactory,
          atApiKey, atApiSecret, atBaseURL);

         AutoTraderCompetitorClient atCompetitorClient = new AutoTraderCompetitorClient(_httpClientFactory, atApiKey, atApiSecret, atBaseURL);
         var atTokenClient = new AutoTraderApiTokenClient(_httpClientFactory, atApiKey, atApiSecret, atBaseURL);
         var tokenResponse = (await atTokenClient.GetToken());
         Stopwatch sw = new Stopwatch();
         sw.Start();



         //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
         //Step 0 - fetch leaving vehicle information if we need it.
         //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
         List<LeavingVehicle6mItem> leaving6mData = new();
         List<DailyValuationChange> dailyValuationChanges = new List<DailyValuationChange>();

         //convert ads lookup to calcParams lookup
         List<AdvertParamsForStrategyCalculator> adParams = new List<AdvertParamsForStrategyCalculator>();
         foreach (var advertGrouping in advertsByRetailerSiteId)
         {
            int retailerId = advertGrouping.Key;
            var retailerSite = retailersWithStrategies.First(x => x.RetailerSite.Id == retailerId).RetailerSite;
            foreach (var ad in advertGrouping)
            {
               adParams.Add(new AdvertParamsForStrategyCalculator(ad, retailerSite, null, null));
            }
         }
         var adParamsByRetailerId = adParams.ToLookup(x => x.RetailerSiteId);

         bool leavingDataRequired = CheckIfDataRequiredForStrategyFactorName(adParamsByRetailerId, retailersWithStrategies, StrategyFactorName.LeavingData);
         if (leavingDataRequired)
         {
            leaving6mData = await leavingPricesDataAccess.GetLeavingVehicles6mSummary(dealerGroup);
         }

         bool dailyChangeDataRequired = CheckIfDataRequiredForStrategyFactorName(adParamsByRetailerId, retailersWithStrategies, StrategyFactorName.DailyValuationChange);
         if (dailyChangeDataRequired)
         {
            dailyValuationChanges = await leavingPricesDataAccess.GetDailyValuationChanges(dealerGroup, runDate);
         }

         //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
         //Now loop through and apply strategy
         //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
         foreach (var retailerSiteWithStrategy in retailersWithStrategies)
         {
            if (!retailerSiteWithStrategy.RetailerSite.IsActive)
            {
               continue; //silently move on if not active.
            }

            if (retailerSiteWithStrategy.RuleSet == null)
            {
               continue;
            }
            IEnumerable<VehicleAdvertWithRating> thisSiteAdverts = advertsByRetailerSiteId[retailerSiteWithStrategy.RetailerSite.Id];
            List<VehicleAdvertWithRatingWithDaysToSellParams> thisSiteAdsWithParams = thisSiteAdverts.ToList().ConvertAll(x => new VehicleAdvertWithRatingWithDaysToSellParams(x));

            //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
            //Step 1 - tag each advert as whether it will need to get the price required to achieve a days to sell.   This is slow so needs to be run in a batch
            //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
            foreach (var item in thisSiteAdsWithParams)
            {
               if (item.Advert.GetRelevantRetailValuationAdj == 0)
               {
                  //can't do anything with this
                  continue;
               }

               // if (item.Advert.VehicleReg == "MD23WRF")
               // {
               //    { }
               // }

               //VehicleAdvertWithRating advert, RetailerSite retailerSite, TokenResponse token, string competitorLink
               var competitorLink = linksLookup[item.Advert.AdId];
               item.calcParams = new AdvertParamsForStrategyCalculator(item.Advert, retailerSiteWithStrategy.RetailerSite, tokenResponse, competitorLink);
               item.TargetDaysToSell = ApplyStrategyService.DoesRuleSetRequireCalcOfPriceForDaysToSell(item.calcParams, retailerSiteWithStrategy.RuleSet);
               var ad = item.Advert;
               item.MileageToUse = CalculateStrategyImpactService.WorkoutMileageToUse(ad.OdometerReading, ad.FirstRegisteredDate, ad.DateOnForecourt);
            }
            //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
            //Step 2 - For those tagged adverts, find the PP to achieve the DTS in a batch 
            //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
            var advertsToPreworkoutPriceForDTS = thisSiteAdsWithParams.Where(x => x.TargetDaysToSell != null).ToList();

            //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
            //Step 2a - do the rough estimate of the required price
            //-----------------------------------------------------------------------------------------------------------------------------------------------------------------

            //Put tasks into adverts
            List<Task> roughEstimateTasks = new List<Task>();

            //advertsToPreworkoutPriceForDTS = advertsToPreworkoutPriceForDTS.Where(x => x.calcParams.VehicleReg == "DF22MWL").ToList();


            foreach (var advert in advertsToPreworkoutPriceForDTS)
            {
               decimal highestPossiblePricePoint = 1.099M;
               decimal lowestPossiblePricePoint = 0.901M;
               if (advert.calcParams.VehicleReg == "HJ71GWA")
               {
                  { }
               }
               try
               {
                  var ad = advert.Advert;
                  //advert.DTSRoughLowPP = 0.9M;// CalculateStrategyImpactService.Workout90PctPrice(ad.ValuationAdjRetail, ad.ValuationMktAvRetail);

                  int averageValuation = (int)advert.calcParams.valuationAverage;
                  int lowPriceAvg = (int)Math.Ceiling(averageValuation * lowestPossiblePricePoint) + 2;  //advert.DTSRoughLowPP
                  int highPriceAvg = (int)Math.Floor(averageValuation * highestPossiblePricePoint) - 2;

                  int adjustedValuation = (int)advert.calcParams.valuationAdjusted;
                  int lowPriceAdj = (int)Math.Ceiling(adjustedValuation * lowestPossiblePricePoint) + 2;  //advert.DTSRoughLowPP
                  int highPriceAdj = (int)Math.Floor(adjustedValuation * highestPossiblePricePoint) - 2;

                  int lowPrice = Math.Max(lowPriceAdj, lowPriceAvg);
                  int highPrice = Math.Min(highPriceAdj, highPriceAvg);

                  advert.LowPPWeUsedForRoughCalc = (decimal)lowPrice / adjustedValuation;
                  advert.HighPPWeUsedForRoughCalc = (decimal)highPrice / adjustedValuation;



                  advert.DTSRoughLowTask = CalculateStrategyImpactService.ProvideDaysToSellTask(
                     retailerSiteWithStrategy.RetailerSite, advert.MileageToUse, lowPrice, atMetricsClient,
                      tokenResponse.AccessToken, ad.DerivativeId, advert.calcParams.portalOptions,
                      advert.calcParams.hasOptionsSpecified, advert.calcParams.firstRegisteredDate,
                      atBaseURL, adjustedValuation, logger);

                  advert.DTSRoughHighTask = CalculateStrategyImpactService.ProvideDaysToSellTask(
                     retailerSiteWithStrategy.RetailerSite, advert.MileageToUse, highPrice, atMetricsClient,
                      tokenResponse.AccessToken, ad.DerivativeId, advert.calcParams.portalOptions,
                      advert.calcParams.hasOptionsSpecified, advert.calcParams.firstRegisteredDate,
                      atBaseURL, adjustedValuation, logger);

               }
               catch (Exception ex)
               {
                  Console.WriteLine($"Error initializing task for advert {advert.Advert.AdId}: {ex.Message}");
               }

            }

            //Execute rough tasks in batches, to limit the amount that hit the endpoint at once
            int batchSize = 20;

            // Process the list in batches
            for (int i = 0; i < advertsToPreworkoutPriceForDTS.Count; i += batchSize)
            {
               // Select the current batch
               var batch = advertsToPreworkoutPriceForDTS.Skip(i).Take(batchSize).ToList();

               // Combine all tasks (low and high estimate tasks) into one list
               List<Task<decimal>> allTasks = new List<Task<decimal>>();
               foreach (var advert in batch)
               {
                  allTasks.Add(advert.DTSRoughLowTask());
                  allTasks.Add(advert.DTSRoughHighTask());
               }

               // Await all tasks in the batch concurrently
               var results = await Task.WhenAll(allTasks);

               //now cycle through each advert to process the results
               int resultIndex = 0; // Tracks the position of the low/high results
               foreach (var advert in batch)
               {

                  if (advert.Advert.VehicleReg == "SE19RZV")
                  {
                     { }
                  }


                  advert.calcParams.RoughEstimateDTSLow = results[resultIndex];
                  resultIndex++;
                  advert.calcParams.RoughEstimateDTSHigh = results[resultIndex];
                  resultIndex++;

                  if (advert.calcParams.RoughEstimateDTSLow == 0 || advert.calcParams.RoughEstimateDTSHigh == 0 || (advert.calcParams.RoughEstimateDTSHigh - advert.calcParams.RoughEstimateDTSLow == 0))
                  {
                     advert.RoughEstimateResultPP = null;
                     advert.RoughResultPrice = null;
                     advert.StopAfterRoughCalc = true;
                  }
                  else
                  {
                     decimal ppRoughResult = CalculateStrategyImpactService.WorkoutPricePointFromDaysToSellResults((decimal)advert.TargetDaysToSell, advert.LowPPWeUsedForRoughCalc, advert.HighPPWeUsedForRoughCalc, advert.calcParams.RoughEstimateDTSHigh, advert.calcParams.RoughEstimateDTSLow);
                     advert.RoughEstimateResultPP = ppRoughResult;
                     var valuation = (int)advert.calcParams.valuationAdjusted;
                     advert.RoughResultPrice = ppRoughResult * valuation;

                     //if rough estimate is too low or too high, have to intervene
                     var roughResultOverAvValuation = advert.RoughResultPrice / (decimal)advert.calcParams.valuationAverage;
                     if (roughResultOverAvValuation < 0.9M)
                     {
                        advert.RoughResultPrice = (decimal)advert.calcParams.valuationAverage * advert.LowPPWeUsedForRoughCalc;
                        advert.RoughEstimateResultPP = (decimal)advert.RoughResultPrice / (decimal)advert.calcParams.valuationAdjusted;
                        decimal resultingDays = advert.calcParams.RoughEstimateDTSLow;// advert.calcParams.RoughEstimateDTSLow + daysAboveMinResult;
                        advert.calcParams.RoughEstimateDTS = (int)Math.Ceiling(resultingDays);
                        advert.calcParams.WeHadToUseFloorForDTS = true;
                        advert.StopAfterRoughCalc = true;
                        advert.FineResultPrice = null;
                     }
                     else if (roughResultOverAvValuation > 1.1M)
                     {
                        advert.RoughResultPrice = (decimal)advert.calcParams.valuationAverage * advert.HighPPWeUsedForRoughCalc;
                        advert.RoughEstimateResultPP = (decimal)advert.RoughResultPrice / (decimal)advert.calcParams.valuationAdjusted;
                        decimal resultingDays = advert.calcParams.RoughEstimateDTSHigh;// advert.calcParams.RoughEstimateDTSLow + daysAboveMinResult;
                        advert.calcParams.RoughEstimateDTS = (int)Math.Ceiling(resultingDays);
                        advert.calcParams.WeHadToUseCeilingForDTS = true;
                        advert.StopAfterRoughCalc = true;
                        advert.FineResultPrice = null;
                     }
                  }

               }

            }


            //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
            //Step 2b - do a second set of tasks in a batch to get the fine-tuned price
            //-----------------------------------------------------------------------------------------------------------------------------------------------------------------

            //Setup the tasks
            List<Task> fineTuneTasks = new List<Task>();
            //var tst = advertsToPreworkoutPriceForDTS.Where(x => x.RoughEstimateResultPP == null).ToList();

            foreach (var advert in advertsToPreworkoutPriceForDTS.Where(x => !x.StopAfterRoughCalc))
            {

               if (advert.Advert.VehicleReg == "HJ71GWA")
               {
                  { }
               }

               if (advert.StopAfterRoughCalc)
               {
                  continue;
               }

               if (advert.RoughEstimateResultPP == null)
               {
                  continue;
               }

               try
               {
                  var ad = advert.Advert;
                  int valuationAdjusted = (int)advert.calcParams.valuationAdjusted;
                  int valuationAverage = (int)advert.calcParams.valuationAverage;
                  decimal highPP = Math.Ceiling((decimal)advert.RoughEstimateResultPP * 100) / 100;  //set the high and low boundaries at the top of the rough estimate and 1% lower
                  decimal lowPP = highPP - 0.01M;

                  advert.DTSFineLowPP = lowPP;
                  advert.DTSFineHighPP = highPP;

                  int lowPrice = (int)Math.Round(valuationAdjusted * lowPP, 0, MidpointRounding.AwayFromZero);
                  int highPrice = (int)Math.Round(valuationAdjusted * highPP, 0, MidpointRounding.AwayFromZero);

                  if (advert.Advert.VehicleReg == "DF22MWL")
                  {
                     { }
                  }



                  try
                  {
                     if (advert.calcParams == null)
                     {
                        { }
                     }

                     advert.DTSFineLowTask = CalculateStrategyImpactService.ProvideDaysToSellTask(
                        retailerSiteWithStrategy.RetailerSite, advert.MileageToUse, lowPrice,
                         atMetricsClient, tokenResponse.AccessToken, ad.DerivativeId,
                          advert.calcParams.portalOptions, advert.calcParams.hasOptionsSpecified,
                          advert.calcParams.firstRegisteredDate, atBaseURL, valuationAdjusted, logger);

                     advert.DTSFineHighTask = CalculateStrategyImpactService.ProvideDaysToSellTask(
                        retailerSiteWithStrategy.RetailerSite, advert.MileageToUse, highPrice,
                         atMetricsClient,
                         tokenResponse.AccessToken, ad.DerivativeId, advert.calcParams.portalOptions,
                          advert.calcParams.hasOptionsSpecified, advert.calcParams.firstRegisteredDate,
                           atBaseURL, valuationAdjusted, logger);
                  }
                  catch (Exception ex)
                  {
                     { }
                     advert.StopAfterRoughCalc = true;
                     advert.FineResultPrice = null;
                  }

               }
               catch (Exception ex)
               {
                  Console.WriteLine($"Error initializing task for advert {advert.Advert.AdId}: {ex.Message}");
                  advert.StopAfterRoughCalc = true;
               }

            }

            // Process the fine tasks in batches
            var advertsToFineTune = advertsToPreworkoutPriceForDTS.Where(x => !x.StopAfterRoughCalc).ToList();
            for (int i = 0; i < advertsToFineTune.Count; i += batchSize)
            {
               try
               {

                  // Select the current batch
                  var batch = advertsToFineTune.Skip(i).Take(batchSize).ToList();

                  // Combine all tasks (low and high estimate tasks) into one list
                  List<Task<decimal>> allTasks = new List<Task<decimal>>();
                  foreach (var advert in batch)
                  {
                     allTasks.Add(advert.DTSFineLowTask());
                     allTasks.Add(advert.DTSFineHighTask());
                  }

                  // Await all tasks in the batch concurrently
                  try
                  {

                     var results = await Task.WhenAll(allTasks);


                     //now cycle through each advert to process the results
                     int resultIndex = 0; // Tracks the position of the low/high results
                     foreach (var advert in batch)
                     {
                        if (advert.calcParams.VehicleReg == "DF22MWL")
                        {
                           { }
                        }

                        var lowResult = results[resultIndex];
                        resultIndex++;
                        var highResult = results[resultIndex];
                        resultIndex++;

                        if (lowResult == 0 || highResult == 0 || (highResult - lowResult == 0))
                        {
                           advert.FineResultPrice = null;
                        }
                        else
                        {
                           var fineResult = CalculateStrategyImpactService.WorkoutPricePointFromDaysToSellResults((decimal)advert.TargetDaysToSell, advert.DTSFineLowPP, advert.DTSFineHighPP, highResult, lowResult);
                           var valuation = (int)advert.calcParams.valuationAdjusted;
                           advert.FineResultPrice = fineResult * valuation;
                        }

                     }

                  }
                  catch (Exception ex)
                  {
                     { }
                  }


               }
               catch (Exception ex)
               {
                  { }
               }
            }
            //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
            //Step 3 - Now do the regular strategy calculation
            //-----------------------------------------------------------------------------------------------------------------------------------------------------------------
            foreach (var adWithParams in thisSiteAdsWithParams)
            {
               var competitorLink = linksLookup[adWithParams.Advert.AdId];
               //check we have a valuation
               if (adWithParams.Advert.GetRelevantRetailValuationAdj == 0)
               {
                  //can't do anything with this
                  continue;
               }

               if (adWithParams.Advert.VehicleReg == "DF22MWL")
               {
                  { }
               }

               List<StrategyPriceBuildUpItem> thisAdvertBuildUpItems = new List<StrategyPriceBuildUpItem>();

               if (useTestStrategy)
               {
                  thisAdvertBuildUpItems = await CalculateBuildUpItems(
                      atApiKey,
                      atApiSecret,
                      atBaseURL,
                      logger,
                      runDate,
                      atCompetitorClient,
                      atTokenClient,
                      tokenResponse,
                      retailerSiteWithStrategy.RetailerSite,
                      retailerSiteWithStrategy.RetailerSite.Site,
                      retailerSiteWithStrategy.RuleSet,
                      adWithParams, atFutureValsClient, atMetricsClient,
                      leaving6mData, dailyValuationChanges, allTags.ToList()
                      );


                  adWithParams.Advert.TestStrategyPrice = (int)adWithParams.Advert.GetRelevantRetailValuationAdj;

                  //if we have made some changes, update the advert
                  if (thisAdvertBuildUpItems.Count > 0)
                  {
                     buildUpItems.AddRange(thisAdvertBuildUpItems);
                     foreach (var item in thisAdvertBuildUpItems)
                     {
                        adWithParams.Advert.TestStrategyPrice += item.Impact;
                     }
                  }


                  updatedAdverts.Add(adWithParams.Advert);
               }
               else
               {
                  thisAdvertBuildUpItems = await CalculateBuildUpItems(
                      atApiKey,
                      atApiSecret,
                      atBaseURL,
                      logger,
                      runDate,
                      atCompetitorClient,
                      atTokenClient,
                      tokenResponse,
                      retailerSiteWithStrategy.RetailerSite,
                      retailerSiteWithStrategy.RetailerSite.Site,
                      retailerSiteWithStrategy.RuleSet,
                      adWithParams
                  , atFutureValsClient, atMetricsClient, leaving6mData, dailyValuationChanges, allTags.ToList());
                  adWithParams.Advert.StrategyPrice = adWithParams.Advert.GetRelevantRetailValuationAdj;
                  //if we have made some changes, update the advert
                  if (thisAdvertBuildUpItems.Count > 0)
                  {
                     buildUpItems.AddRange(thisAdvertBuildUpItems);
                     foreach (var item in thisAdvertBuildUpItems)
                     {
                        adWithParams.Advert.StrategyPrice += item.Impact;
                     }
                  }
                  updatedAdverts.Add(adWithParams.Advert);
               }


            }
            if (logger != null)
            {
               logger.Info($"Updated strategy price for {thisSiteAdverts.Count()} snapshot(s) for site {retailerSiteWithStrategy.RetailerSite.Name}");
            }
         }




         var strategyFactorItemVehicleWebsiteRatingsDataAccess = new StrategyFactorItemVehicleWebsiteRatingsDataAccess(connString);

         //save the factor impacts and strategy prices
         var factorImpacts = buildUpItems.ConvertAll(x => new StrategyFactorItemVehicleWebsiteRating(x, runDate, useTestStrategy));

         //remove existing if doing test strategy
         if (useTestStrategy)
         {
            await strategyFactorItemVehicleWebsiteRatingsDataAccess.DeleteTestFactorImpactsForToday(dealerGroup);
         }
         await strategyFactorItemVehicleWebsiteRatingsDataAccess.SaveFactorImpacts(factorImpacts);
         await vehicleAdvertSnapshotsDataAccess.SaveUpdatedStrategyForSnapshots(updatedAdverts, bandingsDict);

      }

      public static bool CheckIfDataRequiredForStrategyFactorName(ILookup<int, AdvertParamsForStrategyCalculator> advertsByRetailerSiteId, List<RetailerSiteWithStrategy> retailersWithStrategies, StrategyFactorName targetFactorName)
      {
         bool dataIsRequired = false;
         int retailerSiteIndex = 0;
         while (retailerSiteIndex < retailersWithStrategies.Count && !dataIsRequired)
         {
            var retailerSiteWithStrategy = retailersWithStrategies[retailerSiteIndex];
            if (!retailerSiteWithStrategy.RetailerSite.IsActive)
            {
               retailerSiteIndex++;
               continue; //silently move on if not active.
            }

            if (retailerSiteWithStrategy.RuleSet == null)
            {
               retailerSiteIndex++;
               continue;
            }
            List<AdvertParamsForStrategyCalculator> thisSiteParams = advertsByRetailerSiteId[retailerSiteWithStrategy.RetailerSite.Id].ToList();

            //determine if any rules for this retailer even include leaving data (most won't)
            bool specificRulesIncludeTargetFactor = retailerSiteWithStrategy.RuleSet.StrategySelectionRules.Any(x => x.StrategyVersion.StrategyFactors.Any(x => x.Name == targetFactorName)); //StrategyFactorName.LeavingData
            bool defaultRulesIncludeTargetFactor = retailerSiteWithStrategy.RuleSet.DefaultStrategyVersion.StrategyFactors.Any(x => x.Name == targetFactorName); //StrategyFactorName.LeavingData
            if (specificRulesIncludeTargetFactor || defaultRulesIncludeTargetFactor)
            {
               int index = 0;
               while (index < thisSiteParams.Count() && !dataIsRequired)
               {
                  var item = thisSiteParams[index];
                  if (item.valuationAdjusted == 0)
                  {
                     //can't do anything with this
                     index++;
                     continue;
                  }
                  dataIsRequired = ApplyStrategyService.DoWeNeedAParticularFactorsData(item, retailerSiteWithStrategy.RuleSet, targetFactorName); //StrategyFactorName.LeavingData
                  index++;
               }


            }
            retailerSiteIndex++;
         }

         return dataIsRequired;
      }

      public async Task CalculateStrategyDaysToSellForTodayAdverts(DealerGroupName dealerGroup, string atApiKey, string atApiSecret, string atBaseURL, ILog logger, bool useTestStrategy, Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingsDict)
      {
         if (!useTestStrategy)
         {
            return;
         }

         DateTime runDate = DateTime.Now;

         RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(connString);
         VehicleAdvertSnapshotsDataAccess vehicleAdvertSnapshotsDataAccess = new VehicleAdvertSnapshotsDataAccess(connString);
         VehicleAdvertsService vehicleAdvertsService = new VehicleAdvertsService(connString);
         var config = new AutoTraderConfig() { AutotraderApiKey = atApiKey, AutotraderApiSecret = atApiSecret, AutotraderBaseURL = atBaseURL };
         DaysToSellService daysToSellService = new DaysToSellService(connString, config, _httpClientFactory);
         List<VehicleAdvertWithRating> advertsToUpdate = new List<VehicleAdvertWithRating>();

         //Fetch the ads
         ILookup<int, VehicleAdvertWithRating> advertsByRetailerSiteId = await GetTodaysAdvertsByRetailerSiteId(dealerGroup, runDate, retailerSitesDataAccess, vehicleAdvertsService);

         // Fetch the competitorLInks
         List<int> advertIds = new List<int>();
         foreach (var retailerCollection in advertsByRetailerSiteId)
         {
            foreach (var ad in retailerCollection)
            {
               advertsToUpdate.Add(ad);
            }
         }


         AutoTraderCompetitorClient atCompetitorClient = new AutoTraderCompetitorClient(_httpClientFactory, atApiKey, atApiSecret, atBaseURL);
         var atTokenClient = new AutoTraderApiTokenClient(_httpClientFactory, atApiKey, atApiSecret, atBaseURL);
         var tokenResponse = (await atTokenClient.GetToken());

         // Perform bulk check of days to sell if we are using test strategy
         var daysToSellRequests = advertsToUpdate.ConvertAll(x => new DaysToSellRequest(x));

         daysToSellRequests.ForEach(x => x.AdvertisedPrice = (int)x.TestStrategyPrice);

         var dtsList = await daysToSellService.FindDaysToSell(daysToSellRequests, logger);

         foreach (var daysToSellResponse in dtsList)
         {
            var ad = advertsToUpdate.Find(x => x.AdId == daysToSellResponse.AdId);
            ad.TestStrategyDaysToSell = (int)Math.Ceiling(daysToSellResponse.DaysToSellAtCurrentSelling ?? 0);
         }

         await vehicleAdvertSnapshotsDataAccess.SaveUpdatedStrategyForSnapshots(advertsToUpdate, bandingsDict);
      }

      private static async Task<List<StrategyPriceBuildUpItem>> CalculateBuildUpItems(
          string atApiKey,
          string atApiSecret,
          string atBaseURL,
          ILog logger,
          DateTime runDate,
          AutoTraderCompetitorClient atCompetitorClient,
          AutoTraderApiTokenClient atTokenClient,
          TokenResponse tokenResponse,
          RetailerSite retailerSite, Site site,
          StrategySelectionRuleSet ruleSet,
          VehicleAdvertWithRatingWithDaysToSellParams advertWithParams,
         AutoTraderFutureValuationsClient atFutureValsClient,
         AutoTraderVehicleMetricsClient atMetricsClient,
         List<LeavingVehicle6mItem> leaving6mData,
         List<DailyValuationChange> dailyValuationChanges,
         List<TagDTO> tags
         )
      {
         List<StrategyPriceBuildUpItem> thisAdvertBuildUpItems = new List<StrategyPriceBuildUpItem>();

         tokenResponse = await atTokenClient.CheckExpiryAndRegenerate(tokenResponse);

         var preCalcedDTSResult = advertWithParams.FineResultPrice ?? advertWithParams.RoughResultPrice ?? 0;  //if neither rough nor fine, send 0 and the applyRuleSet will know to just set zero impact

         await ApplyStrategyService.ApplyRuleSet(
             runDate,
             advertWithParams.calcParams,
             ruleSet,
             retailerSite,
             site, thisAdvertBuildUpItems,
             tokenResponse,
             atCompetitorClient,
             atBaseURL,
             logger, preCalcedDTSResult, atFutureValsClient, atMetricsClient, leaving6mData, dailyValuationChanges, tags);
         return thisAdvertBuildUpItems;
      }

      private static async Task<ILookup<int, VehicleAdvertWithRating>> GetTodaysAdvertsByRetailerSiteId(DealerGroupName dealerGroup, DateTime runDate, RetailerSitesDataAccess retailerSitesDataAccess, VehicleAdvertsService vehicleAdvertsService)
      {
         List<RetailerSite> retailerSitesThisDG = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);
         var siteIds = retailerSitesThisDG.Select(x => x.Site_Id).Distinct().ToList();
         var retailerSiteIds = retailerSitesThisDG.Select(x => x.Id).ToList();

         //load up all the snapshots for today
         GetVehicleAdvertsWithRatingsParams parms = new GetVehicleAdvertsWithRatingsParams()
         {
            Reg = null,
            Vin = null,
            RetailerSiteIds = string.Join(',', retailerSiteIds),
            EffectiveDate = runDate.Date,
            UserEligibleSites = string.Join(',', siteIds),
            IncludeNewVehicles = true,
            IncludeLCVs = true,
            IncludeUnPublishedAdverts = true,
            IncludeNonOptedOutVehicles = true,
            IncludeOptedOutVehicles = true
         };

         var lifecycles = AutoPriceHelperService.GetAllLifecycleStatusesNoSoldOrWastebin();
         IEnumerable<VehicleAdvertWithRating> adverts = await vehicleAdvertsService.FetchAndFilterVehicleAdvertsFromMainDbTables(parms, dealerGroup, siteIds, retailerSiteIds, lifecycles);
         var advertsByRetailerSiteId = adverts.ToLookup(x => x.RetailerSiteId);
         return advertsByRetailerSiteId;
      }

      public async Task<IEnumerable<VehicleAdvertWithRating>> FetchVehicleAdvertsWithRatings(DateTime? chosenDateIn, DealerGroupName dealerGroup, List<string> lifecycleStatuses)
      {
         DateTime chosenDate = chosenDateIn.HasValue ? (DateTime)chosenDateIn : DateTime.Now.Date;
         var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(connString);
         throw new Exception("no");
      }




   }
}
