﻿using CPHI.Spark.BusinessLogic.Services;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using OfficeOpenXml;
using OfficeOpenXml.FormulaParsing.Excel.Functions.RefAndLookup;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection.Metadata;

namespace CPHI.Spark.BusinessLogic.AutoPrice
{
   public static class ValuationExcelSheetMakerService
   {


      public static string MakeSheet()
      {

         using (ExcelPackage p = new ExcelPackage()) //initiates new workbook
         {


            //  --------------------------------------------------------------------------------------------------------------------------------
            // make people sheet
            //  --------------------------------------------------------------------------------------------------------------------------------


            ExcelWorksheet personSheet = p.Workbook.Worksheets.Add("Test");
            personSheet.Cells[1, 1].Value = "Hello wrld";


            bool exists = Directory.Exists($@".\output\");
            if (!exists)
               Directory.CreateDirectory($@".\output\");

            string path = $@".\output\testFile {DateTime.Now.ToString("ddMMM yyyy")}.xlsx"; // 

            FileInfo file = new FileInfo(path);
            p.SaveAs(file);

            return path;
         }
      }

      public static string MakeValuationPriceScenarioBatch(IEnumerable<ValuationPriceScenarioBatchResult> data, int batchId, DealerGroupName dealerGroup, string condition)
      {

         using (ExcelPackage excelPackage = new ExcelPackage()) //initiates new workbook
         {
            MakePriceScenarioBriefSheet(excelPackage, data, condition);

            bool exists = Directory.Exists($@".\output\");
            if (!exists)
               Directory.CreateDirectory($@".\output\");

            string path = $@".\output\Spark Valuation Batch #{batchId} {DateTime.Now.ToString("ddMMM yyyy")}.xlsx"; // 

            FileInfo fileInfo = new FileInfo(path);
            excelPackage.SaveAs(fileInfo);

            return path;
         }

      }

      public static string MakeValuationsResultSpreadsheet(IEnumerable<ValuationBatchResult> valuationItems, int batchId, string filename, string whenUploadAndByWho, bool valueAtAllSites, bool showBulkValuationResultsAtHomeSite, int userHomeSiteId)
      {

         using (ExcelPackage excelPackage = new ExcelPackage()) //initiates new workbook
         {
            if (valueAtAllSites)
            {
               //1. Best site sheet
               var bestSiteItems = new List<ValuationBatchResult>();
               if (showBulkValuationResultsAtHomeSite)
               {
                  bestSiteItems = valuationItems.Where(x => x.SiteId == userHomeSiteId).ToList();
               }
               else
               {
                  bestSiteItems = valuationItems.Where(x => x.LocationRank == 1).ToList();
               }
               string bestSiteSheetName = valueAtAllSites ? "OneRowPerVehicle" : "ValuationResults";
               var bestSiteSheet = MakeNormalSheet(excelPackage, bestSiteItems, filename, whenUploadAndByWho, "OneRowPerVehicle", true, "Ranks");
               //set explanation
               var explanationCell = bestSiteSheet.Cells[2, 11];
               explanationCell.Value = "This sheet shows each vehicle only once, at the site where the strategy price is highest.  If the strategy price is the same for two sites, the site with the lower days to sell is chosen.";
               explanationCell.Style.Font.Italic = true;

               //2. All sites sheet
               var allSitesSheet = MakeNormalSheet(excelPackage, valuationItems, filename, whenUploadAndByWho, "VehiclesAtAllSites", true, "RanksMulti");
               //set explanation
               explanationCell = allSitesSheet.Cells[2, 11];
               explanationCell.Value = "This sheet shows all vehicles, each valued at every site.";
               explanationCell.Style.Font.Italic = true;
               MakeRanksSheet(excelPackage, bestSiteItems,"Ranks");
               MakeRanksSheet(excelPackage, valuationItems,"RanksMulti");
            }
            else
            {
               //single site group
               var sheet = MakeNormalSheet(excelPackage, valuationItems, filename, whenUploadAndByWho, "ValuationResults", false, "Ranks");
               MakeRanksSheet(excelPackage, valuationItems,"Ranks");
            }


          




            bool outputDirExists = Directory.Exists($@".\output\");
            if (!outputDirExists)
            { Directory.CreateDirectory($@".\output\"); }

            string path = $@".\output\Spark Valuation Batch #{batchId} {DateTime.Now.ToString("ddMMM yyyy")}.xlsx"; // 

            FileInfo fileInfo = new FileInfo(path);
            excelPackage.SaveAs(fileInfo);

            return path;
         }

      }
      private static ExcelWorksheet MakeRanksSheet(ExcelPackage excelPackage, IEnumerable<ValuationBatchResult> rows, string sheetName)
      {
         //Setup the sheet
         ExcelWorksheet sheet = excelPackage.Workbook.Worksheets.Add(sheetName);
         Color sparkOrange = ColorTranslator.FromHtml("#ffc100");
         sheet.TabColor = sparkOrange;
         //sheet.Hidden =  eWorkSheetHidden.Hidden;
         sheet.Name = sheetName;
         sheet.View.ZoomScale = 85;
         sheet.View.FreezePanes(5, 3);
         sheet.DefaultColWidth = 10;

         int rowIndex = 5;
         foreach (var row in rows)
         {
            List<string> competitorPPs = row.CompetitorPricePositions.Split(",").ToList();
            int ppIndex = 1;
            foreach (var comp in competitorPPs)
            {
               var cell = sheet.Cells[rowIndex, ppIndex];
               try
               {
                  if (comp != string.Empty)
                  {
                     var asDecimal = decimal.Parse(comp);
                     cell.Value = asDecimal;
                     ppIndex++;
                  }
               }
               catch (Exception ex)
               {
                  { }
               }
            }
            rowIndex++;
         }
         return sheet;
      }


      private static ExcelWorksheet MakeNormalSheet(ExcelPackage excelPackage, IEnumerable<ValuationBatchResult> rows, string fileName, string whenUploadAndByWho, string sheetName, bool isMultiSiteGroup, string ranksSheetName)
      {
         //Setup the sheet
         ExcelWorksheet sheet = excelPackage.Workbook.Worksheets.Add("ValuationResults");
         Color sparkOrange = ColorTranslator.FromHtml("#ffc100");
         sheet.TabColor = sparkOrange;
         sheet.Name = sheetName;
         sheet.View.ZoomScale = 85;
         sheet.View.FreezePanes(5, 3);
         sheet.DefaultColWidth = 20;

         //Do a header
         sheet.Cells[1, 1].Value = fileName;
         ApplyStyle(ValuationSheetStyleTag.BoldUnderline, sheet.Cells[1, 1]);
         sheet.Cells[1, 4].Value = whenUploadAndByWho;


         ///set where to build main table from
         int currentRow = 3;
         int currentCol = 1;

         
         ///get colDefs
         List<ValuationSheetColDef> colDefs = ValuationExcelSheetColumnService.CreateColumnDefinitions(ranksSheetName);

         //set autofilter on, starting at row 4 and continuing until row 4 + rows.length
         sheet.Cells[4, currentCol, 4 + rows.Count(), colDefs.Count].AutoFilter = true;

         ///build main table
         foreach (var col in colDefs)
         {
            sheet.Column(currentCol).Width = col.Width;

            var header1Cell = sheet.Cells[currentRow, currentCol];
            var header2Cell = sheet.Cells[currentRow + 1, currentCol];
            if (col.Header1Label != null && col.Header1Label != string.Empty)
            {
               header1Cell.Value = col.Header1Label;
            }
            if (col.Header2Label != null && col.Header2Label != string.Empty)
            {
               header2Cell.Value = col.Header2Label;
            }

            //Group the grouped cols
            if (col.Grouped)
            {
               //set the column to grouped level 1
               sheet.Column(currentCol).OutlineLevel = 1;
               sheet.Column(currentCol).Collapsed = true;
            }

            //Group the other cols if no values
            if (col.OnlyShowIfSomeValues)
            {
               bool haveSomeValues = rows.Select(x => x.GetPropertyValue(col.FieldName)).Any(x => x != null);
               if (!haveSomeValues)
               {
                  sheet.Column(currentCol).OutlineLevel = 1;
                  sheet.Column(currentCol).Collapsed = true;
               }
            }

            //Apply header styles
            foreach (var style in col.Header1Styles)
            {
               ApplyStyle(style, header1Cell);
            }
            foreach (var style in col.Header2Styles)
            {
               ApplyStyle(style, header2Cell);
            }

            //fill out body rows
            int bodyRowIndex = 0;
            foreach (var row in rows)
            {
               int currentRowIndex = currentRow + 2 + bodyRowIndex;
               var cell = sheet.Cells[currentRowIndex, currentCol];
               if (col.FieldName != null)
               {
                  cell.Value = row.GetPropertyValue(col.FieldName);
                  if (col.FieldName == "ValuationId")
                  {
                     SetValuationModalHyperLink(cell, row);
                  }
               }
               foreach (var style in col.BodyStyles)
               {
                  ApplyStyle(style, cell);
               }


               //do formula
               if (col.Formula != null)
               {
                  var formula = col.Formula.Replace("{rowIndex}", currentRowIndex.ToString());
                  cell.Formula = formula;
               }


               bodyRowIndex++;

            }

            currentCol++;
         }

         //Set filter on column 72 to only show rows that have a value of 1 .   Can't be done with EPPlus.   Possibly worth switching to ClosedXML, apparently also free and can do this.
         //sheet.Cells["BT1"].AutoFilterColumn(71, "1");



         sheet.Calculate();


         return sheet;
      }


      private static ExcelWorksheet MakeLookupSheet_NOTINUSE(ExcelPackage excelPackage, IEnumerable<FeeLookupItems> rows, string fileName, string whenUploadAndByWho, string sheetName, bool isMultiSiteGroup)
      {
         //Setup the sheet
         ExcelWorksheet sheet = excelPackage.Workbook.Worksheets.Add("ValuationResults");
         Color sparkOrange = ColorTranslator.FromHtml("#ffc100");
         sheet.TabColor = sparkOrange;
         sheet.Name = sheetName;
         sheet.View.ZoomScale = 85;
         sheet.View.FreezePanes(5, 3);
         sheet.DefaultColWidth = 20;
         //sheet.View.ShowGridLines = false;

         //Do a header
         //sheet.Cells[1, 1].Value = fileName;
         //ApplyStyle(ValuationSheetStyleTag.BoldUnderline, sheet.Cells[1, 1]);
         //sheet.Cells[1, 4].Value = whenUploadAndByWho;


         ///set where to build main table from
         int currentRow = 1;
         int currentCol = 1;

         ///get colDefs
         List<ValuationSheetColDef> colDefs = ValuationExcelSheetColumnService.CreateLookupColumnDefinitions();

         //set autofilter on, starting at row 4 and continuing until row 4 + rows.length
         sheet.Cells[1, currentCol, 1 + rows.Count(), colDefs.Count].AutoFilter = true;

         ///build main table
         foreach (var col in colDefs)
         {
            sheet.Column(currentCol).Width = col.Width;

            var header1Cell = sheet.Cells[currentRow, currentCol];
            var header2Cell = sheet.Cells[currentRow + 1, currentCol];
            if (col.Header1Label != null && col.Header1Label != string.Empty)
            {
               header1Cell.Value = col.Header1Label;
            }
            if (col.Header2Label != null && col.Header2Label != string.Empty)
            {
               header2Cell.Value = col.Header2Label;
            }


            //Apply header styles
            foreach (var style in col.Header1Styles)
            {
               ApplyStyle(style, header1Cell);
            }
            foreach (var style in col.Header2Styles)
            {
               ApplyStyle(style, header2Cell);
            }

            //fill out body rows
            int bodyRowIndex = 0;
            foreach (var row in rows)
            {
               int currentRowIndex = currentRow + 2 + bodyRowIndex;
               var cell = sheet.Cells[currentRowIndex, currentCol];
               if (col.FieldName != null)
               {
                  cell.Value = row.GetPropertyValue(col.FieldName);
               }
               foreach (var style in col.BodyStyles)
               {
                  ApplyStyle(style, cell);
               }

               bodyRowIndex++;

            }

            currentCol++;
         }

         sheet.Calculate();

         return sheet;
      }

      private static void SetValuationModalHyperLink(ExcelRange cell, ValuationBatchResult row)
      {
         object valuationId = row.GetPropertyValue("ValuationId");
         object vehicleReg = row.GetPropertyValue("Reg");
         object condition = row.GetPropertyValue("Condition");
         object mileage = row.GetPropertyValue("Mileage");

         SetAndFormatValuationModalHyperLink(cell, valuationId, vehicleReg, mileage, condition);
      }

      private static void MakePriceScenarioBriefSheet(ExcelPackage excelPackage, IEnumerable<ValuationPriceScenarioBatchResult> data, string condition)
      {
         Color sparkOrange = ColorTranslator.FromHtml("#ffc100");
         Color sparkGreen = ColorTranslator.FromHtml("#097969");
         ExcelWorksheet sheet = excelPackage.Workbook.Worksheets.Add("ValuationResults");
         sheet.View.ZoomScale = 90;
         sheet.View.ShowGridLines = false;
         sheet.View.FreezePanes(3, 8);
         sheet.TabColor = sparkOrange;


         sheet.DefaultColWidth = 20;

         sheet.Cells[1, 24].Value = "Days to Sell";
         sheet.Cells[1, 35].Value = "Adjusted Price";


         // Headers 1
         int headers1 = 2;
         sheet.Cells[headers1, 1].Value = "Valuation Id";
         sheet.Cells[headers1, 2].Value = "VRM";
         sheet.Cells[headers1, 3].Value = "VIN";

         sheet.Cells[headers1, 4].Value = "Mileage";
         sheet.Cells[headers1, 5].Value = "Current Price";
         sheet.Cells[headers1, 6].Value = "Make";
         sheet.Cells[headers1, 7].Value = "Model";
         sheet.Cells[headers1, 8].Value = "Derivative";
         sheet.Cells[headers1, 9].Value = "Current PP";
         sheet.Cells[headers1, 10].Value = "1st PP";
         sheet.Cells[headers1, 11].Value = "2nd PP";
         sheet.Cells[headers1, 12].Value = "3rd PP";


         sheet.Cells[headers1, 13].Value = "Lowest PP Retailer";
         sheet.Cells[headers1, 14].Value = "Lowest PP VRM";
         sheet.Cells[headers1, 15].Value = "Lowest PP Mileage";

         sheet.Cells[headers1, 16].Value = "2nd Lowest PP Retailer";
         sheet.Cells[headers1, 17].Value = "2nd Lowest PP VRM";
         sheet.Cells[headers1, 18].Value = "2nd Lowest PP Mileage";

         sheet.Cells[headers1, 19].Value = "3rd Lowest PP Retailer";
         sheet.Cells[headers1, 20].Value = "3rd Lowest PP VRM";
         sheet.Cells[headers1, 21].Value = "3rd Lowest PP Mileage";

         sheet.Cells[headers1, 22].Value = "Best Retailer";
         sheet.Cells[headers1, 23].Value = "RR";
         sheet.Cells[headers1, 24].Value = "90%";
         sheet.Cells[headers1, 25].Value = "91%";
         sheet.Cells[headers1, 26].Value = "92%";
         sheet.Cells[headers1, 27].Value = "93%";
         sheet.Cells[headers1, 28].Value = "94%";
         sheet.Cells[headers1, 29].Value = "95%";
         sheet.Cells[headers1, 30].Value = "96%";
         sheet.Cells[headers1, 31].Value = "97%";
         sheet.Cells[headers1, 32].Value = "98%";
         sheet.Cells[headers1, 33].Value = "99%";
         sheet.Cells[headers1, 34].Value = "100%";

         sheet.Cells[headers1, 35].Value = "85%";
         sheet.Cells[headers1, 36].Value = "86%";
         sheet.Cells[headers1, 37].Value = "87%";
         sheet.Cells[headers1, 38].Value = "88%";
         sheet.Cells[headers1, 39].Value = "89%";
         sheet.Cells[headers1, 40].Value = "90%";
         sheet.Cells[headers1, 41].Value = "91%";
         sheet.Cells[headers1, 42].Value = "92%";
         sheet.Cells[headers1, 43].Value = "93%";
         sheet.Cells[headers1, 44].Value = "94%";
         sheet.Cells[headers1, 45].Value = "95%";
         sheet.Cells[headers1, 46].Value = "96%";
         sheet.Cells[headers1, 47].Value = "97%";
         sheet.Cells[headers1, 48].Value = "98%";
         sheet.Cells[headers1, 49].Value = "99%";
         sheet.Cells[headers1, 50].Value = "100%";

         sheet.Cells[headers1, 51].Value = "Cheapest";
         sheet.Cells[headers1, 52].Value = "SecondCheapest";
         sheet.Cells[headers1, 53].Value = "Spec";
         sheet.Cells[headers1, 54].Value = "CAP Clean";
         sheet.Cells[headers1, 55].Value = "Chosen Retail Price";
         sheet.Cells[headers1, 56].Value = "Bid Price";
         sheet.Cells[headers1, 57].Value = "Notes";
         sheet.Cells[headers1, 58].Value = "Recall Status";


         int rowCount = headers1 += 1;

         foreach (var row in data)
         {
            SetAndFormatValuationModalHyperLink(sheet.Cells[rowCount, 1], row.Id, row.VehicleReg, row.Mileage, condition);

            sheet.Cells[rowCount, 1].Value = row.Id;
            sheet.Cells[rowCount, 2].Value = row.VehicleReg;
            sheet.Cells[rowCount, 3].Value = row.Vin;

            sheet.Cells[rowCount, 4].Value = row.Mileage;
            sheet.Cells[rowCount, 5].Value = row.CurrentRetailPrice;
            sheet.Cells[rowCount, 6].Value = row.Make;
            sheet.Cells[rowCount, 7].Value = row.Model;
            sheet.Cells[rowCount, 8].Value = row.Derivative;
            sheet.Cells[rowCount, 9].Value = row.CurrentPP;
            sheet.Cells[rowCount, 10].Value = row.LowestPP;
            sheet.Cells[rowCount, 11].Value = row.SecondLowestPP;
            sheet.Cells[rowCount, 12].Value = row.ThirdLowestPP;

            sheet.Cells[rowCount, 13].Value = row.LowestPPRetailer;
            sheet.Cells[rowCount, 14].Value = row.LowestPPVehicleReg;
            sheet.Cells[rowCount, 15].Value = row.LowestPPMileage;
            sheet.Cells[rowCount, 16].Value = row.SecondLowestPPRetailer;
            sheet.Cells[rowCount, 17].Value = row.SecondLowestPPVehicleReg;
            sheet.Cells[rowCount, 18].Value = row.SecondLowestPPMileage;
            sheet.Cells[rowCount, 19].Value = row.ThirdLowestPPRetailer;
            sheet.Cells[rowCount, 20].Value = row.ThirdLowestPPVehicleReg;
            sheet.Cells[rowCount, 21].Value = row.ThirdLowestPPMileage;

            sheet.Cells[rowCount, 22].Value = row.RetailerName;
            sheet.Cells[rowCount, 23].Value = row.RetailRating;
            sheet.Cells[rowCount, 24].Value = row.D90;
            sheet.Cells[rowCount, 25].Value = row.D91;
            sheet.Cells[rowCount, 26].Value = row.D92;
            sheet.Cells[rowCount, 27].Value = row.D93;
            sheet.Cells[rowCount, 28].Value = row.D94;
            sheet.Cells[rowCount, 29].Value = row.D95;
            sheet.Cells[rowCount, 30].Value = row.D96;
            sheet.Cells[rowCount, 31].Value = row.D97;
            sheet.Cells[rowCount, 32].Value = row.D98;
            sheet.Cells[rowCount, 33].Value = row.D99;
            sheet.Cells[rowCount, 34].Value = row.D100;

            sheet.Cells[rowCount, 35].Value = row.P85;
            sheet.Cells[rowCount, 36].Value = row.P86;
            sheet.Cells[rowCount, 37].Value = row.P87;
            sheet.Cells[rowCount, 38].Value = row.P88;
            sheet.Cells[rowCount, 39].Value = row.P89;
            sheet.Cells[rowCount, 40].Value = row.P90;
            sheet.Cells[rowCount, 41].Value = row.P91;
            sheet.Cells[rowCount, 42].Value = row.P92;
            sheet.Cells[rowCount, 43].Value = row.P93;
            sheet.Cells[rowCount, 44].Value = row.P94;
            sheet.Cells[rowCount, 45].Value = row.P95;
            sheet.Cells[rowCount, 46].Value = row.P96;
            sheet.Cells[rowCount, 47].Value = row.P97;
            sheet.Cells[rowCount, 48].Value = row.P98;
            sheet.Cells[rowCount, 49].Value = row.P99;
            sheet.Cells[rowCount, 50].Value = row.P100;
            sheet.Cells[rowCount, 51].Value = row.StrategyPrice;
            sheet.Cells[rowCount, 52].Value = row.StrategyPrice2;
            sheet.Cells[rowCount, 53].Value = row.Spec;
            sheet.Cells[rowCount, 54].Value = row.CAPValuation;

            sheet.Cells[rowCount, 55].Value = 0; // "Chosen Retail Price";
            sheet.Cells[rowCount, 56].Formula = $"=ROUNDUP(IF(BC{rowCount}=0, 0, IF(BC{rowCount}<=15000, BC{rowCount}-1500, IF(BC{rowCount}<25000, BC{rowCount}-2000, IF(BC{rowCount}<30000, BC{rowCount}-2500, BC{rowCount}-3000)))), -2)"; // "Bid Price";
            sheet.Cells[rowCount, 57].Value = row.Notes;
            sheet.Cells[rowCount, 58].Value = row.RecallStatus;

            // Add conditional formatting to the cell
            var conditionalFormattingRule = sheet.Cells[rowCount, 56].ConditionalFormatting.AddGreaterThan();
            conditionalFormattingRule.Formula = $"1.1*BB{rowCount}"; // Compare with 110% of the value in column AZ // Note: Need to amend this if Cap Value column is not BA!

            conditionalFormattingRule.Style.Fill.PatternType = ExcelFillStyle.Solid;
            conditionalFormattingRule.Style.Fill.BackgroundColor.Color = System.Drawing.Color.FromArgb(255, 199, 206); // Light red background

            conditionalFormattingRule.Style.Font.Color.Color = System.Drawing.Color.Red;

            rowCount += 1;
         }

         //autofit cols
         for (int i = 1; i < 56; i++)
         {
            sheet.Column(i).AutoFit(10);
         }
         sheet.Column(58).AutoFit(10);

         // Set width of Columns
         sheet.Column(3).Width = 23;
         sheet.Column(8).Width = 30; //Derivative
         sheet.Column(13).Width = 25; //Lowest PP Retailer 
         sheet.Column(16).Width = 25; //Lowest PP Retailer 
         sheet.Column(19).Width = 25; //Lowest PP Retailer 

         sheet.Column(23).Width = 5; //RR

         sheet.Column(24).Width = 6; //Days to Sell
         sheet.Column(25).Width = 6; //Days to Sell
         sheet.Column(26).Width = 6; //Days to Sell
         sheet.Column(27).Width = 6; //Days to Sell
         sheet.Column(28).Width = 6; //Days to Sell
         sheet.Column(29).Width = 6; //Days to Sell
         sheet.Column(30).Width = 6; //Days to Sell
         sheet.Column(31).Width = 6; //Days to Sell
         sheet.Column(32).Width = 6; //Days to Sell
         sheet.Column(33).Width = 6; //Days to Sell
         sheet.Column(34).Width = 6; //Days to Sell

         sheet.Column(35).Width = 9; //Adjusted Price
         sheet.Column(36).Width = 9; //Adjusted Price
         sheet.Column(37).Width = 9; //Adjusted Price
         sheet.Column(38).Width = 9; //Adjusted Price
         sheet.Column(39).Width = 9; //Adjusted Price
         sheet.Column(40).Width = 9; //Adjusted Price
         sheet.Column(41).Width = 9; //Adjusted Price
         sheet.Column(42).Width = 9; //Adjusted Price
         sheet.Column(43).Width = 9; //Adjusted Price
         sheet.Column(44).Width = 9; //Adjusted Price
         sheet.Column(45).Width = 9; //Adjusted Price
         sheet.Column(46).Width = 9; //Adjusted Price
         sheet.Column(47).Width = 9; //Adjusted Price
         sheet.Column(48).Width = 9; //Adjusted Price
         sheet.Column(49).Width = 9; //Adjusted Price
         sheet.Column(50).Width = 9; //Adjusted Price
         sheet.Column(51).Width = 9; //Adjusted Price
         sheet.Column(52).Width = 9; //Adjusted Price
         sheet.Column(53).Width = 9; //Adjusted Price
         sheet.Column(54).Width = 9; //Adjusted Price
         sheet.Column(56).Width = 9; //Adjusted Price

         sheet.Column(57).Width = 100; // Notes
         sheet.Column(58).Width = 20; // Recall Status



         //format cols

         //currency cols
         List<int> currencyCols = new List<int>() { 5, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 55, 56 };



         foreach (var currencyCol in currencyCols)
         {
            ExcelNumberFormatsService.SetNumberFormatCurrency(sheet.Cells[3, currencyCol, rowCount, currencyCol]);
         }

         //Percent cols
         List<int> percentCols = new List<int>() { 9, 10, 11, 12 };
         foreach (var percentCol in percentCols)
         {
            ExcelNumberFormatsService.SetNumberFormatPercentage(sheet.Cells[3, percentCol, rowCount, percentCol]);
         }

         //Number formart int
         List<int> numberCols = new List<int>() { 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34 };



         foreach (var numberCol in numberCols)
         {
            ExcelNumberFormatsService.SetNumberFormatUnits(sheet.Cells[3, numberCol, rowCount, numberCol]);
         }

         // set colors
         for (int i = 1; i <= 23; i++)
         {
            sheet.Cells[1, i].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[1, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);

            sheet.Cells[2, i].Style.Font.Color.SetColor(System.Drawing.Color.White);
            sheet.Cells[2, i].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[2, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
         }

         for (int i = 52; i <= 58; i++)
         {
            sheet.Cells[1, i].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[1, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);

            sheet.Cells[2, i].Style.Font.Color.SetColor(System.Drawing.Color.White);
            sheet.Cells[2, i].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[2, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
         }

         for (int i = 24; i <= 34; i++)
         {
            sheet.Cells[1, i].Style.Font.Color.SetColor(System.Drawing.Color.White);
            sheet.Cells[1, i].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[1, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Green);

            sheet.Cells[2, i].Style.Font.Color.SetColor(System.Drawing.Color.White);
            sheet.Cells[2, i].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[2, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Green);
         }

         for (int i = 35; i <= 50; i++)
         {
            sheet.Cells[1, i].Style.Font.Color.SetColor(System.Drawing.Color.White);
            sheet.Cells[1, i].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[1, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.IndianRed);
            sheet.Cells[2, i].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[2, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.IndianRed);
            sheet.Cells[2, i].Style.Font.Color.SetColor(System.Drawing.Color.White);
         }

         for (int i = 3; i < rowCount; i++)
         {
            sheet.Cells[i, 55].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[i, 55].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGoldenrodYellow);

            sheet.Cells[i, 56].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[i, 56].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
         }

         for (int i = 51; i <= 58; i++)
         {
            sheet.Cells[1, i].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[1, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);

            sheet.Cells[2, i].Style.Font.Color.SetColor(System.Drawing.Color.White);
            sheet.Cells[2, i].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[2, i].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Black);
         }

         for (int row = 1; row <= sheet.Dimension.Rows - 1; row++)
         {
            for (int col = 1; col <= sheet.Dimension.Columns; col++)
            {
               ExcelRange cell = sheet.Cells[row, col];

               // Set all borders for the cell
               cell.Style.Border.Top.Style = ExcelBorderStyle.Thin;
               cell.Style.Border.Left.Style = ExcelBorderStyle.Thin;
               cell.Style.Border.Right.Style = ExcelBorderStyle.Thin;
               cell.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            }
         }

         // Select the range of cells you want to merge
         ExcelRange mergeDaysToCellRange = sheet.Cells["x1:ah1"];
         mergeDaysToCellRange.Merge = true;

         ExcelRange mergeAdjustedPriceRange = sheet.Cells["AI1:AX1"];
         mergeAdjustedPriceRange.Merge = true;
      }


      private static void SetAndFormatValuationModalHyperLink(ExcelRange cell, object valuationId, object vehicleReg, object mileage, object condition)
      {
         var linkToValuationModal = $@"https://spark.cphi.co.uk/valuation/{valuationId}/{vehicleReg}/{mileage}/{condition}";
         cell.Hyperlink = new Uri(linkToValuationModal, UriKind.Absolute);
         cell.Style.Font.UnderLine = true;
         cell.Style.Font.Color.SetColor(System.Drawing.Color.Blue);
      }


      private static void ApplyStyle(ValuationSheetStyleTag style, ExcelRange cell)
      {
         if (style == ValuationSheetStyleTag.BoldUnderline)
         {
            cell.Style.Font.Bold = true;
            cell.Style.Font.UnderLine = true;
         }
         else if (style == ValuationSheetStyleTag.LightOrange)
         {
            string hex = "#FFF0C5";
            cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
            cell.Style.Fill.BackgroundColor.SetColor(ColorTranslator.FromHtml(hex));
         }
         else if (style == ValuationSheetStyleTag.DarkOrange)
         {
            string hex = "#FFDF80";
            cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
            cell.Style.Fill.BackgroundColor.SetColor(ColorTranslator.FromHtml(hex));
         }
         else if (style == ValuationSheetStyleTag.PaleYellow)
         {
            string hex = "#FFFFCC";
            cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
            cell.Style.Fill.BackgroundColor.SetColor(ColorTranslator.FromHtml(hex));
         }
         else if (style == ValuationSheetStyleTag.Centred)
         {
            cell.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
         }
         else if (style == ValuationSheetStyleTag.Wrapped)
         {
            //set cell wrap
            cell.Style.WrapText = true;
         }
         else if (style == ValuationSheetStyleTag.TopAlign)
         {
            //set the cell to vertical alignment top:
            cell.Style.VerticalAlignment = ExcelVerticalAlignment.Top;
         }
         else if (style == ValuationSheetStyleTag.Date)
         {
            cell.Style.Numberformat.Format = "dd/mm/yy";
         }
         else if (style == ValuationSheetStyleTag.Number)
         {
            cell.Style.Numberformat.Format = "#,##0;-#,##0;-";
         }
         else if (style == ValuationSheetStyleTag.Currency)
         {
            cell.Style.Numberformat.Format = "\\£#,##0;-\\£#,##0;-";
         }
         else if (style == ValuationSheetStyleTag.Percent)
         {
            cell.Style.Numberformat.Format = "#.0%;-#.0%;-";
         }
         else if (style == ValuationSheetStyleTag.GreyBorderLeft)
         {
            string hex = "#A6A6A6";
            cell.Style.Border.Left.Style = ExcelBorderStyle.Thin;
            cell.Style.Border.Left.Color.SetColor(ColorTranslator.FromHtml(hex));
         }
         else if (style == ValuationSheetStyleTag.GreyBorderRight)
         {
            string hex = "#A6A6A6";
            cell.Style.Border.Right.Style = ExcelBorderStyle.Thin;
            cell.Style.Border.Right.Color.SetColor(ColorTranslator.FromHtml(hex));
         }
         else if (style == ValuationSheetStyleTag.GreyBorderBottom)
         {
            string hex = "#A6A6A6";
            cell.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            cell.Style.Border.Bottom.Color.SetColor(ColorTranslator.FromHtml(hex));
         }
         else if (style == ValuationSheetStyleTag.GreyBorderTop)
         {
            string hex = "#A6A6A6";
            cell.Style.Border.Top.Style = ExcelBorderStyle.Thin;
            cell.Style.Border.Top.Color.SetColor(ColorTranslator.FromHtml(hex));
         }

      }
   }
}
