using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.AspNetCore.JsonPatch;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;

namespace CPHI.Spark.BusinessLogic.Services
{
    public interface ITagService
    {
        Task<IEnumerable<TagDTO>> SearchTags(TagSearchDTO searchTerm);
        Task<TagDTO> GetTag(int id, DealerGroupName dealerGroup);
        Task<TagDTO> CreateTag(CreateTagDTO tagDTO);
        Task<TagDTO> PatchTag(int id, JsonPatchDocument<Tag> patchDocument, DealerGroupName dealerGroup);
    }

    public class TagService : ITagService
    {
        private readonly string _connectionString;
        private readonly ITagDataAccess _tagDataAccess;

        public TagService(string connectionString)
        {
            _connectionString = connectionString;
            _tagDataAccess = new TagDataAccess(_connectionString);
        }

        public async Task<IEnumerable<TagDTO>> SearchTags(TagSearchDTO dto)
        {
            return await _tagDataAccess.SearchTags(dto);
        }

        public async Task<TagDTO> GetTag(int id, DealerGroupName dealerGroup)
        {
            var tag = await _tagDataAccess.GetTag(id, dealerGroup);
            if (tag == null)
            {
                throw new KeyNotFoundException($"Tag with ID {id} not found for dealer group {dealerGroup}");
            }
            return tag;
        }

        public async Task<TagDTO> CreateTag(CreateTagDTO tagDTO)
        {
            // Validate the tag data
            ValidateTag(tagDTO);

            return await _tagDataAccess.CreateTag(tagDTO);
        }

        public async Task<TagDTO> PatchTag(int id, JsonPatchDocument<Tag> patchDocument, DealerGroupName dealerGroup)
        {
            // Update the tag
            return await _tagDataAccess.PatchTag(id, patchDocument, dealerGroup);
        }

        private void ValidateTag(TagDTO tagDTO)
        {
            if (string.IsNullOrWhiteSpace(tagDTO.Label))
            {
                throw new ArgumentException("Tag label is required");
            }

            if (tagDTO.Label.Length > 50)
            {
                throw new ArgumentException("Tag label cannot exceed 50 characters");
            }

            if (tagDTO.StrategyImpactPct < 0)
            {
                throw new ArgumentException("Strategy impact percentage cannot be negative");
            }
        }
    }
}
