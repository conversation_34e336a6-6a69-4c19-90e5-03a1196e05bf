using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.AspNetCore.JsonPatch;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;

namespace CPHI.Spark.BusinessLogic.Services
{
   public interface IVehicleAdvertTagService
   {
      Task<IEnumerable<VehicleAdvertTagDTO>> SearchVehicleAdvertTags(VehicleAdvertTagSearchDTO dto);
      Task<VehicleAdvertTagDTO> GetVehicleAdvertTag(int id, DealerGroupName dealerGroup);
      Task<int?> DeleteVehicleAdvertTag(int id, DealerGroupName dealerGroup);
      Task<VehicleAdvertTagDTO> CreateVehicleAdvertTag(CreateVehicleAdvertTagDTO vehicleAdvertTagDTO);
      Task<VehicleAdvertTagDTO> PatchVehicleAdvertTag(int id, JsonPatchDocument<VehicleAdvertTag> patchDocument, int userId, DealerGroupName dealerGroup);
      Task<bool> ApplyTagToVehicles(int tagId, ApplyTagToVehiclesDTO dto, DealerGroupName dealerGroup, int? userId);
      Task<bool> RemoveTagFromVehicles(int tagId, ApplyTagToVehiclesDTO dto, DealerGroupName dealerGroup, int? userId);
   }

   public class VehicleAdvertTagService : IVehicleAdvertTagService
   {
      private readonly string _connectionString;
      private readonly IVehicleAdvertTagDataAccess _vehicleAdvertTagDataAccess;

      public VehicleAdvertTagService(string connectionString)
      {
         _connectionString = connectionString;
         _vehicleAdvertTagDataAccess = new VehicleAdvertTagDataAccess(_connectionString);
      }

      public async Task<IEnumerable<VehicleAdvertTagDTO>> SearchVehicleAdvertTags(VehicleAdvertTagSearchDTO dto)
      {
         return await _vehicleAdvertTagDataAccess.SearchVehicleAdvertTags(dto);
      }

      public async Task<VehicleAdvertTagDTO> GetVehicleAdvertTag(int id, DealerGroupName dealerGroup)
      {
         var vehicleAdvertTag = await _vehicleAdvertTagDataAccess.GetVehicleAdvertTag(id, dealerGroup);
         if (vehicleAdvertTag == null)
         {
            throw new KeyNotFoundException($"VehicleAdvertTag with ID {id} not found for dealer group {dealerGroup}");
         }
         return vehicleAdvertTag;
      }
      public async Task<int?> DeleteVehicleAdvertTag(int id, DealerGroupName dealerGroup)
      {
         int? vehicleId = await _vehicleAdvertTagDataAccess.DeleteVehicleAdvertTag(id, dealerGroup);

         if (vehicleId == null)
         {
            throw new KeyNotFoundException($"VehicleAdvertTag with ID {id} not found for dealer group {dealerGroup}");
         }
         return vehicleId;
      }

      public async Task<VehicleAdvertTagDTO> CreateVehicleAdvertTag(CreateVehicleAdvertTagDTO vehicleAdvertTagDTO)
      {
         // Validate the vehicle advert tag data
         ValidateVehicleAdvertTag(vehicleAdvertTagDTO);

         return await _vehicleAdvertTagDataAccess.CreateVehicleAdvertTag(vehicleAdvertTagDTO);
      }

      public async Task<VehicleAdvertTagDTO> PatchVehicleAdvertTag(int id, JsonPatchDocument<VehicleAdvertTag> patchDocument, int userId, DealerGroupName dealerGroup)
      {
         // Update the vehicle advert tag
         return await _vehicleAdvertTagDataAccess.PatchVehicleAdvertTag(id, patchDocument, dealerGroup);
      }

      public async Task<bool> ApplyTagToVehicles(int id, ApplyTagToVehiclesDTO dto, DealerGroupName dealerGroupName, int? userId)
      {
         return await _vehicleAdvertTagDataAccess.ApplyTagToVehicles(id, dto, dealerGroupName, userId);
      }

      public async Task<bool> RemoveTagFromVehicles(int id, ApplyTagToVehiclesDTO dto, DealerGroupName dealerGroupName, int? userId)
      {
         return await _vehicleAdvertTagDataAccess.RemoveTagFromVehicles(id, dto, dealerGroupName, userId);
      }

      private void ValidateVehicleAdvertTag(VehicleAdvertTagDTO vehicleAdvertTagDTO)
      {
         if (vehicleAdvertTagDTO.TagId <= 0)
         {
            throw new ArgumentException("TagId is required and must be greater than 0");
         }

         if (vehicleAdvertTagDTO.VehicleAdvertId <= 0)
         {
            throw new ArgumentException("VehicleAdvertId is required and must be greater than 0");
         }
      }
   }
}
