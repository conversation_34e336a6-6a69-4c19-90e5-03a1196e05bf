# BigMotoringWorldApiClient Usage Guide

## Overview
The `BigMotoringWorldApiClient` is designed to send price changes to the BigMotoringWorld API. It follows the same patterns as other API clients in the system like `RallyeApiClient`.

## API Endpoint
- **URL**: `https://func-prd-uks-spark-api.azurewebsites.net/api/SetPrice`
- **Method**: POST
- **Authentication**: Query parameter `code` with API key
- **Content-Type**: application/json

## Request Format
```json
{
    "StockNumber": 123456,
    "Price": 12345,
    "ChangedBy": "Chris"
}
```

## Configuration
Add the following to your `appsettings.json`:

```json
{
  "AppSettings": {
    "BigMotoringWorldBaseURL": "https://func-prd-uks-spark-api.azurewebsites.net",
    "BigMotoringWorldApiCode": "fpmQV_Kq3gzSbi_nHVwPbBnkb cNDns9M8RjsqsD3iUpfAzFuK7hkOQ=="
  }
}
```

## Usage Examples

### Basic Usage
```csharp
// Create client
var client = new BigMotoringWorldApiClient(
    httpClientFactory, 
    ConfigService.BigMotoringWorldBaseURL, 
    ConfigService.BigMotoringWorldApiCode
);

// Send single price change
var priceChange = new BigMotoringWorldPriceChange(123456, 12345, "AutoPrice System");
bool success = await client.SendPriceChangeAsync(priceChange);

// Send multiple price changes
var priceChanges = new List<BigMotoringWorldPriceChange>
{
    new BigMotoringWorldPriceChange(123456, 12345, "AutoPrice System"),
    new BigMotoringWorldPriceChange(789012, 23456, "AutoPrice System")
};
var results = await client.SendPriceChangesAsync(priceChanges);
```

### Integration with Existing Price Change System
```csharp
// Convert from PricingChangeMinimal (existing system format)
var pricingChanges = GetPricingChanges(); // List<PricingChangeMinimal>
var results = await client.SendPriceChangesAsync(pricingChanges, "AutoPrice System");

// Process results
for (int i = 0; i < pricingChanges.Count && i < results.Count; i++)
{
    var priceChange = pricingChanges[i];
    var success = results[i];
    
    if (success)
    {
        priceChange.DateConfirmed = DateTime.UtcNow;
        logger.Info($"Successfully updated stock {priceChange.StockNumber}");
    }
    else
    {
        logger.Error($"Failed to update stock {priceChange.StockNumber}");
    }
}
```

### Test Connection
```csharp
bool isConnected = await client.TestConnectionAsync();
if (!isConnected)
{
    logger.Error("Unable to connect to BigMotoringWorld API");
}
```

## Error Handling
- The client includes retry logic for transient errors (408, 500, 502, 503, 504)
- Failed requests return `false` rather than throwing exceptions
- Detailed error logging is included for troubleshooting

## Integration with UpdateWebsitePricesService
The client is automatically integrated into the price update system. To enable BigMotoringWorld price updates for a retailer site:

1. Set the `WhereUpdatePrices` field to include `"BigMotoringWorld"`
2. Ensure configuration values are set in appsettings.json
3. Price changes will be automatically sent during the daily price update process

## Notes
- Each price change is sent individually (no batch API support currently)
- Small delays (100ms) are added between requests to avoid overwhelming the API
- StockNumber must be a valid integer (string values are parsed)
- The API expects integer prices (no decimal places)
