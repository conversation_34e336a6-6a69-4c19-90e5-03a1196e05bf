using CPHI.Spark.Model.ViewModels.AutoPricing;
using Newtonsoft.Json;
using System.Net;
using System.Text;

namespace CPHI.Spark.DataAccess.AutoPrice
{
    public interface IBigMotoringWorldApiClient
    {
        Task<bool> SendPriceChangeAsync(BigMotoringWorldPriceChange priceChange);
        Task<List<bool>> SendPriceChangesAsync(List<BigMotoringWorldPriceChange> priceChanges);
        Task<List<bool>> SendPriceChangesAsync(List<PricingChangeMinimal> priceChanges, string changedBy);
        Task<bool> TestConnectionAsync();
    }

    public class BigMotoringWorldApiClient : IBigMotoringWorldApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _apiCode;

        private const int Retries = 3;
        private static readonly TimeSpan RetryDelay = TimeSpan.FromSeconds(3);

        private static readonly HttpStatusCode[] RetryableStatusCodes = {
            HttpStatusCode.RequestTimeout,      // 408
            HttpStatusCode.InternalServerError, // 500
            HttpStatusCode.BadGateway,          // 502
            HttpStatusCode.ServiceUnavailable,  // 503
            HttpStatusCode.GatewayTimeout,      // 504
        };

        public BigMotoringWorldApiClient(IHttpClientFactory httpClientFactory, string baseUrl, string apiCode)
        {
            _httpClient = httpClientFactory.CreateClient();
            _baseUrl = baseUrl?.TrimEnd('/') ?? throw new ArgumentNullException(nameof(baseUrl));
            _apiCode = apiCode ?? throw new ArgumentNullException(nameof(apiCode));
        }

        public async Task<bool> SendPriceChangeAsync(BigMotoringWorldPriceChange priceChange)
        {
            if (priceChange == null)
                throw new ArgumentNullException(nameof(priceChange));

            try
            {
                var json = JsonConvert.SerializeObject(priceChange);
                var response = await SendRequestWithRetryAsync(json);
                return response;
            }
            catch (Exception ex)
            {
                // Log the exception if needed
                Console.WriteLine($"Error sending price change for stock {priceChange.StockNumber}: {ex.Message}");
                return false;
            }
        }

        public async Task<List<bool>> SendPriceChangesAsync(List<BigMotoringWorldPriceChange> priceChanges)
        {
            if (priceChanges == null || !priceChanges.Any())
                throw new ArgumentException("No price changes provided");

            var results = new List<bool>();

            // Process each price change individually
            // Note: If BigMotoring World supports batch operations in the future,
            // this could be optimized to send multiple changes in a single request
            foreach (var priceChange in priceChanges)
            {
                var result = await SendPriceChangeAsync(priceChange);
                results.Add(result);

                // Add a small delay between requests to avoid overwhelming the API
                await Task.Delay(100);
            }

            return results;
        }

        public async Task<List<bool>> SendPriceChangesAsync(List<PricingChangeMinimal> priceChanges, string changedBy)
        {
            if (priceChanges == null || !priceChanges.Any())
                throw new ArgumentException("No price changes provided");

            if (string.IsNullOrWhiteSpace(changedBy))
                throw new ArgumentException("ChangedBy parameter is required", nameof(changedBy));

            // Convert PricingChangeMinimal to BigMotoringWorldPriceChange
            var bigMotoringWorldChanges = new List<BigMotoringWorldPriceChange>();

            foreach (var priceChange in priceChanges)
            {
                try
                {
                    var bmwChange = new BigMotoringWorldPriceChange(priceChange, changedBy);
                    bigMotoringWorldChanges.Add(bmwChange);
                }
                catch (ArgumentException ex)
                {
                    // Log invalid stock number format and skip this item
                    Console.WriteLine($"Skipping price change due to invalid format: {ex.Message}");
                    continue;
                }
            }

            return await SendPriceChangesAsync(bigMotoringWorldChanges);
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                // Create a test price change to verify the connection
                var testPriceChange = new BigMotoringWorldPriceChange(123456, 12345, "TestConnection");
                var json = JsonConvert.SerializeObject(testPriceChange);
                
                // Note: This will actually send a test request. 
                // In a production environment, you might want a dedicated test endpoint
                var response = await SendRequestWithRetryAsync(json);
                return response;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> SendRequestWithRetryAsync(string jsonContent)
        {
            var url = $"{_baseUrl}/api/SetPrice?code={_apiCode}";

            for (int attempt = 0; attempt < Retries; attempt++)
            {
                try
                {
                    using var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                    using var response = await _httpClient.SendAsync(request);

                    if (response.IsSuccessStatusCode)
                    {
                        return true;
                    }

                    // Retry on certain HTTP status codes
                    if (attempt < Retries - 1 && RetryableStatusCodes.Contains(response.StatusCode))
                    {
                        await Task.Delay(RetryDelay);
                        continue;
                    }

                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"BigMotoringWorld API call failed ({response.StatusCode}): {errorContent}", null, response.StatusCode);
                }
                catch (HttpRequestException) when (attempt < Retries - 1)
                {
                    await Task.Delay(RetryDelay);
                }
            }

            return false;
        }
    }
}
