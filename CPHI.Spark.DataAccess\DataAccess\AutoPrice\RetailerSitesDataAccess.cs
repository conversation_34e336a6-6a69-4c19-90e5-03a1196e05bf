﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace CPHI.Spark.DataAccess.AutoPrice
{
   public interface IRetailerSitesDataAccess
   {
      Task<List<RetailerSite>> GetRetailerSites(DealerGroupName dealerGroup);
      Task<List<RetailerSite>> GetRetailerSitesWithBuyingStrategies(DateTime runDate, DealerGroupName dealerGroup);
      Task<List<RetailerSite>> GetRetailerSitesWithStrategies(DateTime runDate, DealerGroupName dealerGroup);
      Task<Dictionary<int, RetailerSiteStrategyBandingDefinition>> GetRetailerStrategyBandings();
   }

   public class RetailerSitesDataAccess : IRetailerSitesDataAccess
   {
      private readonly string _connectionString;
      public RetailerSitesDataAccess(string connectionString)
      {
         this._connectionString = connectionString;
      }


      public async Task<List<RetailerSite>> GetRetailerSites(DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            //find most recent snapshot from today (only expect there to be one but just in case)
            int dealerGroupId = (int)dealerGroup;

            var excludeIds = new List<int>();
            if (dealerGroup == DealerGroupName.RRGUK)
            {
               // excludeIds.AddRange(new List<int>() { 1, 2, 3, 4, 5 });
            }
            return await db.RetailerSites
                .Where(x => x.DealerGroup_Id == dealerGroupId && x.IsActive == true && !excludeIds.Contains(x.Id))
                .Include(x => x.Site)
                .Include(x => x.TradePriceSetting)
                .ToListAsync();
         }
      }

      public async Task<Dictionary<int, RetailerSiteStrategyBandingDefinition>> GetRetailerStrategyBandings()
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            string query = @"
SELECT
Id as RetailerSiteId,
OverUnderIsPercent ,
OverUnderThreshold ,
VeryThreshold ,
VeryThresholdIsPercent 
FROM autoprice.RetailerSites
";
            return (await dapper.GetAllAsync<RetailerSiteStrategyBandingDefinition>(query, null, commandType: System.Data.CommandType.Text)).ToDictionary(x => x.RetailerSiteId);

         }
      }

      public async Task<List<RetailerSite>> GetRetailerSitesWithStrategies(DateTime runDate, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var strategiesInUse = await db.RetailerSites.Where(x => x.DealerGroup_Id == (int)dealerGroup)
                                    //this path gets us the default strategy for the ruleset
                                    .Include(x => x.StrategySelectionRuleSet)
                                        .ThenInclude(x => x.DefaultStrategyVersion)
                                            .ThenInclude(x => x.StrategyFactors)
                                                .ThenInclude(x => x.StrategyFactorItems)
                                    //this path gets us the criteria for the rules in the ruleset
                                    .Include(x => x.StrategySelectionRuleSet)
                                        .ThenInclude(x => x.StrategySelectionRules)
                                            .ThenInclude(x => x.StrategySelectionCriterias)
                                                .ThenInclude(x => x.StrategyFieldName)
                                    //this path gets us the site
                                    .Include(x => x.Site)
                                    //this path gets us the resulting strategy version for the ruleset
                                    .Include(x => x.StrategySelectionRuleSet)
                                        .ThenInclude(x => x.StrategySelectionRules)
                                            .ThenInclude(x => x.StrategyVersion)
                                                .ThenInclude(x => x.StrategyFactors)
                                                    .ThenInclude(x => x.StrategyFactorItems).ToListAsync();


            return strategiesInUse;
         }
      }

      public async Task<List<BuyingCostsSet>> GetBuyingCostsSets(int dealerGroup_Id)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var buyingCostsSets = await db.BuyingCostsSets
                .Where(x => x.DealerGroup_Id == dealerGroup_Id)
                .AsNoTracking()
                .ToListAsync();

            return buyingCostsSets;
         }
      }

      public async Task<List<RetailerSite>> GetRetailerSitesWithBuyingStrategies(DateTime runDate, DealerGroupName dealerGroup)
      {

         using (var db = new CPHIDbContext(_connectionString))
         {

            // 1. Load the base sites
            var retailerSites = await db.RetailerSites
                .Where(x => x.DealerGroup_Id == (int)dealerGroup)
                .Include(x => x.Site)              // basic related data
                .ToListAsync();

            // 2. Gather distinct rule set IDs
            var ruleSetIds = retailerSites
                .Where(s => s.BuyingStrategySelectionRuleSet_Id != null)
                .Select(s => s.BuyingStrategySelectionRuleSet_Id.Value)
                .Distinct()
                .ToList();

            var ruleSet2Ids = retailerSites
                .Where(s => s.BuyingStrategySelectionRuleSet2_Id != null)
                .Select(s => s.BuyingStrategySelectionRuleSet2_Id.Value)
                .Distinct()
                .ToList();

            // 3. Batch-load the related rule sets with all their children
            var buyingStrategyRuleSets = await db.StrategySelectionRuleSets
                .Where(x => ruleSetIds.Contains(x.Id))
                .Include(x => x.DefaultStrategyVersion)
                    .ThenInclude(x => x.StrategyFactors)
                        .ThenInclude(x => x.StrategyFactorItems)
                .Include(x => x.StrategySelectionRules)
                    .ThenInclude(x => x.StrategySelectionCriterias)
                        .ThenInclude(x => x.StrategyFieldName)
                .Include(x => x.StrategySelectionRules)
                    .ThenInclude(x => x.StrategyVersion)
                        .ThenInclude(x => x.StrategyFactors)
                            .ThenInclude(x => x.StrategyFactorItems)
                .AsSplitQuery()
               .ToListAsync();

            var buyingStrategyRuleSets2 = await db.StrategySelectionRuleSets
                .Where(x => ruleSet2Ids.Contains(x.Id))
                .Include(x => x.DefaultStrategyVersion)
                    .ThenInclude(x => x.StrategyFactors)
                        .ThenInclude(x => x.StrategyFactorItems)
                .Include(x => x.StrategySelectionRules)
                    .ThenInclude(x => x.StrategySelectionCriterias)
                        .ThenInclude(x => x.StrategyFieldName)
                .Include(x => x.StrategySelectionRules)
                    .ThenInclude(x => x.StrategyVersion)
                        .ThenInclude(x => x.StrategyFactors)
                            .ThenInclude(x => x.StrategyFactorItems)
                .AsSplitQuery()
                            .ToListAsync();

            // 4. Assign the loaded rule sets to the corresponding sites
            foreach (var site in retailerSites)
            {
               if (site.BuyingStrategySelectionRuleSet_Id != null)
               {
                  site.BuyingStrategySelectionRuleSet =
                      buyingStrategyRuleSets.FirstOrDefault(x => x.Id == site.BuyingStrategySelectionRuleSet_Id);
               }
               if (site.BuyingStrategySelectionRuleSet2_Id != null)
               {
                  site.BuyingStrategySelectionRuleSet2 =
                      buyingStrategyRuleSets2.FirstOrDefault(x => x.Id == site.BuyingStrategySelectionRuleSet2_Id);
               }
            }

            return retailerSites;

         }

      }

      public async Task<List<RetailerSite>> GetRetailerSitesWithTestStrategies(DateTime runDate, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var strategiesInUse = await db.RetailerSites.Where(x => x.DealerGroup_Id == (int)dealerGroup)
                                    //this path gets us the default strategy for the ruleset
                                    .Include(x => x.TestStrategySelectionRuleSet)
                                        .ThenInclude(x => x.DefaultStrategyVersion)
                                            .ThenInclude(x => x.StrategyFactors)
                                                .ThenInclude(x => x.StrategyFactorItems)
                                    //this path gets us the criteria for the rules in the ruleset
                                    .Include(x => x.TestStrategySelectionRuleSet)
                                        .ThenInclude(x => x.StrategySelectionRules)
                                            .ThenInclude(x => x.StrategySelectionCriterias)
                                                .ThenInclude(x => x.StrategyFieldName)
                                    //this path gets us the site
                                    .Include(x => x.Site)
                                    //this path gets us the resulting strategy version for the ruleset
                                    .Include(x => x.TestStrategySelectionRuleSet)
                                        .ThenInclude(x => x.StrategySelectionRules)
                                            .ThenInclude(x => x.StrategyVersion)
                                                .ThenInclude(x => x.StrategyFactors)
                                                    .ThenInclude(x => x.StrategyFactorItems)
                                                    .AsSplitQuery() // instruct EF to split into multiple queries
                                                    .AsNoTracking() // if you're not updating the results, use this for a small performance boost
                                                    .ToListAsync();


            return strategiesInUse;
         }
      }



   }

}

