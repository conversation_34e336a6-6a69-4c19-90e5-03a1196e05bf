using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess;
using Dapper;
using Microsoft.EntityFrameworkCore;
using System.Data;
using Microsoft.AspNetCore.JsonPatch;

namespace CPHI.Spark.DataAccess.AutoPrice
{
   public interface ITagDataAccess
   {
      Task<IEnumerable<TagDTO>> SearchTags(TagSearchDTO dto);
      Task<TagDTO> GetTag(int id, DealerGroupName dealerGroup);
      Task<TagDTO> CreateTag(CreateTagDTO tagDTO);
      Task<TagDTO> UpdateTag(int id, TagDTO tagDTO, DealerGroupName dealerGroup);
      Task<TagDTO> PatchTag(int id, JsonPatchDocument<Tag> patch, DealerGroupName dealerGroup);
   }

   public class TagDataAccess : ITagDataAccess
   {
      private readonly string _connectionString;

      public TagDataAccess(string connectionString)
      {
         _connectionString = connectionString;
      }

      public async Task<IEnumerable<TagDTO>> SearchTags(TagSearchDTO dto)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var query = db.Tags
               .Include(x => x.CreatedBy)
               .Include(x => x.UpdatedBy)
               .Where(x => x.DealerGroupId == (int)dto.DealerGroupName).AsQueryable();

            if (dto.IsActive.HasValue)
            {
               query = query.Where(x => x.IsActive == dto.IsActive);
            }

            var result = await query
               .AsNoTracking()
               .Select(x => new TagDTO(x))
               .ToListAsync();

            return result;
         }
      }

      public async Task<TagDTO> GetTag(int id, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var tag = db.Tags
               .Where(x => x.Id == id && x.DealerGroupId == (int)dealerGroup)
               .Include(x => x.DealerGroup)
               .AsNoTracking()
               .FirstOrDefault();

            return new TagDTO(tag);
         }
      }

      public async Task<TagDTO> CreateTag(CreateTagDTO tagDTO)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var tag = new Tag
            {
               DealerGroupId = tagDTO.DealerGroupId,
               Label = tagDTO.Label,
               IsActive = tagDTO.IsActive,
               StrategyImpactPct = tagDTO.StrategyImpactPct,
               StrategyImpactAmount = tagDTO.StrategyImpactAmount,
               Colour = tagDTO.Colour
            };

            db.Tags.Add(tag);
            await db.SaveChangesAsync();

            // Return the created tag as DTO
            return new TagDTO(tag);
         }
      }

      public async Task<TagDTO> UpdateTag(int id, TagDTO tagDTO, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var tag = await db.Tags.FirstOrDefaultAsync(t => t.Id == id && t.DealerGroupId == (int)dealerGroup);

            if (tag == null)
            {
               throw new KeyNotFoundException($"Tag with ID {id} not found for dealer group {dealerGroup}");
            }

            tag.Label = tagDTO.Label;
            tag.IsActive = tagDTO.IsActive;
            tag.StrategyImpactPct = tagDTO.StrategyImpactPct;
            tag.StrategyImpactAmount = tagDTO.StrategyImpactAmount;

            await db.SaveChangesAsync();

            // Return the updated tag as DTO
            return new TagDTO(tag);
         }
      }

      public async Task<TagDTO> PatchTag(int id, JsonPatchDocument<Tag> patch, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var tag = db.Tags.FirstOrDefault(t => t.Id == id && t.DealerGroupId == (int)dealerGroup);

            if (tag == null)
            {
               throw new KeyNotFoundException($"Tag with ID {id} not found for dealer group {dealerGroup}");
            }

            patch.ApplyTo(tag);

            db.Tags.Update(tag);

            await db.SaveChangesAsync();

            return new TagDTO(tag);
         }
      }
   }
}

