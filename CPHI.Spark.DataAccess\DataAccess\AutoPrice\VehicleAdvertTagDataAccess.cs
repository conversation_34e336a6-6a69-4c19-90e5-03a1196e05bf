using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess;
using Dapper;
using Microsoft.EntityFrameworkCore;
using System.Data;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Configuration.UserSecrets;

namespace CPHI.Spark.DataAccess.AutoPrice
{
   public interface IVehicleAdvertTagDataAccess
   {
      Task<IEnumerable<VehicleAdvertTagDTO>> SearchVehicleAdvertTags(VehicleAdvertTagSearchDTO dto);
      Task<VehicleAdvertTagDTO> GetVehicleAdvertTag(int id, DealerGroupName dealerGroup);
      Task<int?> DeleteVehicleAdvertTag(int id, DealerGroupName dealerGroup);
      Task<VehicleAdvertTagDTO> CreateVehicleAdvertTag(CreateVehicleAdvertTagDTO vehicleAdvertTagDto);
      Task<VehicleAdvertTagDTO> UpdateVehicleAdvertTag(int id, VehicleAdvertTagDTO vehicleAdvertTagDto, int userId, DealerGroupName dealerGroup);
      Task<VehicleAdvertTagDTO> PatchVehicleAdvertTag(int id, JsonPatchDocument<VehicleAdvertTag> patch, DealerGroupName dealerGroup);
      Task<bool> ApplyTagToVehicles(int id, ApplyTagToVehiclesDTO dto, DealerGroupName dealerGroup, int? userId);
      Task<bool> RemoveTagFromVehicles(int id, ApplyTagToVehiclesDTO dto, DealerGroupName dealerGroup, int? userId);
   }

   public class VehicleAdvertTagDataAccess : IVehicleAdvertTagDataAccess
   {
      private readonly string _connectionString;

      public VehicleAdvertTagDataAccess(string connectionString)
      {
         _connectionString = connectionString;
      }

      public async Task<IEnumerable<VehicleAdvertTagDTO>> SearchVehicleAdvertTags(VehicleAdvertTagSearchDTO dto)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var query = db.VehicleAdvertTags.Where(x => x.Tag.DealerGroupId == (int)dto.DealerGroupName).AsQueryable();

            if (dto.IsActive.HasValue)
            {
               query = query.Where(x => x.Tag.IsActive == dto.IsActive);
            }

            if (dto.TagId.HasValue)
            {
               query = query.Where(x => x.TagId == dto.TagId);
            }

            if (dto.DealerGroupName.HasValue)
            {
               query = query.Where(x => x.Tag.DealerGroupId == (int)dto.DealerGroupName);
            }

            if (dto.VehicleAdvertId.HasValue)
            {
               query = query.Where(x => x.VehicleAdvertId == dto.VehicleAdvertId);
            }

            var result = await query
               .Include(x => x.Tag)
               .Include(x => x.CreatedBy)
               .Include(x => x.UpdatedBy)
               .AsNoTracking()
               .Select(x => new VehicleAdvertTagDTO(x))
               .ToListAsync();

            return result;
         }
      }

      public async Task<VehicleAdvertTagDTO> GetVehicleAdvertTag(int id, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var vehicleAdvertTag = await db.VehicleAdvertTags
               .Where(x => x.Id == id && x.Tag.DealerGroupId == (int)dealerGroup)
               .Include(x => x.Tag)
               .AsNoTracking()
               .Select(x => new VehicleAdvertTagDTO(x))
               .FirstOrDefaultAsync();

            if (vehicleAdvertTag == null)
            {
               throw new KeyNotFoundException($"VehicleAdvertTag with ID {id} not found for dealer group {dealerGroup}");
            }

            return vehicleAdvertTag;
         }
      }
      public async Task<int?> DeleteVehicleAdvertTag(int id, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var vehicleAdvertTag = await db.VehicleAdvertTags
               .Where(x => x.Id == id && x.Tag.DealerGroupId == (int)dealerGroup)
               .Include(x => x.Tag)
               .AsNoTracking()
               .FirstOrDefaultAsync();

            if (vehicleAdvertTag == null)
            {
               throw new KeyNotFoundException($"VehicleAdvertTag with ID {id} not found for dealer group {dealerGroup}");
            }

            db.VehicleAdvertTags.Remove(vehicleAdvertTag);

            await db.SaveChangesAsync();

            return vehicleAdvertTag.VehicleAdvertId;
         }
      }

      public async Task<VehicleAdvertTagDTO> CreateVehicleAdvertTag(CreateVehicleAdvertTagDTO vehicleAdvertTagDTO)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Verify the tag belongs to the dealer group
            var tag = await db.Tags.FirstOrDefaultAsync(t => t.Id == vehicleAdvertTagDTO.TagId && t.DealerGroupId == (int)vehicleAdvertTagDTO.DealerGroupName);
            if (tag == null)
            {
               throw new ArgumentException($"Tag with ID {vehicleAdvertTagDTO.TagId} not found for dealer group {vehicleAdvertTagDTO.DealerGroupName}");
            }

            var vehicleAdvertTag = new VehicleAdvertTag
            {
               TagId = vehicleAdvertTagDTO.TagId,
               VehicleAdvertId = vehicleAdvertTagDTO.VehicleAdvertId,
               CreatedById = vehicleAdvertTagDTO.CreatedById,
               UpdatedById = vehicleAdvertTagDTO.UpdatedById,
               CreatedDate = DateTime.UtcNow,
               UpdatedDate = DateTime.UtcNow
            };

            db.VehicleAdvertTags.Add(vehicleAdvertTag);
            await db.SaveChangesAsync();

            // Return the created vehicle advert tag as DTO
            return new VehicleAdvertTagDTO(vehicleAdvertTag);
         }
      }

      public async Task<VehicleAdvertTagDTO> UpdateVehicleAdvertTag(int id, VehicleAdvertTagDTO vehicleAdvertTagDto, int userId, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var vehicleAdvertTag = await db.VehicleAdvertTags
                .Include(vat => vat.Tag)
                .FirstOrDefaultAsync(vat => vat.Id == id && vat.Tag.DealerGroupId == (int)dealerGroup);

            if (vehicleAdvertTag == null)
            {
               throw new KeyNotFoundException($"VehicleAdvertTag with ID {id} not found for dealer group {dealerGroup}");
            }

            // Verify the new tag belongs to the dealer group if TagId is being changed
            if (vehicleAdvertTag.TagId != vehicleAdvertTagDto.TagId)
            {
               var newTag = await db.Tags.FirstOrDefaultAsync(t => t.Id == vehicleAdvertTagDto.TagId && t.DealerGroupId == (int)dealerGroup);
               if (newTag == null)
               {
                  throw new ArgumentException($"Tag with ID {vehicleAdvertTagDto.TagId} not found for dealer group {dealerGroup}");
               }
               vehicleAdvertTag.TagId = vehicleAdvertTagDto.TagId;
            }

            vehicleAdvertTag.VehicleAdvertId = vehicleAdvertTagDto.VehicleAdvertId;
            vehicleAdvertTag.UpdatedById = userId;
            vehicleAdvertTag.UpdatedDate = DateTime.UtcNow;

            await db.SaveChangesAsync();

            // Return the updated vehicle advert tag as DTO
            return await GetVehicleAdvertTag(id, dealerGroup);
         }
      }

      public async Task<VehicleAdvertTagDTO> PatchVehicleAdvertTag(int id, JsonPatchDocument<VehicleAdvertTag> patch, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var vehicleAdvertTag = db.VehicleAdvertTags
                .FirstOrDefault(x => x.Id == id && x.Tag.DealerGroupId == (int)dealerGroup);

            if (vehicleAdvertTag == null)
            {
               throw new KeyNotFoundException($"VehicleAdvertTag with ID {id} not found for dealer group {dealerGroup}");
            }

            patch.ApplyTo(vehicleAdvertTag);

            vehicleAdvertTag.UpdatedDate = DateTime.UtcNow;

            db.VehicleAdvertTags.Update(vehicleAdvertTag);

            await db.SaveChangesAsync();

            return new VehicleAdvertTagDTO(vehicleAdvertTag);
         }
      }

      public async Task<bool> ApplyTagToVehicles(int tagId, ApplyTagToVehiclesDTO dto, DealerGroupName dealerGroup, int? userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var existingTags = await db.VehicleAdvertTags
               .Where(x => x.VehicleAdvert.RetailerSite.DealerGroup_Id == (int)dealerGroup)
               .Where(x => dto.VehicleAdvertIds.Contains(x.VehicleAdvertId))
               .Where(x => x.TagId == tagId)
               .Select(x => x.VehicleAdvertId)
               .ToListAsync();

            var toAdd = dto.VehicleAdvertIds.Except(existingTags);

            foreach (var advertId in toAdd)
            {
               var newTag = new VehicleAdvertTag()
               {
                  VehicleAdvertId = advertId,
                  TagId = tagId,
                  CreatedDate = DateTime.Now,
                  UpdatedDate = DateTime.Now,
                  CreatedById = userId,
                  UpdatedById = userId,
               };

               db.VehicleAdvertTags.Add(newTag);
            }

            await db.SaveChangesAsync();

            return true;
         }
      }

      public async Task<bool> RemoveTagFromVehicles(int tagId, ApplyTagToVehiclesDTO dto, DealerGroupName dealerGroup, int? userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var existingTags = await db.VehicleAdvertTags
               .Where(x => x.VehicleAdvert.RetailerSite.DealerGroup_Id == (int)dealerGroup)
               .Where(x => dto.VehicleAdvertIds.Contains(x.VehicleAdvertId))
               .Where(x => x.TagId == tagId)
               .ToListAsync();

            db.VehicleAdvertTags.RemoveRange(existingTags);

            await db.SaveChangesAsync();

            return true;
         }
      }
   }
}
