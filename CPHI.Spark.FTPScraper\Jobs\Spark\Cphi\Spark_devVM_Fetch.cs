﻿using CPHI.Spark.FTPScraper.Infrastructure;
using log4net;
using Microsoft.Extensions.Options;
using Quartz;
using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using WinSCP;

namespace CPHI.Spark.FTPScraper.Jobs.Spark.RRG
{
   [DisallowConcurrentExecution]
   public class Spark_devVM_Fetch : IJob
   {
      private static readonly ILog logger = LogManager.GetLogger(typeof(Spark_devVM_Fetch));
      private readonly FtpScraperSettings _settings;
      private string fileDestination;
      private string customerName;
      private TransferOptions transferOptions;


      public Spark_devVM_Fetch(IOptions<FtpScraperSettings> settings)
      {
         _settings = settings.Value;
      }

      public async Task Execute(IJobExecutionContext context)
      {

         Stopwatch stopwatch = new Stopwatch();
         stopwatch.Start();

         logger.Info($" SparkCphiFetch started.");

         string errorMessage = string.Empty;

         fileDestination = _settings.FileDestination.Replace("{destinationFolder}", "rrg");
         customerName = "RRG";

         try
         {
            /// Setup session options
            SessionOptions sessionOptions = new SessionOptions
            {
               Protocol = Protocol.Sftp,
               HostName = _settings.HostnameCphi,
               UserName = _settings.UsernameCphi,
               Password = _settings.PasswordCphi,
            };

            sessionOptions.SshHostKeyPolicy = SshHostKeyPolicy.GiveUpSecurityAndAcceptAny;

            using (Session session = new Session())
            {
               // Connect
               session.Open(sessionOptions);
               transferOptions = new TransferOptions();
               transferOptions.TransferMode = TransferMode.Binary;

               //GetCitNowFilesRenault(session);
               await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "bca", false, null, null, null);

               await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "carco", false, null, null, null);

               await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "nexusPoint", false, null, null, null);

               await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "wjking", false, null, null, null);

               await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "arthurscars", false, null, ".csv", "arthurs cars stock");

               await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "bigMotoringWorld", false, "BIGStockToSpark", ".csv", "BigMotoringWorldStock");

            }

            stopwatch.Stop();
            logger.Info($" SparkCphiFetch completed.");
         }
         catch (Exception e)
         {
            stopwatch.Stop();
            errorMessage = e.ToString();
            Console.WriteLine("Error: {0}", e);

            logger.Info($" FTPScraper Executed, encountered error.");
         }
         finally
         {
            Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
            {
               Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
               Project = "FTPScraper",
               Customer = customerName,
               Environment = "PROD",
               Task = this.GetType().Name,
               StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
               EndDate = DateTime.UtcNow,
               Status = errorMessage.Length > 0 ? "Fail" : "Pass",
               Notes = errorMessage,
               HTML = string.Empty
            };

            await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
         }
      }

   }
}
