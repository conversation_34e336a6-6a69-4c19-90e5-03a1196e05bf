﻿using CPHI.Spark.FTPScraper.Infrastructure;
using CPHI.Spark.FTPScraper.Jobs.Spark.RRG;
using log4net;
using Microsoft.Extensions.Options;
using Quartz;
using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using WinSCP;

namespace CPHI.Spark.FTPScraper.Jobs.Spark.RRGSpain
{
    [DisallowConcurrentExecution]
    public class SparkRRGSpainCphiSiteFetch : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(SparkRRGSpainCphiSiteFetch));
        private readonly FtpScraperSettings _settings;
        private string fileDestination;
        private string customerName;
        private TransferOptions transferOptions;

        public SparkRRGSpainCphiSiteFetch(IOptions<FtpScraperSettings> settings)
        {
            _settings = settings.Value;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            logger.Info($" SparkRRGSpainCphiSiteFetch started.");

            string errorMessage = string.Empty;
            fileDestination = _settings.FileDestination.Replace("{destinationFolder}", "spain");
            customerName = "RRGSpain";

            try
            {
                /// Setup session options
                SessionOptions sessionOptions = new SessionOptions
                {
                    Protocol = Protocol.Sftp,
                    HostName = _settings.Hostname,
                    UserName = _settings.Username,
                    Password = _settings.Password,
                };

                sessionOptions.SshHostKeyPolicy = SshHostKeyPolicy.GiveUpSecurityAndAcceptAny;

                using (Session session = new Session())
                {
                    // Connect
                    session.Open(sessionOptions);
                    transferOptions = new TransferOptions();
                    transferOptions.TransferMode = TransferMode.Binary;

                    //GetDistrinetFilesSpain(session);
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "rrgspain/DISTRINET", false, null, null, null);

                }

                stopwatch.Stop();
                logger.Info($" SparkRRGSpainCphiSiteFetch completed.");
            }
            catch (Exception e)
            {
                stopwatch.Stop();
                errorMessage = e.ToString();
                Console.WriteLine("Error: {0}", e);

                logger.Info($" FTPScraper Executed, encountered error.");
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "FTPScraper",
                    Customer = customerName,
                    Environment = "PROD",
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };

                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }
        }


        //private void GetDistrinetFilesSpain(Session session)
        //{
        //    try
        //    {
        //        TransferOperationResult transferResult = session.GetFiles(@"/rrgspain/DISTRINET/*", fileDestination, true, transferOptions);

        //        transferResult.Check();

        //        foreach (TransferEventArgs transfer in transferResult.Transfers)
        //        {
        //            DateTime fileDate = DateTime.Now;
        //            string newNameMainPart = transfer.FileName.Split('/')[3];

        //            string oldFileName = Path.Combine(fileDestination, transfer.FileName.Replace("/rrgspain/DISTRINET/", ""));
        //            string newFileName = Path.Combine(fileDestination, newNameMainPart);

        //            //File.Move(oldFileName, newFileName);
        //            //File.Copy(newFileName, devFileName);

        //        }

        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }

        //}

        


    }
}
