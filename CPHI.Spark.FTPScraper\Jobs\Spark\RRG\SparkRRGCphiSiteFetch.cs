﻿using CPHI.Spark.FTPScraper.Infrastructure;
using log4net;
using Microsoft.Extensions.Options;
using Quartz;
using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using WinSCP;

namespace CPHI.Spark.FTPScraper.Jobs.Spark.RRG
{
    [DisallowConcurrentExecution]
    public class SparkRRGCphiSiteFetch : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(SparkRRGCphiSiteFetch));
        private readonly FtpScraperSettings _settings;
        private string fileDestination;
        private string customerName;
        private TransferOptions transferOptions;


        public SparkRRGCphiSiteFetch(IOptions<FtpScraperSettings> settings)
        {
            _settings = settings.Value;
        }

        public async Task Execute(IJobExecutionContext context)
        {
                
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            logger.Info($" SparkRRGCphiSiteFetch started.");

            string errorMessage = string.Empty;
            fileDestination = _settings.FileDestination.Replace("{destinationFolder}", "rrg");
            customerName = "RRG";

            try
            {
                /// Setup session options
                SessionOptions sessionOptions = new SessionOptions
                {
                    Protocol = Protocol.Sftp,
                    HostName = _settings.Hostname,
                    UserName = _settings.Username,
                    Password = _settings.Password,
                };

                sessionOptions.SshHostKeyPolicy = SshHostKeyPolicy.GiveUpSecurityAndAcceptAny;

                using (Session session = new Session())
                {
                    // Connect
                    session.Open(sessionOptions);
                    transferOptions = new TransferOptions();
                    transferOptions.TransferMode = TransferMode.Binary;

                    //GetCitNowFilesRenault(session);
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "citnow", false, "Sales", ".csv", "SPK36a");
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "citnow", false, "Workshop", ".csv", "SPK36b");

                    //GetedynamixFilesRenault(session);
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "edynamix", false, "VHC", ".csv");

                    //GetAutoTraderFilesRenault(session);
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "atg", false, "ATG", ".csv");

                    // GetSLinesSeviceAndPartsRRG
                    // "SPK32", "SPK35", "VM_SPKSline3776", "VM_SPKSlinemth.csv"
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "cdk", false, "SPK32", ".csv");
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "cdk", false, "SPK35", ".csv");

                    // Both of these files will be renamed to SPK11
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "cdk", false, "VM_SPKSline3776", ".csv", "SPK11");
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "cdk", false, "VM_SPKSlinemth", ".csv", "SPK11");

                    // Brindley Stock
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "pinewood", false, "01 Head Office Used Vehicles Stock List", ".csv");

                    // V12 Stock
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "v12", false, "V12SparkStockFeed", ".csv");

                    // Waylands Stock
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "Waylands", false, "Spark_Waylands_Extract", ".csv");

                    // Startin Stock
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "startin", false, "Startin", ".csv");


                }

                stopwatch.Stop();
                logger.Info($" SparkRRGCphiSiteFetch completed.");
            }
            catch (Exception e)
            {
                stopwatch.Stop();
                errorMessage = e.ToString();
                Console.WriteLine("Error: {0}", e);

                logger.Info($" FTPScraper Executed, encountered error.");
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "FTPScraper",
                    Customer = customerName,
                    Environment = "PROD",
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };

                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }
        }

    }
}
