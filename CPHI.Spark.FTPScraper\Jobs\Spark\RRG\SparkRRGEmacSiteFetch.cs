﻿using CPHI.Spark.FTPScraper.Infrastructure;
using log4net;
using Microsoft.Extensions.Options;
using Quartz;
using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using WinSCP;

namespace CPHI.Spark.FTPScraper.Jobs.Spark.RRG
{
    [DisallowConcurrentExecution]
    public class SparkRRGEmacSiteFetch : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(SparkRRGCphiSiteFetch));

        private readonly FtpScraperSettings _settings;
        private string fileDestination;
        private string customerName;
        private TransferOptions transferOptions;

        public SparkRRGEmacSiteFetch(IOptions<FtpScraperSettings> settings)
        {
            _settings = settings.Value;
        }


        public async Task Execute(IJobExecutionContext context)
        {

            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            logger.Info($" SparkRRGEmacSiteFetch started.");

            string errorMessage = string.Empty;
            fileDestination = _settings.FileDestination.Replace("{destinationFolder}", "rrg");
            customerName = "RRG";

            try
            {
                /// Setup session options
                SessionOptions sessionOptions = new SessionOptions
                {
                    Protocol = Protocol.Sftp,
                    HostName = _settings.Hostname,
                    UserName = _settings.Username,
                    Password = _settings.Password,
                };

                sessionOptions.SshHostKeyPolicy = SshHostKeyPolicy.GiveUpSecurityAndAcceptAny;

                using (Session session = new Session())
                {
                    // Connect
                    session.Open(sessionOptions);
                    transferOptions = new TransferOptions();
                    transferOptions.TransferMode = TransferMode.Binary;

                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "citnow", false, "RRG MTD Plans", ".csv", "SPK40");
                }

                stopwatch.Stop();

                logger.Info($" SparkRRGEmacSiteFetch completed.");
            }
            catch (Exception e)
            {
                stopwatch.Stop();
                errorMessage = e.ToString();
                Console.WriteLine("Error: {0}", e);

                logger.Info($" FTPScraper Executed, encountered error.");
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "FTPScraper",
                    Customer = customerName,
                    Environment = "PROD",
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };

                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }


        }


        //private void GetEmacFilesRenault(Session session)
        //{
        //    transferOptions.FileMask = "*RRG MTD Plans*";
        //    TransferOperationResult transferResult = session.GetFiles(@"/*", fileDestination, true, transferOptions);
        //    transferResult.Check();// Throw on any error

        //    foreach (TransferEventArgs transfer in transferResult.Transfers)
        //    {
        //        string newNameMainPart = "SPK40";
        //        string timePrefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";
        //        string newFileName = Path.Combine(fileDestination, timePrefix + newNameMainPart + ".csv");
        //        string oldFileName = Path.Combine(fileDestination, transfer.FileName.Replace("/citnow/", ""));

        //        File.Move(oldFileName, newFileName);
        //        logger.Info($" GetEmacFilesRenault - Saved File: {newFileName}");
        //    }
        //}




       


    }
}
