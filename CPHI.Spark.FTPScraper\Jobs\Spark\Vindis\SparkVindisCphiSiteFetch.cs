﻿using CPHI.Spark.FTPScraper.Infrastructure;
using CPHI.Spark.FTPScraper.Jobs.Spark.RRGSpain;
using log4net;
using Microsoft.Extensions.Options;
using Quartz;
using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using WinSCP;

namespace CPHI.Spark.FTPScraper.Jobs.Spark.Vindis
{
    [DisallowConcurrentExecution]
    public class SparkVindisCphiSiteFetch : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(SparkVindisCphiSiteFetch));
        private readonly FtpScraperSettings _settings;
        private string fileDestination;
        private string customerName;
        private TransferOptions transferOptions;

        public SparkVindisCphiSiteFetch(IOptions<FtpScraperSettings> settings)
        {
            _settings = settings.Value;
        }


        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            logger.Info($" SparkVindisCphiSiteFetch started.");

            string errorMessage = string.Empty;
            fileDestination = _settings.FileDestination.Replace("{destinationFolder}", "vindis");
            customerName = "Vindis";

            try
            {
                /// Setup session options
                SessionOptions sessionOptions = new SessionOptions
                {
                    Protocol = Protocol.Sftp,
                    HostName = _settings.Hostname,
                    UserName = _settings.Username,
                    Password = _settings.Password,
                };

                sessionOptions.SshHostKeyPolicy = SshHostKeyPolicy.GiveUpSecurityAndAcceptAny;

                using (Session session = new Session())
                {
                    // Connect
                    session.Open(sessionOptions);
                    transferOptions = new TransferOptions();
                    transferOptions.TransferMode = TransferMode.Binary;

                    //GetCitNowFilesVindis(session);
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "citnow", false, "SPKV", ".csv", null);

                    //GetBookingsVindis(session);
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "vindis", false, "export", ".xls", null);

                    //GetWinAppScraper(session);
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "vindisWinAppScraper", false, null, ".txt");

                }

                stopwatch.Stop();
                logger.Info($" SparkVindisCphiSiteFetch completed.");
            }
            catch (Exception e)
            {
                stopwatch.Stop();
                errorMessage = e.ToString();
                Console.WriteLine("Error: {0}", e);

                logger.Info($" FTPScraper Executed, encountered error.");
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "FTPScraper",
                    Customer = customerName,
                    Environment = "PROD",
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };

                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }
        }

        //private void GetCitNowFilesVindis(Session session)
        //{
        //    //Logger.Info($" GetCitNowFilesVindis.");

        //    transferOptions.FileMask = "*SPKV*";
        //    TransferOperationResult transferResult = session.GetFiles(@"/citnow/*", fileDestination, true, transferOptions);
        //    transferResult.Check();// Throw on any error

        //    foreach (TransferEventArgs transfer in transferResult.Transfers)
        //    {
        //        string newNameMainPart = transfer.FileName.Replace("/citnow/", "").Replace(".csv", "");
        //        string timePrefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";

        //        string oldFileName = Path.Combine(fileDestination, transfer.FileName.Replace("/citnow/", ""));
        //        string newFileName = Path.Combine(fileDestination, timePrefix + newNameMainPart + ".csv");


        //        File.Move(oldFileName, newFileName);
        //        Logger.Info($" GetCitNowFilesVindis - Saved File: {newFileName}");
        //    }

        //}
        //private void GetBookingsVindis(Session session)
        //{
        //    //Get export and export2
        //    transferOptions.FileMask = "export.xls, export2.xls";
        //    TransferOperationResult transferResult = session.GetFiles(@"/vindis/*", fileDestination, true, transferOptions);
        //    transferResult.Check();

        //    foreach (TransferEventArgs transfer in transferResult.Transfers)
        //    {
        //        DateTime fileDate = DateTime.Now;
        //        string newNameMainPart = transfer.FileName.Split('/')[2].Replace(".xls", "");

        //        string timePrefix = fileDate.ToString("yyyyMMdd_HHmmss") + "-";
        //        string newFileName = Path.Combine(fileDestination, timePrefix + newNameMainPart + ".xls");
        //        string oldFileName = Path.Combine(fileDestination, transfer.FileName.Replace("/vindis/", ""));

        //        File.Move(oldFileName, newFileName);
        //        Logger.Info($" GetBookingsVindis - Saved File: {newFileName}");

        //    }

        //}

        //private void GetWinAppScraper(Session session)
        //{
        //    Logger.Info($" GetWinAppScraper");
        //    //Get files scraped by WindowsAppScraper
        //    transferOptions.FileMask = ""; // Clearing out any previous file masks.
        //    TransferOperationResult transferResult = session.GetFiles(@"/vindisWinAppScraper/*", fileDestination, true, transferOptions);
        //    transferResult.Check();

        //    Logger.Info($" GetWinAppScraper | {session.HomePath}");
        //    Logger.Info($" GetWinAppScraper | {transferResult.IsSuccess}");
        //    Logger.Info($" GetWinAppScraper | {transferResult.Transfers.Count}");

        //    foreach (TransferEventArgs transfer in transferResult.Transfers)
        //    {
        //        DateTime fileDate = DateTime.Now;
        //        string newNameMainPart = transfer.FileName.Split('/')[2].Replace(".TXT", "");

        //        string timePrefix = fileDate.ToString("yyyyMMdd_HHmmss") + "-";
        //        string newFileName = Path.Combine(fileDestination, timePrefix + newNameMainPart + ".TXT");
        //        string oldFileName = Path.Combine(fileDestination, transfer.FileName.Replace("/vindisWinAppScraper/", ""));

        //        File.Move(oldFileName, newFileName);
        //        Logger.Info($" GetWinAppScraper - Saved File: {newFileName}");
        //    }

        //}


    }
}



