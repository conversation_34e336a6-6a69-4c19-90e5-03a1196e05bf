﻿using Quartz;
using System;
using System.IO;
using log4net;
using WinSCP;
using System.Threading.Tasks;
using CPHI.Spark.FTPScraper.Infrastructure;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.FTPScraper.Jobs.Spark.Vindis;

namespace CPHI.Spark.FTPScraper.Jobs.Stockpulse.Jardine
{
    [DisallowConcurrentExecution]
    public class StockpulseJardineFetch : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(StockpulseJardineFetch));
        private readonly FtpScraperSettings _settings;
        private string fileDestination;
        private string customerName;
        private TransferOptions transferOptions;
        private readonly IConfiguration _configuration;

        public StockpulseJardineFetch(IOptions<FtpScraperSettings> settings, IConfiguration config)
        {
            _settings = settings.Value;
            _configuration = config;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            logger.Info($" StockpulseJardineFetch started.");

            string errorMessage = string.Empty;

            fileDestination = _settings.JardineFileDestination;
            customerName = "Jardine";

            try
            {
                /// Setup session options
                SessionOptions sessionOptions = new SessionOptions
                {
                    Protocol = Protocol.Sftp,
                    HostName = _settings.Hostname,
                    UserName = _settings.Username,
                    Password = _settings.Password,
                };

                sessionOptions.SshHostKeyPolicy = SshHostKeyPolicy.GiveUpSecurityAndAcceptAny;

                using (Session session = new Session())
                {
                    // Connect
                    session.Open(sessionOptions);
                    transferOptions = new TransferOptions();
                    transferOptions.TransferMode = TransferMode.Binary;

                    //GetStockFile(session);
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "jardines", true, "Compare_Stock", ".csv", "STK-Jardine-Stock");

                    //GetWIPFile(session);
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "jardines", true, "Compare_WIP", ".csv", "STK-Jardine-WIP");

                    //GetTBFile(session);
                    await TransferService.GenericGetFiles(transferOptions, logger, session, fileDestination, "jardines", true, "Compare_Nominal", ".csv", "STK-Jardine-TB");

                }

                stopwatch.Stop();
                logger.Info($" StockpulseJardineFetch completed.");
            }
            catch (Exception e)
            {
                stopwatch.Stop();
                errorMessage = e.ToString();
                Console.WriteLine("Error: {0}", e);

                logger.Info($" FTPScraper Executed, encountered error.");
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "FTPScraper",
                    Customer = customerName,
                    Environment = "PROD",
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };

                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }
        }


        //private void GetStockFile(Session session)
        //{
        //    transferOptions.FileMask = "dms_stock*";
        //    TransferOperationResult transferResult = session.GetFiles(@"/jardines/*", jardineFileDestination, true, transferOptions);
        //    transferResult.Check();// Throw on any error

        //    foreach (TransferEventArgs transfer in transferResult.Transfers)
        //    {
        //        string newNameMainPart = "STK-Jardine-Stock";
        //        string timePrefix = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss") + "-";
        //        string newFileName = Path.Combine(jardineFileDestination, timePrefix + newNameMainPart + ".csv");
        //        string oldFileName = Path.Combine(jardineFileDestination, transfer.FileName.Replace("/jardines/", ""));

        //        File.Move(oldFileName, newFileName);
        //    }
        //}

        //private void GetWIPFile(Session session)
        //{
        //    transferOptions.FileMask = "reconciling*";
        //    TransferOperationResult transferResult = session.GetFiles(@"/jardines/*", jardineFileDestination, true, transferOptions);
        //    transferResult.Check();// Throw on any error

        //    foreach (TransferEventArgs transfer in transferResult.Transfers)
        //    {
        //        string newNameMainPart = "STK-Jardine-WIP";
        //        string timePrefix = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss") + "-";
        //        string newFileName = Path.Combine(jardineFileDestination, timePrefix + newNameMainPart + ".csv");
        //        string oldFileName = Path.Combine(jardineFileDestination, transfer.FileName.Replace("/jardines/", ""));

        //        File.Move(oldFileName, newFileName);
        //    }
        //}

        //private void GetTBFile(Session session)
        //{
        //    transferOptions.FileMask = "trialbalance*";
        //    TransferOperationResult transferResult = session.GetFiles(@"/jardines/*", jardineFileDestination, true, transferOptions);
        //    transferResult.Check();// Throw on any error

        //    foreach (TransferEventArgs transfer in transferResult.Transfers)
        //    {
        //        string newNameMainPart = "STK-Jardine-TB";
        //        string timePrefix = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss") + "-";
        //        string newFileName = Path.Combine(jardineFileDestination, timePrefix + newNameMainPart + ".csv");
        //        string oldFileName = Path.Combine(jardineFileDestination, transfer.FileName.Replace("/jardines/", ""));

        //        File.Move(oldFileName, newFileName);
        //    }
        //}

    }
}
