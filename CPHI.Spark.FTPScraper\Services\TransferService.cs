﻿using System;
using System.Linq;
using System.Text;
using System.IO;
using System.Threading.Tasks;
using WinSCP;
using log4net;

namespace CPHI.Spark.FTPScraper
{

    public static class TransferService
    {

        public static async Task GenericGetFiles(
           TransferOptions transferOptions,
           ILog logger,
           Session session,
           string fileDestination,
           string folderName,
           bool timeStampUtc,
           string filePattern = null,
           string suffix = null,
           string newFileName = null)
        {
            // Determine the file mask based on whether a new file name is provided or not, or if the filePattern is null
            if (filePattern == null)
            {
                transferOptions.FileMask = "*"; // Match all files if filePattern is null
            }
            else
            {
                transferOptions.FileMask = newFileName == null ? $"*{filePattern}*" : $"*{newFileName}*";
            }

            // List the directory and get the RemoteDirectoryInfo object
            RemoteDirectoryInfo directoryInfo = session.ListDirectory($"/{folderName}/");

            foreach (RemoteFileInfo file in directoryInfo.Files)
            {
                if (file.IsDirectory)
                {
                    continue; // Skip directories
                }

                // Check if filePattern is null or if the file name contains the filePattern
                if (filePattern == null || file.Name.Contains(filePattern))
                {
                    logger.Info($" GenericGetFiles ({filePattern ?? "All Files"}) - Found matching file: {file.Name}");

                    if (await WaitForFileToCompleteAsync(session, $"/{folderName}/{file.Name}"))
                    {
                        // Generate a new name for the file on the FTP server
                        string timePrefix = timeStampUtc ? DateTime.UtcNow.ToString("yyyyMMdd_HHmmss") : DateTime.Now.ToString("yyyyMMdd_HHmmss");

                        // If suffix is provided, use it; otherwise, use the original file extension
                        string fileExtension = suffix == null ? Path.GetExtension(file.Name) : suffix;

                        // Remove the leading dot (.) from the fileExtension for clean concatenation
                        fileExtension = fileExtension.StartsWith(".") ? fileExtension : "." + fileExtension;

                        // Determine the base file name 
                        // If newFileName provided use that
                        // Else use existing filename
                        string baseFileName = newFileName != null ? newFileName : Path.GetFileNameWithoutExtension(file.Name); // Remove original extension

                        // Ensure we are not appending the extension twice
                        string newRemoteFileName = $"{timePrefix}-{baseFileName}{fileExtension}";

                        string newRemoteFilePath = $"/{folderName}/{newRemoteFileName}";

                        // Rename the file on the FTP server
                        string oldRemoteFilePath = $"/{folderName}/{file.Name}";
                        session.MoveFile(oldRemoteFilePath, newRemoteFilePath);

                        // Download the renamed file
                        try
                        {
                            TransferOperationResult downloadResult = session.GetFiles(newRemoteFilePath, fileDestination, true, transferOptions);
                            downloadResult.Check(); // Throw on any error

                            // Important that file mask is here
                            foreach (TransferEventArgs transfer in downloadResult.Transfers)
                            {
                                string downloadedFilePath = Path.Combine(fileDestination, transfer.FileName.Replace($"/{folderName}/", ""));
                                logger.Info($" GenericGetFiles ({filePattern ?? "All Files"}) - Saved File: {downloadedFilePath}");
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.Error($" GenericGetFiles ({filePattern ?? "All Files"}) - Error downloading file: {ex.Message}");
                        }
                    }
                }
            }
        }



        public static async Task<bool> WaitForFileToCompleteAsync(Session session, string filePath)
        {
            const int checkInterval = 5000; // 5 seconds
            const int stabilityCheckCount = 2;
            long previousSize = 0;
            int stableCount = 0;

            while (true)
            {
                RemoteFileInfo fileInfo = session.GetFileInfo(filePath);
                long currentSize = fileInfo.Length;

                if (currentSize == previousSize)
                {
                    stableCount++;
                    if (stableCount >= stabilityCheckCount)
                    {
                        return true; // File size has been stable for required checks
                    }
                }
                else
                {
                    stableCount = 0; // Reset the stable count if the size changes
                }

                previousSize = currentSize;

                await Task.Delay(checkInterval); // Non-blocking delay
            }
        }


    }



}
