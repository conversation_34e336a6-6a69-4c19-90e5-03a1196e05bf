﻿using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using Microsoft.Exchange.WebServices.Data;
using Quartz;
using System;
using System.Diagnostics;
using System.Net;


namespace CPHI.Spark.Loader
{
   [DisallowConcurrentExecution]
   public class EmailCleanupJob : IJob
   {
      private readonly IDapper dapper = new Dapperr();
      private static readonly ILog Logger = LogManager.GetLogger(typeof(EmailCleanupJob));

      private int errorCount;
      public CPHIDbContext db;
      private LogMessage logMessage;


      public async System.Threading.Tasks.Task Execute(IJobExecutionContext context)
      {
         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

         try
         {
            var service = await EmailerService.ProvideExchangeService(ConfigService.mailAccountOutbound);

            DateTime sevenDaysAgo = DateTime.Now.AddDays(-7); // 7 days ago

            await DeleteEmails(service, sevenDaysAgo, "Spark - Today's Price Changes Generated");
            await System.Threading.Tasks.Task.Delay(5000); //5sec break
            await DeleteEmails(service, sevenDaysAgo, "Vehicle Valuation - Completed");


            stopwatch.Stop();
         }

         catch (Exception err)
         {
            stopwatch.Stop();
            errorMessage = err.ToString();
            logMessage.FailNotes = logMessage.FailNotes + $"General failure " + err.ToString();
            errorCount++;
            logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
         }
         finally
         {
            Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
            {
               Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
               Project = "Loader",
               Customer = "All",
               Environment = ConfigService.isDev == true ? "Dev" : "Prod",
               Task = GetType().Name,
               StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
               EndDate = DateTime.UtcNow,
               Status = errorMessage.Length > 0 ? "Fail" : "Pass",
               Notes = errorMessage,
               HTML = string.Empty
            };

            await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
         }

      }

      public async System.Threading.Tasks.Task DeleteEmails(ExchangeService service, DateTime tenDaysAgo, string subjectFilterText)
      {
         SearchFilter dateFilter = new SearchFilter.IsLessThan(EmailMessageSchema.DateTimeSent, tenDaysAgo);
         SearchFilter subjectFilter = new SearchFilter.ContainsSubstring(
             EmailMessageSchema.Subject,
             subjectFilterText,
             ContainmentMode.Prefixed,
             ComparisonMode.IgnoreCase);

         SearchFilter.SearchFilterCollection filterCollection =
             new SearchFilter.SearchFilterCollection(LogicalOperator.And, dateFilter, subjectFilter);

         // Use paging to process in chunks
         ItemView view = new ItemView(100);
         view.PropertySet = new PropertySet(BasePropertySet.IdOnly);

         FindItemsResults<Item> findResults;
         do
         {
            await System.Threading.Tasks.Task.Delay(2000); // Gentle pacing before next fetch

            findResults = await service.FindItems(WellKnownFolderName.SentItems, filterCollection, view);

            foreach (Item item in findResults.Items)
            {
               await item.Delete(DeleteMode.HardDelete);
            }

            view.Offset += findResults.Items.Count;

         } while (findResults.MoreAvailable);


      }


   }


}
