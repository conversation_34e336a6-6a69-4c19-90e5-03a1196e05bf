﻿using Quartz;
using System;
using System.Collections.Generic;

using System.IO;
using System.Linq;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Globalization;
using Dapper;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.Import;
using System.Diagnostics;
using System.Text;
using System.Drawing;
using static CPHI.Spark.Loader.HelpersService;

namespace CPHI.Spark.Loader
{
   [DisallowConcurrentExecution]
   public class DistrinetBooksJob : IJob
   {

      private static readonly ILog logger = LogManager.GetLogger(typeof(DistrinetBooksJob));


      HeadersAndRows headersAndRows;
      private int errorCount;
      private const int maxErrorLimit = 30;
      private const int maxDelayMinutes = 10;
      private LogMessage logMessage;
      //private readonly IDapper dapper = new Dapperr();
      private const string fileSearch = "*-DistBook.xlsx";
      bool isFirstOfMonth;
      /*
| Column                                 | Example                                             | Sensitive            |
|----------------------------------------|-----------------------------------------------------|----------------------|
| Nº chasis                              | VF1RJK00070545799                                   | No                   |
| Nº pedido                              | 054148                                              | No                   |
| Nº de orden                            | 271AZ                                               | No                   |
| Nº cuenta propietaria                  | 039000                                              | No                   |
| Avance                                 | EXPE                                                | No                   |
| Fecha de entrega cliente acordada      | 44910                                               | No                   |
| Control fecha entrega cliente          | 44965                                               | No                   |
| MADA compromiso                        | 05/2023                                             | No                   |
| Fecha prob dispo                       | 44949                                               | No                   |
| Fecha real dispo                       |                                                     | No                   |
| Cliente                                | 5ctelecomlpa. Sl                                    | YES - customer name  |
| Nombre                                 |                                                     | No                   |
| Correo electrónico                     | <EMAIL>                              | YES - customer email |
| Código vendedor                        | 02822                                               | No                   |
| Estado del Pedido                      | Pedido afectado                                     | No                   |
| Fecha de creación del pedido           | 44875                                               | No                   |
| Fecha real de afectación               | 44890                                               | No                   |
| Fecha real de anulación                |                                                     | No                   |
| Fecha real de entrega                  |                                                     | No                   |
| Denominación modelo                    | Express Furgón                                      | No                   |
| Denominación versión                   | advance 1.5 blue dci 55 kw (75cv)                   | No                   |
| Denominación opciones                  | rueda de repuesto,criterio técnico para lanzamiento | No                   |
| Denominación pintura                   | blanco glaciar                                      | No                   |
| Tapicería                              |                                                     | No                   |
| Armonía                                | armonía interior negro                              | No                   |
| Modelo                                 | DF1                                                 | No                   |
| Versión                                | F E1 AA6U S                                         | No                   |
| Opciones                               | RSEC01 TCHN0                                        | No                   |
| Color                                  | 369                                                 | No                   |
| Tapicería                              |                                                     | No                   |
| Armonía                                | HARM01                                              | No                   |
| Accesorios montados                    |                                                     | No                   |
| Flexibilidad                           | Vehiculo no modificable                             | No                   |
| Fecha fin flexibilidad naranja         | 44918                                               | No                   |
| Fecha fin flexibilidad verde           | 44918                                               | No                   |
| PGEO BCV                               | TRSP                                                | No                   |
| MADC prevista                          |                                                     | No                   |
| PGEO MADC real                         | 44942                                               | No                   |
| Fecha ARA real                         |                                                     | No                   |
| Fecha de facturación                   |                                                     | No                   |
| Fecha de salida CI                     |                                                     | No                   |
| Fecha entrada CI                       |                                                     | No                   |
| Fecha DAD                              |                                                     | No                   |
| Comentario                             |                                                     | No                   |
| Comentario vehículo                    |                                                     | No                   |
| Cuenta destinataria                    |                                                     | No                   |
| Estatus calidad                        |                                                     | No                   |
| Lista de accesorios                    |                                                     | No                   |
| N° recepción comunitaria               | e2*2007/46*0717*11                                  | No                   |
| Estado facturación                     |                                                     | No                   |
| numero de motor                        | D263031                                             | No                   |
| Tipo de DR                             | CL                                                  | No                   |
| Comentario pedido                      |                                                     | No                   |
| Nº cuenta sub-propietaria              |                                                     | No                   |
| Teléfono cliente privado               |                                                     | No                   |
| Tipo de cliente                        | Flotas de Proximidad                                | No                   |
| Nº contrato advensys                   |                                                     | No                   |
| Estado aduana                          |                                                     | No                   |
| Bloqueo financiero                     | 0                                                   | No                   |
| Bloqueo calidad                        | 0                                                   | No                   |
| Bloqueo comercial                      | 0                                                   | No                   |
| Bloqueo de transporte                  | 0                                                   | No                   |
| Motivo del bloqueo Transporte          |                                                     | No                   |
| Tipo energía                           | Diesel                                              | No                   |
| Zona de marcado                        |                                                     | No                   |
| Fecha salida vehículo del carrocero    |                                                     | No                   |
| Fecha provisional de fin del carrozaje |                                                     | No                   |
| Fecha real de fin del carrozaje        |                                                     | No                   |
| (DHS) Carrossier PV Homologation       | No                                                  | No                   |
| Comentario del carrocero               |                                                     | No                   |
| Téléphone mobile                       | 34 646519710                                        | No                   |
| Demanda de bloqueo de calidad          | 0                                                   | No                   |
| Nombre del barco                       |                                                     | No                   |
| Puerto de embarque                     |                                                     | No                   |
| (Puerto) Fecha salida real             |                                                     | No                   |
| Puerto de llegada                      |                                                     | No                   |
| (Puerto) Fecha llegada real            |                                                     | No                   |

*/


      public async Task Execute(IJobExecutionContext context)
      {
         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         isFirstOfMonth = DateTime.Now.Day == 1;

         // Check for presence of lock, if so, return as already running
         if (LocksService.DistrinetBooks) { return; }

         string[] filesToProcess = await GetFilesToProcess();

         if (filesToProcess == null)
         {
            CreateNoFilesFoundMessage();
            return;
         }

         // Create lock to prevent other instances running
         LocksService.DistrinetBooks = true;

         if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
         {
            System.Threading.Thread.Sleep(1000 * 60 * 3); //sleep to prevent concurrent load on database with production loader
         }


         try
         {
            logger.Info($@"New files found at {ConfigService.incomingRoot}.");   //update logger
                                                                                 //


            foreach (var fileToProcess in filesToProcess)
            {
               logMessage = new LogMessage();
               logMessage.DealerGroup_Id = 2;
               logMessage.SourceDate = DateTime.UtcNow;
               logMessage.Job = this.GetType().Name;


               string region = String.Empty;
               if (fileToProcess.Contains("Valencia")) { region = "Valencia"; }
               if (fileToProcess.Contains("Madrid")) { region = "Madrid"; }

               logger.Info($"Starting file {fileToProcess}");
               string newFilePath = fileToProcess.Replace(".xlsx", "-p.xlsx");
               if (File.Exists(newFilePath))
               {
                  logger.Error($@"Could not interpret {fileToProcess}, -p file already found ");
                  logMessage.FailNotes += "Processing file already found.";
               }

               File.Move(fileToProcess, newFilePath); // append _processing to the file to prevent any other instances also processing these files

               if (newFilePath.Contains("NoBook"))
               {
                  using (var db = new CPHIDbContext())
                  {
                     string fileName = Path.GetFileName(fileToProcess);// fileToProcess.Substring(53, fileMainName.Length - 53);
                     await PostFileProcessCleanup(db, newFilePath, region);
                  }
               }
               else
               {
                  //get franchise
                  string franchise = string.Empty;
                  if (newFilePath.Contains("Renault")) { franchise = "Renault"; }
                  else if (newFilePath.Contains("Dacia")) { franchise = "Dacia"; }
                  else if (newFilePath.Contains("Alpine")) { franchise = "Alpine"; }

                  List<Model.Import.Distrinet_Book> incoming = ReadRowsFromFile(newFilePath, franchise);
                  logger.Info($"Read {incoming.Count} rows");

                  using (var db = new CPHIDbContext())
                  {
                     string fileName = Path.GetFileName(fileToProcess);// fileToProcess.Substring(53, fileMainName.Length - 53);
                     TruncateTableAndLoad(db, incoming, franchise, fileName);
                     await PostFileProcessCleanup(db, newFilePath, region);
                     db.ChangeTracker.Clear();
                  }
               }


               //now trigger the cache to rebuild
               await UpdateWebAppService.Trigger($"distrinetBooksUpdateDate{region}");
               stopwatch.Stop();
            }

         }

         //Global error catcher
         catch (Exception err)
         {
            stopwatch.Stop();
            errorMessage = err.ToString();
            await GeneralFailureMsg(err);
         }
         finally
         {
            LocksService.DistrinetBooks = false;

            Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
            {
               Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
               Project = "Loader",
               Customer = "RRG Spain",
               Environment = ConfigService.isDev == true ? "Dev" : "Prod",
               Task = this.GetType().Name,
               StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
               EndDate = DateTime.UtcNow,
               Status = errorMessage.Length > 0 ? "Fail" : "Pass",
               Notes = errorMessage,
               HTML = string.Empty
            };
            await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
         }

      }

      private async Task<string[]> GetFilesToProcess()
      {
         string[] allMatchingFiles = await Task.Run(() => Directory.GetFiles(ConfigService.incomingRoot, fileSearch));// Getting files from folder
         if (allMatchingFiles.Length == 0) { return null; }// No files found.
         if (LocksService.DistrinetBooks) { CentralLoggingService.ReportLock("DistrinetBooksJob"); return null; } //return if already running

         return allMatchingFiles;
      }

      private void CreateNoFilesFoundMessage()
      {
         TimeSpan age = DateTime.UtcNow - PulsesService.DistrinetOrders;

         if (age.Minutes > maxDelayMinutes)
         {
            PulsesService.DistrinetOrders = DateTime.UtcNow;
            logger.Info($@"[{DateTime.UtcNow}] | DistrinetBooks | No files found matching pattern *-DistBook");
         }
      }

      private async Task GeneralFailureMsg(Exception err)
      {
         logMessage.FailNotes += $"General failure " + err.ToString();

         if (!isFirstOfMonth)
         {
            errorCount++;
         }

         logMessage.FailNotes = $"{errorCount} errors " + $"{Environment.NewLine}{Environment.NewLine} ------------------------ Errors ----------------------------- {Environment.NewLine}" + logMessage.FailNotes;

         await CentralLoggingService.ReportError("DistrinetBooks", logMessage);
      }

      private List<Model.Import.Distrinet_Book> ReadRowsFromFile(string newFilePath, string franchise)
      {
         headersAndRows = GetDataFromFilesService.GetExcelFileContents(newFilePath, 0, 1, null);
         var validStartRowIndex = headersAndRows.rows.FindIndex(0, headersAndRows.rows.Count, r => r[0] == "Nº chasis");

         headersAndRows = GetDataFromFilesService.GetExcelFileContents(newFilePath, validStartRowIndex + 1, validStartRowIndex + 2, null);
         Dictionary<string, int> headersLookup = DistrinetOrdersInterpretService.GetHeaderColIndexForOrdersAndBook(headersAndRows.headers);

         List<Model.Import.Distrinet_Book> results = new List<Model.Import.Distrinet_Book>();

         int incomingProcessCount = 0;

         foreach (List<string> row in headersAndRows.rows)
         {
            try
            {
               if (row.All(x => x == string.Empty)) { continue; }// skip empties

               string chassis = DistrinetOrdersInterpretService.GetString(row[headersLookup["Nº CHASIS"]]);
               if (chassis == "Confidentiel/Restricted B") { continue; } //some weird footer thing

               // If null, don't load (should be very rare) as requried field
               if (DistrinetOrdersInterpretService.GetDateNew(row[headersLookup["FECHA DE CREACIÓN DEL PEDIDO"]]) == null)
               {
                  continue;
               }

               // This gets converted to an int in the MERGE - lets confirm it is parsable as an int
               string cuentaSub = DistrinetOrdersInterpretService.GetString(row[headersLookup["Nº CUENTA SUB-PROPIETARIA"]]);

               if (!int.TryParse(cuentaSub, out int parsedCuentaSub))
               {
                  continue;
               }

               results.Add(DistrinetOrdersInterpretService.InterpretRowForBook(row, headersLookup, franchise, chassis));   //TO DO
               incomingProcessCount++;
            }

            catch (Exception err)
            {
               if (errorCount < maxErrorLimit) logMessage.FailNotes += $" failed on adding item, Item: {incomingProcessCount} {err}";

               if (!isFirstOfMonth)
               {
                  errorCount++;
               }
               ;

               continue;
            }
         }

         return results;
      }

      private void TruncateTableAndLoad(CPHIDbContext db, List<Model.Import.Distrinet_Book> incomingItems, string franchise, string loadFileName)
      {

         try
         {
            db.Database.ExecuteSqlRaw($"TRUNCATE TABLE [import].[CPHI_Distrinet_Books]");
            db.CPHI_Distrinet_Books.AddRange(incomingItems);
            db.SaveChanges();

            string region = "Madrid";
            if (loadFileName.Contains("Valencia")) { region = "valencia"; }

            DateTime monthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            db.Database.ExecuteSqlRaw($"EXEC [import].[MERGE_Distrinet_Books] '{franchise}', '{region}', '{monthStart.ToString("yyyy-M-d")}'");

            logger.Info($@"Data load complete.");

         }
         catch (Exception err)
         {
            logMessage.FailNotes += $"Failed adding new Distrinet_Books range {err}";

            if (!isFirstOfMonth)
            {
               errorCount++;
            }
         }

      }


      public async Task PostFileProcessCleanup(CPHIDbContext db, string newFilePath, string region)
      {
         try
         {

            //move file
            RemoveUnwantedData(newFilePath);
            File.Move(newFilePath, newFilePath.Replace(@"\inbound", @"\processed").Replace("-p", $"-processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}"));

            //log: errors
            if (errorCount > 0)
            {
               // We have errors so use the reporter
               logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
               await CentralLoggingService.ReportError("DistrinetBooks", logMessage);
            }

            //log: no errors
            else
            {
               // Completed succesfully, save log and remove lockfile
               logMessage.FinishDate = DateTime.UtcNow;
               logMessage.IsCompleted = true;
               logMessage.DealerGroup_Id = 2;
               db.LogMessages.Add(logMessage);


               string param = "distrinetBookUpdateDate" + region;
               //update the last update date
               GlobalParam lastUpdateDate = db.GlobalParams.First(x => x.Description == param);
               lastUpdateDate.DateFrom = DateTime.UtcNow; // 
               lastUpdateDate.TextValue = DateTime.UtcNow.ToLongDateString();
               db.SaveChanges();

            }
         }

         catch (Exception err)
         {
            if (!isFirstOfMonth)
            {
               errorCount++;
            }

            logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
            logMessage.FinishDate = DateTime.UtcNow;
            logMessage.ErrorCount = errorCount;
            logMessage.DealerGroup_Id = 2;
            db.LogMessages.Add(logMessage);
            await CentralLoggingService.ReportError("DistrinetBooks", logMessage);
            throw; //throwing so that the Finally block can log the error
         }


      }

      private void RemoveUnwantedData(string filepath)
      {
         int toDelete1 = headersAndRows.headers.IndexOf("Cliente") + 1;
         int toDelete2 = headersAndRows.headers.IndexOf("Correo electrónico") + 1;

         int[] columnIndicesToDelete = { toDelete1, toDelete2 };

         HelpersService.DeleteColumnsAndSaveXLSX(filepath, filepath, columnIndicesToDelete, 1, false);

      }


   }


}
