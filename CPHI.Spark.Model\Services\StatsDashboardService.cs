﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CPHI.Spark.Model.AutoPrice
{
    public static class StatsDashboardService
    {


        public static void TotUpDaysListed(StatsDaysListed daysListed, VehicleAdvertWithRating advert)
        {
            if (advert.DaysListed < 20)
            {
                daysListed.Under20++;
            }
            else if (advert.DaysListed < 40)
            {
                daysListed.Under40++;
            }
            else if (advert.DaysListed < 60)
            {
                daysListed.Under60++;
            }
            else
            {
                daysListed.SixtyPlus++;
            }
        }

        public static void TotUpVsStrategyPrice(StatsVsStrategyPrice vsStrategyPrice, VehicleAdvertWithRating advert, bool useTestStrategy)
        {
            string banding = useTestStrategy ? advert.VsTestStrategyBanding : advert.VsStrategyBanding;
            switch (banding)
            {
                case "VeryUnderPriced": vsStrategyPrice.VeryUnderPriced++; break;
                case "UnderPriced": vsStrategyPrice.UnderPriced++; break;
                case "OnStrategyPrice": vsStrategyPrice.OnStrategy++; break;
                case "OverPriced": vsStrategyPrice.OverPriced++; break;
                case "VeryOverPriced": vsStrategyPrice.VeryOverPriced++; break;
                default: vsStrategyPrice.NoStrategyPrice++; break;
            }

            

        }

        public static void TotUpPerformanceRating(StatsPerformanceRating performanceRating, VehicleAdvertWithRating advert)
        {
            if (advert.PerfRatingScore == 0)
            {
                performanceRating.None++;
            }
            else if (advert.PerfRatingScore <= 25)
            {
                performanceRating.Low++;
            }
            else if (advert.PerfRatingScore <= 50)
            {
                performanceRating.BelowAvg++;
            }
            else if (advert.PerfRatingScore <= 75)
            {
                performanceRating.AboveAvg++;
            }
            else
            {
                performanceRating.Excellent++;
            }
        }

        public static void TotUpRetailRating(StatsRetailRating retailRating, VehicleAdvertWithRating advert)
        {
            if (advert.RetailRating < 20)
            {
                retailRating.Below20++;
            }
            else if (advert.RetailRating < 40)
            {
                retailRating.Below40++;
            }
            else if (advert.RetailRating < 60)
            {
                retailRating.Below60++;
            }
            else if (advert.RetailRating < 80)
            {
                retailRating.Below80++;
            }
            else
            {
                retailRating.EightyPlus++;
            }
        }

    }



        

}
