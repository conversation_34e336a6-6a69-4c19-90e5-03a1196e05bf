﻿using Microsoft.Extensions.Diagnostics.HealthChecks;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class ATNewVehicleGet
   {
      public ATNewVehicleGet_Vehicle vehicle { get; set; }
      public ATNewVehicleGet_Valuations valuations { get; set; }
      public List<ATNewVehicleGet_Feature> features { get; set; }
      public ATNewVehicleGet_vehicleMetrics vehicleMetrics { get; set; }
      public ATVehicleGet_History history { get; set; }
      public List<ATVehicleGet_Mots> motTests { get; set; }

      public ATVehicleGet_Links links { get; set; }
      public string errorMessage { get; set; }

   }


}
