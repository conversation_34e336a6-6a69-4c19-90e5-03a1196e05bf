using Newtonsoft.Json;
using System;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
    public class BigMotoringWorldPriceChange
    {
        [JsonProperty("StockNumber")]
        public int StockNumber { get; set; }

        [JsonProperty("Price")]
        public int Price { get; set; }

        [JsonProperty("ChangedBy")]
        public string ChangedBy { get; set; }

        public BigMotoringWorldPriceChange()
        {
        }

        public BigMotoringWorldPriceChange(int stockNumber, int price, string changedBy)
        {
            StockNumber = stockNumber;
            Price = price;
            ChangedBy = changedBy;
        }

        // Constructor from PricingChangeMinimal
        public BigMotoringWorldPriceChange(PricingChangeMinimal priceChange, string changedBy)
        {
            // Parse StockNumber from string to int
            if (int.TryParse(priceChange.StockNumber, out int stockNum))
            {
                StockNumber = stockNum;
            }
            else
            {
                throw new ArgumentException($"Invalid StockNumber format: {priceChange.StockNumber}");
            }

            Price = priceChange.NewPrice;
            ChangedBy = changedBy;
        }
    }
}
