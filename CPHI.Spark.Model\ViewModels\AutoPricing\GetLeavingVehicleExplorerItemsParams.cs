﻿using System;
using System.Collections.Generic;

public class GetLeavingVehicleExplorerItemsParams
{
   //for orderbook
   //SiteIds, VehicleTypeTypes, OrderTypeTypes, Franchises
   public List<int> SiteIds { get; set; }
   public List<string> VehicleTypeTypes { get; set; }
   public List<string> OrderTypeTypes { get; set; }
   public List<string> Franchises { get; set; }



   //for leaving vehicle items
   public List<int> ChosenRetailerSiteIds { get; set; }
   public DateTime StartDate { get; set; }
   public DateTime EndDate { get; set; }
   public bool IncludeNewVehicles { get; set; } = false;
   public bool IncludeUsedVehicles { get; set; } = false;
   public bool IncludeLCVs { get; set; } = true;
}