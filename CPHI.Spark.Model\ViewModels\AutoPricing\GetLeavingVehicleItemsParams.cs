﻿using System;
using System.Collections.Generic;

public class GetLeavingVehicleItemsParams
{
   public GetLeavingVehicleItemsParams() { }
   public GetLeavingVehicleItemsParams(GetLeavingVehicleExplorerItemsParams parmsIn)
   {
      ChosenRetailerSiteIds = parmsIn.ChosenRetailerSiteIds;
      StartDate = parmsIn.StartDate;
      EndDate = parmsIn.EndDate;
      IncludeNewVehicles = parmsIn.IncludeNewVehicles;
      IncludeUsedVehicles = parmsIn.IncludeUsedVehicles;
      IncludeLCVs = parmsIn.IncludeLCVs;
   }
    public List<int> ChosenRetailerSiteIds { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public bool IncludeNewVehicles { get; set; } = false;
    public bool IncludeUsedVehicles { get; set; } = false;
    public bool IncludeLCVs { get; set; } = true;
}
