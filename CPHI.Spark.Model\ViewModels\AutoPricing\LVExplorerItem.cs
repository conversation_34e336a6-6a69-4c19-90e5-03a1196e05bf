﻿using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.Services;
using System;

namespace CPHI.Spark.Model.ViewModels.AutoPricing


{
   public class LVExplorerItem
   {
      public LVExplorerItem(LeavingVehicleItem leavingItem, OrderbookDetailedRow orderbookRow)
      {
         if (leavingItem != null)
         {
            FirstSnapId = leavingItem.FirstSnapId;
            LastSnapId = leavingItem.LastSnapId;
            AdvertId = leavingItem.AdvertId;
            Region = leavingItem.Region;
            RetailerSiteName = leavingItem.RetailerSiteName;
            RetailerId = leavingItem.RetailerId;
            RetailerSiteId = leavingItem.RetailerSiteId;
            BodyType = leavingItem.BodyType;
            FuelType = leavingItem.FuelType;
            TransmissionType = leavingItem.TransmissionType;
            Make = leavingItem.Make;
            Model = leavingItem.Model;
            RegYear = leavingItem.RegYear;
            VehicleReg = leavingItem.VehicleReg;
            DerivativeId = leavingItem.DerivativeId;
            Derivative = leavingItem.Derivative;
            ListedDate = leavingItem.ListedDate;
            RemovedDate = leavingItem.RemovedDate;
            FirstRegisteredDate = leavingItem.FirstRegisteredDate;
            Mileage = leavingItem.Mileage;
            FirstPrice = leavingItem.FirstPrice;
            FirstValuation = leavingItem.FirstValuation;
            LastPrice = leavingItem.LastPrice;
            LastValuation = leavingItem.LastValuation;
            LastPriceIndicator = leavingItem.LastPriceIndicator;
            LastRetailRating = leavingItem.LastRetailRating;
            FirstRetailDaysToSell = leavingItem.FirstRetailDaysToSell;
            ImageURL = leavingItem.ImageURL;
            DaysOnStrategy = leavingItem.DaysOnStrategy;
            DaysOptedOut = leavingItem.DaysOptedOut;
            RC_IsSold = leavingItem.RC_IsSold;
            RC_DaysListed = leavingItem.RC_DaysListed;
            RC_Price = leavingItem.RC_Price;
            RC_SellerName = leavingItem.RC_SellerName;
            RC_Segment = leavingItem.RC_Segment;
            RC_Valuation = leavingItem.RC_Valuation;
            RC_SoldDate = leavingItem.RC_SoldDate;
            DidBeatWholesaleTarget = leavingItem.DidBeatWholesaleTarget;
            DateOnForecourt = leavingItem.DateOnForecourt;
            CreatedInSparkDate = leavingItem.CreatedInSparkDate;
         }

         if (orderbookRow != null)
         {
            DealId = orderbookRow.DealId;
            StockNumber = orderbookRow.StockNumber;
            Reg = orderbookRow.Reg;
            OemReference = orderbookRow.OemReference;
            LastPhysicalLocation = orderbookRow.LastPhysicalLocation;
            ModelYear = orderbookRow.ModelYear;
            Description = orderbookRow.Description;
            VehicleAge = orderbookRow.VehicleAge;
            StockDate = orderbookRow.StockDate;
            RegisteredDate = orderbookRow.RegisteredDate;
            IsInvoiced = orderbookRow.IsInvoiced;
            Customer = orderbookRow.Customer;
            FinanceCo = orderbookRow.FinanceCo;
            OrderDate = orderbookRow.OrderDate;
            InvoiceDate = orderbookRow.InvoiceDate;
            IsLateCost = orderbookRow.IsLateCost;
            ActualDeliveryDate = orderbookRow.ActualDeliveryDate;
            HandoverDate = orderbookRow.HandoverDate;
            IsDelivered = orderbookRow.IsDelivered;
            Units = orderbookRow.Units;
            Sale = orderbookRow.Sale;
            CoS = orderbookRow.CoS;
            Discount = orderbookRow.Discount;
            FuelSale = orderbookRow.FuelSale;
            FuelCost = orderbookRow.FuelCost;
            OemDeliverySale = orderbookRow.OemDeliverySale;
            OemDeliveryCost = orderbookRow.OemDeliveryCost;
            AccessoriesSale = orderbookRow.AccessoriesSale;
            AccessoriesCost = orderbookRow.AccessoriesCost;
            PDICost = orderbookRow.PDICost;
            MechPrep = orderbookRow.MechPrep;
            BodyPrep = orderbookRow.BodyPrep;
            NewBonus1 = orderbookRow.NewBonus1;
            NewBonus2 = orderbookRow.NewBonus2;
            PaintProtectionAccessorySale = orderbookRow.PaintProtectionAccessorySale;
            PaintProtectionAccessoryCost = orderbookRow.PaintProtectionAccessoryCost;
            BrokerCost = orderbookRow.BrokerCost;
            IntroCommission = orderbookRow.IntroCommission;
            Error = orderbookRow.Error;
            Other = orderbookRow.Other;
            VatCost = orderbookRow.VatCost;
            PartExOverAllowance1 = orderbookRow.PartExOverAllowance1;
            FactoryBonus = orderbookRow.FactoryBonus;
            RegBonus = orderbookRow.RegBonus;
            IsFinanced = orderbookRow.IsFinanced;
            HasServicePlan = orderbookRow.HasServicePlan;
            HasTyre = orderbookRow.HasTyre;
            HasAlloy = orderbookRow.HasAlloy;
            HasTyreAlloy = orderbookRow.HasTyreAlloy;
            HasWheelGuard = orderbookRow.HasWheelGuard;
            HasCosmetic = orderbookRow.HasCosmetic;
            HasPaintProtection = orderbookRow.HasPaintProtection;
            HasGap = orderbookRow.HasGap;
            HasWarranty = orderbookRow.HasWarranty;
            TotalProductCount = orderbookRow.TotalProductCount;
            FinanceCommission = orderbookRow.FinanceCommission;
            FinanceSubsidy = orderbookRow.FinanceSubsidy;
            SelectCommission = orderbookRow.SelectCommission;
            ProPlusCommission = orderbookRow.ProPlusCommission;
            StandardsCommission = orderbookRow.StandardsCommission;
            RCIFinanceCommission = orderbookRow.RCIFinanceCommission;
            ServicePlanSale = orderbookRow.ServicePlanSale;
            ServicePlanCost = orderbookRow.ServicePlanCost;
            ServicePlanCommission = orderbookRow.ServicePlanCommission;
            TyreSale = orderbookRow.TyreSale;
            TyreCost = orderbookRow.TyreCost;
            TyreCommission = orderbookRow.TyreCommission;
            AlloySale = orderbookRow.AlloySale;
            AlloyCost = orderbookRow.AlloyCost;
            AlloyCommission = orderbookRow.AlloyCommission;
            TyreAlloySale = orderbookRow.TyreAlloySale;
            TyreAlloyCost = orderbookRow.TyreAlloyCost;
            TyreAlloyCommission = orderbookRow.TyreAlloyCommission;
            WheelGuardSale = orderbookRow.WheelGuardSale;
            WheelGuardCost = orderbookRow.WheelGuardCost;
            WheelGuardCommission = orderbookRow.WheelGuardCommission;
            CosmeticSale = orderbookRow.CosmeticSale;
            CosmeticCost = orderbookRow.CosmeticCost;
            CosmeticCommission = orderbookRow.CosmeticCommission;
            PaintProtectionSale = orderbookRow.PaintProtectionSale;
            PaintProtectionCost = orderbookRow.PaintProtectionCost;
            GapSale = orderbookRow.GapSale;
            GapCost = orderbookRow.GapCost;
            GapCommission = orderbookRow.GapCommission;
            WarrantySale = orderbookRow.WarrantySale;
            WarrantyCost = orderbookRow.WarrantyCost;
            StandardWarrantyCost = orderbookRow.StandardWarrantyCost;
            SalesmanId = orderbookRow.SalesmanId;
            SalesmanDmsId = orderbookRow.SalesmanDmsId;
            SalesmanName = orderbookRow.SalesmanName;
            SiteDescription = orderbookRow.SiteDescription;
            SiteId = orderbookRow.SiteId;
            SiteCode = orderbookRow.SiteCode;
            RegionDescription = orderbookRow.RegionDescription;
            IsCar = orderbookRow.IsCar;
            Franchise = orderbookRow.Franchise;
            OrderType = orderbookRow.OrderType;
            OrderTypeDescription = orderbookRow.OrderTypeDescription;
            VehicleType = orderbookRow.VehicleType;
            VehicleTypeCode = orderbookRow.VehicleTypeCode;
            VehicleTypeDescription = orderbookRow.VehicleTypeDescription;
            DeliverySiteDescription = orderbookRow.DeliverySiteDescription;
            VehicleSource = orderbookRow.VehicleSource;
            VehicleSale = orderbookRow.VehicleSale;
            VehicleCost = orderbookRow.VehicleCost;
            AccountingDate = orderbookRow.AccountingDate;
            EnquiryNumber = orderbookRow.EnquiryNumber;
            IsClosed = orderbookRow.IsClosed;
            FinanceType = orderbookRow.FinanceType;
            MetalProfit = orderbookRow.MetalProfit;
            OtherProfit = orderbookRow.OtherProfit;
            AddOnProfit = orderbookRow.AddOnProfit;
            FinanceProfit = orderbookRow.FinanceProfit;
            TotalProfit = orderbookRow.TotalProfit;
            LastAdvertisedPrice = orderbookRow.LastAdvertisedPrice;
            Variant = orderbookRow.Variant;
            VariantTxt = orderbookRow.VariantTxt;
            Delivered = orderbookRow.Delivered;
            VehicleClassDescription = orderbookRow.VehicleClassDescription;
            DealfileSentDate = orderbookRow.DealfileSentDate;
            Comment = orderbookRow.Comment;
         }
      }







      //----------------------------------------------
      //Props derived from LeavingVehicleItem
      //----------------------------------------------
      //create a region for the leaving item
      #region LeavingVehicleItem
      public int FirstSnapId { get; set; }
      public int LastSnapId { get; set; }
      public int AdvertId { get; set; }
      public string Region { get; set; }
      public string RetailerSiteName { get; set; }
      public int RetailerId { get; set; }
      public int RetailerSiteId { get; set; }
      public string BodyType { get; set; }
      public string FuelType { get; set; }
      public string TransmissionType { get; set; }
      public string Make { get; set; }
      public string Model { get; set; }
      public int RegYear { get; set; }
      public string VehicleReg { get; set; }
      public string DerivativeId { get; set; }
      public string Derivative { get; set; }
      public string MakeModelDerivative { get => $"{Make} {Model} {Derivative}"; }


      public DateTime ListedDate { get; set; }
      public DateTime RemovedDate { get; set; }
      public DateTime FirstRegisteredDate { get; set; }
      public int Mileage { get; set; }
      public DateTime EarlierOfDoFAndCreatedInSparkDate
      {
         get
         {
            DateTime dayBeforeCreated;

            try
            {
               dayBeforeCreated = CreatedInSparkDate.AddDays(-1);
            }
            catch
            {
               dayBeforeCreated = DateTime.Now.AddDays(-1);
            }

            if (DateOnForecourt.HasValue && DateOnForecourt.Value < dayBeforeCreated)
            {
               return DateOnForecourt.Value.Date;
            }
            else
            {
               return dayBeforeCreated.Date;
            }
         }

      }
      public int DaysListed
      {
         get
         {
            return AutoPriceHelperService.DatesSpanned(EarlierOfDoFAndCreatedInSparkDate, RemovedDate.Date) ?? 0;
         }
      }
      public int FirstPrice { get; set; }
      public int FirstValuation { get; set; }
      public int LastPrice { get; set; }
      public int LastValuation { get; set; }
      private string _LastPriceIndicator;
      public string LastPriceIndicator
      {
         get { return _LastPriceIndicator; }
         set { _LastPriceIndicator = PriceStrategyClassifierService.ProvidePriceIndicatorName(value); }
      }
      public int LastRetailRating { get; set; }
      public int FirstRetailDaysToSell { get; set; }
      public int DaysListedVsFirstRetailDaysToSell { get => DaysListed - FirstRetailDaysToSell; }
      public string ImageURL { get; set; }

      //getters
      public string DaysListedBand { get => BandingsService.Stratify(DaysListed, "Days Listed"); }
      public decimal FirstPP { get => BandingsService.CalculatePricePosition(FirstValuation, FirstPrice); }
      public string FirstPPBand { get => BandingsService.Stratify(FirstPP, "Price Position"); }
      public decimal LastPP { get => BandingsService.CalculatePricePosition(LastValuation, LastPrice); } //divide last price by FIRST valuation
      public string LastPPBand { get => BandingsService.Stratify(LastPP, "Price Position"); }

      public string RetailRatingBand { get => BandingsService.Stratify(LastRetailRating, "Retail Rating"); }
      public string MileageBand { get => BandingsService.Stratify(Mileage, "Mileage"); }
      public string LastPriceBand { get => BandingsService.Stratify(LastPrice, "Price"); }
      public string AgeBand
      {
         get
         {
            decimal age = (RemovedDate - FirstRegisteredDate).Days / 365M;
            return BandingsService.ProvideAgeBand(age);
         }
      }

      public int DaysOnStrategy { get; set; }
      public bool IsOnStrategy
      {
         get
         {
            if (DaysListed == 0)
            {
               return false;
            }
            else
            {
               return ((decimal)DaysOnStrategy / (decimal)DaysListed) > 0.8M ? true : false;
            }
         }
      }

      public int? DaysOptedOut { get; set; }

      public int PercentDaysOptedOut
      {
         get
         {
            if (DaysOptedOut == null)
            {
               return 0;
            }
            if (DaysOptedOut == 0)
            {
               return 0;
            }
            if (DaysListed == 0)
            {
               return 0;
            }
            decimal percentOptedOut = (decimal)DaysOptedOut / (decimal)DaysListed * 100M;
            return (int)Math.Round(percentOptedOut, 0, MidpointRounding.AwayFromZero);
         }
      }

      public string OptedOutPctBand
      {
         get
         {
            return BandingsService.ProvidePctOptedOutBanding(PercentDaysOptedOut);
         }
      }

      public string AchievedSaleType { get => BandingsService.CalculateAchievedSaleType(FirstPP, LastPP, DaysListed); }

      public bool? RC_IsSold { get; set; }
      public int? RC_DaysListed { get; set; }
      public int? RC_Price { get; set; }
      public decimal? RC_PPPct { get => RC_Valuation > 0 ? (decimal)RC_Price / (decimal)RC_Valuation : 0; }
      public string RC_SellerName { get; set; }
      public string RC_Segment { get; set; }
      public int? RC_Valuation { get; set; }
      public DateTime? RC_SoldDate { get; set; }
      public bool DidBeatWholesaleTarget { get; set; }

      public DateTime? DateOnForecourt { get; set; }
      public DateTime CreatedInSparkDate { get; set; }

      #endregion

      //----------------------------------------------
      //Props derived from OrderbookDetailedRow
      //----------------------------------------------

      #region OrderbookDetailedRow
      public int DealId { get; set; }
      public string StockNumber { get; set; }
      public string Reg { get; set; }
      public string OemReference { get; set; }
      public string LastPhysicalLocation { get; set; }
      public int ModelYear { get; set; }
      public string Description { get; set; }
      public int VehicleAge { get; set; }
      public DateTime? StockDate { get; set; }
      public DateTime? RegisteredDate { get; set; }
      public bool IsInvoiced { get; set; }
      //Deal Specifics
      public string Customer { get; set; }
      public string FinanceCo { get; set; }
      public DateTime OrderDate { get; set; }
      public DateTime? InvoiceDate { get; set; }
      public bool IsLateCost { get; set; }
      public DateTime? ActualDeliveryDate { get; set; }
      public DateTime? HandoverDate { get; set; }
      public bool IsDelivered { get; set; }



      //Vehicle Profit
      public int Units { get; set; }
      public decimal Sale { get; set; }
      public decimal CoS { get; set; }
      public decimal Discount { get; set; }
      public decimal FuelSale { get; set; }
      public decimal FuelCost { get; set; }
      public decimal OemDeliverySale { get; set; }
      public decimal OemDeliveryCost { get; set; }

      public decimal AccessoriesSale { get; set; }
      public decimal AccessoriesCost { get; set; }
      public decimal PDICost { get; set; }
      public decimal MechPrep { get; set; }
      public decimal BodyPrep { get; set; }
      public decimal NewBonus1 { get; set; }
      public decimal NewBonus2 { get; set; }
      public decimal PaintProtectionAccessorySale { get; set; }
      public decimal PaintProtectionAccessoryCost { get; set; }
      public decimal BrokerCost { get; set; }
      public decimal IntroCommission { get; set; }
      public decimal Error { get; set; }
      public decimal Other { get; set; }
      public decimal VatCost { get; set; }


      //PartEx Specifics
      public decimal PartExOverAllowance1 { get; set; }
      public decimal FactoryBonus { get; set; }
      public decimal RegBonus { get; set; }


      public bool IsFinanced { get; set; }
      public bool HasServicePlan { get; set; }
      public bool HasTyre { get; set; }
      public bool HasAlloy { get; set; }
      public bool HasTyreAlloy { get; set; }
      public bool? HasWheelGuard { get; set; }
      public bool HasCosmetic { get; set; }
      public bool HasPaintProtection { get; set; }
      public bool HasGap { get; set; }
      public bool HasWarranty { get; set; }
      public int TotalProductCount { get; set; }



      //F&I Profit
      public decimal FinanceCommission { get; set; }
      public decimal FinanceSubsidy { get; set; }

      public decimal SelectCommission { get; set; }
      public decimal ProPlusCommission { get; set; }
      public decimal StandardsCommission { get; set; }
      public decimal RCIFinanceCommission { get; set; }



      public decimal ServicePlanSale { get; set; }
      public decimal ServicePlanCost { get; set; }
      public decimal ServicePlanCommission { get; set; }

      public decimal TyreSale { get; set; }
      public decimal TyreCost { get; set; }
      public decimal TyreCommission { get; set; }

      public decimal AlloySale { get; set; }
      public decimal AlloyCost { get; set; }
      public decimal AlloyCommission { get; set; }


      public decimal TyreAlloySale { get; set; }
      public decimal TyreAlloyCost { get; set; }
      public decimal TyreAlloyCommission { get; set; }



      public decimal WheelGuardSale { get; set; }
      public decimal WheelGuardCost { get; set; }
      public decimal WheelGuardCommission { get; set; }


      public decimal CosmeticSale { get; set; }
      public decimal CosmeticCost { get; set; }
      public decimal CosmeticCommission { get; set; }


      public decimal PaintProtectionSale { get; set; }
      public decimal PaintProtectionCost { get; set; }

      public decimal GapSale { get; set; }
      public decimal GapCost { get; set; }
      public decimal GapCommission { get; set; }

      public decimal WarrantySale { get; set; }
      public decimal WarrantyCost { get; set; }

      public decimal StandardWarrantyCost { get; set; }

      public int SalesmanId { get; set; }
      public string SalesmanDmsId { get; set; }
      public string SalesmanName { get; set; }
      public string SiteDescription { get; set; }
      public int SiteId { get; set; }
      public int SiteCode { get; set; }

      public string RegionDescription { get; set; }
      public bool IsCar { get; set; }
      public string Franchise { get; set; }
      public string OrderType { get; set; }  //use type

      public string OrderTypeDescription { get; set; }
      public string VehicleType { get; set; } //use type
      public string VehicleTypeCode { get; set; }
      public string VehicleTypeDescription { get; set; }
      public string DeliverySiteDescription { get; set; }


      public string VehicleSource { get; set; }
      public decimal VehicleSale { get; set; }
      public decimal VehicleCost { get; set; }

      public DateTime AccountingDate { get; set; }
      public int EnquiryNumber { get; set; }

      public bool IsClosed { get; set; }
      public string FinanceType { get; set; }




      public decimal MetalProfit { get; set; }
      public decimal OtherProfit { get; set; }
      public decimal AddOnProfit { get; set; }
      public decimal FinanceProfit { get; set; }
      public decimal TotalProfit { get; set; }
      public decimal LastAdvertisedPrice { get; set; }
      public string Variant { get; set; }
      public string VariantTxt { get; set; }
      public bool Delivered { get; set; }
      public string VehicleClassDescription { get; set; }
      public string DealfileSentDate { get; set; }
      public string Comment { get; set; }

      #endregion
   }

}