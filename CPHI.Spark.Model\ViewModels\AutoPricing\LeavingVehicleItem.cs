﻿using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.Services;
using System;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class LeavingVehicleItem
   {
      // Constructor, if needed
      public LeavingVehicleItem() { }

      public int FirstSnapId { get; set; }
      public int LastSnapId { get; set; }
      public int AdvertId { get; set; }
      public string Region { get; set; }
      public string RetailerSiteName { get; set; }
      public int RetailerId { get; set; }
      public int RetailerSiteId { get; set; }
      public int SiteId { get; set; }
      public string BodyType { get; set; }
      public string FuelType { get; set; }
      public string TransmissionType { get; set; }
      public string Make { get; set; }
      public string Model { get; set; }
      public int RegYear { get; set; }
      public string VehicleReg { get; set; }
      public string DerivativeId { get; set; }
      public string Derivative { get; set; }
      public string MakeModelDerivative { get => $"{Make} {Model} {Derivative}"; }
      
      
      public DateTime ListedDate { get; set; }
      public DateTime RemovedDate { get; set; }
      public DateTime FirstRegisteredDate { get; set; }
      public int Mileage { get; set; }
      public DateTime EarlierOfDoFAndCreatedInSparkDate
      {
         get
         {
            DateTime dayBeforeCreated;

            try
            {
               dayBeforeCreated = CreatedInSparkDate.AddDays(-1);
            }
            catch
            {
               dayBeforeCreated = DateTime.Now.AddDays(-1);
            }

            if (DateOnForecourt.HasValue && DateOnForecourt.Value < dayBeforeCreated)
            {
               return DateOnForecourt.Value.Date;
            }
            else
            {
               return dayBeforeCreated.Date;
            }
         }

      }
      public int DaysListed
      {
         get
         {
            return AutoPriceHelperService.DatesSpanned(EarlierOfDoFAndCreatedInSparkDate, RemovedDate.Date) ?? 0;
         }
      }
      public int FirstPrice { get; set; }
      public int FirstValuation { get; set; }
      public int LastPrice { get; set; }
      public int LastValuation { get; set; }
      private string _LastPriceIndicator;
      public string LastPriceIndicator
      {
         get { return _LastPriceIndicator; }
         set { _LastPriceIndicator = PriceStrategyClassifierService.ProvidePriceIndicatorName(value); }
      }
      public int LastRetailRating { get; set; }
      public int FirstRetailDaysToSell { get; set; }
      public int DaysListedVsFirstRetailDaysToSell { get => DaysListed - FirstRetailDaysToSell; }
      public string ImageURL { get; set; }

      //getters
      public string DaysListedBand { get => BandingsService.Stratify(DaysListed, "Days Listed"); }
      public decimal FirstPP { get => BandingsService.CalculatePricePosition(FirstValuation, FirstPrice); }
      public string FirstPPBand { get => BandingsService.Stratify(FirstPP, "Price Position"); }
      public decimal LastPP { get => BandingsService.CalculatePricePosition(LastValuation, LastPrice); } //divide last price by FIRST valuation
      public string LastPPBand { get => BandingsService.Stratify(LastPP, "Price Position"); }

      public string RetailRatingBand { get => BandingsService.Stratify(LastRetailRating, "Retail Rating"); }
      public string MileageBand { get => BandingsService.Stratify(Mileage, "Mileage"); }
      public string LastPriceBand { get => BandingsService.Stratify(LastPrice, "Price"); }
      public string AgeBand
      {
         get
         {
            decimal age = (RemovedDate - FirstRegisteredDate).Days / 365M;
            return BandingsService.ProvideAgeBand(age);
         }
      }

      public int DaysOnStrategy { get; set; }
      public bool IsOnStrategy
      {
         get
         {
            if (DaysListed == 0)
            {
               return false;
            }
            else
            {
               return ((decimal)DaysOnStrategy / (decimal)DaysListed) > 0.8M ? true : false;
            }
         }
      }

      public int? DaysOptedOut { get; set; }

      public int PercentDaysOptedOut
      {
         get
         {
            if(DaysOptedOut == null)
            {
               return 0;
            }
            if (DaysOptedOut == 0)
            {
               return 0;
            }
            if (DaysListed == 0)
            {
               return 0;
            }
            decimal percentOptedOut = (decimal)DaysOptedOut / (decimal)DaysListed * 100M;
            return (int)Math.Round(percentOptedOut, 0, MidpointRounding.AwayFromZero);
         }
      }

      public string OptedOutPctBand
      {
         get
         {
            return BandingsService.ProvidePctOptedOutBanding(PercentDaysOptedOut);
         }
      }

      public string AchievedSaleType { get => BandingsService.CalculateAchievedSaleType(FirstPP, LastPP, DaysListed); }

      public string VehicleType { get; set; }

      //SPK5511 track RetailCustomer
      public bool? RC_IsSold { get; set; }
      public int? RC_DaysListed { get; set; }
      public int? RC_Price { get; set; }
      public decimal? RC_PPPct {get=> RC_Valuation > 0 ? (decimal)RC_Price / (decimal)RC_Valuation : 0; }
      public string RC_SellerName { get; set; }
      public string RC_Segment{ get; set; }
      public int? RC_Valuation { get; set; }
      public DateTime? RC_SoldDate { get; set; }
      public bool DidBeatWholesaleTarget { get; set; }

      public DateTime? DateOnForecourt { get; set; }
      public DateTime CreatedInSparkDate { get; set; }
   }

}
