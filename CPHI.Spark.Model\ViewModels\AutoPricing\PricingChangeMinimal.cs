﻿using System;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class PricingChangeMinimal
   {
      public PricingChangeMinimal() { }
      public PricingChangeMinimal(PricingChangeNew change, bool isAutoChange)
      {
         IsAutoChange = isAutoChange;
         StockNumber = change.StockNumber;
         VehicleReg = change.VehicleReg;
         Derivative = change.Derivative;
         Make = change.Make;
         WebsiteStockIdentifier = change.WebsiteStockIdentifier;
         WasPrice = change.WasPrice;
         NewPrice = change.NewPrice;
         RetailerSiteId = change.RetailerSiteId;
         RetailerName = change.RetailerName;
         PriceChangeId = change.PriceChangeId;
         DateConfirmed = change.DateConfirmed;
         IsOptedOutOnDay = change.IsOptedOutOnDay;
         ApprovedById = change.ApprovedById;
         ApprovedByName = change.ApprovedByName;
         AdminFee = change.AdminFee;
         ClickDealerFee = change.ClickDealerFee;

         // For Rallye screens
         Model = change.Model;
         AttentionGrabber = change.AttentionGrabber;
         FuelType = change.FuelType;
         TransmissionType = change.TransmissionType;
         FirstRegisteredDate = change.FirstRegisteredDate;
         OdometerReading = change.OdometerReading;
         VehicleType = change.VehicleType;
      }
      public string VehicleReg { get; set; }
      public string StockNumber { get; set; }
      public string Make { get; set; }
      public bool IsAutoChange { get; set; }

      public string Derivative { get; set; }
      public string WebsiteStockIdentifier { get; set; }
      public decimal? WasPrice { get; set; }
      public int NewPrice { get; set; }
      public int RetailerSiteId { get; set; }
      public string RetailerName { get; set; }
      public int PriceChangeId { get; set; }
      public DateTime? DateConfirmed { get; set; }
      public bool IsOptedOutOnDay { get; set; }
      public int? ApprovedById { get; set; }
      public string ApprovedByName { get; set; }
      public decimal AdminFee { get; set; }
      public int? ClickDealerFee { get; set; }
      public decimal TotalChangeValue { get => WasPrice == 0 ? 0 : NewPrice - (WasPrice ?? 0); }
      public decimal TotalChangePercent { get => (WasPrice ?? 0) != 0 ? (decimal)TotalChangeValue / WasPrice.Value : 0; }


      // We need these for Rallye screens
      public string Model { get; set; }
      public string AttentionGrabber { get; set; }
      public string FuelType { get; set; }
      public string TransmissionType { get; set; }
      public DateTime? FirstRegisteredDate { get; set; }
      public int? OdometerReading { get; set; }
      public string VehicleType { get; set; }
      public string Result { get; set; }
   }
}
