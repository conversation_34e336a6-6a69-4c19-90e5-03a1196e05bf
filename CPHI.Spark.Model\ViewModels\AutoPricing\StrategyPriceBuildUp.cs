﻿using System;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class StrategyPriceBuildUp
   {
      //This is sent to the client to illustrate how the strategy price has been built up, either for an advert snapshot or for a newly valued vehicle

      public StrategyPriceBuildUp() { }


      public string VersionName { get; set; }
      public string FactorName { get; set; }


      public int? FactorItemId { get; set; }
      public string FactorItemLabel { get; set; }
      public decimal FactorItemValue { get; set; }
      public decimal? FactorItemValueAmount { get; set; }

      public decimal? AppliedFactorItemValue { get; set; }
      public decimal? AppliedFactorItemValueAmount { get; set; }
      public string AppliedFactorItemLabel { get; set; }

      public string SourceValue { get; set; }
      public decimal Impact { get; set; }
      public string ExtendedNotes { get; set; }  //we will need to populate this as we go

      //public string TestSourceValue { get; set; }
      //public decimal TestImpact { get; set; }
      //public string TestExtendedNotes { get; set; }  //we will need to populate this as we go

      public bool IsRelatedToTestStrategy { get; set; }

      public string RuleSetComment { get; set; }

      public int VehicleAdvertSnapshotId { get; set; }   //should populate this also

   }

}
