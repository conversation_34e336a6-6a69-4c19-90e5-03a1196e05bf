﻿namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class ValuationCostingDTO
   {

      public ValuationCostingDTO()
      {

      }
      public ValuationCostingDTO(VehicleValuation valuation)
      {
         Sales = valuation.Sales;
         Cost = valuation.Cost;
         Valet = valuation.Valet;
         SpareKey = valuation.SpareKey;
         MOT = valuation.MOT;
         MOTAdvisory = valuation.MOTAdvisory;
         Servicing = valuation.Servicing;
         Tyres = valuation.Tyres;
         //Warranty =  valuation.Warranty;
         Parts = valuation.Parts;
         VatCost = valuation.VatCost;
         Valuation = valuation.Valuation;
      }

      public int? Sales { get; set; }
      public int? Cost { get; set; }
      public int? Valet { get; set; }
      public int? SpareKey { get; set; }
      public int? MOT { get; set; }
      public int? MOTAdvisory { get; set; }
      public int? Servicing { get; set; }
      public int? Tyres { get; set; }
      public int? Warranty { get; set; }
      public int? Parts { get; set; }
      public bool? IsVatQualifying { get; set; }
      public int? VatCost { get; set; }

      public int? Valuation { get; set; }  //? not sure


      public int? Delivery { get; set; }
      public int? Paint { get; set; }
      public int? AdditionalMech { get; set; }
      public int? Profit { get; set; }
      public int? Other { get; set; }


   }


}
