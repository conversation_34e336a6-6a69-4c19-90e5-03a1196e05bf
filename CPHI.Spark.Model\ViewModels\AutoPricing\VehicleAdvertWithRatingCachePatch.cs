﻿using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.autoPricing;
using CPHI.Spark.Model.Services;
using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Collections.Generic;
using System.Reflection.Metadata.Ecma335;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{

   public class VehicleAdvertWithRatingCachePatch
   {
      public int VehicleAdvertId { get; set; }
      public JsonPatchDocument<VehicleAdvertWithRating> Patch { get; set; }
   }
}