﻿using CPHI.Spark.Model.Services;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   //------------------------------
   // 1. The Params
   //------------------------------
   public class VehicleMetricParams  //new approach, we will receive one of these
   {
      public int vehicleAdvertSnapshotId { get; set; }
      public string advertiserId { get; set; }
      public string derivativeId { get; set; }
      public string firstRegistrationDate { get; set; }
      public int odometerReadingMiles { get; set; }
      public List<VehicleMetricLocationParams> locations { get; set; }  //it will have these in it
   }


   public class VehicleMetricAPIParam  //The params we use when we have an advertiserId for each location
   {
      public VehicleMetricAPIParam(VehicleMetricParams paramsIn)
      {
         vehicleAdvertSnapshotId = paramsIn.vehicleAdvertSnapshotId;
         advertiserId = paramsIn.advertiserId;

         vehicle = new VehicleMetricAPI_Vehicle(paramsIn.derivativeId, paramsIn.firstRegistrationDate, paramsIn.odometerReadingMiles);
         locations = paramsIn.locations.ConvertAll(x => new VehicleMetricAPI_Location(x));
      }

      public int vehicleAdvertSnapshotId { get; set; }
      public string advertiserId { get; set; }
      public VehicleMetricAPI_Vehicle vehicle { get; set; }
      public List<VehicleMetricAPI_Location> locations { get; set; }
   }

   public class VehicleMetricAPIParamBasedOnLongLat  //The params we use when instead we have long / lats for each location
   {
      public VehicleMetricAPIParamBasedOnLongLat(VehicleMetricParams paramsIn)
      {
         vehicleAdvertSnapshotId = paramsIn.vehicleAdvertSnapshotId;
         advertiserId = paramsIn.advertiserId;

         vehicle = new VehicleMetricAPI_Vehicle(paramsIn.derivativeId, paramsIn.firstRegistrationDate, paramsIn.odometerReadingMiles);
         locations = paramsIn.locations.ConvertAll(x => new VehicleMetricAPI_LocationBasedOnLongLat(x));
      }
      public int vehicleAdvertSnapshotId { get; set; }
      public string advertiserId { get; set; }
      public VehicleMetricAPI_Vehicle vehicle { get; set; }
      public List<VehicleMetricAPI_LocationBasedOnLongLat> locations { get; set; }
   }


   //The new Generic location params, containing either an advertiserId OR lat/long
   public class VehicleMetricLocationParams
   {
      public VehicleMetricLocationParams() { }
      public VehicleMetricLocationParams(RetailerSite rs, bool useLongLats)
      {
         RetailerSiteId = rs.Id;
         if (useLongLats)
         {
            latitude = decimal.Parse(rs.Site.Latitude);
            longitude = decimal.Parse(rs.Site.Longitude);
         }
         else
         {
            advertiserId = rs.RetailerId.ToString();
            //we don't set long and lat as we know the advertiserId
         }
      }
      public int RetailerSiteId { get; set; }
      public string advertiserId { get; set; }
      public decimal? latitude { get; set; }
      public decimal? longitude { get; set; }
   }



   //public class GetAdvertWithRetailRatingAtMultipleLocationsAtParams
   //{
   //    public VehicleMetricAPI_Vehicle vehicle { get; set; }
   //    public List<VehicleMetricAPI_LocationParams> locations { get; set; }
   //}

   public class GetAdvertPriceAdjustedDaysToSellAtParams
   {
      public VehicleMetricAPI_Vehicle vehicle { get; set; }
      public VehicleMetricAPI_Adverts adverts { get; set; }

      public List<ValuationAPI_Feature>? features { get; set; }
      public List<VehicleMetricAPI_Location>? locations { get; set; }
   }

   public class VehicleMetricAPI_Vehicle
   {
      public VehicleMetricAPI_Vehicle() { }
      public VehicleMetricAPI_Vehicle(string derivativeId, string firstRegistrationDate, int odometerReadingMiles)
      {
         this.derivativeId = derivativeId;
         this.firstRegistrationDate = firstRegistrationDate;
         this.odometerReadingMiles = odometerReadingMiles;
      }

      public string derivativeId { get; set; }
      public string firstRegistrationDate { get; set; }
      public int odometerReadingMiles { get; set; }

   }

   public class VehicleMetricAPI_Adverts
   {
      public VehicleMetricAPI_RetailAdverts retailAdverts { get; set; }
   }

   public class VehicleMetricAPI_RetailAdverts
   {
      public ATNewVehicleGet_Valuation price { get; set; }
   }


   //---API Response--- What we get back from the AT API
   public class VehicleMetricAPIResponse  //if we are relying on advertiserId
   {
      public VehicleMetricAPI_VehicleMetrics vehicleMetrics { get; set; }
      public string errorMessage { get; set; }
      public int vehicleAdvertSnapshotId { get; set; }
   }

   public class VehicleMetricAPIResponseBasedOnLongLat  //if we are relying on long/lat
   {
      public VehicleMetricAPI_VehicleMetricsBasedOnLongLat vehicleMetrics { get; set; }
      public string errorMessage { get; set; }
      public int vehicleAdvertSnapshotId { get; set; }
   }

   public class VehicleMetricProcessedResponse
   {
      public VehicleMetricProcessedResponse() { }

      public VehicleMetricProcessedResponse(VehicleMetricAPIResponseBasedOnLongLat response)
      {
         errorMessage = response.errorMessage;
         vehicleAdvertSnapshotId = response.vehicleAdvertSnapshotId;
         supply = response.vehicleMetrics?.retail?.supply?.value ?? 0;
         demand = response.vehicleMetrics?.retail?.demand?.value ?? 0;
         marketCondition = response.vehicleMetrics?.retail?.marketCondition?.value ?? 0;
         daysToSell = response.vehicleMetrics?.retail?.daysToSell?.value ?? 0;
         rating = response.vehicleMetrics?.retail?.rating?.value ?? 0;

         locations = response.vehicleMetrics?.retail?.locations?.ConvertAll(x => new VehicleMetricProcessedLocation(x));
      }
      public VehicleMetricProcessedResponse(VehicleMetricAPIResponse response)
      {
         errorMessage = response.errorMessage;
         vehicleAdvertSnapshotId = response.vehicleAdvertSnapshotId;
         supply = response.vehicleMetrics?.retail?.supply?.value ?? 0;
         demand = response.vehicleMetrics?.retail?.demand?.value ?? 0;
         marketCondition = response.vehicleMetrics?.retail?.marketCondition?.value ?? 0;
         daysToSell = response.vehicleMetrics?.retail?.daysToSell?.value ?? 0;
         rating = response.vehicleMetrics?.retail?.rating?.value ?? 0;

         locations = response.vehicleMetrics?.retail?.locations?.ConvertAll(x => new VehicleMetricProcessedLocation(x));
      }
      public string errorMessage { get; set; }
      public int vehicleAdvertSnapshotId { get; set; }

      public decimal? supply { get; set; }
      public decimal? demand { get; set; }
      public decimal? marketCondition { get; set; }
      public decimal? daysToSell { get; set; }
      public decimal? rating { get; set; }
      public List<VehicleMetricProcessedLocation> locations { get; set; }
   }

   public class VehicleMetricProcessedLocation
   {
      public VehicleMetricProcessedLocation(VehicleMetricAPI_Location location)
      {
         rating = (int)Math.Floor(location.rating?.value ?? 0);
         daysToSell = (int)Math.Ceiling(location.daysToSell?.value ?? 0);

         advertiserId = location.advertiserId;
      }
      public VehicleMetricProcessedLocation(VehicleMetricAPI_LocationBasedOnLongLat location)
      {
         rating = (int)Math.Floor(location.rating?.value ?? 0);
         daysToSell = (int)Math.Ceiling(location.daysToSell?.value ?? 0);

         longitude = location.longitude;
         latitude = location.latitude;
      }
      public string advertiserId { get; set; }
      public decimal? longitude { get; set; }
      public decimal? latitude { get; set; }
      public int RetailerSiteId { get; set; }
      public int rating { get; set; }
      public int daysToSell { get; set; }
   }

   public class VehicleMetricAPI_VehicleMetrics
   {
      public VehicleMetricAPI_Retail retail { get; set; }
   }

   public class VehicleMetricAPI_VehicleMetricsBasedOnLongLat
   {
      public VehicleMetricAPI_RetailBasedOnLongLat retail { get; set; }
   }

   public class VehicleMetricAPI_Retail
   {
      public VehicleMetricAPI_Value supply { get; set; }
      public VehicleMetricAPI_Value demand { get; set; }
      public VehicleMetricAPI_Value marketCondition { get; set; }
      public List<VehicleMetricAPI_Location> locations { get; set; }
      public VehicleMetricAPI_Value daysToSell { get; set; }
      public VehicleMetricAPI_Value rating { get; set; }
   }

   public class VehicleMetricAPI_RetailBasedOnLongLat
   {
      public VehicleMetricAPI_Value supply { get; set; }
      public VehicleMetricAPI_Value demand { get; set; }
      public VehicleMetricAPI_Value marketCondition { get; set; }
      public List<VehicleMetricAPI_LocationBasedOnLongLat> locations { get; set; }
      public VehicleMetricAPI_Value daysToSell { get; set; }
      public VehicleMetricAPI_Value rating { get; set; }
   }




   public class VehicleMetricAPI_Location
   {
      public VehicleMetricAPI_Location(VehicleMetricLocationParams locationParams)
      {
         advertiserId = locationParams.advertiserId;

      }
      public VehicleMetricAPI_Location() { }
      public VehicleMetricAPI_Location(RetailerSite rs)
      {
         advertiserId = rs.RetailerId.ToString();
      }


      public string advertiserId { get; set; }
      public VehicleMetricAPI_Value rating { get; set; }
      public VehicleMetricAPI_Value daysToSell { get; set; }

   }

   public class VehicleMetricAPI_LocationBasedOnLongLat
   {
      public VehicleMetricAPI_LocationBasedOnLongLat(VehicleMetricLocationParams parmsIn)
      {
         latitude = parmsIn.latitude;
         longitude = parmsIn.longitude;
      }
      public VehicleMetricAPI_LocationBasedOnLongLat() { }
      public VehicleMetricAPI_LocationBasedOnLongLat(RetailerSite retailerSite)
      {
         latitude = decimal.Parse(retailerSite.Site.Latitude);
         longitude = decimal.Parse(retailerSite.Site.Longitude);
      }

      public decimal? latitude { get; set; }
      public decimal? longitude { get; set; }
      public VehicleMetricAPI_Value rating { get; set; }
      public VehicleMetricAPI_Value daysToSell { get; set; }

   }


   public class VehicleMetricAPI_LocationParams
   {
      public string advertiserId { get; set; }

   }


   public class VehicleMetricAPI_Value
   {
      public decimal? value { get; set; }
   }

   //------------------------------------------

   public class VehicleAdvertWithRetailRating
   {

      public int VehicleAdvertSnapshotId { get; set; }
      public int RetailerIdentifier { get; set; }
      public string Derivative { get; set; }
      public DateTime? FirstRegisteredDate { get; set; }
      public int OdometerReading { get; set; }
      public decimal? RetailRating { get; set; }
      public string Make { get; set; }
      public int RetailerSitesId { get; set; }

   }


   public class VehicleLocationStrategyPriceBuild
   {
      public VehicleLocationStrategyPriceBuild(VehicleMetricProcessedLocation location, RetailerSite retailerSiteIn, string userLatitude, string userLongitude)
      {
         RetailerSite = retailerSiteIn;
         RetailRating = location.rating;// location.rating.value.HasValue ? (int)Math.Floor(location.rating.value ?? 0) : 0;
         DaysToSell = location.daysToSell;//.value.HasValue ? (int)Math.Ceiling(location.daysToSell.value ?? 0) : 0;
         BuildUpItems = new List<StrategyPriceBuildUpItem>();
         Distance = (int)Math.Round(DistanceCalculatorService.CalculateDistance(decimal.Parse(userLatitude), decimal.Parse(userLongitude), decimal.Parse(retailerSiteIn.Site.Latitude), decimal.Parse(retailerSiteIn.Site.Longitude)), 0);
      }

      //public VehicleLocationStrategyPriceBuild(VehicleMetricAPI_Location location,RetailerSite retailerSiteIn, string userGeoX, string userGeoY)
      //{
      //    RetailerSite = retailerSiteIn;
      //    RetailRating =  location.rating.value.HasValue ? (int)Math.Floor(location.rating.value ?? 0) : 0;
      //    DaysToSell = location.daysToSell.value.HasValue ? (int)Math.Ceiling(location.daysToSell.value ?? 0) : 0;
      //    BuildUpItems = new List<StrategyPriceBuildUpItem>();
      //    Distance = (int)Math.Round(DistanceCalculatorService.CalculateDistance(decimal.Parse(userGeoX), decimal.Parse(userGeoY),decimal.Parse(retailerSiteIn.Site.GeoX),decimal.Parse(retailerSiteIn.Site.GeoY)),0);
      //}

      public RetailerSite RetailerSite { get; set; }
      public int RetailRating { get; set; }
      public int DaysToSell { get; set; }
      public int StrategyPrice { get; set; } = 0;
      public int Distance { get; set; }
      public List<StrategyPriceBuildUpItem> BuildUpItems { get; set; }


      //Buying targets
      public decimal? TargetWarrantyFee { get; set; }
      public int? TargetMargin { get; set; }
      public decimal? TargetAdditionalMech { get; set; }
      public decimal? TargetPaintPrep { get; set; }
      public decimal? TargetAuctionFee { get; set; }
      public decimal? TargetDelivery { get; set; }
      public decimal? TargetOtherCost { get; set; }
   }

   public class Message
   {
      public string type { get; set; }
      public string feature { get; set; }
      public string message { get; set; }
   }

   public class VehicleMetricAPIResponseError
   {
      public List<Warning> warnings { get; set; }
      public List<Message> messages { get; set; }
   }

   public class Warning
   {
      public string type { get; set; }
      public string feature { get; set; }
      public string message { get; set; }
   }



}