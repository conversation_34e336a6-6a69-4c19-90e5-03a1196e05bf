﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class VehicleValuationWithStrategyPrice
   {
      public VehicleValuationWithStrategyPrice(VehicleValuationRatingBySite vehicleValuationRatingBySite, int strategyPrice)
      {
         var valuation = vehicleValuationRatingBySite.VehicleValuation;

         // Initialize properties from VehicleValuation
         Id = valuation.Id;
         VehicleReg = valuation.VehicleReg;
         FirstRegistered = valuation.FirstRegistered;
         Mileage = valuation.Mileage;
         Condition = valuation.Condition;
         DerivativeId = valuation.DerivativeId;

         ValuationMktAvRetail = valuation.ValuationMktAvRetail;
         ValuationMktAvTrade = valuation.ValuationMktAvTrade;
         ValuationMktAvPartEx = valuation.ValuationMktAvPartEx;
         ValuationMktAvPrivate = valuation.ValuationMktAvPrivate;

         ValuationAdjRetail = valuation.ValuationAdjRetail;
         ValuationAdjTrade = valuation.ValuationAdjTrade;
         ValuationAdjPartEx = valuation.ValuationAdjPartEx;
         ValuationAdjPrivate = valuation.ValuationAdjPrivate;
         HasBeenValued = valuation.HasBeenValued;

         OwnershipCondition = valuation.OwnershipCondition;
         Vin = valuation.Vin;
         Make = valuation.Make;
         Model = valuation.Model;
         Generation = valuation.Generation;
         Derivative = valuation.Derivative;
         VehicleType = valuation.VehicleType;
         Trim = valuation.Trim;
         BodyType = valuation.BodyType;
         FuelType = valuation.FuelType;
         TransmissionType = valuation.TransmissionType;
         Drivetrain = valuation.Drivetrain;

         Seats = valuation.Seats;
         Doors = valuation.Doors;
         Cylinders = valuation.Cylinders;
         Co2EmissionGPKM = valuation.Co2EmissionGPKM;
         TopSpeedMPH = valuation.TopSpeedMPH;
         ZeroToSixtyMPHSeconds = valuation.ZeroToSixtyMPHSeconds;
         EngineCapacityCC = valuation.EngineCapacityCC;
         EnginePowerBHP = valuation.EnginePowerBHP;

         Owners = valuation.Owners;


         Colour = valuation.Colour;

         Gears = valuation.Gears;
         StartStop = valuation.StartStop;

         BatteryRangeMiles = valuation.BatteryRangeMiles;
         BatteryCapacityKWH = valuation.BatteryCapacityKWH;

         DriveType = valuation.DriveType;

         VehicleExciseDutyWithoutSupplementGBP = valuation.VehicleExciseDutyWithoutSupplementGBP;
         sector = valuation.sector;
         SIV = valuation.SIV;
         IsVatQualifying = valuation.IsVatQualifying;
         Reference1 = valuation.Reference1;
         Reference2 = valuation.Reference2;
         Reference3 = valuation.Reference3;

         CurrentRetailPrice = valuation.CurrentRetailPrice;
         CAPValuation = valuation.CAPValuation;

         LowestPPPrice = valuation.LowestPPPrice;
         LowestPPRetailer = valuation.LowestPPRetailer;
         LowestPPVehicleReg = valuation.LowestPPVehicleReg;
         LowestPPMileage = valuation.LowestPPMileage;
         LowestPPValuation = valuation.LowestPPValuation;

         SecondLowestPPPrice = valuation.SecondLowestPPPrice;
         SecondLowestPPRetailer = valuation.SecondLowestPPRetailer;
         SecondLowestPPVehicleReg = valuation.SecondLowestPPVehicleReg;
         SecondLowestPPMileage = valuation.SecondLowestPPMileage;
         SecondLowestPPValuation = valuation.SecondLowestPPValuation;

         ThirdLowestPPPrice = valuation.ThirdLowestPPPrice;
         ThirdLowestPPRetailer = valuation.ThirdLowestPPRetailer;
         ThirdLowestPPVehicleReg = valuation.ThirdLowestPPVehicleReg;
         ThirdLowestPPMileage = valuation.ThirdLowestPPMileage;
         ThirdLowestPPValuation = valuation.ThirdLowestPPValuation;

         IsSpecKnown = valuation.IsSpecKnown;
         RetailRating = valuation.RetailRating;

         ValuationAdjTradeExVat = valuation.ValuationAdjTradeExVat;
         ValuationMktAvRetailExVat = valuation.ValuationMktAvRetailExVat;
         ValuationAdjRetailExVat = valuation.ValuationAdjRetailExVat;
         ValuationMktAvTradeExVat = valuation.ValuationMktAvTradeExVat;

         EventType = valuation.EventType;
         EventDate = valuation.EventDate;
         Location = valuation.Location;
         LotNumber = valuation.LotNumber;
         Seller = valuation.Seller;
         Link = valuation.Link;

         V5Status = valuation.V5Status;
         MotExpiry = valuation.MotExpiry;
         MileageWarranty = valuation.MileageWarranty;
         ServiceHistory = valuation.ServiceHistory;
         Services = valuation.Services;
         DateOfLastService = valuation.DateOfLastService;
         InsuranceCat = valuation.InsuranceCat;
         NumberOfKeys = valuation.NumberOfKeys;
         OnFinance = valuation.OnFinance;
         Imported = valuation.Imported;
         Notes = valuation.Notes;
         CapValue = valuation.CapValue;
         ReserveOrBuyItNow = valuation.ReserveOrBuyItNow;

         Sales = valuation.Sales;
         Valet = valuation.Valet;
         SpareKey = valuation.SpareKey;
         MOT = valuation.MOT;
         MOTAdvisory = valuation.MOTAdvisory;
         Servicing = valuation.Servicing;
         Paint = (int)Math.Round(vehicleValuationRatingBySite.TargetPaintPrep ?? 0, 0, MidpointRounding.AwayFromZero);
         Tyres = valuation.Tyres;
         Warranty = (int)Math.Round(vehicleValuationRatingBySite.Warranty ?? 0, 0, MidpointRounding.AwayFromZero); ;
         Parts = valuation.Parts;
         AdditionalMech = (int)Math.Round(vehicleValuationRatingBySite.TargetAdditionalMech ?? 0, 0, MidpointRounding.AwayFromZero);
         Fee = (int)Math.Round(vehicleValuationRatingBySite.TargetAuctionFee ?? 0, 0, MidpointRounding.AwayFromZero);
         Delivery = (int)Math.Round(vehicleValuationRatingBySite.TargetDelivery ?? 0, 0, MidpointRounding.AwayFromZero);
         Other = (int)Math.Round(vehicleValuationRatingBySite.TargetOtherCost ?? 0, 0, MidpointRounding.AwayFromZero);
         Cost = valuation.Cost;
         Profit = (int)Math.Round(vehicleValuationRatingBySite.TargetOtherCost ?? 0, 0, MidpointRounding.AwayFromZero); ;
         VatCost = valuation.VatCost;

         Valuation = valuation.Valuation;

         StrategyPrice = strategyPrice;

         Notes2 = valuation.Notes2;
         Notes3 = valuation.Notes3;
         Notes4 = valuation.Notes4;
         ModelYear = valuation.ModelYear;
         Upholstery = valuation.Upholstery;
         RetailPrice = valuation.RetailPrice;
         ServicedWithinSchedule = valuation.ServicedWithinSchedule;
         ApprovedUsed = valuation.ApprovedUsed;
         Refurbished = valuation.Refurbished;

         // Initialize properties specific to VehicleValuationWithStrategyPrice
         // (Add your specific logic here)
      }



      public int Id { get; set; }


      //Regular props
      public string VehicleReg { get; set; }
      public DateTime? FirstRegistered { get; set; }
      public int Mileage { get; set; }
      public string Condition { get; set; }
      public string DerivativeId { get; set; }


      public int ValuationMktAvRetail { get; set; }
      public int ValuationMktAvTrade { get; set; }
      public int ValuationMktAvPartEx { get; set; }
      public int ValuationMktAvPrivate { get; set; }

      public int ValuationAdjRetail { get; set; }
      public int ValuationAdjTrade { get; set; }
      public int ValuationAdjPartEx { get; set; }
      public int ValuationAdjPrivate { get; set; }
      public bool HasBeenValued { get; set; }


      //all other properties
      public string OwnershipCondition { get; set; }
      public string Vin { get; set; }
      public string Make { get; set; }
      public string Model { get; set; }
      public string Generation { get; set; }
      public string Derivative { get; set; }
      public string VehicleType { get; set; }
      public string Trim { get; set; }
      public string BodyType { get; set; }
      public string FuelType { get; set; }
      public string TransmissionType { get; set; }
      public string Drivetrain { get; set; }

      public int? Seats { get; set; }

      public int? Doors { get; set; }

      public int? Cylinders { get; set; }

      public int? EngineTorqueNM { get; set; }

      public int? Co2EmissionGPKM { get; set; }

      public int? TopSpeedMPH { get; set; }

      public decimal? ZeroToSixtyMPHSeconds { get; set; }


      public int? EngineCapacityCC { get; set; }

      public int? EnginePowerBHP { get; set; }

      public int? Owners { get; set; }





      public string Colour { get; set; }




      public int? Gears { get; set; }

      public bool? StartStop { get; set; }


      public decimal? BatteryRangeMiles { get; set; }

      public decimal? BatteryCapacityKWH { get; set; }



      public string DriveType { get; set; }

      public int? VehicleExciseDutyWithoutSupplementGBP { get; set; }
      public string sector { get; set; }
      public int? SIV { get; set; }
      public bool? IsVatQualifying { get; set; }
      public string Reference1 { get; set; }
      public string Reference2 { get; set; }
      public string Reference3 { get; set; }

      public decimal? CurrentRetailPrice { get; set; }
      public decimal? CAPValuation { get; set; }


      //Lowest competitor
      public decimal? LowestPPPrice { get; set; }
      [MaxLength(150)]
      public string LowestPPRetailer { get; set; }
      [Column(TypeName = "varchar(50)")]
      public string LowestPPVehicleReg { get; set; }
      public int? LowestPPMileage { get; set; }
      public decimal? LowestPPValuation { get; set; }


      //SecondLowestCompetitor
      public decimal? SecondLowestPPPrice { get; set; }
      [MaxLength(150)]
      public string SecondLowestPPRetailer { get; set; }
      [Column(TypeName = "varchar(50)")]
      public string SecondLowestPPVehicleReg { get; set; }
      public int? SecondLowestPPMileage { get; set; }
      public decimal? SecondLowestPPValuation { get; set; }



      //ThirdLowestCompetitor
      public decimal? ThirdLowestPPPrice { get; set; }
      [MaxLength(150)]
      public string ThirdLowestPPRetailer { get; set; }
      [Column(TypeName = "varchar(50)")]
      public string ThirdLowestPPVehicleReg { get; set; }
      public int? ThirdLowestPPMileage { get; set; }
      public decimal? ThirdLowestPPValuation { get; set; }





      public bool IsSpecKnown { get; set; }
      public int? RetailRating { get; set; }

      public int ValuationAdjTradeExVat { get; set; }
      public int ValuationMktAvRetailExVat { get; set; }
      public int ValuationAdjRetailExVat { get; set; }
      public int ValuationMktAvTradeExVat { get; set; }


      public string? EventType { get; set; }

      public string? EventDate { get; set; }

      public string? Location { get; set; }

      public string? LotNumber { get; set; }

      public string? Seller { get; set; }
      public string? Link { get; set; }

      public string? V5Status { get; set; }

      public string? MotExpiry { get; set; }
      public bool? MileageWarranty { get; set; }
      public string? ServiceHistory { get; set; }
      public int? Services { get; set; }
      public string? DateOfLastService { get; set; }
      public string? InsuranceCat { get; set; }
      public string? NumberOfKeys { get; set; }
      public bool? OnFinance { get; set; }
      public bool? Imported { get; set; }
      public string? Notes { get; set; }
      public int? CapValue { get; set; }
      public int? ReserveOrBuyItNow { get; set; }

      //Costing
      public int? Sales { get; set; }
      public int? Valet { get; set; }
      public int? SpareKey { get; set; }
      public int? MOT { get; set; }
      public int? MOTAdvisory { get; set; }
      public int? Servicing { get; set; }
      public int? Paint { get; set; }
      public int? Tyres { get; set; }
      public int? Warranty { get; set; }
      public int? Parts { get; set; }
      public int? AdditionalMech { get; set; }
      public int? Fee { get; set; }
      public int? Delivery { get; set; }
      public int? Other { get; set; }
      public int? Cost { get; set; }
      public int? Profit { get; set; }
      public int? VatCost { get; set; }

      public int? Valuation { get; set; }
      public int? StrategyPrice { get; set; }


      // New Props for Santander/VCRS
      public string? Notes2 { get; set; }
      public string? Notes3 { get; set; }
      public string? Notes4 { get; set; }
      public int? ModelYear { get; set; }
      public string? Upholstery { get; set; }
      public decimal? RetailPrice { get; set; }
      public bool? ServicedWithinSchedule { get; set; }
      public bool? ApprovedUsed { get; set; }
      public bool? Refurbished { get; set; }


   }
}
