﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace CPHI.Spark.Model.ViewModels
{
    public class OrderbookParams
    {
      public OrderbookParams() { }
      public OrderbookParams(GetLeavingVehicleExplorerItemsParams explorerParams)
      {
         OrderDateStart = new DateTime(2010, 1, 1);
         OrderDateEnd = new DateTime(3010, 1, 1);
         IncludeLateCosts = false;
         IncludeNormalCosts = true;
         IncludeOrders = true;
         IncludeInvoiced = true;
         SalesmanId = 0;
         ManagerId = 0;

         DeliveryDateType = "Delivery";

         //already have these
         DeliveryDateStart = explorerParams.StartDate;
         DeliveryDateEnd = explorerParams.EndDate;

         //other things the user can actually select - , SiteIds, VehicleTypeTypes, OrderTypeTypes, Franchises
         SiteIds = explorerParams.SiteIds.Distinct().ToList();
         VehicleTypeTypes = explorerParams.VehicleTypeTypes.Distinct().ToList();
         OrderTypeTypes = explorerParams.OrderTypeTypes.Distinct().ToList();
         Franchises = explorerParams.Franchises.Distinct().ToList();

      }
        public DateTime DeliveryDateStart { get; set; }
        public DateTime DeliveryDateEnd { get; set; }
        public string DeliveryDateType { get; set; }

        public DateTime OrderDateStart { get; set; }
        public DateTime OrderDateEnd { get; set; }


        public List<int> SiteIds { get; set; }
        public List<string> VehicleTypeTypes { get; set; }
        public List<string> OrderTypeTypes { get; set; }
        public List<string> Franchises { get; set; }
        public bool IncludeLateCosts { get; set; }
        public bool IncludeNormalCosts{ get; set; }
        public bool IncludeOrders{ get; set; }
        public bool IncludeInvoiced{ get; set; }

        public int? SalesmanId { get; set; } = 0;
        public int ManagerId { get; set; } = 0;
    }


}
