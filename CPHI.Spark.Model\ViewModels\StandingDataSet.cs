﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using StockPulse.WebApi.Model.ViewModels;
using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels
{
   public class StandingDataSet
   {
      public IEnumerable<string> FranchiseCodes { get; set; }
      public IEnumerable<SiteVM> Sites { get; set; }
      public IEnumerable<VehicleType> VehicleTypes { get; set; }
      public IEnumerable<OrderType> OrderTypes { get; set; }
      public IEnumerable<GlobalParam> GlobalParams { get; set; }
      public IEnumerable<DepartmentClientApp> Departments { get; set; }
      public IEnumerable<RetailerSite> RetailerSites { get; set; }
      public IEnumerable<OptOutReasonVM> OptOutReasons { get; set; }
      public LastUpdatedDates LastUpdatedDates { get; set; }
      public string DealerGroup { get; set; }
      public string Blobname { get; set; }
      public List<UserPreference> UserPreferences { get; set; }
      public IEnumerable<ClaimVM> UserClaims { get; set; }
      //public DateTime LatestSnapshotDate { get; set; }
      public IEnumerable<TagDTO> Tags { get; set; }


   }
}

