﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.Model.ViewModels
{
   public class TagDTO
   {
      // Parameterless constructor for inheritance and serialization
      public TagDTO()
      {
      }

      public TagDTO(Tag tag)
      {
         Id = tag.Id;
         DealerGroupId = tag.DealerGroupId;
         Label = tag.Label;
         IsActive = tag.IsActive;
         StrategyImpactPct = tag.StrategyImpactPct;
         StrategyImpactAmount = tag.StrategyImpactAmount;
         Colour = tag.Colour;

         CreatedDate = tag.CreatedDate;
         CreatedBy = new VehicleAdvertTagPersonDTO(tag.CreatedBy);

         UpdatedDate = tag.UpdatedDate;
         UpdatedBy = new VehicleAdvertTagPersonDTO(tag.UpdatedBy);
      }

      public int Id { get; set; }
      public int DealerGroupId { get; set; }
      public DealerGroupVM DealerGroup { get; set; }

      public string Label { get; set; }

      public bool IsActive { get; set; }

      public decimal StrategyImpactPct { get; set; }

      public decimal StrategyImpactAmount { get; set; }

      public string Colour { get; set; }

      public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
      public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;

      public int? CreatedById { get; set; }
      public VehicleAdvertTagPersonDTO CreatedBy { get; set; }
      public int? UpdatedById { get; set; }
      public VehicleAdvertTagPersonDTO UpdatedBy { get; set; }
   }

   public class TagSearchDTO()
   {
      public DealerGroupName DealerGroupName { get; set; }
      public bool? IsActive { get; set; }

   }
   public class CreateTagDTO : TagDTO
   {

   }
}
