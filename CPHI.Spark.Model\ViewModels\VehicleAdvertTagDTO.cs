﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.Model.ViewModels
{
   public class VehicleAdvertTagDTO
   {
      public VehicleAdvertTagDTO()
      {
         // Parameterless constructor for inheritance and serialization
      }

      public VehicleAdvertTagDTO(VehicleAdvertTag vehicleAdvertTag)
      {
         Id = vehicleAdvertTag.Id;
         TagId = vehicleAdvertTag.TagId;
         VehicleAdvertId = vehicleAdvertTag.VehicleAdvertId;
         CreatedDate = vehicleAdvertTag.CreatedDate;
         UpdatedDate = vehicleAdvertTag.UpdatedDate;
         CreatedById = vehicleAdvertTag.CreatedById;
         CreatedBy = (vehicleAdvertTag.CreatedBy != null ? new VehicleAdvertTagPersonDTO(vehicleAdvertTag.CreatedBy) : null);
         UpdatedById = vehicleAdvertTag.UpdatedById;
         UpdatedBy = (vehicleAdvertTag.UpdatedBy != null ? new VehicleAdvertTagPersonDTO(vehicleAdvertTag.UpdatedBy) : null);
      }

      public int Id { get; set; }

      public int TagId { get; set; }

      public TagDTO Tag { get; set; }

      public int VehicleAdvertId { get; set; }

      public ViewModels.VehicleAdvertDetail VehicleAdvert { get; set; }

      public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
      public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;

      //  created this tag on the vehicle advert

      public int? CreatedById { get; set; }
      public ViewModels.VehicleAdvertTagPersonDTO CreatedBy { get; set; }

      public int? UpdatedById { get; set; }
      public ViewModels.VehicleAdvertTagPersonDTO UpdatedBy { get; set; }

   }


   public class VehicleAdvertTagPersonDTO
   {
      public VehicleAdvertTagPersonDTO(Person person)
      {
         Id = person?.Id;
         Name = person?.Name;
      }
      public int? Id { get; set; }
      public string Name { get; set; }
   }

   public class VehicleAdvertTagSearchDTO
   {
      public int? Id { get; set; }
      public int? VehicleAdvertId { get; set; }
      public int? TagId { get; set; }
      public bool? IsActive { get; set; }
      public DealerGroupName? DealerGroupName { get; set; }

   }
   public class CreateVehicleAdvertTagDTO : VehicleAdvertTagDTO
   {
      public DealerGroupName DealerGroupName { get; set; }
   }

   public class ApplyTagToVehiclesDTO
   {
      public int TagId { get; set; }
      public List<int> VehicleAdvertIds { get; set; }

      public bool? UpdateCache { get; set; }
   }
   public class RemoveTagFromVehiclesDTO
   {
      public int TagId { get; set; }
      public List<int> VehicleAdvertIds { get; set; }
      public bool? UpdateCache { get; set; }
   }
}
