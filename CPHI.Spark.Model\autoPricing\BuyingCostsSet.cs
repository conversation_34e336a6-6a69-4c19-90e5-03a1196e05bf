﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model
{
   [Table("BuyingCostsSets", Schema = "autoprice")]
   public class BuyingCostsSet
   {
      public int Id { get; set; }
      public int DealerGroup_Id { get; set; }
      [ForeignKey("DealerGroup_Id")]
      public DealerGroup DealerGroup { get; set; }

      public int? RetailerSite_Id { get; set; }
      [ForeignKey("RetailerSite_Id")]
      public RetailerSite RetailerSite { get; set; }

      public BulkUploadPredefinedTemplateType? TemplateType { get; set; }
      [MaxLength(500)]
      public string? MakePattern { get; set; }
      [MaxLength(500)]
      public string? ModelPattern { get; set; }

      public int? FromMonths { get; set; }

      public decimal? ValuationUpTo { get; set; }

      [MaxLength(100)]
      public string CostType { get; set; }
      public bool IsPercent { get; set; }
      public decimal Amount { get; set; }

     
   }


   public enum BulkUploadPredefinedTemplateType
    {
        AstonBarclay,
        CarWow,
        Manheim,
        Motorway,
        Motorway2,
        Motorway3,
        Motorway4,
        Shoreham,
        Shoreham2,
        FleetAuctionGroup,
        BCA,
        Motability,
        Motability2,
        SantanderLive,
        VCRSLive,
        UCaRS,
        Other

    }
}
