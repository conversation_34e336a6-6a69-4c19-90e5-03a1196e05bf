﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model
{
   [Table("StrategyFactorItemVehicleWebsiteRatings", Schema = "autoprice")]
   public class StrategyFactorItemVehicleWebsiteRating
   {
      public StrategyFactorItemVehicleWebsiteRating() { }



      public StrategyFactorItemVehicleWebsiteRating(int? strategyFactorItemId, int vehicleAdvertSnapshotId, DateTime createdDate, string sourceValue, decimal impact, string extendedNotes, int? strategySelectionRuleSetId)
      {
         StrategyFactorItem_Id = strategyFactorItemId;
         VehicleAdvertSnapshot_Id = vehicleAdvertSnapshotId;
         CreatedDate = createdDate;
         SourceValue = sourceValue;
         SourceValueType = "string";//not sure we used this anymore
         Impact = impact;
         ExtendedNotes = extendedNotes;
         if (strategySelectionRuleSetId != null)
         {
            StrategySelectionRuleSet_Id = (int)strategySelectionRuleSetId;
         }
      }

      public StrategyFactorItemVehicleWebsiteRating(StrategyPriceBuildUpItem buildUpItem, DateTime runDate, bool isTestStrategy)
      {
         //this constructor is to convert the StrategyPriceBuildUps made by the main strategy calculator into these items to save in the db
         if (buildUpItem.StrategySelectionRuleSetId != null)
         {
            StrategySelectionRuleSet_Id = (int)buildUpItem.StrategySelectionRuleSetId;
         }
         CreatedDate = runDate;
         VehicleAdvertSnapshot_Id = buildUpItem.VehicleAdvertSnapshotId;
         SourceValue = buildUpItem.SourceValue;
         SourceValueType = "string";
         Impact = buildUpItem.Impact;
         ExtendedNotes = buildUpItem.ExtendedNotes;
         IsRelatedToTestStrategy = isTestStrategy;
         StrategyFactorItem_Id = buildUpItem.StrategyFactorItemId;
         FactorItemValue = buildUpItem.FactorItemValue;
         FactorItemValueAmount = buildUpItem.FactorItemValueAmount;
         FactorItemLabel = buildUpItem.FactorItemLabel;
      }


      public int Id { get; set; }
      //FKs

      //which rule set led us to use this factor
      public int? StrategySelectionRuleSet_Id { get; set; }
      [ForeignKey("StrategySelectionRuleSet_Id")]
      public StrategySelectionRuleSet StrategySelectionRuleSet { get; set; }

      //which factor was used to create this impact
      public int? StrategyFactorItem_Id { get; set; }
      [ForeignKey("StrategyFactorItem_Id")]
      public StrategyFactorItem StrategyFactorItem { get; set; }

      //which snapshot does this relate to
      public int VehicleAdvertSnapshot_Id { get; set; }
      [ForeignKey("VehicleAdvertSnapshot_Id")]
      public VehicleAdvertSnapshot VehicleAdvertSnapshot { get; set; }


      public DateTime CreatedDate { get; set; }
      [MaxLength(50)]
      public string SourceValue { get; set; }
      [MaxLength(50)]
      public string SourceValueType { get; set; }
      public decimal Impact { get; set; }
      [MaxLength(250)]
      public string ExtendedNotes { get; set; }

      //SPK-4776 add testStrategy
      public bool IsRelatedToTestStrategy { get; set; } //these only exist  if we have manually run a test strategy 

      public decimal? FactorItemValue { get; set; }
      public decimal? FactorItemValueAmount { get; set; }

      [MaxLength(100)]
      public string FactorItemLabel { get; set; }
   }


}
