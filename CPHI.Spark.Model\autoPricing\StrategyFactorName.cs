﻿namespace CPHI.Spark.Model
{
      public enum StrategyFactorName
      {
            RetailRatingBand,
            DaysListedBand,
            OnBrand<PERSON>heck,
            <PERSON>tailerName,
            MatchCheapestCompetitor,
            DaysToSell,
            MinimumProfit,
            RoundToNearestEnding,
            RoundUpToNearestEnding,
            RoundDownToNearestEnding,
            RoundToNearestMultiple,
            RoundUpToNearestMultiple,
            RoundDownToNearestMultiple,
            RoundToPriceBreak,
            RR_DL_Matrix,
            DaysListed,

      //SPK-4670 use days in stock
      DaysInStock,
      DaysInStockBand,
      RR_DS_Matrix,
      RR_DB_Matrix,
      DTS_DL_Matrix,

      //SPK-4793 new layer, change to DTS
      ValuationChangeUntilSell,

      MakeModel,
      MilesPerYear,

      //SPK-5168
      SpecificColour,
      Colour,
      AgeAndOwners,
      //SPK-5168

      //SPK-5200
      AchieveMarketPositionScore,

      //SPK-5387
      WholesaleAdjustment,
      RetailRating10sBand,
      MakeFuelType,
      MakeAgeBand,

      Brand,
      ModelName,
      Mileage,

      //New factor for performance rating score
      PerformanceRatingScore,

            //New factor for minimum price position
            MinimumPricePosition,
      MaximumPricePosition,

      //New factor for retail rating
      RetailRating,

      //New factor for fuel type
      FuelType,

      //New factor for value band
      ValueBand,

      //New factor for registration year
      RegYear,

      //New factor for live market condition
      LiveMarketCondition,

      //New factor for date range
      DateRange,
      //New factor for Leaving vehicle data
      LeavingData,

      //New factor for Plate Year vs Days In Stock Matrix
      PY_DS_Matrix,

      //New factor for Price Band vs Days In Stock Matrix
      VB_DS_Matrix,

      //New factor for Set To Advertised Price
      SetToAdvertisedPrice,

      TrackMarketPosition,
      DailyValuationChange,

      // Apply strategy impacts defined on the tags themselves
      ApplyTagAdjustments,

      // Apply Adjustment given a specific tag
      Tag
   }

}
