﻿

using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace CPHI.Spark.Model
{
   [Table("Tags", Schema = "autoprice")]
   public class Tag
   {
      // Parameterless constructor for Entity Framework
      public Tag()
      {
      }

      public Tag(TagDTO tagDTO)
      {
         Id = tagDTO.Id;
         DealerGroupId = tagDTO.DealerGroupId;
         Label = tagDTO.Label;
         IsActive = tagDTO.IsActive;
         StrategyImpactPct = tagDTO.StrategyImpactPct;
         StrategyImpactAmount = tagDTO.StrategyImpactAmount;
         Colour = tagDTO.Colour;
      }

      public int Id { get; set; }

      [ForeignKey("DealerGroup")]
      public int DealerGroupId { get; set; }
      public virtual DealerGroup DealerGroup { get; set; }

      [MaxLength(50)]
      public string Label { get; set; }

      [DefaultValue(true)]
      public bool IsActive { get; set; }

      [DefaultValue(100)]
      public decimal StrategyImpactPct { get; set; }

      [DefaultValue(0)]
      public decimal StrategyImpactAmount { get; set; }

      [MaxLength(15)]
      // Can be RGB Hex or a named colour
      public string Colour { get; set; }

      public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
      public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;

      //  created this tag on the vehicle advert

      public int? CreatedById { get; set; }
      [ForeignKey("CreatedById")]
      public Person CreatedBy { get; set; }

      public int? UpdatedById { get; set; }
      [ForeignKey("UpdatedById")]
      public Person UpdatedBy { get; set; }
   }
}
