﻿

using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace CPHI.Spark.Model
{
   [Table("VehicleAdverts", Schema = "autoprice")]
   public class VehicleAdvert
   {
      public VehicleAdvert() { }

      ///this is where we make a vehicle advert from a stocks item that is not on the portal
      public VehicleAdvert(VehicleValuation vehicleValuation, int retailerId, string stockNumberFull, int retailerSiteId, StockListRow stock)
      {
         StockNumber = stockNumberFull;
         RetailerIdentifier = retailerId.ToString();
         VehicleReg = stock.Reg;
         Chassis = stock.Chassis;
         FirstPrice = (int)stock.Selling;
         //WebSiteStockIdentifier
         //WebSiteSearchIdentifier
         Make = vehicleValuation.Make;
         Model = vehicleValuation.Model;
         Generation = vehicleValuation.Generation;
         Derivative = vehicleValuation.Derivative;
         VehicleType = vehicleValuation.VehicleType;
         Trim = vehicleValuation.Trim;
         BodyType = vehicleValuation.BodyType;
         FuelType = vehicleValuation.FuelType;
         TransmissionType = vehicleValuation.TransmissionType;
         BadgeEngineSizeLitres = vehicleValuation.BadgeEngineSizeLitres;
         DriveTrain = vehicleValuation.Drivetrain;
         Seats = vehicleValuation.Seats;
         Doors = vehicleValuation.Doors;
         EngineCapacityCC = vehicleValuation.EngineCapacityCC;
         EnginePowerBHP = vehicleValuation.EnginePowerBHP;
         Owners = vehicleValuation.Owners;
         FirstRegisteredDate = vehicleValuation.FirstRegistered;
         Colour = vehicleValuation.Colour;
         Gears = vehicleValuation.Gears;
         VehicleExciseDuty = vehicleValuation.VehicleExciseDutyWithoutSupplementGBP;
         Sector = vehicleValuation.sector;
         DateOnForecourt = stock.StockDate;
         RetailerSite_Id = retailerSiteId;
         AdvertiserAdvertStatus = "IN STOCK NOT ON PORTAL";
         //AttentionGrabber
         AutotraderAdvertStatus = "IN STOCK NOT ON PORTAL";
         Description = $"{vehicleValuation.Make} {vehicleValuation.Model} {vehicleValuation.Derivative}";
         //Description2
         //ExportAdvertStatus
         //LocatorAdvertStatus
         //ProfileAdvertStatus
         OwnershipCondition = "Used";
         ImageUrls = null;
         //VideoUrl
         OdometerReading = vehicleValuation.Mileage;
         DerivativeId = vehicleValuation.DerivativeId;
         CreatedInSparkDate = DateTime.Now;
         Stock_Id = stock.Id;
      }




      //when we construct from a AT listing e.g. the main morning fetch
      public VehicleAdvert(AutoTraderVehicleListing itemIn, string stockNumberFull, int retailerSiteId, DateTime incomingDate)
      {
         StockNumber = stockNumberFull;
         VehicleReg = itemIn.vehicle.registration;
         Chassis = itemIn.vehicle.vin;
         RetailerIdentifier = itemIn.advertiser.advertiserId;
         WebSiteStockIdentifier = itemIn.metadata.stockId;
         WebSiteSearchIdentifier = itemIn.metadata.searchId;

         RetailerSite_Id = retailerSiteId;

         OwnershipCondition = itemIn.vehicle.ownershipCondition;

         CompetitorLink = itemIn.links?.competitor?.href;

         //Vehicle props
         Make = itemIn.vehicle.standard?.make;
         Model = itemIn.vehicle.standard?.model;
         Generation = itemIn.vehicle.generation;
         Derivative = itemIn.vehicle.derivative;
         DerivativeId = itemIn.vehicle.derivativeId;
         VehicleType = itemIn.vehicle.vehicleType;
         Trim = itemIn.vehicle.standard?.trim;
         BodyType = itemIn.vehicle.standard?.bodyType;
         FuelType = itemIn.vehicle.standard?.fuelType;
         TransmissionType = itemIn.vehicle.standard?.transmissionType;
         DriveTrain = itemIn.vehicle.standard?.drivetrain;
         Seats = itemIn.vehicle.seats;
         BadgeEngineSizeLitres = itemIn.vehicle.badgeEngineSizeLitres != null ? (decimal)itemIn.vehicle.badgeEngineSizeLitres : null;
         Doors = itemIn.vehicle.doors;
         EngineCapacityCC = itemIn.vehicle.engineCapacityCC;
         EnginePowerBHP = itemIn.vehicle.enginePowerBHP;
         Owners = itemIn.vehicle.owners;
         OdometerReading = itemIn.vehicle.odometerReadingMiles;
         FirstRegisteredDate = itemIn.vehicle.firstRegistrationDate;
         Colour = itemIn.vehicle.standard?.colour;
         SpecificColour = itemIn.vehicle.colour;
         Gears = itemIn.vehicle.gears;
         FirstPrice = (int)(itemIn.adverts?.retailAdverts?.totalPrice?.amountGBP ?? 0);

         VehicleExciseDuty = itemIn.vehicle.vehicleExciseDutyWithoutSupplementGBP;
         Sector = itemIn.vehicle.sector;
         DateOnForecourt = itemIn.metadata?.dateOnForecourt;

         //Advert stuff
         AttentionGrabber = itemIn.adverts?.retailAdverts?.attentionGrabber;
         Description = itemIn.adverts?.retailAdverts?.description;
         Description2 = itemIn.adverts?.retailAdverts?.description2;
         AutotraderAdvertStatus = itemIn.adverts?.retailAdverts?.autotraderAdvert?.status;
         AdvertiserAdvertStatus = itemIn.adverts?.retailAdverts?.advertiserAdvert?.status;
         LocatorAdvertStatus = itemIn.adverts?.retailAdverts?.locatorAdvert?.status;
         ExportAdvertStatus = itemIn.adverts?.retailAdverts?.exportAdvert?.status;
         ProfileAdvertStatus = itemIn.adverts?.retailAdverts?.profileAdvert?.status;

         //Media stuff
         ImageUrls = string.Join('|', itemIn.media.images.Select(x => x.href));
         VideoUrl = itemIn.media.video.href;


         //Spark
         CreatedInSparkDate = incomingDate;

      }

      public int Id { get; set; }

      //FK
      public int RetailerSite_Id { get; set; }
      [ForeignKey("RetailerSite_Id")]
      public RetailerSite RetailerSite { get; set; }
      public int? Stock_Id { get; set; }
      [ForeignKey("Stock_Id")]
      public Stock Stock { get; set; }


      //Identification Props
      [MaxLength(50)]
      public string StockNumber { get; set; }
      [MaxLength(50)]
      public string RetailerIdentifier { get; set; }
      [Column(TypeName = "varchar(7)")]
      [StringLength(7, ErrorMessage = "The data cannot exceed 50 characters.")]
      public string VehicleReg { get; set; }
      [Column(TypeName = "varchar(20)")]
      [StringLength(20, ErrorMessage = "The data cannot exceed 50 characters.")]
      public string Chassis { get; set; }
      [MaxLength(75)]
      public string WebSiteStockIdentifier { get; set; }
      [MaxLength(50)]
      public string WebSiteSearchIdentifier { get; set; }

      //Vehicle Props
      [MaxLength(50)]
      public string OwnershipCondition { get; set; } //new Feb 24
      [MaxLength(50)]
      public string Make { get; set; }
      [MaxLength(150)]
      public string Model { get; set; }
      [MaxLength(50)]
      public string Generation { get; set; }
      [MaxLength(250)]
      public string Derivative { get; set; }
      [MaxLength(75)]
      public string DerivativeId { get; set; }
      [MaxLength(50)]
      public string VehicleType { get; set; }
      [MaxLength(75)]
      public string Trim { get; set; }
      [MaxLength(50)]
      public string BodyType { get; set; }
      [MaxLength(50)]
      public string FuelType { get; set; }
      [MaxLength(50)]
      public string TransmissionType { get; set; }
      [MaxLength(50)]
      public string DriveTrain { get; set; }
      public string CompetitorLink { get; set; }
      public int? Seats { get; set; }
      public int? Doors { get; set; }
      public int? EngineCapacityCC { get; set; }
      public int? EnginePowerBHP { get; set; }
      public decimal? BadgeEngineSizeLitres { get; set; }
      public int? Owners { get; set; }
      public DateTime? FirstRegisteredDate { get; set; }
      [MaxLength(150)]
      public string Colour { get; set; }
      [MaxLength(150)]
      public string SpecificColour { get; set; }
      public int? Gears { get; set; }
      public int? VehicleExciseDuty { get; set; }
      [MaxLength(50)]
      public string Sector { get; set; }

      public DateTime? DateOnForecourt { get; set; }

      //Advert things
      [MaxLength(75)]
      public string AttentionGrabber { get; set; }
      public string Description { get; set; }
      public string Description2 { get; set; }
      public int? OdometerReading { get; set; }  //this is the first ever mileage

      [MaxLength(50)]
      public string AutotraderAdvertStatus { get; set; }
      [MaxLength(50)]
      public string AdvertiserAdvertStatus { get; set; }
      [MaxLength(50)]
      public string LocatorAdvertStatus { get; set; }
      [MaxLength(50)]
      public string ExportAdvertStatus { get; set; }
      [MaxLength(50)]
      public string ProfileAdvertStatus { get; set; }

      public string ImageUrls { get; set; }
      [MaxLength(250)]
      public string VideoUrl { get; set; }

      public DateTime CreatedInSparkDate { get; set; }

      public int FirstPrice { get; set; }
      public int DailyPriceMovesCount { get; set; }
      public int MostRecentDailyPriceMove { get; set; }
      public DateTime? MostRecentDailyPriceMoveDate { get; set; }

      public int? LatestVehicleAdvertSnapshotId { get; set; }
      [ForeignKey(nameof(LatestVehicleAdvertSnapshotId))]
      public VehicleAdvertSnapshot LatestVehicleAdvertSnapshot { get; set; }

      public int? FirstListedSnapshotId { get; set; }
      [ForeignKey(nameof(FirstListedSnapshotId))]
      public VehicleAdvertSnapshot FirstListedSnapshot { get; set; }

      public int? LastListedSnapshotId { get; set; }
      [ForeignKey(nameof(LastListedSnapshotId))]
      public VehicleAdvertSnapshot LastListedSnapshot { get; set; }

      [DefaultValue(false)]
      public bool HasLeft { get; set; }

      /* From Leaving Vehicle */

      public int? FirstVehicleAdvertSnapshotId { get; set; }
      [ForeignKey("FirstVehicleAdvertSnapshotId")]
      public VehicleAdvertSnapshot FirstVehicleAdvertSnapshot { get; set; }

      public bool? HaveSiv { get; set; }
      public decimal? Siv { get; set; }
      public decimal? Profit { get; set; }

      public int? DaysOnStrategy { get; set; }
      public int? DaysOptedOut { get; set; }

      public List<VehicleAdvertTag> VehicleAdvertTags { get; set; }
   }
}
