﻿using Microsoft.AspNetCore.Identity;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model
{
   [Table("VehicleAdvertTags", Schema = "autoprice")]
   public class VehicleAdvertTag
   {
      public int Id { get; set; }

      [ForeignKey("Tag")]
      public int TagId { get; set; }

      public virtual Tag Tag { get; set; }

      [ForeignKey("VehicleAdvert")]
      public int VehicleAdvertId { get; set; }

      public virtual VehicleAdvert VehicleAdvert { get; set; }

      public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
      public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;

      //  created this tag on the vehicle advert

      public int? CreatedById { get; set; }
      [ForeignKey("CreatedById")]
      public Person CreatedBy { get; set; }

      public int? UpdatedById { get; set; }
      [ForeignKey("UpdatedById")]
      public Person UpdatedBy { get; set; }
   }
}
