﻿using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Microsoft.AspNetCore.Mvc.Diagnostics;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Net.NetworkInformation;
using System.Numerics;

namespace CPHI.Spark.Model
{
   [Table("VehicleValuations", Schema = "autoprice")]
   public class VehicleValuation
   {
      public VehicleValuation() { }
      public VehicleValuation(VehiclesForValuationParams valuation)
      {
         VehicleReg = valuation.VehicleReg;
         Mileage = valuation.Mileage.Value;
         Condition = valuation.Condition.ToString();
         SIV = valuation.Siv;
         Reference1 = valuation.Reference1;
         Reference2 = valuation.Reference2;
         Reference3 = valuation.Reference3;
         IsVatQualifying = valuation.IsVatQualifying;
         CurrentRetailPrice = valuation.CurrentPrice;
         CAPValuation = valuation.CAPClean;

         // New stuff
         EventType = valuation.EventType;
         EventDate = valuation.EventDate;
         Location = valuation.Location;
         LotNumber = valuation.LotNumber;
         Seller = valuation.Seller;
         Link = valuation.Link;
         V5Status = valuation.V5Status;
         MotExpiry = valuation.MotExpiry;
         MileageWarranty = valuation.MileageWarranty;
         ServiceHistory = valuation.ServiceHistory;
         Services = valuation.Services;
         DateOfLastService = valuation.DateOfLastService;
         InsuranceCat = valuation.InsuranceCat;
         NumberOfKeys = valuation.NumberOfKeys;
         OnFinance = valuation.OnFinance;
         Imported = valuation.Imported;
         Notes = valuation.Notes;
         CapValue = valuation.CapValue;
         ReserveOrBuyItNow = valuation.ReserveOrBuyItNow;
         CapNew = valuation.CapNew;
         CapId = valuation.CapId;
         CapRetail = valuation.CapRetail;
         CapAverage = valuation.CapAverage;
         CapBelow = valuation.CapBelow;

         Notes2 = valuation.Notes2;
         Notes3 = valuation.Notes3;
         Notes4 = valuation.Notes4;
         ModelYear = valuation.ModelYear;
         Upholstery = valuation.Upholstery;
         RetailPrice = valuation.RetailPrice;
         ServicedWithinSchedule = valuation.ServicedWithinSchedule;
         ApprovedUsed = valuation.ApprovedUsed;
         Refurbished = valuation.Refurbished;

         // New Feb 2025
         Co2EmissionGPKM = valuation.Co2;
         TransmissionType = valuation.Transmission;
         FuelType = valuation.FuelType;
         Make = valuation.Make;
         Model = valuation.Model;
         Vin = valuation.Vin;
         BodyType = valuation.BodyType;
         FirstRegistered = valuation.RegistrationDate;
         Colour = valuation.SpecificColour;
         SpecificColour = valuation.SpecificColour;

         Engine = valuation.Engine;
         PreviousUse = valuation.PreviousUse;
      }

      public VehicleValuation(ValuationResultForNewVehicleToSave valuation, int batchId)
      {
         VehicleValuationBatch_Id = batchId;

         VehicleReg = valuation.VehicleReg;
         FirstRegistered = valuation.FirstRegistered;
         Mileage = valuation.Mileage;
         Condition = valuation.Condition;
         DerivativeId = valuation.DerivativeId;
         RetailRating = valuation.RetailRating;

         ValuationAdjPrivate = valuation.Valuation?.PrivateThisVehicle.Value ?? 0;
         ValuationAdjPartEx = valuation.Valuation?.PartExThisVehicle.Value ?? 0;
         ValuationAdjTrade = valuation.Valuation?.TradeThisVehicle.Value ?? 0;
         ValuationAdjRetail = valuation.Valuation?.RetailThisVehicle.Value ?? 0;

         ValuationMktAvPrivate = valuation.Valuation?.PrivateAverageSpec.Value ?? 0;
         ValuationMktAvPartEx = valuation.Valuation?.PartExAverageSpec.Value ?? 0;
         ValuationMktAvTrade = valuation.Valuation?.TradeAverageSpec.Value ?? 0;
         ValuationMktAvRetail = valuation.Valuation?.RetailAverageSpec.Value ?? 0;

         //Costing
         Sales = valuation.Costing.Sales;
         Cost = valuation.Costing.Cost;
         Valet = valuation.Costing.Valet;
         SpareKey = valuation.Costing.SpareKey;
         MOT = valuation.Costing.MOT;
         MOTAdvisory = valuation.Costing.MOTAdvisory;
         Servicing = valuation.Costing.Servicing;
         Tyres = valuation.Costing.Tyres;
         Parts = valuation.Costing.Parts;
         IsVatQualifying = valuation.Costing.IsVatQualifying;
         VatCost = valuation.Costing.VatCost;
         Valuation = valuation.Costing.Valuation;
         Notes = valuation.Notes;

      }

      public int Id { get; set; }
      //FKs
      public int VehicleValuationBatch_Id { get; set; }
      [ForeignKey("VehicleValuationBatch_Id")]
      public VehicleValuationBatch VehicleValuationBatch { get; set; }

      //Regular props
      [MaxLength(50)]
      public string VehicleReg { get; set; }
      public DateTime? FirstRegistered { get; set; }
      public int Mileage { get; set; }
      [MaxLength(50)]
      public string Condition { get; set; }
      [MaxLength(75)]
      public string DerivativeId { get; set; }


      public int ValuationMktAvRetail { get; set; }  
      public int ValuationMktAvRetailExVat { get; set; }  
      public int ValuationMktAvTrade { get; set; }  
      public int ValuationMktAvTradeExVat { get; set; } 
      public int ValuationMktAvPartEx { get; set; } 
      public int ValuationMktAvPrivate { get; set; }  

      public int ValuationAdjRetail { get; set; } 
      public int ValuationAdjRetailExVat { get; set; }  
      public int ValuationAdjTrade { get; set; }  
      public int ValuationAdjTradeExVat { get; set; }  
      public int ValuationAdjPartEx { get; set; } 
      public int ValuationAdjPrivate { get; set; } 



      public bool HasBeenValued { get; set; }


      //all other properties
      [MaxLength(50)]
      public string OwnershipCondition { get; set; }
      [MaxLength(50)]
      public string Vin { get; set; }
      [MaxLength(50)]
      public string Make { get; set; }
      [MaxLength(50)]
      public string Model { get; set; }
      [MaxLength(75)]
      public string Generation { get; set; }
      [MaxLength(150)]
      public string Derivative { get; set; }
      [MaxLength(50)]
      public string VehicleType { get; set; }
      [MaxLength(50)]
      public string Trim { get; set; }
      [MaxLength(50)]
      public string BodyType { get; set; }
      [MaxLength(50)]
      public string FuelType { get; set; }
      [MaxLength(50)]
      public string TransmissionType { get; set; }
      [MaxLength(50)]
      public string Drivetrain { get; set; }
      public string CompetitorLink { get; set; }

      public int? Seats { get; set; }

      public int? Doors { get; set; }

      public int? Cylinders { get; set; }

      public int? Co2EmissionGPKM { get; set; }

      public int? TopSpeedMPH { get; set; }

      public decimal? ZeroToSixtyMPHSeconds { get; set; }

      public int? EngineCapacityCC { get; set; }

      public int? EnginePowerBHP { get; set; }
      public decimal? BadgeEngineSizeLitres { get; set; }

      public int? Owners { get; set; }


      [MaxLength(50)]
      public string Colour { get; set; }

      [MaxLength(150)]
      public string SpecificColour { get; set; }

      public int? Gears { get; set; }

      public bool? StartStop { get; set; }


      public decimal? BatteryRangeMiles { get; set; }

      public decimal? BatteryCapacityKWH { get; set; }

      [MaxLength(50)]
      public string DriveType { get; set; }


      public int? VehicleExciseDutyWithoutSupplementGBP { get; set; }
      [MaxLength(50)]
      public string sector { get; set; }
      public int? SIV { get; set; }
      public bool? IsVatQualifying { get; set; }
      [Column(TypeName = "varchar(250)")]
      public string Reference1 { get; set; }
      [Column(TypeName = "varchar(250)")]
      public string Reference2 { get; set; }
      [Column(TypeName = "varchar(250)")]
      public string Reference3 { get; set; }

      public decimal? CurrentRetailPrice { get; set; }
      public decimal? CAPValuation { get; set; }


      //Lowest competitor
      public decimal? LowestPPPrice { get; set; }
      [MaxLength(150)]
      public string LowestPPRetailer { get; set; }
      [Column(TypeName = "varchar(50)")]
      public string LowestPPVehicleReg { get; set; }
      public int? LowestPPMileage { get; set; }
      public decimal? LowestPPValuation { get; set; }


      //SecondLowestCompetitor
      public decimal? SecondLowestPPPrice { get; set; }
      [MaxLength(150)]
      public string SecondLowestPPRetailer { get; set; }
      [Column(TypeName = "varchar(50)")]
      public string SecondLowestPPVehicleReg { get; set; }
      public int? SecondLowestPPMileage { get; set; }
      public decimal? SecondLowestPPValuation { get; set; }



      //ThirdLowestCompetitor
      public decimal? ThirdLowestPPPrice { get; set; }
      [MaxLength(150)]
      public string ThirdLowestPPRetailer { get; set; }
      [Column(TypeName = "varchar(50)")]
      public string ThirdLowestPPVehicleReg { get; set; }
      public int? ThirdLowestPPMileage { get; set; }
      public decimal? ThirdLowestPPValuation { get; set; }





      public bool IsSpecKnown { get; set; }
      public int? RetailRating { get; set; }



      [MaxLength(50)]
      public string? EventType { get; set; }
      [MaxLength(50)]
      public string? EventDate { get; set; }
      [MaxLength(50)]
      public string? Location { get; set; }
      [MaxLength(50)]
      public string? LotNumber { get; set; }
      [MaxLength(50)]
      public string? Seller { get; set; }
      [MaxLength(150)]
      public string? Link { get; set; }
      [MaxLength(50)]
      public string? V5Status { get; set; }
      [MaxLength(50)]
      public string? MotExpiry { get; set; }
      public bool? MileageWarranty { get; set; }
      [MaxLength(250)]
      public string? ServiceHistory { get; set; }
      public int? Services { get; set; }
      [MaxLength(50)]
      public string? DateOfLastService { get; set; }
      [MaxLength(50)]
      public string? InsuranceCat { get; set; }
      [MaxLength(50)]
      public string? NumberOfKeys { get; set; }
      public bool? OnFinance { get; set; }
      public bool? Imported { get; set; }
      public string? Notes { get; set; }
      public int? CapValue { get; set; }
      public int? ReserveOrBuyItNow { get; set; }

      //Costing
      public int? Sales { get; set; }
      public int? Valet { get; set; }
      public int? SpareKey { get; set; }
      public int? MOT { get; set; }
      public int? MOTAdvisory { get; set; }
      public int? Servicing { get; set; }
      public int? Tyres { get; set; }
      public int? Parts { get; set; }
      public int? Cost { get; set; }

      public int? VatCost { get; set; }



      public int? Valuation { get; set; }

      public decimal? CapId { get; set; }

      public decimal? CapNew { get; set; }
      public decimal? CapRetail { get; set; }
      public decimal? CapAverage { get; set; }
      public decimal? CapBelow { get; set; }

      [MaxLength(11)]
      public string RecallStatus { get; set; }


      public int CompetitorCount { get; set; }
      public string CompetitorPricePositions { get; set; }

      // New Props for Santander/VCRS
      [MaxLength(250)]
      public string? Notes2 { get; set; }
      [MaxLength(250)]
      public string? Notes3 { get; set; }
      [MaxLength(250)]
      public string? Notes4 { get; set; }
      public int? ModelYear { get; set; }
      public string? Upholstery { get; set; }
      public decimal? RetailPrice { get; set; }
      public bool? ServicedWithinSchedule { get; set; }
      public bool? ApprovedUsed { get; set; }
      public bool? Refurbished { get; set; }


      public string? Engine { get; set; }
      public string PreviousUse { get; set; }

      // Added July 2025
      public string MOTHistory { get; set; }

      [MaxLength(1000)]
      public string ErrorMessage { get; set; }












      public void TruncateStringPropsAsRequired()
      {

         Dictionary<string, int> fieldMaxLengths = new Dictionary<string, int>
{
    { "VehicleReg", 50 },
    { "Condition", 50 },
    { "DerivativeId", 75 },
    { "BodyType", 50 },
    { "Colour", 50 },
    { "Derivative", 150 },
    { "DriveType", 50 },
    { "Drivetrain", 50 },
    { "FuelType", 50 },
    { "Generation", 75 },
    { "Make", 50 },
    { "Model", 50 },
    { "OwnershipCondition", 50 },
    { "TransmissionType", 50 },
    { "Trim", 50 },
    { "VehicleType", 50 },
    { "Vin", 50 },
    { "sector", 50 },
    { "Reference1", 250 },
    { "Reference2", 250 },
    { "Reference3", 250 },
    { "LowestPPVehicleReg", 50 },
    { "LowestPPRetailer", 150 },
    { "DateOfLastService", 50 },
    { "EventDate", 50 },
    { "EventType", 50 },
    { "InsuranceCat", 50 },
    { "Link", 150 },
    { "Location", 50 },
    { "LotNumber", 20 },
    { "MotExpiry", 50 },
    { "NumberOfKeys", 50 },
    { "Seller", 50 },
    { "ServiceHistory", 250 },
    { "V5Status", 50 },
    { "SecondLowestPPRetailer", 150 },
    { "SecondLowestPPVehicleReg", 50 },
    { "ThirdLowestPPRetailer", 150 },
    { "ThirdLowestPPVehicleReg", 50 },
    { "RecallStatus", 11 },
    { "CompetitorPricePositions", 2000 }
};


         string truncationWarning = string.Empty;
         foreach (var field in fieldMaxLengths)
         {
            var propertyInfo = this.GetType().GetProperty(field.Key);
            if (propertyInfo != null && propertyInfo.PropertyType == typeof(string))
            {
               var currentValue = propertyInfo.GetValue(this) as string;

               if (currentValue != null && currentValue.Length > field.Value)
               {
                  // Log the old and truncated values
                  string truncatedValue = currentValue.Substring(0, field.Value);

                  // Set the new truncated value
                  propertyInfo.SetValue(this, truncatedValue);
                  string thisPropError = $"Truncated '{field.Key}' from '{currentValue}' to '{truncatedValue}'";
                  truncationWarning += thisPropError;
               }
            }
         }
         if (truncationWarning.Length > 0)
         {
            throw new ArgumentOutOfRangeException(truncationWarning);
         }
      }

   }

}
