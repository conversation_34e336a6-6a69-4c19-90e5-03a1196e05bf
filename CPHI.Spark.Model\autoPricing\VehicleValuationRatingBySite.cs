﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model
{
   [Table("VehicleValuationRatingBySites", Schema = "autoprice")]
   public class VehicleValuationRatingBySite
   {
      public VehicleValuationRatingBySite()
      {

      }

      public VehicleValuationRatingBySite(int vehicleValuationId, LocationAndStrategyPrice locationAndStrategyPrice, 
         ValuationCostingDTO valCosting)
      {
         VehicleValuation_Id = vehicleValuationId;
         RetailerSite_Id = locationAndStrategyPrice.RetailerSiteId;
         RetailRating = locationAndStrategyPrice.RetailRating;
         StrategyPrice = locationAndStrategyPrice.StrategyPrice;
         PriceScenario = 1;

         // Fix issue with margins not saving
         // TargetDelivery = ?? -- There is no frontend property which matches this?
         TargetDelivery = valCosting.Delivery;
         TargetMargin = valCosting.Profit;
         TargetAdditionalMech = valCosting.AdditionalMech;
         TargetPaintPrep = valCosting.Paint;
         TargetOtherCost = valCosting.Other;
         Warranty = valCosting.Warranty;
      }

      public VehicleValuationRatingBySite(int vehicleValuationId, int retailerSiteId, decimal? retailRating, decimal? retailDaysToSell, decimal strategyPrice, decimal priceScenario)
      {
         VehicleValuation_Id = vehicleValuationId;
         RetailerSite_Id = retailerSiteId;
         RetailRating = retailRating.HasValue ? retailRating.Value : 0;
         StrategyPrice = strategyPrice;
         DaysToSellAtCurrentSelling = retailDaysToSell.HasValue ? retailDaysToSell.Value : 0;
         PriceScenario = priceScenario;
      }

      public VehicleValuationRatingBySite(int vehicleValuationId, int retailerSiteId, decimal? retailRating, decimal? retailDaysToSell, decimal strategyPrice, decimal strategyPrice2, decimal priceScenario)
      {
         VehicleValuation_Id = vehicleValuationId;
         RetailerSite_Id = retailerSiteId;
         RetailRating = retailRating.HasValue ? retailRating.Value : 0;
         StrategyPrice = strategyPrice;

         StrategyPrice2 = strategyPrice2;
         DaysToSellAtCurrentSelling = retailDaysToSell.HasValue ? retailDaysToSell.Value : 0;
         PriceScenario = priceScenario;
      }

      //TODO Nimish build this constructor once we know what the response will look like
      //public VehicleValuationRatingBySite(VehicleMetricAPIResponse response, VehicleMetricAPI_Location location, decimal strategyPrice, System.Collections.Generic.List<RetailerSite> retailers)
      //{
      //    VehicleValuation_Id = response.vehicleWebsiteRatingId;
      //    RetailerSite_Id = retailers.FirstOrDefault(r => r.RetailerId == int.Parse(location.advertiserId)).Id;
      //    RetailDaysToSell = location.daysToSell.value;
      //    RetailRating = location.rating.value;
      //    StrategyPrice = strategyPrice;
      //}

      [Key]
      public int Id { get; set; }

      //FKs
      public int VehicleValuation_Id { get; set; }
      [ForeignKey("VehicleValuation_Id")]
      public VehicleValuation VehicleValuation { get; set; }

      public int RetailerSite_Id { get; set; }
      [ForeignKey("RetailerSite_Id")]
      public RetailerSite RetailerSite { get; set; }

      public decimal RetailRating { get; set; }
      public decimal DaysToSellAtCurrentSelling { get; set; }
      public decimal DaysToSellAtCurrentSelling2 { get; set; }
      public decimal StrategyPrice { get; set; }
      public decimal StrategyPrice2 { get; set; }
      public decimal PriceScenario { get; set; }  //used by V12 when the do their scenarios thing e.g. what if we sell at 98%, 96% etc.


      //Buying targets
      public decimal? Warranty { get; set; }
      public int? TargetMargin { get; set; }                                
      public decimal? TargetAdditionalMech { get; set; }        
      public decimal? TargetPaintPrep { get; set; }          
      public decimal? TargetAuctionFee { get; set; }                    
      public decimal? TargetDelivery { get; set; }                  
      public decimal? TargetOtherCost { get; set; }




   }
}
