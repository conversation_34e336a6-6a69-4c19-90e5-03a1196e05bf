{
   //-------------------------------------------------x
   // THIS IS RPA
   //-------------------------------------------------x

   "Logging": {
      "LogLevel": {
         "Default": "Information",
         "Microsoft": "Warning",
         "Microsoft.Hosting.Lifetime": "Information"
      }
   },

   "ConnectionStrings": {
      "DefaultConnection": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkVindis;Persist Security Info=True; User ID=SparkVindisLoader;Password=******************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=90;"
   },

   "AllowedHosts": "*",
   "EmailSettings": {

      "mailAppTenantId": "7def64f8-8b3f-400a-8ad0-e50eb7e77eef",
      "mailAppId": "9a44d66f-cfe2-4d19-b129-62b3e0dd28aa",
      "mailSecret": "Taylor Swift, definitely",
      "mailSecretValue": "****************************************",
      "mailAccount": "<EMAIL>"
   },

   "AppSettings": {
      "mailUser": "<EMAIL>",
      "mailPwd": "X4KkGrS46MKHqEXR",
      "mailIn": "Inbox",
      "triggerJobSenders": "<EMAIL>,<EMAIL>",
      "mailArchive": "processed",
      "mailError": "error",

      //-------------------------------------------------x
      // Common
      //-------------------------------------------------x
      "serviceName": "CPHI.Spark.WebScraper",
      "fileDestination": "c:\\cphiRoot\\{destinationFolder}\\inbound\\",
      //"fileDestinationDev": "c:\\cphiRoot\\{destinationFolder}Dev\\inbound\\",
      "fileDownloadLocation": "c:\\cphiRoot\\downloads",
      "sendFullReportEmailsTo": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
      "sendEmailsTo": "<EMAIL>,<EMAIL>"
      //"sendEmailsTo": "<EMAIL>,<EMAIL>"
   },
   "Monitor": {
      "AddLogMessageURL": "https://cphimonitorapi.azurewebsites.net/api/monitor/",
      "AppKey": "2"
   },

   "serviceName": "CPHI.Spark.RPA",
   "chattyMode": "true",
   "customerName": "Vindis",
   "speedRatio": "1.2",
   "alsoDoStage": "false",
   "powerPassword": "25octLIF",
   "manuallyAddedStocknumbers": null,
   "siteIds": "1,2,3,4,5,6,7,8,9,11,12,13,14,15,16,17,19,20",
   "daysToGoBack": "5",


   "overrideRunJobNow": null, //put the job name in here if you wish to force run it  e.g. "PeopleJob"
   //"ClientSettingsProvider.ServiceUri": ""

   "Quartz": {
      "RPAJob": "0 0 4 * * ?"
   },

   "ManualStartDelays": { // delay in minutes from the start of the first job.
      "RPAJob": "0"
   }


}