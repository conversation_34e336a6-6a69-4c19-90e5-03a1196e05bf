﻿using log4net;
using System;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Collections.Generic;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.RRG;
using CPHI.Spark.Model.ViewModels.Vindis;
using Dapper;
using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Reporter.Jobs;
using CPHI.Spark.Repository;
using CPHI.Spark.Model.Services;

namespace CPHI.Spark.Reporter.DataAccess
{

   public class CommissionDataAccess
   {
      private static readonly ILog Logger = LogManager.GetLogger(typeof(SalesmanStatement_RRG_Job));
      public DateTime monthToRunFor;

      private readonly CPHIDbContext db;

      public CommissionDataAccess(CPHIDbContext db)
      {
         this.db = db;
      }




      //db call to get the sites
      public List<siteIdAndDescription> getSites(Model.DealerGroupName customer)
      {
         DataTable dataTable = new DataTable();
         {

            string connString = ConfigService.GetConnectionString(customer);
            SqlConnection conn = new SqlConnection(connString);
            SqlCommand cmd = new SqlCommand("SELECT * from Sites", conn);
            cmd.CommandType = CommandType.Text;
            conn.Open();

            // create data adapter
            SqlDataAdapter da = new SqlDataAdapter(cmd);
            // this will query your database and return the result to your datatable
            da.Fill(dataTable);
            conn.Close();
            da.Dispose();

            //convert to list (seems a little tedious)
            List<siteIdAndDescription> sites = new List<siteIdAndDescription>();
            sites = (from row in dataTable.AsEnumerable()
                     select new siteIdAndDescription()
                     {
                        Id = row.Field<int>("Id"),
                        Description = row.Field<string>("Description")
                     }).ToList();
            return sites;
         }
      }

      public async Task SaveCommissionPayoutsRun(List<CommissionPayout> payouts)
      {

         //test for truncation

         var truncateMessages = TruncaterService.Truncate(payouts);



         await db.CommissionPayouts.AddRangeAsync(payouts);





         foreach (var payout in payouts)
         {
            if (payout.StockNumber?.Count() > 50)
            {
               payout.StockNumber = payout.StockNumber.Substring(0, 50);
            }
             if (payout.Customer?.Count() > 50)
            {
               payout.Customer = payout.Customer.Substring(0, 50);
            }
             if (payout.Reg?.Count() > 12)
            {
               payout.Reg = payout.Reg.Substring(0, 12);
            }
             if (payout.SalesRole?.Count() > 12)
            {
               payout.SalesRole = payout.SalesRole.Substring(0, 12);
            }
             if (payout.PayoutType?.Count() > 50)
            {
               payout.PayoutType = payout.PayoutType.Substring(0, 50);
            }
         }

         try
         {
         await db.SaveChangesAsync();

         }
         catch(Exception ex)
         {
            throw;
         }


      }




      public List<PersonKeyInfo> getPeopleKeyInfo(DateTime monthToRunFor, Model.DealerGroupName customer)
      {
         DataTable dataTable = new DataTable();
         {

            string connString = ConfigService.GetConnectionString(customer);
            SqlConnection conn = new SqlConnection(connString);


            SqlCommand cmd = new SqlCommand($"dbo.GET_PeopleAndRoleForMonth", conn);
            cmd.Parameters.AddWithValue("Year", monthToRunFor.ToString("yyyy"));
            cmd.Parameters.AddWithValue("Month", monthToRunFor.Month);

            cmd.CommandType = CommandType.StoredProcedure;
            conn.Open();

            // create data adapter
            SqlDataAdapter da = new SqlDataAdapter(cmd);
            // this will query your database and return the result to your datatable
            da.Fill(dataTable);
            conn.Close();
            da.Dispose();

            //convert to list (seems a little tedious)
            List<PersonKeyInfo> people = new List<PersonKeyInfo>();
            people = (from row in dataTable.AsEnumerable()
                      select new PersonKeyInfo()
                      {
                         PersonId = row.Field<int>("PersonId"),
                         SiteId = row.Field<int>("SiteId"),
                         SiteDescription = row.Field<string>("SiteDescription"),
                         Name = row.Field<string>("Name"),
                         DmsId = row.Field<string>("DmsId"),
                         Role = row.Field<string>("Role"),
                         Email = row.Field<string>("Email")
                      }).ToList();

            return people;

         }
      }

      public async Task<CommissionItemsCollection> GetCommission(DateTime month, DateTime latestPaidMonth, Model.DealerGroupName customer)
      {

         Spark.BusinessLogic.RRG.ReporterCommissionLogic logic = new Spark.BusinessLogic.RRG.ReporterCommissionLogic(ConfigService.GetConnectionString(customer));


         if ((customer == Model.DealerGroupName.Vindis && ConfigService.VindisIncludePriorMonthAdjustments) || (customer == Model.DealerGroupName.RRGUK && ConfigService.RRGIncludePriorMonthAdjustments))
         {
            return await logic.GetFullStatementWithAdjustments(month, latestPaidMonth, Logger);

         }
         else
         {
            return await logic.GetFullStatementNoAdjustments(month, Logger);
         }


      }

      public async Task<CommissionItemsCollection> GetCommissionLBDM(DateTime month, DateTime latestPaidMonth, Model.DealerGroupName customer)
      {

         Spark.BusinessLogic.RRG.ReporterCommissionLogic logic = new Spark.BusinessLogic.RRG.ReporterCommissionLogic(ConfigService.GetConnectionString(customer));

         if (ConfigService.RRGIncludePriorMonthAdjustments)
         {
            return await logic.GetFullStatementWithAdjustmentsLBDM(month, month.AddMonths(-1), Logger);
         }
         else
         {
            return await logic.GetFullStatementNoAdjustmentsLBDM(month, Logger);
         }

      }

      public async Task<IEnumerable<CommissionPayoutPersonSummary>> GetCommissionVindis(DateTime month, bool ignoreAuditPass, Model.DealerGroupName customer)
      {
         string connString = ConfigService.GetConnectionString(customer);
         Spark.BusinessLogic.Vindis.CommissionLogicService cl = new Spark.BusinessLogic.Vindis.CommissionLogicService(connString);

         var res = await cl.GetFullStatementForMonth(month, ignoreAuditPass, null);//
         return res;
      }


      public async Task<IEnumerable<UsageReportRecipient>> GetUsersWithLogins(Model.DealerGroupName customer)
      {

         using (var dapper = new Dapperr())
         {
            return await dapper.GetAllAsync<UsageReportRecipient>(customer, "dbo.GET_UsersWithLogins", null, CommandType.StoredProcedure);
         }
      }


      public async Task<IEnumerable<DailyUsageItem>> GetDailyUsageItems(Model.DealerGroupName customer)
      {

         using (var dapper = new Dapperr())
         {
            return await dapper.GetAllAsync<DailyUsageItem>(customer, "dbo.GET_DailyUsageItems", null, CommandType.StoredProcedure);
         }
      }

      /*
      public async Task<IEnumerable<SummaryUsageItem>> GetUsageDataPeopleItems()
      {
          using (var dapper = new Dapperr())
          {
              return await dapper.GetAllAsync<SummaryUsageItem>("dbo.GET_SummaryUsageItems", null, CommandType.StoredProcedure);
          }
      }
      */

      public async Task<IEnumerable<FinanceAndAddonItem>> GetFinanceAddons(FinanceAddonsParams parms, int userId, Model.DealerGroupName customer)
      {
         using (var dapper = new Dapperr())
         {
            var paramList = new DynamicParameters();

            string vehicleTypes = string.Join(",", parms.VehicleTypeIds);
            string orderTypes = string.Join(",", parms.OrderTypeIds);

            paramList.Add("YearMonths", parms.YearMonths);
            paramList.Add("UserId", userId);
            paramList.Add("Sites", parms.Sites);
            paramList.Add("OrderTypes", orderTypes);
            paramList.Add("VehicleTypes", vehicleTypes);
            paramList.Add("Franchises", parms.Franchises);
            paramList.Add("ShowAllSites", parms.ShowAllSites);

            // Excludes Service Plan & Warranty from the PPU 
            //paramList.Add("Franchises", excludeServicePlanAndWarranty);

            return await dapper.GetAllAsync<FinanceAndAddonItem>(customer, "dbo.GET_FinanceAddons", paramList, CommandType.StoredProcedure);
         }
      }

      public async Task<IEnumerable<FinanceAndAddonItem>> GetFinanceAddonsForSite(FinanceAddonsParams parms, int userId, Model.DealerGroupName customer)
      {

         using (var dapper = new Dapperr())
         {
            var paramList = new DynamicParameters();

            string vehicleTypes = string.Join(",", parms.VehicleTypeIds);
            string orderTypes = string.Join(",", parms.OrderTypeIds);

            paramList.Add("YearMonths", parms.YearMonths);
            paramList.Add("UserId", userId);
            paramList.Add("SiteIds", parms.Sites);
            paramList.Add("OrderTypes", orderTypes);
            paramList.Add("VehicleTypes", vehicleTypes);
            paramList.Add("Franchises", parms.Franchises);

            // Excludes Service Plan & Warranty from the PPU 
            //paramList.Add("ExcludeServicePlanAndWarranty", excludeServicePlanAndWarranty == true ? 1 : 0);

            return await dapper.GetAllAsync<FinanceAndAddonItem>(customer, "dbo.GET_FinanceAddonsForSite", paramList, CommandType.StoredProcedure);
         }
      }


   }

}

