﻿using CPHI.Repository;
using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Reporter.Services;
using CPHI.Spark.Reporter.Services.AutoPrice;
using CPHI.Spark.Repository;
using log4net;
using Quartz;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.Reporter.Jobs.AutoPrice
{
   


   [DisallowConcurrentExecution]
   public class VehicleValuation_Job : IJob
   {
      private static readonly ILog logger = LogManager.GetLogger(typeof(VehicleValuation_Job));

      // ------------------------
      // START HERE 
      // ------------------------
      public async Task Execute(IJobExecutionContext context)
      {
         Stopwatch sw = new Stopwatch();
         sw.Start();

         VehicleValuationService updateWebsitePricesService = new VehicleValuationService(HttpClientFactoryService.HttpClientFactory);

         List<Model.DealerGroupName> valuationGroups = ConfigService.BuildEnumList(ConfigService.ValuationJob_Groups);
         await updateWebsitePricesService.ValueVehicles(logger, valuationGroups);

      }
   }
}

