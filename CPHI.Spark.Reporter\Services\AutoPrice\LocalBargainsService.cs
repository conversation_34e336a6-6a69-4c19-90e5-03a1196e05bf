﻿using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;

using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{
   public class LocalBargainsService
   {


      public async Task DeleteOldBargains(ILog logger, List<Model.DealerGroupName> dealerGroups)
      {
         if (dealerGroups.Count == 0)
         { return; }


         foreach (var dealerGroup in dealerGroups)
         {


            logger.Info($"");
            logger.Info($"-----------------------------------------");
            logger.Info($"DeleteOldBargains: Starting {dealerGroup}");
            var logMessage = LoggingService.InitLogMessage();
            try
            {
               string connString = ConfigService.GetConnectionString(dealerGroup);
               ILocalBargainDataAccess localBargainDataAccess = new LocalBargainDataAccess(connString);
               await localBargainDataAccess.DeleteOldBargains();
               logger.Info($"DeleteOldBargains: Finished {dealerGroup}");
            }
            catch (Exception ex)
            {
               await EmailerService.LogException(ex, logger, "DeleteOldBargains");
               LoggingService.AddErrorLogMessage(logMessage, ex.Message);
            }
            finally
            {
               await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "DeleteOldBargains");
            }

         }

      }


      public async Task FindLocalBargains(ILog logger, List<Model.DealerGroupName> dealerGroups)
      {
         if (dealerGroups.Count == 0)
         { return; }


         //Setup data access layers
         foreach (var dealerGroup in dealerGroups)
         {


            logger.Info($"");
            logger.Info($"-----------------------------------------");
            logger.Info($"LocalBargains: Starting {dealerGroup}");
            var logMessage = LoggingService.InitLogMessage();

                  string connString = ConfigService.GetConnectionString(dealerGroup);
                  ILocalBargainDataAccess localBargainDataAccess = new LocalBargainDataAccess(connString);
                  //Setup results
                  List<LocalBargain> localBargainsToSave = new List<LocalBargain>();
                  try
                  {
                     VehicleAdvertsDataAccess vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(connString);
                     RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(connString);
                     TagDataAccess tagDataAccess = new TagDataAccess(connString);
                     //  HttpClient httpClient = new HttpClient();
                     var atStockClient = new AutoTraderStockClient(HttpClientFactoryService.HttpClientFactory,
                     ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret,
                      ConfigService.AutotraderBaseURL);
                     AutoTraderCompetitorClient competitorClient = new AutoTraderCompetitorClient(
                        HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret,
                         ConfigService.AutotraderBaseURL);
                     var atFutureValsClient = new AutoTraderFutureValuationsClient(HttpClientFactoryService.HttpClientFactory,
                     ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret,
                      ConfigService.AutotraderBaseURL);
                     var atMetricsClient = new AutoTraderVehicleMetricsClient(HttpClientFactoryService.HttpClientFactory,
                      ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret,
                      ConfigService.AutotraderBaseURL);
                     var atTokenClient = new AutoTraderApiTokenClient(HttpClientFactoryService.HttpClientFactory,
                      ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret,
                      ConfigService.AutotraderBaseURL);

               //Get the list of derivatives and makes
               IEnumerable<VehicleDerivativeAndMake> derivativesAndMakes = await vehicleAdvertsDataAccess.GetVehicleDerivativesAndMakes(dealerGroup);
               var derivativesLookup = derivativesAndMakes.ToLookup(x => x.RetailerSiteId);

               DateTime runDate = DateTime.Now;
               var retailerSites = await retailerSitesDataAccess.GetRetailerSitesWithStrategies(runDate, dealerGroup);

               //declare valid number plate years
               var validNumberPlateYears = GetValidNumberPlateYears();

                     var allTags = await tagDataAccess.SearchTags(new TagSearchDTO() { IsActive = true, DealerGroupName = dealerGroup });

                     //loop over sites
                     var tokenResponse = await atTokenClient.GetToken();
                     DateTime tokenGeneratedTime = DateTime.Now;

               foreach (var retailerSite in retailerSites)
               {
                  tokenResponse = await atTokenClient.CheckExpiryAndRegenerate(tokenResponse);

                  var thisSiteLocalBargains = new List<LocalBargain>();

                  int derivativesConsidered = 0;
                  int listingsReviewed = 0;
                  var thisMakeDerivatives = derivativesLookup[retailerSite.Id];

                  /// loop through derivatives
                  foreach (var derivative in thisMakeDerivatives)
                  {
                     derivativesConsidered++;
                     /// find derivative listings and loop through
                     List<AutoTraderVehicleListing> thisDerivativeListings = await CollectCompetitorAdvertsForDerivativeAndSite(
                        tokenResponse.AccessToken,
                        validNumberPlateYears,
                        derivative.Derivative,
                        retailerSite,
                        logger,
                        atStockClient);

                     foreach (var vehicleListing in thisDerivativeListings)
                     {
                        listingsReviewed++;

                        if (vehicleListing.adverts.retailAdverts?.totalPrice?.amountGBP == null)
                        {
                           continue;
                        }


                        int? valuationGiven = vehicleListing.valuations.adjusted?.retail?.amountGBP;
                        if (valuationGiven == null)
                        {
                           continue;
                        }
                        int valuationToUse = (int)valuationGiven;

                        decimal strategyPrice = valuationToUse;

                        decimal? retailRating = vehicleListing.vehicleMetrics?.local?.retail?.rating?.value;
                        if (retailRating == null)
                        {
                           continue;
                        }
                        if (retailerSite.LocalBargainsMinRetailRating > 0 && (decimal)retailRating < retailerSite.LocalBargainsMinRetailRating)
                        {
                           continue;
                        }


                        AdvertParamsForStrategyCalculator calcParams = new AdvertParamsForStrategyCalculator(vehicleListing, true, retailerSite, tokenResponse); //assume always on-brand as we only look at derivatives for our brands

                              List<StrategyPriceBuildUpItem> thisAdvertBuildUpItems = new List<StrategyPriceBuildUpItem>();
                              List<LeavingVehicle6mItem> leaving6mData = new(); //TODO fix this properly
                              List<DailyValuationChange> dailyValuationChanges = new List<DailyValuationChange>();
                              await ApplyStrategyService.ApplyRuleSet(
                                  runDate,
                                  calcParams,
                                  retailerSite.StrategySelectionRuleSet,
                                  retailerSite,
                                  retailerSite.Site,
                                  thisAdvertBuildUpItems,
                                  tokenResponse,
                                  competitorClient,
                                  ConfigService.AutotraderBaseURL,
                                  logger, null, atFutureValsClient, atMetricsClient,leaving6mData, dailyValuationChanges, allTags.ToList());


                        if (thisAdvertBuildUpItems.Count() > 0)
                        {
                           var impacts = thisAdvertBuildUpItems.Select(x => x.Impact).ToList();
                           foreach (var item in thisAdvertBuildUpItems)
                           {
                              strategyPrice += item.Impact;
                           }
                        }


                        decimal currentSelling = (decimal)vehicleListing.adverts.retailAdverts.totalPrice.amountGBP;
                        decimal opportunity = strategyPrice - currentSelling;

                        if (opportunity > retailerSite.LocalBargainThreshold)
                        {
                           //we have a bargain!
                           var localBargain = new LocalBargain(vehicleListing, (int)Math.Round(strategyPrice, 0, MidpointRounding.AwayFromZero), runDate, retailerSite.Id);
                           thisSiteLocalBargains.Add(localBargain);
                        }
                     }

                  }
                  //}
                  if (thisSiteLocalBargains.Count == 0)
                  {
                     logger.Warn($"LocalBargains: {retailerSite.Name}: Reviewed {listingsReviewed} listing(s) across {derivativesConsidered} derivative(s), found {thisSiteLocalBargains.Count} local bargain(s) with > {retailerSite.LocalBargainThreshold}");
                  }
                  else
                  {
                     logger.Info($"LocalBargains: {retailerSite.Name}: Reviewed {listingsReviewed} listing(s) across {derivativesConsidered} derivative(s), found {thisSiteLocalBargains.Count} local bargain(s) with > {retailerSite.LocalBargainThreshold}");
                  }

                  localBargainsToSave.AddRange(thisSiteLocalBargains);
               }

               //Manually truncate any local bargain strings that are too long
               List<string> truncateMessages = TruncaterService.Truncate<LocalBargain>(localBargainsToSave);
               if (truncateMessages.Count > 0)
               {
                  foreach (var message in truncateMessages)
                  {
                     logger.Warn(message);
                  }
               }


               await localBargainDataAccess.CreateLocalBargains(localBargainsToSave);
               logger.Info("LocalBargains: Saved local bargains");
            }
            catch (Exception ex)
            {
               await EmailerService.LogException(ex, logger, "LocalBargains");
               LoggingService.AddErrorLogMessage(logMessage, ex.Message);
            }
            finally
            {
               await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "LocalBargains");
            }
         }


      }



      private static List<int> GetValidNumberPlateYears()
      {
         List<int> validNumberPlateYears = new List<int>();
         int currentYear = DateTime.Now.Year;
         int startYear = currentYear - 6; // Going back 6 years

         for (int year = startYear; year <= currentYear; year++)
         {
            int marchPlate = year - 2000; // March registration
            int septemberPlate = year - 1950; // September registration

            validNumberPlateYears.Add(marchPlate);
            validNumberPlateYears.Add(septemberPlate);
         }

         return validNumberPlateYears;
      }



      private static async Task<List<AutoTraderVehicleListing>> CollectCompetitorAdvertsForDerivativeAndSite(
                                                                                                             string token,
                                                                                                             List<int> validNumberPlateYears,
                                                                                                             string derivative,
                                                                                                             RetailerSite retailerSite,
                                                                                                             ILog logger, AutoTraderStockClient atStockClient)
      {
         List<AutoTraderVehicleListing> vehicleListingResult = new List<AutoTraderVehicleListing>();
         //vehicleListingResult.Clear();


         //returns max 20 in one go
         var competitorVehicles = await atStockClient.GetCompetitorVehicle(1, retailerSite.RetailerId, derivative, retailerSite.LocalBargainsSearchRadius, retailerSite.Postcode, validNumberPlateYears.First(), validNumberPlateYears.Last(), token, ConfigService.AutotraderBaseURL);

         vehicleListingResult.AddRange(competitorVehicles.results);

         //check if more than 200 results were found
         if (competitorVehicles.totalResults <= 200)
         {
            // great, can loop through all required pages up to page 10
            int totalCallsToMake = competitorVehicles.totalResults / 20;
            for (int i = 2; i <= totalCallsToMake; i++)
            {
               var r = await atStockClient.GetCompetitorVehicle(i, retailerSite.RetailerId, derivative, retailerSite.LocalBargainsSearchRadius, retailerSite.Postcode, validNumberPlateYears.First(), validNumberPlateYears.Last(), token, ConfigService.AutotraderBaseURL);
               vehicleListingResult.AddRange(r.results);
            }
         }
         else
         {
            // > 200 so separately collect listings for each plate year
            vehicleListingResult.Clear(); //we already have added the first 20 from the first query above, clear these

            foreach (var plateYear in validNumberPlateYears)
            {
               competitorVehicles = await atStockClient.GetCompetitorVehicle(1, retailerSite.RetailerId, derivative, retailerSite.LocalBargainsSearchRadius, retailerSite.Postcode, plateYear, plateYear, token, ConfigService.AutotraderBaseURL);
               vehicleListingResult.AddRange(competitorVehicles.results);

               if (competitorVehicles.totalResults > 200)
               {
                  //Wow, > 200 results for a derivative for a plate year.   Seems unlikely to ever get here, so if so then Log error - as we cannot loop through more than 200 results
                  logger.Error($"Found more than 200 results ({competitorVehicles.totalResults}) for Derivative:{derivative}, RetailerIdentifier:{retailerSite.RetailerId}, plateYear:{plateYear}. We will not be able to get more than 200 records. ");
                  competitorVehicles.totalResults = 200;
               }

               int totalCallsToMake = Math.Min(10, competitorVehicles.totalResults / 20);
               for (int i = 2; i <= totalCallsToMake; i++)
               {
                  var r = await atStockClient.GetCompetitorVehicle(i, retailerSite.RetailerId, derivative, retailerSite.LocalBargainsSearchRadius, retailerSite.Postcode, plateYear, plateYear, token, ConfigService.AutotraderBaseURL);
                  vehicleListingResult.AddRange(r.results);
               }
            }
         }

         return vehicleListingResult;
      }





   }
}
