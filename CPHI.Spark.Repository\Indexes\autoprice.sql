CREATE NONCLUSTERED INDEX stocksIsRemovedIncRegChassis
ON [dbo].[Stocks] ([IsRemoved])
INCLUDE ([Reg],[Chassis])




CREATE NONCLUSTERED INDEX Stocks_Chassis
ON [dbo].[Stocks] ([Chassis])


CREATE NONCLUSTERED INDEX [StocksIsRemovedReg]
ON [dbo].[Stocks] ([IsRemoved],[Reg])
INCLUDE ([Siv])

CREATE NONCLUSTERED INDEX [StocksIsRemovedChassis]
ON [dbo].[Stocks] ([IsRemoved],[Chassis])
INCLUDE ([Siv])



CREATE NONCLUSTERED INDEX [VehicleOptOuts_ActualEndDate]
ON [autoprice].[VehicleOptOuts] ([ActualEndDate])
INCLUDE ([VehicleAdvert_Id])

CREATE NONCLUSTERED INDEX [VehicleOptOuts_CreateDate_ActualEndDate]
ON [autoprice].[VehicleOptOuts] ([CreatedDate],[ActualEndDate])
INCLUDE ([Person_Id],[OriginalEndDate],[VehicleAdvert_Id])

CREATE NONCLUSTERED INDEX [Stocks_IsRemoved_Chassis]
ON [dbo].[Stocks] ([IsRemoved],[Chassis])
INCLUDE ([StockNumberFull])


CREATE NONCLUSTERED INDEX IDX_DealLatests_Units
ON [dbo].[DealLatests] ([Units])
INCLUDE ([Reg],[InvoiceDate],[TotalNLProfit])
GO

CREATE NONCLUSTERED INDEX IX_VehicleAdverts_Optimization
ON autoprice.VehicleAdverts (Id, RetailerSite_Id, Stock_Id, CreatedInSparkDate)
INCLUDE (VehicleReg);

CREATE NONCLUSTERED INDEX IDX_VehicleAdverts_WasOptedOut_CreatedDate_NowPrice
ON [autoprice].[PriceChangeAutoItems] ([WasOptedOutOfWhenGenerated],[CreatedDate],[NowPrice])
INCLUDE ([VehicleAdvertSnapshot_Id],[WasPrice],[DateConfirmed],[ApprovedDate])



DROP INDEX [SnapshotDateVarious] ON [autoprice].[VehicleAdvertSnapshots];
/****** Object:  Index [SnapshotDateVarious]    Script Date: 17/11/2024 21:16:50 ******/
CREATE NONCLUSTERED INDEX [SnapshotDateVarious] ON [autoprice].[VehicleAdvertSnapshots]
(
	[SnapshotDate] ASC
)
INCLUDE(
[VehicleAdvert_Id],
[PerformanceRatingScore],
[ValuationMktAvRetail],
[RetailRating],
[RetailDaysToSellAtValuation],
[StrategyPrice],
[DaysToSellAtCurrentSelling],
[SuppliedPrice],
[ValuationAdjRetail],
[ValuationMktAvRetailExVat],
[TotalPrice],
[ValuationAdjRetailExVat]
) 
WITH (STATISTICS_NORECOMPUTE = OFF, DROP_EXISTING = OFF, ONLINE = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO

CREATE NONCLUSTERED INDEX [OwnershipCondition]
ON [autoprice].[VehicleAdverts] ([OwnershipCondition])
INCLUDE ([VehicleReg],[Model],[BodyType],[FuelType],[DriveTrain],[FirstRegisteredDate],[RetailerSite_Id],[Stock_Id])



-- For update by Reg
CREATE NONCLUSTERED INDEX IX_Stocks_Reg
ON stocks (Reg, Site_Id, VehicleAdvert_Id, IsRemoved, RemovedDate);

-- For update by Chassis
CREATE NONCLUSTERED INDEX IX_Stocks_Chassis
ON stocks (Chassis, Site_Id, VehicleAdvert_Id, IsRemoved, RemovedDate);
