


##########################################
Firstly in dev:   
##########################################

A:   
Complete this body:
-- example file in C:\Users\<USER>\cphi.co.uk\files - Documents\CPHi\Sytner   sites setup.xlsb    lets you drop in the details and get the regions array out, combine with detail below in postman
-- Note, had to ensure that Site name and fakename is less than 30 characters.
-- Also, have to set header  'newDGSecret' of 'rxpA4pCgMC38MKEQ'
-- password is irrelevant as is replaced by our hash
-- Regions and sites just use whatever you want it should handle it

{
    "Password": "whatEver123!",
    "CreateStandardStrategy": true,
    "NewDealerGroupName": "Nuneaton",
    "NewGroupShortName": "nuneaton",
    "IsMultiSiteGroup": false,
    "Regions": [
        
         {            "RegionName": "All",            "Sites": [
{    "SiteName": "Nuneaton Car Sales",    "DealerNumber": 10003482,    "Makes": "Various",    "Postcode": "CV116SQ",    "GeoX": 52.51363,    "GeoY": -1.44875,
"RetailerType":"Independent"  }
]}

    ]
}

Use postman to post the above in

https://localhost:44338/api/AutoPrice/CreateNewDealerGroup  




B: Go into back end / Model / DealerGroupName.cs and add the DealerGroup

C: Go into reporter appsettings:
    - set run temp job now 
    - set conn strings to dev
    - Use Option A.1 in the temp job
    Run it check we have access 



##########################################
Now in prod:   
##########################################


A:  Run webapp Production. Repeat step A from before.  Restart back end.
If you need to update a DG Number as it did not create sequentially then do this:
COMMIT TRAN
ROLLBACK
BEGIN TRAN
DECLARE @wrongDgId int = 24;
DECLARE @rightDgId int = 22;
SET IDENTITY_INSERT DealerGroups ON;
INSERT INTO DealerGroups 
(id,name,DealerGroupId,SystemUser_Id) 
(SELECT @rightDgId, Name,@rightDgId,SystemUser_Id FROM DealerGroups WHERE Id = @wrongDgId)
update people set DealerGroup_Id = @rightDgId where DealerGroup_Id = @wrongDgId
update sites set DealerGroup_Id = @rightDgId where DealerGroup_Id = @wrongDgId
update GlobalParams set DealerGroup_Id = @rightDgId where DealerGroup_Id = @wrongDgId
update LogMessages set DealerGroup_Id = @rightDgId where DealerGroup_Id = @wrongDgId
update autoprice.retailersites set DealerGroup_Id = @rightDgId where DealerGroup_Id = @wrongDgId
SET IDENTITY_INSERT DealerGroups OFF;
DELETE FROM DealerGroups where id = @wrongDgId

B: Do the front end setup:  
    1. Create them an environment in the front end.  Ensure you update fullSideMenu description.   
	2. Create a dealership background image.   Ensure matches to the dealership{dealershipBackgroundImageName} property in env.

    
	 

C: Fire up webApp against production locally
    1. Log out and log in then select the new dealergroup
    2. Set up their strategies properly using the UI in the app OK
    3. Setup site settings

D: Run reporter manually to pull in their ads and create snapshots.   Just use the "OPTION A.2" method.
    

E: Use this to move snapshots back to when they 'should' have first appeared if we had been running then:  
SELECT * FROM DealerGroups
COMMIT TRAN
BEGIN TRAN
UPDATE snaps SET SnapshotDate =  COALESCE(snaps.DateOnForecourt,snaps.SnapshotDate)
FROM autoprice.VehicleAdvertSnapshots snaps
INNER JOIN autoprice.VehicleAdverts ads on ads.Id = snaps.VehicleAdvert_Id
INNER JOIN autoprice.RetailerSites rs on rs.id = ads.RetailerSite_Id
WHERE CONVERT(date,snaps.SnapshotDate ) = CONVERT(date,getDate())
AND rs.DealerGroup_Id = {dgId}  

**DO NOT FORGET TO COMMIT THE TRAN!!**

F: Run reporter AGAIN manually to pull in their ads.  This will create today's snapshots (as we backdated the last lot).
This time do the full set of OPTION B.    There is a part of the reporter temp job can unhide to do this.   Include location optimiser and local bargains as necessary

G: Update the file GET_MorningEndToEndAutoPriceSummary.StoredProcedure.sql to add them into the morning checks

H: Redeploy the reporter with all the settings updated so we get their stuff everyday

I: Redeploy the web app

K: Ask James to backload 6m data





##########################################
If we are adding a site to an existing DG
##########################################


A: Manually set their other sites to disabled, noting which ones you changed (?!)

SELECT string_Agg(Id,',') from autoprice.RetailerSites WHERE IsActive = 1
UPDATE autoprice.RetailerSites
SET IsActive = 0 
WHERE Id IN (83,84,92,93,94)

B: Set them up using endpoint api/AutoPriceNewDealergroup/AddNewSitesForExistingDealergroup.   have thing ready to go in postman

C: Ensure they are on the right strategy etc. and right settings in the app

D: Run reporter manually to pull in their ads and create snapshots.   Just fetch, don't worry about updating strategy etc.   Use option A.1 or A.2 depending dev or prod

E: Use this to move snapshots back to when they 'should' have first appeared if we had been running then.
<insert the newly created retailer site ids>   <don't forget to commit the tran>
COMMIT TRAN
BEGIN TRAN
UPDATE snaps SET SnapshotDate =  COALESCE(snaps.DateOnForecourt,snaps.SnapshotDate)
FROM autoprice.VehicleAdvertSnapshots snaps
INNER JOIN autoprice.VehicleAdverts ads on ads.Id = snaps.VehicleAdvert_Id
INNER JOIN autoprice.RetailerSites rs on rs.id = ads.RetailerSite_Id
WHERE CONVERT(date,snaps.SnapshotDate ) = CONVERT(date,getDate())
AND rs.Id IN (109)

F: Run reporter AGAIN manually to pull in their ads.  This will create today's snapshots (as we backdated the last lot).
This time do the full set of steps. OPTION B   There is a part of the reporter temp job can unhide to do this.   Include location optimiser and local bargains as necessary

G: Undisable their other sites

H: Add access for whoever needs it

K: Ask James to backload 6m data



*/
