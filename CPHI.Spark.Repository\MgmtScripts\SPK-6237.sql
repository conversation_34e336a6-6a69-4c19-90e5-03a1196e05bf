----------------------------------------------RRG Database----------------------------------------------
--from FixedCostPreps

INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES 
(NULL,NULL,0,597,NULL,'AddtionalMech',1,NULL,NULL,'Captur'),
(NULL,NULL,0,657,NULL,'AddtionalMech',1,NULL,NULL,'Clio'),
(NULL,NULL,0,620,NULL,'AddtionalMech',1,NULL,NULL,'Duster'),
(NULL,NULL,0,624,NULL,'AddtionalMech',1,NULL,NULL,'Austral'),
(NULL,NULL,0,624,NULL,'AddtionalMech',1,NULL,NULL,'Arkana'),
(NULL,NULL,0,596,NULL,'AddtionalMech',1,NULL,NULL,'Sandero Stepway'),
(NULL,NULL,0,176,NULL,'AddtionalMech',1,NULL,NULL,'Trafic'),
(NULL,NULL,0,560,NULL,'AddtionalMech',1,NULL,NULL,'Megane E-tech'),
(NULL,NULL,0,527,NULL,'AddtionalMech',1,NULL,NULL,'Zoe'),
(NULL,NULL,0,134,NULL,'AddtionalMech',1,NULL,NULL,'Kangoo'),
(NULL,NULL,0,627,NULL,'AddtionalMech',1,NULL,NULL,'Sandero'),
(NULL,NULL,0,932,NULL,'AddtionalMech',1,NULL,NULL,'Kadjar'),
(NULL,NULL,0,665,NULL,'AddtionalMech',1,NULL,NULL,'Jogger'),
(NULL,NULL,0,804,NULL,'AddtionalMech',1,NULL,NULL,'A110'),
(NULL,NULL,0,457,NULL,'AddtionalMech',1,NULL,NULL,'Spring'),
(NULL,NULL,0,531,NULL,'AddtionalMech',1,NULL,NULL,'Symbioz'),
(NULL,NULL,0,645,NULL,'AddtionalMech',1,NULL,NULL,'Rafale'),
(NULL,NULL,0,639,NULL,'AddtionalMech',1,NULL,NULL,'Juke'),
(NULL,NULL,0,682,NULL,'AddtionalMech',1,NULL,NULL,NULL)
GO

--from FixedCostWarranties
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES
(NULL,NULL,0,228.92,NULL,'Warranty',1,25,'Dacia','.*spring.*'),
(NULL,NULL,0,211.37,NULL,'Warranty',1,25,'Dacia','^(?!.*spring).*$'),
(NULL,NULL,0,228.92,NULL,'Warranty',1,25,'Renault','4|5|kangoo|twizy|zoe|megane e-tech|scenic e-tech'),
(NULL,NULL,0,211.37,NULL,'Warranty',1,25,'Renault','^(?!.*(4|5|kangoo|twizy|zoe|megane e-tech|scenic e-tech)).*'),
(NULL,NULL,0,351.29,NULL,'Warranty',1,25,'Alpine','.*'),
(NULL,NULL,0,211.37,NULL,'Warranty',1,25,'DEFAULT','.*'),
(NULL,NULL,0,299.93,NULL,'Warranty',1,60,'Dacia','.*spring.*'),
(NULL,NULL,0,268.05,NULL,'Warranty',1,60,'Dacia','^(?!.*spring).*$'),
(NULL,NULL,0,299.93,NULL,'Warranty',1,60,'Renault','4|5|kangoo|twizy|zoe|megane e-tech|scenic e-tech'),
(NULL,NULL,0,268.05,NULL,'Warranty',1,60,'Renault','^(?!.*(4|5|kangoo|twizy|zoe|megane e-tech|scenic e-tech)).*'),
(NULL,NULL,0,522.48,NULL,'Warranty',1,60,'Alpine','.*'),
(NULL,NULL,0,268.05,NULL,'Warranty',1,60,'DEFAULT','.*')
GO

--from TargetMargins
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES
(NULL,10000,0,1500,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,15000,0,1500,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,17500,0,1750,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,20000,0,2000,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,22500,0,2250,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,25000,0,2500,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,27500,0,2750,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,30000,0,3000,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,35000,0,3500,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,40000,0,4000,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,50000,0,5000,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,60000,0,6000,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,70000,0,7000,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,80000,0,8000,NULL,'Margin',1,NULL,NULL,NULL),
(NULL,999999,0,9000,NULL,'Margin',1,NULL,NULL,NULL)
GO



--from ValuationAuctionFeeItems
--N/A

--from RetailerSites - Paint
--N/A

--from RetailerSites - Delivery
--N/A

--from RetailerSites - Other
--N/A


------------------------------------------------------------------------------------------------------------------
----------------------------------------------Autoprice Database----------------------------------------------
--from FixedCostPreps
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES 
(NULL,NULL,0,650,NULL,'AddtionalMech',26,0,NULL,'A Class'),
(NULL,NULL,0,1200,NULL,'AddtionalMech',26,19,NULL,'A Class'),
(NULL,NULL,0,1500,NULL,'AddtionalMech',26,49,NULL,'A Class'),
(NULL,NULL,0,1550,NULL,'AddtionalMech',26,19,NULL,'AMG GT'),
(NULL,NULL,0,600,NULL,'AddtionalMech',26,0,NULL,'B Class'),
(NULL,NULL,0,950,NULL,'AddtionalMech',26,19,NULL,'B Class'),
(NULL,NULL,0,1550,NULL,'AddtionalMech',26,49,NULL,'B Class'),
(NULL,NULL,0,1550,NULL,'AddtionalMech',26,49,NULL,'B Electric'),
(NULL,NULL,0,600,NULL,'AddtionalMech',26,0,NULL,'C Class'),
(NULL,NULL,0,1250,NULL,'AddtionalMech',26,19,NULL,'C Class'),
(NULL,NULL,0,1750,NULL,'AddtionalMech',26,49,NULL,'C Class'),
(NULL,NULL,0,600,NULL,'AddtionalMech',26,0,NULL,'CLA'),
(NULL,NULL,0,1200,NULL,'AddtionalMech',26,19,NULL,'CLA'),
(NULL,NULL,0,1750,NULL,'AddtionalMech',26,49,NULL,'CLA'),
(NULL,NULL,0,1050,NULL,'AddtionalMech',26,0,NULL,'CLS'),
(NULL,NULL,0,1150,NULL,'AddtionalMech',26,19,NULL,'CLS'),
(NULL,NULL,0,1550,NULL,'AddtionalMech',26,49,NULL,'CLS'),
(NULL,NULL,0,650,NULL,'AddtionalMech',26,0,NULL,'E Class'),
(NULL,NULL,0,1500,NULL,'AddtionalMech',26,19,NULL,'E Class'),
(NULL,NULL,0,2000,NULL,'AddtionalMech',26,49,NULL,'E Class'),
(NULL,NULL,0,600,NULL,'AddtionalMech',26,0,NULL,'EQA'),
(NULL,NULL,0,850,NULL,'AddtionalMech',26,19,NULL,'EQA'),
(NULL,NULL,0,600,NULL,'AddtionalMech',26,0,NULL,'EQB'),
(NULL,NULL,0,800,NULL,'AddtionalMech',26,0,NULL,'EQC'),
(NULL,NULL,0,1050,NULL,'AddtionalMech',26,19,NULL,'EQC'),
(NULL,NULL,0,600,NULL,'AddtionalMech',26,0,NULL,'EQE'),
(NULL,NULL,0,550,NULL,'AddtionalMech',26,0,NULL,'EQS'),
(NULL,NULL,0,550,NULL,'AddtionalMech',26,0,NULL,'G Class'),
(NULL,NULL,0,1550,NULL,'AddtionalMech',26,19,NULL,'G Class'),
(NULL,NULL,0,1550,NULL,'AddtionalMech',26,49,NULL,'G Class'),
(NULL,NULL,0,700,NULL,'AddtionalMech',26,0,NULL,'GLA'),
(NULL,NULL,0,1500,NULL,'AddtionalMech',26,19,NULL,'GLA'),
(NULL,NULL,0,1750,NULL,'AddtionalMech',26,49,NULL,'GLA'),
(NULL,NULL,0,600,NULL,'AddtionalMech',26,0,NULL,'GLB'),
(NULL,NULL,0,1100,NULL,'AddtionalMech',26,19,NULL,'GLB'),
(NULL,NULL,0,750,NULL,'AddtionalMech',26,0,NULL,'GLC'),
(NULL,NULL,0,1500,NULL,'AddtionalMech',26,18,NULL,'GLC'),
(NULL,NULL,0,1800,NULL,'AddtionalMech',26,49,NULL,'GLC'),
(NULL,NULL,0,1050,NULL,'AddtionalMech',26,0,NULL,'GLE'),
(NULL,NULL,0,1500,NULL,'AddtionalMech',26,19,NULL,'GLE'),
(NULL,NULL,0,2000,NULL,'AddtionalMech',26,49,NULL,'GLE'),
(NULL,NULL,0,800,NULL,'AddtionalMech',26,0,NULL,'GLS'),
(NULL,NULL,0,1500,NULL,'AddtionalMech',26,19,NULL,'GLS'),
(NULL,NULL,0,1800,NULL,'AddtionalMech',26,49,NULL,'GLS'),
(NULL,NULL,0,1550,NULL,'AddtionalMech',26,19,NULL,'GT'),
(NULL,NULL,0,500,NULL,'AddtionalMech',26,0,NULL,'Non Fran'),
(NULL,NULL,0,1000,NULL,'AddtionalMech',26,19,NULL,'Non Fran'),
(NULL,NULL,0,1200,NULL,'AddtionalMech',26,49,NULL,'Non Fran'),
(NULL,NULL,0,900,NULL,'AddtionalMech',26,0,NULL,'S Class'),
(NULL,NULL,0,1550,NULL,'AddtionalMech',26,19,NULL,'S Class'),
(NULL,NULL,0,2050,NULL,'AddtionalMech',26,49,NULL,'S Class'),
(NULL,NULL,0,550,NULL,'AddtionalMech',26,0,NULL,'SL'),
(NULL,NULL,0,1550,NULL,'AddtionalMech',26,19,NULL,'SL'),
(NULL,NULL,0,2050,NULL,'AddtionalMech',26,49,NULL,'SL'),
(NULL,NULL,0,900,NULL,'AddtionalMech',26,19,NULL,'SLC'),
(NULL,NULL,0,1200,NULL,'AddtionalMech',26,49,NULL,'SLC'),
(NULL,NULL,0,1050,NULL,'AddtionalMech',26,49,NULL,'SLK'),
(NULL,NULL,0,550,NULL,'AddtionalMech',26,0,NULL,'Smart'),
(NULL,NULL,0,850,NULL,'AddtionalMech',26,19,NULL,'Smart'),
(NULL,NULL,0,1050,NULL,'AddtionalMech',26,49,NULL,'Smart'),
(NULL,NULL,0,900,NULL,'AddtionalMech',26,0,NULL,'V Class'),
(NULL,NULL,0,1300,NULL,'AddtionalMech',26,19,NULL,'V Class'),
(NULL,NULL,0,1550,NULL,'AddtionalMech',26,49,NULL,'V Class'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A1'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A3'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A3 Cabriolet'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A4'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A4 Allroad'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A4 Avant'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A5'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A5 Avant'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A5 Cabriolet'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A6 Allroad'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A6 Avant'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A6 e-tron Avant'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A6 e-tron Sportback'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A6 Saloon'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A6 Unspecified'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A7'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'A8'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'e-tron'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'e-tron GT'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'e-tron S'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'Q2'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'Q3'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'Q4 e-tron'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'Q5'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'Q6 e-tron'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'Q7'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'Q8'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'Q8 e-tron'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'R8'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'RS e-tron GT'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'RS Q3'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'RS3'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'RS4 Avant'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'RS5'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'RS6 Avant'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'RS7'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'RSQ8'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'S e-tron GT'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'S1'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'S3'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'S4'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'S4 Avant'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'S5'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'S5 Avant'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'S6 Avant'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'S6 e-tron Avant'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'S6 e-tron Sportback'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'S6 Saloon'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'SQ2'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'SQ5'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'SQ6 e-tron'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'SQ7'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'SQ8'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'SQ8 e-tron'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'TT'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'TT RS'),
(NULL,NULL,0,999,NULL,'AddtionalMech',38,0,NULL,'TTS'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'1 Series'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'2 Series'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'2 Series Active Tourer'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'2 Series Gran Coupe'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'2 Series Gran Tourer'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'3 Series'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'4 Series'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'4 Series Gran Coupe'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'5 Series'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'6 Series'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'7 Series'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'8 Series'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'8 Series Gran Coupe'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'Alpina D3'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'i3'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'i4'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'i5'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'i7'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'i8'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'iX'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'iX1'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'iX2'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'iX3'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'M2'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'M3'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'M4'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'M5'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'X1'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'X2'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'X3'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'X3 M'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'X4'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'X5'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'X5 M'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'X6'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'X7'),
(NULL,NULL,0,1108,NULL,'AddtionalMech',38,0,NULL,'Z4'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Ateca'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Born'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Formentor'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Leon'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Tavascan'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Terramar'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'Carens'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'Ceed'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'EV3'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'EV6'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'EV9'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'Niro'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'Optima'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'Picanto'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'ProCeed'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'Rio'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'Sorento'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'Soul'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'Sportage'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'Stinger'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'Stonic'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'Venga'),
(NULL,NULL,0,716,NULL,'AddtionalMech',38,0,NULL,'XCeed'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'A Class'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'AMG GT'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'B Class'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'C Class'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'CLA'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'CLE'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'CLS'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'E Class'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'EQA'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'EQB'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'EQC'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'EQE'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'EQS'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'EQV'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'G Class'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'GLA'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'GLB'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'GLC'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'GLE'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'GLS'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'M Class'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'Maybach S Class'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'S Class'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'SL'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'SLC'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'V Class'),
(NULL,NULL,0,1154,NULL,'AddtionalMech',38,0,NULL,'X Class'),
(NULL,NULL,0,2500,NULL,'AddtionalMech',38,0,NULL,NULL),
(NULL,NULL,0,2500,NULL,'AddtionalMech',38,0,NULL,'718 Boxster'),
(NULL,NULL,0,2500,NULL,'AddtionalMech',38,0,NULL,'718 Cayman'),
(NULL,NULL,0,2500,NULL,'AddtionalMech',38,0,NULL,'718 Spyder'),
(NULL,NULL,0,2500,NULL,'AddtionalMech',38,0,NULL,'911'),
(NULL,NULL,0,2500,NULL,'AddtionalMech',38,0,NULL,'Boxster'),
(NULL,NULL,0,2500,NULL,'AddtionalMech',38,0,NULL,'Cayenne'),
(NULL,NULL,0,2500,NULL,'AddtionalMech',38,0,NULL,'Cayman'),
(NULL,NULL,0,2500,NULL,'AddtionalMech',38,0,NULL,'Macan'),
(NULL,NULL,0,2500,NULL,'AddtionalMech',38,0,NULL,'Panamera'),
(NULL,NULL,0,2500,NULL,'AddtionalMech',38,0,NULL,'Taycan'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Alhambra'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Arona'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Ateca'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'CUPRA Ateca'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Ibiza'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Leon'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Mii'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Tarraco'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Arteon'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Caravelle'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'e-Golf'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'e-up!'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Golf'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Golf SV'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'ID. Buzz'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'ID.3'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'ID.4'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'ID.5'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'ID.7'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Jetta'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Passat'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Polo'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Scirocco'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Sharan'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Taigo'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Tayron'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'T-Cross'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Tiguan'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Tiguan Allspace'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Touareg'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Touran'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'Transporter Shuttle'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'T-Roc'),
(NULL,NULL,0,750,NULL,'AddtionalMech',38,0,NULL,'up!')

GO
--from FixedCostWarranties
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES
(NULL,NULL,0,38,NULL,'Warranty',26,13,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,43,NULL,'Warranty',26,14,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,48,NULL,'Warranty',26,15,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,53,NULL,'Warranty',26,16,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,59,NULL,'Warranty',26,17,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,64,NULL,'Warranty',26,18,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,80,NULL,'Warranty',26,19,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,84,NULL,'Warranty',26,20,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,90,NULL,'Warranty',26,21,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,95,NULL,'Warranty',26,22,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,110,NULL,'Warranty',26,23,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,116,NULL,'Warranty',26,24,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,209,NULL,'Warranty',26,25,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,221,NULL,'Warranty',26,26,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,232,NULL,'Warranty',26,27,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,246,NULL,'Warranty',26,28,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,260,NULL,'Warranty',26,29,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,274,NULL,'Warranty',26,30,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,312,NULL,'Warranty',26,31,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,323,NULL,'Warranty',26,32,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,337,NULL,'Warranty',26,33,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,349,NULL,'Warranty',26,34,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,389,NULL,'Warranty',26,35,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,403,NULL,'Warranty',26,36,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,403,NULL,'Warranty',26,37,'Mercedes-Benz','^(?=.*AMG)(?=.*(A Class|CLA\b|GLA|B Class|GLB)).*$'),
(NULL,NULL,0,44,NULL,'Warranty',26,13,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,50,NULL,'Warranty',26,14,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,56,NULL,'Warranty',26,15,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,63,NULL,'Warranty',26,16,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,71,NULL,'Warranty',26,17,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,79,NULL,'Warranty',26,18,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,94,NULL,'Warranty',26,19,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,101,NULL,'Warranty',26,20,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,108,NULL,'Warranty',26,21,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,117,NULL,'Warranty',26,22,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,134,NULL,'Warranty',26,23,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,144,NULL,'Warranty',26,24,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,250,NULL,'Warranty',26,25,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,264,NULL,'Warranty',26,26,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,281,NULL,'Warranty',26,27,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,298,NULL,'Warranty',26,28,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,319,NULL,'Warranty',26,29,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,337,NULL,'Warranty',26,30,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,376,NULL,'Warranty',26,31,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,393,NULL,'Warranty',26,32,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,411,NULL,'Warranty',26,33,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,434,NULL,'Warranty',26,34,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,476,NULL,'Warranty',26,35,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,502,NULL,'Warranty',26,36,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,502,NULL,'Warranty',26,37,'Mercedes-Benz','^(?=.*AMG)(?=.*(C Class|GLC|EQC|SLC|SLK)).*$'),
(NULL,NULL,0,47,NULL,'Warranty',26,13,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,53,NULL,'Warranty',26,14,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,61,NULL,'Warranty',26,15,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,69,NULL,'Warranty',26,16,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,78,NULL,'Warranty',26,17,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,86,NULL,'Warranty',26,18,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,104,NULL,'Warranty',26,19,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,112,NULL,'Warranty',26,20,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,120,NULL,'Warranty',26,21,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,131,NULL,'Warranty',26,22,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,150,NULL,'Warranty',26,23,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,163,NULL,'Warranty',26,24,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,278,NULL,'Warranty',26,25,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,295,NULL,'Warranty',26,26,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,314,NULL,'Warranty',26,27,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,333,NULL,'Warranty',26,28,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,357,NULL,'Warranty',26,29,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,377,NULL,'Warranty',26,30,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,421,NULL,'Warranty',26,31,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,441,NULL,'Warranty',26,32,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,461,NULL,'Warranty',26,33,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,488,NULL,'Warranty',26,34,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,536,NULL,'Warranty',26,35,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,565,NULL,'Warranty',26,36,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,565,NULL,'Warranty',26,37,'Mercedes-Benz','^(?=.*AMG)(?=.*(E Class|GLE|CLK)).*$'),
(NULL,NULL,0,50,NULL,'Warranty',26,13,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,58,NULL,'Warranty',26,14,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,66,NULL,'Warranty',26,15,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,76,NULL,'Warranty',26,16,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,88,NULL,'Warranty',26,17,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,105,NULL,'Warranty',26,18,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,128,NULL,'Warranty',26,19,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,144,NULL,'Warranty',26,20,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,170,NULL,'Warranty',26,21,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,181,NULL,'Warranty',26,22,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,206,NULL,'Warranty',26,23,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,209,NULL,'Warranty',26,24,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,335,NULL,'Warranty',26,25,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,256,NULL,'Warranty',26,26,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,377,NULL,'Warranty',26,27,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,401,NULL,'Warranty',26,28,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,431,NULL,'Warranty',26,29,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,472,NULL,'Warranty',26,30,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,530,NULL,'Warranty',26,31,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,571,NULL,'Warranty',26,32,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,634,NULL,'Warranty',26,33,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,660,NULL,'Warranty',26,34,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,722,NULL,'Warranty',26,35,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,731,NULL,'Warranty',26,36,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,741,NULL,'Warranty',26,37,'Mercedes-Benz','^(?=.*AMG)(?=.*(AMG GT|S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class|SLS|MAYBACH)).*$'),
(NULL,NULL,0,93,NULL,'Warranty',26,25,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,105,NULL,'Warranty',26,26,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,116,NULL,'Warranty',26,27,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,130,NULL,'Warranty',26,28,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,144,NULL,'Warranty',26,29,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,158,NULL,'Warranty',26,30,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,196,NULL,'Warranty',26,31,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,207,NULL,'Warranty',26,32,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,221,NULL,'Warranty',26,33,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,233,NULL,'Warranty',26,34,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,273,NULL,'Warranty',26,35,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,287,NULL,'Warranty',26,36,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,287,NULL,'Warranty',26,37,'Mercedes-Benz','^(?!.*AMG).*(A Class|CLA\b|GLA|B Class|GLB).*$'),
(NULL,NULL,0,106,NULL,'Warranty',26,25,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,121,NULL,'Warranty',26,26,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,137,NULL,'Warranty',26,27,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,154,NULL,'Warranty',26,28,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,175,NULL,'Warranty',26,29,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,193,NULL,'Warranty',26,30,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,232,NULL,'Warranty',26,31,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,249,NULL,'Warranty',26,32,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,267,NULL,'Warranty',26,33,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,290,NULL,'Warranty',26,34,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,332,NULL,'Warranty',26,35,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,358,NULL,'Warranty',26,36,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,358,NULL,'Warranty',26,37,'Mercedes-Benz','^(?!.*AMG).*(C Class|GLC|EQC|SLC|SLK).*$'),
(NULL,NULL,0,115,NULL,'Warranty',26,25,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,132,NULL,'Warranty',26,26,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,151,NULL,'Warranty',26,27,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,170,NULL,'Warranty',26,28,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,194,NULL,'Warranty',26,29,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,214,NULL,'Warranty',26,30,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,258,NULL,'Warranty',26,31,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,278,NULL,'Warranty',26,32,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,298,NULL,'Warranty',26,33,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,325,NULL,'Warranty',26,34,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,373,NULL,'Warranty',26,35,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,402,NULL,'Warranty',26,36,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,402,NULL,'Warranty',26,37,'Mercedes-Benz','^(?!.*AMG).*(E Class|GLE|CLK).*$'),
(NULL,NULL,0,122,NULL,'Warranty',26,25,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,143,NULL,'Warranty',26,26,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,164,NULL,'Warranty',26,27,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,188,NULL,'Warranty',26,28,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,218,NULL,'Warranty',26,29,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,259,NULL,'Warranty',26,30,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,317,NULL,'Warranty',26,31,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,358,NULL,'Warranty',26,32,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,421,NULL,'Warranty',26,33,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,447,NULL,'Warranty',26,34,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,509,NULL,'Warranty',26,35,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,518,NULL,'Warranty',26,36,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,528,NULL,'Warranty',26,37,'Mercedes-Benz','^(?!.*AMG).*(S Class|G Class|GLS|GL\b|SL\b|CL\b|V Class|R Class|M Class).*$'),
(NULL,NULL,0,24,NULL,'Warranty',26,25,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,38,NULL,'Warranty',26,26,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,54,NULL,'Warranty',26,27,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,64,NULL,'Warranty',26,28,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,74,NULL,'Warranty',26,29,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,84,NULL,'Warranty',26,30,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,95,NULL,'Warranty',26,31,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,103,NULL,'Warranty',26,32,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,111,NULL,'Warranty',26,33,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,120,NULL,'Warranty',26,34,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,130,NULL,'Warranty',26,35,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,141,NULL,'Warranty',26,36,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,184,NULL,'Warranty',26,37,'Smart','ForFour|ForTwo'),
(NULL,NULL,0,320,NULL,'Warranty',38,0,'BMW','.*'),
(NULL,NULL,0,300,NULL,'Warranty',38,0,'CUPRA','.*'),
(NULL,NULL,0,300,NULL,'Warranty',38,0,'SEAT','.*'),
(NULL,NULL,0,300,NULL,'Warranty',38,0,'Volkswagen','.*'),
(NULL,NULL,0,198,NULL,'Warranty',38,0,'Kia','.*'),
(NULL,NULL,0,287,NULL,'Warranty',38,0,'Mercedes-Benz','^(?!.*AMG)(?=.*\bA Class\b).*$ '),
(NULL,NULL,0,358,NULL,'Warranty',38,0,'Mercedes-Benz','^(?!.*AMG)(?=.*\bC Class\b).*$ '),
(NULL,NULL,0,402,NULL,'Warranty',38,0,'Mercedes-Benz','^(?!.*AMG)(?=.*\bE Class\b).*$ '),
(NULL,NULL,0,528,NULL,'Warranty',38,0,'Mercedes-Benz','^(?!.*AMG)(?=.*\bS Class\b).*$ '),
(NULL,NULL,0,125,NULL,'Warranty',35,0,NULL,'.*'),
(NULL,NULL,0,125,NULL,'Warranty',35,0,'DEFAULT','.*')

GO

--from TargetMargins
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES
(NULL,10000,1,7.2,NULL,'Margin',35,NULL,NULL,NULL),
(NULL,15000,1,7.2,NULL,'Margin',35,NULL,NULL,NULL),
(NULL,20000,1,7.2,NULL,'Margin',35,NULL,NULL,NULL),
(NULL,25000,1,7.2,NULL,'Margin',35,NULL,NULL,NULL),
(NULL,30000,1,7.2,NULL,'Margin',35,NULL,NULL,NULL),
(NULL,999999,1,7.2,NULL,'Margin',35,NULL,NULL,NULL)

GO

--from Auctionfee
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES
('Motorway',2499.9,0,269,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',4999.9,0,289,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',7499.9,0,309,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',9999.9,0,339,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',14999.9,0,369,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',19999.9,0,389,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',29999.9,0,439,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',39999.9,0,489,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',49999.9,0,519,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',59999.9,0,609,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',69999.9,0,699,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',79999.9,0,789,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',89999.9,0,879,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',99999.9,0,919,NULL,'Auctionfee',29,NULL,NULL,NULL),
('Motorway',999999999,0,1019,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',2499.9,0,199,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',4999.9,0,249,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',7499.9,0,269,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',9999.9,0,299,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',14999.9,0,319,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',19999.9,0,339,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',29999.9,0,389,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',39999.9,0,449,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',49999.9,0,499,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',59999.9,0,599,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',69999.9,0,699,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',79999.9,0,799,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',89999.9,0,899,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',99999.9,0,929,NULL,'Auctionfee',29,NULL,NULL,NULL),
('CarWow',999999999,0,999,NULL,'Auctionfee',29,NULL,NULL,NULL)
GO

--from Paint
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES 
(NULL,NULL,0,250,82,'Paint',16,NULL,NULL,NULL)
GO

--from Delivery
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES 
(NULL,NULL,0,100,82,'Delivery',16,NULL,NULL,NULL)

GO


--from Other
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES 
(NULL,NULL,0,200,282,'Other',35,NULL,NULL,NULL),
(NULL,NULL,0,200,283,'Other',35,NULL,NULL,NULL),
(NULL,NULL,0,200,284,'Other',35,NULL,NULL,NULL),
(NULL,NULL,0,200,297,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,200,298,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,200,299,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,200,300,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,301,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,302,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,303,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,304,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,305,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,306,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,307,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,308,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,311,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,312,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,313,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,314,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,315,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,200,316,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,200,317,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,325,318,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,325,319,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,325,320,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,325,321,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,325,322,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,323,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,324,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,325,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,326,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,327,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,328,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,329,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,330,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,331,'Other',38,NULL,NULL,NULL),
(NULL,NULL,0,175,332,'Other',38,NULL,NULL,NULL)
GO

--Autoprice- Margings
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES
(NULL,NULL,0,2000,NULL,'Margin',6,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',7,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',8,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',9,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',10,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',11,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',12,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',13,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',14,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',15,NULL,NULL,NULL),
(NULL,NULL,0,1000,NULL,'Margin',16,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',17,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',18,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',19,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',20,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',21,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',22,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',23,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',24,NULL,NULL,NULL),
(NULL,NULL,0,2000,231,'Margin',25,NULL,NULL,NULL),
(NULL,NULL,0,3000,232,'Margin',25,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',26,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',28,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',29,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',30,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',31,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',32,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',33,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',34,NULL,NULL,NULL),
(NULL,NULL,0,1300,NULL,'Margin',36,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',37,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',38,NULL,NULL,NULL),
(NULL,NULL,0,2000,NULL,'Margin',39,NULL,NULL,NULL)

GO


--Autoprice - AddtionsMech
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES
(NULL,NULL,0,450,NULL,'AddtionalMech',16,NULL,NULL,NULL),
(NULL,NULL,0,150,NULL,'AddtionalMech',34,NULL,NULL,NULL),
(NULL,NULL,0,550,NULL,'AddtionalMech',35,NULL,NULL,NULL),
(NULL,NULL,0,500,NULL,'AddtionalMech',36,NULL,NULL,NULL)
GO

--Autoprice - Auction Fee
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES
(NULL,NULL,0,400,82,'Auctionfee',16,NULL,NULL,NULL)

GO


-------------------------------------------------------------------------------------------------
--Spain
--Nothing to do





-------------------------------------------------------------------------------------------------
----------------------------------------------Sytner  Database----------------------------------------------

INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES
(NULL,NULL,0,300,241,'Other',27,NULL,NULL,NULL),
(NULL,NULL,0,300,242,'Other',27,NULL,NULL,NULL),
(NULL,NULL,0,300,252,'Other',27,NULL,NULL,NULL),
(NULL,NULL,0,300,257,'Other',27,NULL,NULL,NULL),
(NULL,NULL,0,300,258,'Other',27,NULL,NULL,NULL),
(NULL,NULL,0,300,259,'Other',27,NULL,NULL,NULL),
(NULL,NULL,0,300,260,'Other',27,NULL,NULL,NULL),
(NULL,NULL,0,300,261,'Other',27,NULL,NULL,NULL)
GO

--Sytner - TargetMargins
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES
(NULL,NULL,0,1500,NULL,'Margin',27,NULL,NULL,NULL)

--Sytner - Addtions Mech
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES
(NULL,NULL,0,1100,NULL,'AddtionalMech',27,NULL,NULL,NULL)
GO

-----------------------------------------------------------------------------------------------
----------------------------------------------Vindis  Database----------------------------------------------


--Vindis - Margins
INSERT INTO autoprice.BuyingCostsSets (TemplateType, ValuationUpTo, IsPercent, Amount, RetailerSite_Id, CostType, DealerGroup_Id, FromMonths, MakePattern, ModelPattern)
VALUES
(NULL,NULL,0,2000,NULL,'Margin',3,NULL,NULL,NULL)
GO

