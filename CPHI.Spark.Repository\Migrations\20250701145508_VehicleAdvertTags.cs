﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CPHI.Spark.Repository.Migrations
{
    /// <inheritdoc />
    public partial class VehicleAdvertTags : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Tags",
                schema: "autoprice",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DealerGroupId = table.Column<int>(type: "int", nullable: false),
                    Label = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    StrategyImpactPct = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    StrategyImpactAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Tags", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Tags_DealerGroups_DealerGroupId",
                        column: x => x.DealerGroupId,
                        principalTable: "DealerGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VehicleAdvertTags",
                schema: "autoprice",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TagId = table.Column<int>(type: "int", nullable: false),
                    VehicleAdvertId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedById = table.Column<int>(type: "int", nullable: true),
                    UpdatedById = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleAdvertTags", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VehicleAdvertTags_People_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "People",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VehicleAdvertTags_People_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "People",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_VehicleAdvertTags_Tags_TagId",
                        column: x => x.TagId,
                        principalSchema: "autoprice",
                        principalTable: "Tags",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VehicleAdvertTags_VehicleAdverts_VehicleAdvertId",
                        column: x => x.VehicleAdvertId,
                        principalSchema: "autoprice",
                        principalTable: "VehicleAdverts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Tags_DealerGroupId",
                schema: "autoprice",
                table: "Tags",
                column: "DealerGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleAdvertTags_CreatedById",
                schema: "autoprice",
                table: "VehicleAdvertTags",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleAdvertTags_TagId",
                schema: "autoprice",
                table: "VehicleAdvertTags",
                column: "TagId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleAdvertTags_UpdatedById",
                schema: "autoprice",
                table: "VehicleAdvertTags",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleAdvertTags_VehicleAdvertId",
                schema: "autoprice",
                table: "VehicleAdvertTags",
                column: "VehicleAdvertId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "VehicleAdvertTags",
                schema: "autoprice");

            migrationBuilder.DropTable(
                name: "Tags",
                schema: "autoprice");
        }
    }
}
