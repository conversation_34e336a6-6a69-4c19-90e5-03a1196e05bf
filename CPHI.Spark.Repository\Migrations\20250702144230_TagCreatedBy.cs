﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CPHI.Spark.Repository.Migrations
{
    /// <inheritdoc />
    public partial class TagCreatedBy : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "CreatedById",
                schema: "autoprice",
                table: "Tags",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                schema: "autoprice",
                table: "Tags",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "UpdatedById",
                schema: "autoprice",
                table: "Tags",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedDate",
                schema: "autoprice",
                table: "Tags",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.CreateIndex(
                name: "IX_Tags_CreatedById",
                schema: "autoprice",
                table: "Tags",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Tags_UpdatedById",
                schema: "autoprice",
                table: "Tags",
                column: "UpdatedById");

            migrationBuilder.AddForeignKey(
                name: "FK_Tags_People_CreatedById",
                schema: "autoprice",
                table: "Tags",
                column: "CreatedById",
                principalTable: "People",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Tags_People_UpdatedById",
                schema: "autoprice",
                table: "Tags",
                column: "UpdatedById",
                principalTable: "People",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Tags_People_CreatedById",
                schema: "autoprice",
                table: "Tags");

            migrationBuilder.DropForeignKey(
                name: "FK_Tags_People_UpdatedById",
                schema: "autoprice",
                table: "Tags");

            migrationBuilder.DropIndex(
                name: "IX_Tags_CreatedById",
                schema: "autoprice",
                table: "Tags");

            migrationBuilder.DropIndex(
                name: "IX_Tags_UpdatedById",
                schema: "autoprice",
                table: "Tags");

            migrationBuilder.DropColumn(
                name: "CreatedById",
                schema: "autoprice",
                table: "Tags");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                schema: "autoprice",
                table: "Tags");

            migrationBuilder.DropColumn(
                name: "UpdatedById",
                schema: "autoprice",
                table: "Tags");

            migrationBuilder.DropColumn(
                name: "UpdatedDate",
                schema: "autoprice",
                table: "Tags");
        }
    }
}
