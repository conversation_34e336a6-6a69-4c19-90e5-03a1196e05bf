﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CPHI.Spark.Repository.Migrations
{
    /// <inheritdoc />
    public partial class VehicleAdvertUnique : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_VehicleAdvertTags_VehicleAdvertId",
                schema: "autoprice",
                table: "VehicleAdvertTags");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleAdvertTags_VehicleAdvertId_TagId",
                schema: "autoprice",
                table: "VehicleAdvertTags",
                columns: new[] { "VehicleAdvertId", "TagId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_VehicleAdvertTags_VehicleAdvertId_TagId",
                schema: "autoprice",
                table: "VehicleAdvertTags");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleAdvertTags_VehicleAdvertId",
                schema: "autoprice",
                table: "VehicleAdvertTags",
                column: "VehicleAdvertId");
        }
    }
}
