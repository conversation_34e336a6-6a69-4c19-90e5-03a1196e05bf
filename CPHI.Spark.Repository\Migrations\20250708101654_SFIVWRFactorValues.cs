﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CPHI.Spark.Repository.Migrations
{
    /// <inheritdoc />
    public partial class SFIVWRFactorValues : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "FactorItemValue",
                schema: "autoprice",
                table: "StrategyFactorItemVehicleWebsiteRatings",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "FactorItemValueAmount",
                schema: "autoprice",
                table: "StrategyFactorItemVehicleWebsiteRatings",
                type: "decimal(18,2)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FactorItemValue",
                schema: "autoprice",
                table: "StrategyFactorItemVehicleWebsiteRatings");

            migrationBuilder.DropColumn(
                name: "FactorItemValueAmount",
                schema: "autoprice",
                table: "StrategyFactorItemVehicleWebsiteRatings");
        }
    }
}
