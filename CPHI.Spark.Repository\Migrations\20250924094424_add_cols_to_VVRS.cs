﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CPHI.Spark.Repository.Migrations
{
    /// <inheritdoc />
    public partial class add_cols_to_VVRS : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AdditionalMech",
                schema: "autoprice",
                table: "VehicleValuations");

            migrationBuilder.DropColumn(
                name: "Delivery",
                schema: "autoprice",
                table: "VehicleValuations");

            migrationBuilder.DropColumn(
                name: "Fee",
                schema: "autoprice",
                table: "VehicleValuations");

            migrationBuilder.DropColumn(
                name: "Other",
                schema: "autoprice",
                table: "VehicleValuations");

            migrationBuilder.DropColumn(
                name: "Paint",
                schema: "autoprice",
                table: "VehicleValuations");

            migrationBuilder.DropColumn(
                name: "Profit",
                schema: "autoprice",
                table: "VehicleValuations");

            migrationBuilder.AddColumn<decimal>(
                name: "TargetAdditionalMech",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TargetAuctionFee",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TargetDelivery",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TargetMargin",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TargetOtherCost",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TargetPaintPrep",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites",
                type: "decimal(18,2)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TargetAdditionalMech",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites");

            migrationBuilder.DropColumn(
                name: "TargetAuctionFee",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites");

            migrationBuilder.DropColumn(
                name: "TargetDelivery",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites");

            migrationBuilder.DropColumn(
                name: "TargetMargin",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites");

            migrationBuilder.DropColumn(
                name: "TargetOtherCost",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites");

            migrationBuilder.DropColumn(
                name: "TargetPaintPrep",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites");

            migrationBuilder.AddColumn<int>(
                name: "AdditionalMech",
                schema: "autoprice",
                table: "VehicleValuations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Delivery",
                schema: "autoprice",
                table: "VehicleValuations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Fee",
                schema: "autoprice",
                table: "VehicleValuations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Other",
                schema: "autoprice",
                table: "VehicleValuations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Paint",
                schema: "autoprice",
                table: "VehicleValuations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Profit",
                schema: "autoprice",
                table: "VehicleValuations",
                type: "int",
                nullable: true);
        }
    }
}
