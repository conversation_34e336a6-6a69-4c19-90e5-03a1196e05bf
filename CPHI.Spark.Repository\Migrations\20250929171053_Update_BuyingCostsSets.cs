﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CPHI.Spark.Repository.Migrations
{
    /// <inheritdoc />
    public partial class Update_BuyingCostsSets : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BuyingCostsSets_RetailerSites_RetailerSite_Id",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.DropTable(
                name: "FixedCostPreps",
                schema: "autoprice");

            migrationBuilder.DropTable(
                name: "FixedCostWarranties",
                schema: "autoprice");

            migrationBuilder.DropTable(
                name: "TargetMargins",
                schema: "autoprice");

            migrationBuilder.DropColumn(
                name: "Warranty",
                schema: "autoprice",
                table: "VehicleValuations");

            migrationBuilder.DropColumn(
                name: "TargetAdditionalMech",
                schema: "autoprice",
                table: "RetailerSites");

            migrationBuilder.DropColumn(
                name: "TargetAuctionFee",
                schema: "autoprice",
                table: "RetailerSites");

            migrationBuilder.DropColumn(
                name: "TargetDelivery",
                schema: "autoprice",
                table: "RetailerSites");

            migrationBuilder.DropColumn(
                name: "TargetMargin",
                schema: "autoprice",
                table: "RetailerSites");

            migrationBuilder.DropColumn(
                name: "TargetOtherCost",
                schema: "autoprice",
                table: "RetailerSites");

            migrationBuilder.DropColumn(
                name: "TargetPaintPrep",
                schema: "autoprice",
                table: "RetailerSites");

            migrationBuilder.DropColumn(
                name: "TargetWarrantyFee",
                schema: "autoprice",
                table: "RetailerSites");

            migrationBuilder.DropColumn(
                name: "AuctionFeeAmount",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.DropColumn(
                name: "IsAuctionFeePercent",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.DropColumn(
                name: "IsMarginPercent",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.DropColumn(
                name: "MarginAmount",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.RenameColumn(
                name: "PrepAmount",
                schema: "autoprice",
                table: "BuyingCostsSets",
                newName: "Amount");

            migrationBuilder.RenameColumn(
                name: "IsPrepPercent",
                schema: "autoprice",
                table: "BuyingCostsSets",
                newName: "IsPercent");

            migrationBuilder.AddColumn<decimal>(
                name: "Warranty",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "ValuationUpTo",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AlterColumn<string>(
                name: "TemplateType",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<int>(
                name: "RetailerSite_Id",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<string>(
                name: "CostType",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DealerGroup_Id",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "FromMonths",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MakePattern",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ModelPattern",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_BuyingCostsSets_DealerGroup_Id",
                schema: "autoprice",
                table: "BuyingCostsSets",
                column: "DealerGroup_Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BuyingCostsSets_DealerGroups_DealerGroup_Id",
                schema: "autoprice",
                table: "BuyingCostsSets",
                column: "DealerGroup_Id",
                principalTable: "DealerGroups",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_BuyingCostsSets_RetailerSites_RetailerSite_Id",
                schema: "autoprice",
                table: "BuyingCostsSets",
                column: "RetailerSite_Id",
                principalSchema: "autoprice",
                principalTable: "RetailerSites",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BuyingCostsSets_DealerGroups_DealerGroup_Id",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.DropForeignKey(
                name: "FK_BuyingCostsSets_RetailerSites_RetailerSite_Id",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.DropIndex(
                name: "IX_BuyingCostsSets_DealerGroup_Id",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.DropColumn(
                name: "Warranty",
                schema: "autoprice",
                table: "VehicleValuationRatingBySites");

            migrationBuilder.DropColumn(
                name: "CostType",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.DropColumn(
                name: "DealerGroup_Id",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.DropColumn(
                name: "FromMonths",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.DropColumn(
                name: "MakePattern",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.DropColumn(
                name: "ModelPattern",
                schema: "autoprice",
                table: "BuyingCostsSets");

            migrationBuilder.RenameColumn(
                name: "IsPercent",
                schema: "autoprice",
                table: "BuyingCostsSets",
                newName: "IsPrepPercent");

            migrationBuilder.RenameColumn(
                name: "Amount",
                schema: "autoprice",
                table: "BuyingCostsSets",
                newName: "PrepAmount");

            migrationBuilder.AddColumn<int>(
                name: "Warranty",
                schema: "autoprice",
                table: "VehicleValuations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TargetAdditionalMech",
                schema: "autoprice",
                table: "RetailerSites",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TargetAuctionFee",
                schema: "autoprice",
                table: "RetailerSites",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TargetDelivery",
                schema: "autoprice",
                table: "RetailerSites",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<int>(
                name: "TargetMargin",
                schema: "autoprice",
                table: "RetailerSites",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<decimal>(
                name: "TargetOtherCost",
                schema: "autoprice",
                table: "RetailerSites",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TargetPaintPrep",
                schema: "autoprice",
                table: "RetailerSites",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TargetWarrantyFee",
                schema: "autoprice",
                table: "RetailerSites",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AlterColumn<decimal>(
                name: "ValuationUpTo",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "TemplateType",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "RetailerSite_Id",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "AuctionFeeAmount",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<bool>(
                name: "IsAuctionFeePercent",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsMarginPercent",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "MarginAmount",
                schema: "autoprice",
                table: "BuyingCostsSets",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.CreateTable(
                name: "FixedCostPreps",
                schema: "autoprice",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DealerGroup_Id = table.Column<int>(type: "int", nullable: false),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    FromMonths = table.Column<int>(type: "int", nullable: false),
                    Model = table.Column<string>(type: "nvarchar(120)", maxLength: 120, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FixedCostPreps", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FixedCostPreps_DealerGroups_DealerGroup_Id",
                        column: x => x.DealerGroup_Id,
                        principalTable: "DealerGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FixedCostWarranties",
                schema: "autoprice",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DealerGroup_Id = table.Column<int>(type: "int", nullable: false),
                    RetailerSite_Id = table.Column<int>(type: "int", nullable: true),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    FromMonths = table.Column<int>(type: "int", nullable: false),
                    Make = table.Column<string>(type: "nvarchar(120)", maxLength: 120, nullable: true),
                    ModelPattern = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FixedCostWarranties", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FixedCostWarranties_DealerGroups_DealerGroup_Id",
                        column: x => x.DealerGroup_Id,
                        principalTable: "DealerGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_FixedCostWarranties_RetailerSites_RetailerSite_Id",
                        column: x => x.RetailerSite_Id,
                        principalSchema: "autoprice",
                        principalTable: "RetailerSites",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "TargetMargins",
                schema: "autoprice",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DealerGroupId = table.Column<int>(type: "int", nullable: false),
                    RetailerSiteId = table.Column<int>(type: "int", nullable: true),
                    DaysToSell = table.Column<int>(type: "int", nullable: true),
                    FuelTypePattern = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MakePattern = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Margin = table.Column<int>(type: "int", nullable: true),
                    MarginPct = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    MaxAgeMonths = table.Column<int>(type: "int", nullable: true),
                    MaxValue = table.Column<int>(type: "int", nullable: true),
                    MinAgeMonths = table.Column<int>(type: "int", nullable: true),
                    MinValue = table.Column<int>(type: "int", nullable: true),
                    ModelPattern = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TargetMargins", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TargetMargins_DealerGroups_DealerGroupId",
                        column: x => x.DealerGroupId,
                        principalTable: "DealerGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TargetMargins_RetailerSites_RetailerSiteId",
                        column: x => x.RetailerSiteId,
                        principalSchema: "autoprice",
                        principalTable: "RetailerSites",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_FixedCostPreps_DealerGroup_Id_Model_FromMonths",
                schema: "autoprice",
                table: "FixedCostPreps",
                columns: new[] { "DealerGroup_Id", "Model", "FromMonths" });

            migrationBuilder.CreateIndex(
                name: "IX_FixedCostWarranties_DealerGroup_Id",
                schema: "autoprice",
                table: "FixedCostWarranties",
                column: "DealerGroup_Id");

            migrationBuilder.CreateIndex(
                name: "IX_FixedCostWarranties_RetailerSite_Id",
                schema: "autoprice",
                table: "FixedCostWarranties",
                column: "RetailerSite_Id");

            migrationBuilder.CreateIndex(
                name: "IX_TargetMargins_DealerGroupId",
                schema: "autoprice",
                table: "TargetMargins",
                column: "DealerGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_TargetMargins_RetailerSiteId",
                schema: "autoprice",
                table: "TargetMargins",
                column: "RetailerSiteId");

            migrationBuilder.AddForeignKey(
                name: "FK_BuyingCostsSets_RetailerSites_RetailerSite_Id",
                schema: "autoprice",
                table: "BuyingCostsSets",
                column: "RetailerSite_Id",
                principalSchema: "autoprice",
                principalTable: "RetailerSites",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
