﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CPHI.Spark.Repository.Migrations
{
    /// <inheritdoc />
    public partial class added_global_param_creation_step : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
            IF EXISTS (SELECT 1 FROM DealerGroups WHERE Id = 1)
BEGIN
	IF NOT EXISTS (SELECT 1 FROM GlobalParams WHERE Description = 'ShowManualPriceChangeOption' and DealerGroup_Id = 1)
	BEGIN
		INSERT INTO GlobalParams (Description, textValue, value, DealerGroup_Id)
		values
		('ShowManualPriceChangeOption','True',0,1)
	END
END;
IF EXISTS (SELECT 1 FROM DealerGroups WHERE Id = 36)
BEGIN
	IF NOT EXISTS (SELECT 1 FROM GlobalParams WHERE Description = 'ShowManualPriceChangeOption' and DealerGroup_Id = 36)
	BEGIN
		INSERT INTO GlobalParams (Description, textValue, value, DealerGroup_Id)
		values
		('ShowManualPriceChangeOption','True',0,36)
	END
END
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
