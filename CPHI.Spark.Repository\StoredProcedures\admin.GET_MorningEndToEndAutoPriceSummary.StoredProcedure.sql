CREATE OR ALTER PROCEDURE [admin].[GET_MorningEndToEndAutoPriceSummary]  
AS   
BEGIN  
SET NOCOUNT ON  


---------------------------------------
-- 1. NOTE DOWN EXPECTED JOBS
---------------------------------------
    
CREATE TABLE #ExpectedJobsEveryone(  
[Job] [nvarchar](50) NULL,
DealerGroupName varchar(50)
)

--NOTE DOWN ALL JOBS THAT EVERYONE HAS
INSERT INTO #ExpectedJobsEveryone (Job)
VALUES
( 'FetchAdverts'),
-- ( 'PopulateCacheTable'),
( 'CountPriceChanges'),

( 'LeavingItems'),
( 'StrategyPrices'),
( 'UpdateDaysToSell'),
( 'CompetitorInformation'),
( 'GlobalParams'),
( 'TriggerCache'),
--( 'GenerateAutoChanges'),
--( 'SendEmailWeekday'),
( 'DeleteOldValuations')


--NOTE WHO EVERYONE IS
CREATE TABLE #Everyone(
DealerGroupName varchar(50)
)
INSERT INTO #Everyone (DealerGroupName)
VALUES
('V12'),
('BrindleyGroup'),
('KCSOfSurrey'),
('WaylandsGroup'),
('PentagonGroup'),
('HippoApproved'),
('LMCOfFarnham'),
('Startin'),
('SparshattsGroup'),
--('BellsCrossgar'),
--('CroxdaleGroup'),
('EMGGroup'),
('AcornGroup'),
-- ('Lithia'), removed 26/03/25
('FordsOfWinsford'),
('LSH'),
('Nuneaton'),
('SRMotorGroup'),
('CarWorld'),('WJKing'),('ArthursCars'),
('GravelHillCars'),('BigMotoringWorld'),('JCT600'),('Citygate'),
('BridgendFord')

-------------------------------------------------------------------------
--CREATE OUR FINAL TABLE OF JOBS AND POPULATE IT WITH THE ALL JOBS
-------------------------------------------------------------------------
CREATE TABLE #ExpectedJobs(  
[Job] [nvarchar](50) NULL,
DealerGroupName varchar(50)
)

INSERT INTO #ExpectedJobs (Job, DealerGroupName)
SELECT 
ej.Job,
ee.DealerGroupName
FROM #ExpectedJobsEveryone ej
LEFT JOIN #Everyone ee ON 1=1





------------------------------------------------------------------------------------------
--CAREFULLY INSERT THE JOBS THAT SPECIFICALLY RUN FOR PEOPLE BASED ON APPSETTINGS
------------------------------------------------------------------------------------------
INSERT INTO #ExpectedJobs (Job, DealerGroupName)
VALUES
('InStockNotOnPortal','WaylandsGroup'),
('CreateOptOuts','Startin'),
('LocationOptimiser: CalcStrategy','WaylandsGroup'),
('LocationOptimiser: CalcStrategy','PentagonGroup'),
('LocationOptimiser: CalcStrategy','BrindleyGroup'),
('LocationOptimiser: CalcStrategy','LSH'),
('LocalBargains','BrindleyGroup'),  
('LocalBargains','WaylandsGroup'),  
('LocalBargains','PentagonGroup'),  
('LocalBargains','Startin'),  
('LocalBargains','KCSOfSurrey'),  
('LocalBargains','EMGGroup'),  
('LocalBargains','AcornGroup'),  
('LocalBargains','LSH') ,
('LocalBargains','WJKing')  ,
('LocalBargains','ArthursCars')  ,
('LocationOptimiser: CalcStrategy','WJKing'),
('LocationOptimiser: CalcStrategy','ArthursCars')


------------------------------------------------------------------------------------------
-- Remove Valuations for CarWorld as they don't take that part
------------------------------------------------------------------------------------------
DELETE FROM #ExpectedJobs WHERE Job = 'DeleteOldValuations' AND DealerGroupName = 'CarWorld';


    
    
------------------------------------------------------------------------------------------
--Add in week email check
------------------------------------------------------------------------------------------
IF DATEPART(WEEKDAY, GETDATE()) NOT IN (1, 7) -- Not a weekend
BEGIN
  
   --Put in the weekly only jobs
   INSERT INTO #ExpectedJobs (Job, DealerGroupName)
   SELECT 
   'GenerateAutoChanges',
   DealerGroupName
   FROM #Everyone;

    --Also put in the GenerateAutoChanges
   INSERT INTO #ExpectedJobs (Job, DealerGroupName)
   SELECT 
   'SendEmailWeekday',
   DealerGroupName
   FROM #Everyone;

END;


------------------------------------------------------------------------------------------
-- Remove Daily email for JCT600 as they don't take that part
------------------------------------------------------------------------------------------
DELETE FROM #ExpectedJobs WHERE Job = 'SendEmailWeekday' AND DealerGroupName = 'JCT600';


--Weekend Autoprice
IF DATEPART(WEEKDAY, GETDATE())  IN (1, 7) -- Sunday = 1, Sat = 7
BEGIN
 

   INSERT INTO #ExpectedJobs (Job, DealerGroupName)
   Values (   'GenerateAutoChanges',   'WaylandsGroup')
   
   INSERT INTO #ExpectedJobs (Job, DealerGroupName)
   Values (   'GenerateAutoChanges',   'WaylandsGroup')


END;


DROP TABLE #ExpectedJobsEveryone;
    
------------------------------------------------------------------------------------------
-- Add in Brindley update pinewood.  Only check if yesterday was not w/e and not pub holiday
------------------------------------------------------------------------------------------
DECLARE @yesterday Date = DATEADD(day,-1,CONVERT(date,GETDATE()))
IF DATEPART(WEEKDAY, @yesterday) NOT IN (1, 7) -- Not a weekend
AND NOT EXISTS (SELECT 1 FROM PublicHolidays WHERE Date = CAST(@yesterday AS DATE))
BEGIN
   INSERT INTO #ExpectedJobs (Job, DealerGroupName)
   VALUES
   ('PinewoodPriceUpdates','BrindleyGroup');
END;

------------------------------------------------------------------------------------------
-- Add in Startin if appropriate
------------------------------------------------------------------------------------------
DECLARE @RunOnPublicHolidays BIT = 
   (SELECT TOP 1 UpdatePricesPubHolidays 
   FROM autoprice.RetailerSites 
   WHERE DealerGroup_Id = 17 AND UpdatePricesPubHolidays = 1);


------------------------------------------------------------------------------------------
-- Add in Startin send email if relevant
------------------------------------------------------------------------------------------
IF @RunOnPublicHolidays = 1
OR NOT EXISTS (SELECT 1 FROM PublicHolidays WHERE Date = @yesterday)
BEGIN
   IF 
      (
            (DATEPART(WEEKDAY, @yesterday) = 2 AND EXISTS (SELECT 1 FROM autoprice.RetailerSites WHERE DealerGroup_Id = 17 AND UpdatePricesMon = 1)) OR -- Monday
            (DATEPART(WEEKDAY, @yesterday) = 3 AND EXISTS (SELECT 1 FROM autoprice.RetailerSites WHERE DealerGroup_Id = 17 AND UpdatePricesTue = 1)) OR -- Tuesday
            (DATEPART(WEEKDAY, @yesterday) = 4 AND EXISTS (SELECT 1 FROM autoprice.RetailerSites WHERE DealerGroup_Id = 17 AND UpdatePricesWed = 1)) OR -- Wednesday
            (DATEPART(WEEKDAY, @yesterday) = 5 AND EXISTS (SELECT 1 FROM autoprice.RetailerSites WHERE DealerGroup_Id = 17 AND UpdatePricesThu = 1)) OR -- Thursday
            (DATEPART(WEEKDAY, @yesterday) = 6 AND EXISTS (SELECT 1 FROM autoprice.RetailerSites WHERE DealerGroup_Id = 17 AND UpdatePricesFri = 1)) OR -- Friday
            (DATEPART(WEEKDAY, @yesterday) = 7 AND EXISTS (SELECT 1 FROM autoprice.RetailerSites WHERE DealerGroup_Id = 17 AND UpdatePricesSat = 1)) OR -- Saturday
            (DATEPART(WEEKDAY, @yesterday) = 1 AND EXISTS (SELECT 1 FROM autoprice.RetailerSites WHERE DealerGroup_Id = 17 AND UpdatePricesSun = 1))    -- Sunday
      )
   BEGIN
      INSERT INTO #ExpectedJobs (Job, DealerGroupName)
      VALUES
      ('UpdatePrices', 'Startin');
   END
END;



------------------------------------------------------------------------------------------
--Stocks job for those groups who provide stock
------------------------------------------------------------------------------------------
INSERT INTO #ExpectedJobs (Job, DealerGroupName)
VALUES
('BrindleyGroupStocksJob','BrindleyGroup'),
('WaylandsGroupStocksJob','WaylandsGroup'),
('PentagonGroupStocksJob','PentagonGroup'),
('LMCOfFarnhamStocksJob','LMCOfFarnham'),
('StartinStocksJob','Startin'),
('SparshattsGroupStocksJob','SparshattsGroup'),
('ArthursCarsStocksJob','ArthursCars'),
--('CroxdaleGroupStocksJob','CroxdaleGroup'),
('EMGGroupMantlesStocksJob','EMGGroup'),
('GravelHillCarsStocksJob','GravelHillCars'),
('EMGGroupStocksJob','EMGGroup')



------------------------------------------------------------------------------------------
--Stocks job for those who only do it weekday
------------------------------------------------------------------------------------------
--IF DATEPART(WEEKDAY, @yesterday) NOT IN (1, 7)
--BEGIN
--   INSERT INTO #ExpectedJobs (Job, DealerGroupName)
--VALUES
--   ('V12StocksJob','V12')
--END;


DROP TABLE #Everyone;

---------------------------------------
-- 2. Evaluation
---------------------------------------
--Grab all results since 2pm previous day
SELECT 
lm.Job,  
lm.ErrorCount,
dg.Name as DealerGroupName
INTO #RecentJobs
FROM LogMessages lm 
LEFT JOIN DealerGroups dg ON lm.DealerGroup_Id = dg.Id
WHERE finishDate > DATEADD(hour,14,DATEDIFF(d,0,GETDATE()-1))   
AND lm.Job != 'UpdatePrices'

-- Add UpdatePrice jobs from 8am UK time (earliest start time) onwards
-- Note we are storing as UTC so this could be 7am in logs
INSERT INTO #RecentJobs (Job,ErrorCount,DealerGroupName)
SELECT
lm.Job,  
lm.ErrorCount,
dg.Name as DealerGroupName
FROM LogMessages lm 
LEFT JOIN DealerGroups dg ON lm.DealerGroup_Id = dg.Id
WHERE finishDate > DATEADD(hour,7,DATEDIFF(d,0,GETDATE()-1))   
AND lm.Job = 'UpdatePrices'

--Add in V12 stocks job since 10am previous day
INSERT INTO #RecentJobs (Job,ErrorCount,DealerGroupName)
SELECT
lm.Job,  
lm.ErrorCount,
dg.Name as DealerGroupName
FROM LogMessages lm 
LEFT JOIN DealerGroups dg ON lm.DealerGroup_Id = dg.Id
WHERE finishDate > DATEADD(hour,10,DATEDIFF(d,0,GETDATE()-1))   
AND dg.Name = 'V12'

--Add in Starting 4pm send job since 10am previous day
INSERT INTO #RecentJobs (Job,ErrorCount,DealerGroupName)
SELECT
lm.Job,  
lm.ErrorCount,
dg.Name as DealerGroupName
FROM LogMessages lm 
LEFT JOIN DealerGroups dg ON lm.DealerGroup_Id = dg.Id
WHERE finishDate > DATEADD(hour,16,DATEDIFF(d,0,GETDATE()-1))   
AND dg.Name = 'Startin'

--Build up results
SELECT 
CONCAT(dg.Name, ':',e.Job) as Job,  
IIF ((lm.Job IS NULL), 'Not Found', IIF(lm.ErrorCount>0,'Errors', 'Ok')) as Status
INTO #Results  
FROM #ExpectedJobs e  
LEFT JOIN #RecentJobs lm on lm.Job = e.Job AND e.DealerGroupName = lm.DealerGroupName
LEFT JOIN DealerGroups dg on e.DealerGroupName = dg.Name


--Report results as a single record table
--SELECT STRING_AGG(Job,', ') as FailedList, 'Autoprice - Morning end to end summary' as emailSubject FROM #Results where Status <> 'Ok' 
SELECT 
   STUFF((
      SELECT CHAR(13) + CHAR(10) + Job
      FROM #Results
      WHERE Status <> 'Ok'
      FOR XML PATH(''), TYPE
   ).value('.', 'NVARCHAR(MAX)'), 1, 2, '') AS FailedList,
   'Autoprice - Morning end to end summary' AS emailSubject;


    DROP TABLE #RecentJobs
    DROP TABLE #ExpectedJobs  
    DROP TABLE #Results  

END  
  
GO