CREATE OR ALTER PROCEDURE [autoprice].[GET_LeavingVehicleItems]
(
		 @chosenRetailerSiteIds varchar(max),
		 @startDate Date,
		 @endDate Date,
		 @includeNewVehicles bit,
		 @includeUsedVehicles bit,
		 @includeLCVs bit
)
  
AS  
BEGIN  

SELECT Value as Id INTO #selectedRetailerSites from STRING_SPLIT(@chosenRetailerSiteIds,',')


SET NOCOUNT ON;  

SELECT
firstsnap.id as FirstSnapId,
lastsnap.id as LastSnapId,
r.Description as Region,
rs.Name as RetailerSiteName,
si.Id as SiteId,
ads.BodyType,
ads.FuelType,
ads.TransmissionType,
ads.Make,
ads.Model,
YEAR(ads.FirstRegisteredDate) as RegYear,
ads.VehicleReg,
ads.DerivativeId,
ads.Derivative,
ads.FirstRegisteredDate,
firstsnap.SnapshotDate as ListedDate,
lastsnap.SnapshotDate as RemovedDate,
lastsnap.OdometerReadingMiles as Mileage,
DATEDIFF(day,firstsnap.SnapshotDate,lastsnap.SnapshotDate) + 1 as DaysListed,
COALESCE(firstAdsnap.SuppliedPrice,0) + COALESCE(firstAdsnap.AdminFee,0) as FirstPrice,
COALESCE(
    NULLIF(firstAdsnap.ValuationAdjRetail, 0),
    NULLIF(firstAdsnap.ValuationMktAvRetail, 0),
    NULLIF(firstAdsnap.ValuationAdjRetailExVat, 0),
    NULLIF(firstAdsnap.ValuationMktAvRetailExVat, 0),
    0
) AS FirstValuation,

COALESCE(lastsnap.SuppliedPrice,0) + COALESCE(lastsnap.AdminFee,0) as LastPrice,
COALESCE(
    NULLIF(lastsnap.ValuationAdjRetail, 0),
    NULLIF(lastsnap.ValuationMktAvRetail, 0),
    NULLIF(lastsnap.ValuationAdjRetailExVat, 0),
    NULLIF(lastsnap.ValuationMktAvRetailExVat, 0),
    0
) AS LastValuation,

lastsnap.PriceIndicatorRatingAtCurrentSelling as LastPriceIndicator,
lastsnap.RetailRating as LastRetailRating,
ads.Id as AdvertId,
ads.DaysOnStrategy,
ads.DaysOptedOut,
COALESCE(vt.Description,'Unknown Type') as VehicleType, 
firstAdsnap.RetailDaysToSellAtValuation as FirstRetailDaysToSell,
rci.IsSold as RC_IsSold,
rcsnaps.DaysListed as RC_DaysListed,
rcsnaps.Price as RC_Price,
rcsnaps.SellerName as RC_SellerName,
rcsnaps.Segment as RC_Segment,
rcsnaps.Valuation as RC_Valuation,
CASE
	WHEN rci.IsSold = 1 THEN rcsnaps.SnapshotDate 
	ELSE NULL
END as RC_SoldDate,
CASE
	WHEN tps.Id IS NULL THEN 0
	WHEN COALESCE(lastsnap.SuppliedPrice,0) + COALESCE(lastsnap.AdminFee,0) > COALESCE(lastsnap.ValuationAdjRetail, lastsnap.ValuationMktAvRetail,lastsnap.ValuationAdjRetailExVat, lastsnap.ValuationMktAvRetailExVat,0) * tps.MarginPercentage - tps.MarginAmount THEN 1
	ELSE 0
END as DidBeatWholesaleTarget,
ads.DateOnForecourt,
 ads.CreatedInSparkDate

FROM autoprice.VehicleAdverts ads 
INNER JOIN autoprice.VehicleAdvertSnapshots firstsnap on firstsnap.id = ads.FirstVehicleAdvertSnapshotId
INNER JOIN autoprice.VehicleAdvertSnapshots lastsnap on lastsnap.id = ads.LastListedSnapshotId
INNER JOIN autoprice.VehicleAdvertSnapshots firstAdSnap on firstAdSnap.id = ads.FirstListedSnapshotId
INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
INNER JOIN sites si on si.Id = rs.Site_Id
INNER JOIN Regions r on r.id = si.Region_Id
INNER JOIN #selectedRetailerSites srs on srs.id = rs.Id
LEFT JOIN Stocks stks ON  stks.Id = ads.Stock_Id   
LEFT JOIN dbo.VehicleTypes vt on vt.Id = stks.VehicleType_Id  
LEFT JOIN autoprice.RetailCustomerVehicleSummaryItems rci on rci.VehicleAdvert_Id = ads.id AND rs.TrackLeavingVehicles = 1 --only include 
LEFT JOIN autoprice.RetailCustomerVehicleSnapshots rcsnaps on rcsnaps.id = rci.LastRetailCustomerVehicleSnapshot_Id
LEFT JOIN autoprice.TradePriceSettings tps on tps.RetailerSiteId = rs.Id
WHERE ads.HasLeft = 1
AND lastsnap.SnapshotDate >= @startDate
AND lastSnap.SnapshotDate <= @endDate
AND 
(
	(@includeNewVehicles = 1 AND ads.OwnershipCondition = 'New')
	OR
	(@includeUsedVehicles = 1 AND ads.OwnershipCondition = 'Used')
)
AND rs.IsActive = 1
AND (@includeLCVs = 1 OR ads.VehicleType != 'Van')
AND 
	(
		lastsnap.LifecycleStatus != 'IN STOCK NOT ON PORTAL' OR	
		si.DealerGroup_Id = 28 --Enterprise
	)

DROP TABLE #selectedRetailerSites

END
GO
