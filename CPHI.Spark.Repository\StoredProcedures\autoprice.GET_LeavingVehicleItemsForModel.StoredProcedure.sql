CREATE OR ALTER PROCEDURE [autoprice].[GET_LeavingVehicleItemsForModel]
(
		 @chosenRetailerSiteIds varchar(max),
		 @startDate Date,
		 @endDate Date,
		 @includeNewVehicles bit ,
		 @includeUsedVehicles bit ,
		 @model varchar(100)
)
  
AS  
BEGIN  

SELECT Value as Id INTO #selectedRetailerSites from STRING_SPLIT(@chosenRetailerSiteIds,',')


SET NOCOUNT ON;  

SELECT
firstsnap.id as FirstSnapId,
lastsnap.id as LastSnapId,
r.Description as Region,
rs.Name as RetailerSiteName,
rs.RetailerId as RetailerId,
rs.Id as RetailerSiteId,
ads.BodyType,
ads.FuelType,
ads.TransmissionType,
ads.Make,
ads.Model,
YEAR(ads.FirstRegisteredDate) as RegYear,
ads.VehicleReg,
ads.DerivativeId,
ads.Derivative,
firstsnap.SnapshotDate as ListedDate,
lastsnap.SnapshotDate as RemovedDate,
lastsnap.OdometerReadingMiles as Mileage,
CASE 
        WHEN CHARINDEX('|', ads.ImageURLs) = 0 THEN ads.ImageURLs 
        ELSE LEFT(ads.ImageURLs, CHARINDEX('|', ads.ImageURLs) - 1)
    END as ImageURL,
ads.CreatedInSparkDate,
firstsnap.TotalPrice as FirstPrice,
firstsnap.ValuationMktAvRetail as FirstValuation,
lastsnap.TotalPrice as LastPrice,
lastsnap.ValuationMktAvRetail as LastValuation,
lastsnap.PriceIndicatorRatingAtCurrentSelling as LastPriceIndicator,
lastsnap.RetailRating as LastRetailRating,
ads.Id as AdvertId,
firstsnap.RetailDaysToSellAtValuation as FirstRetailDaysToSell,
si.Id as SiteId
FROM autoprice.VehicleAdverts ads
INNER JOIN autoprice.VehicleAdvertSnapshots firstsnap on firstsnap.id = ads.FirstVehicleAdvertSnapshotId
INNER JOIN autoprice.VehicleAdvertSnapshots lastsnap on lastsnap.id = ads.LatestVehicleAdvertSnapshotId
INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
INNER JOIN sites si on si.Id = rs.Site_Id
INNER JOIN Regions r on r.id = si.Region_Id
INNER JOIN #selectedRetailerSites srs on srs.id = rs.Id
WHERE ads.HasLeft = 1
AND lastsnap.SnapshotDate >= @startDate
AND lastSnap.SnapshotDate <= @endDate
AND 
(
	(@includeNewVehicles = 1 AND ads.OwnershipCondition = 'New')
	OR
	(@includeUsedVehicles = 1 AND ads.OwnershipCondition = 'Used')
)
AND ads.Model = @model
AND rs.IsActive = 1



DROP TABLE #selectedRetailerSites

END
GO
