  

CREATE  OR ALTER PROCEDURE [autoprice].[GET_SitesSettings]  
(  
 @dealerGroupId int,  
 @userId int  
)  
  
AS  
BEGIN  
  
   CREATE TABLE #siteIdsEligibleForUser  (id int);  
   INSERT INTO #siteIdsEligibleForUser(id) SELECT * from STRING_SPLIT((SELECT sites from People where Id = @UserId),',');  
  
   SELECT  
   rs.Id as RetailerSiteId,  
    rs.Name,  
   ssrs.Name AS StrategySelectionRuleSetName,  
   ssrs.Id as StrategySelectionRuleSetId,  
   ssrs.UniqueIdForDg as StrategySelectionRuleSetUniqueIdForDg,  
  
    ssrsBuying.Name AS BuyingStrategySelectionRuleSetName,  
   ssrsBuying.Id as BuyingStrategySelectionRuleSetId,  
   ssrsBuying.UniqueIdForDg as BuyingStrategySelectionRuleSetUniqueIdForDg,  
  
    ssrsBuying2.Name AS BuyingStrategySelectionRuleSet2Name,  
   ssrsBuying2.Id as BuyingStrategySelectionRuleSet2Id,  
   ssrsBuying2.UniqueIdForDg as BuyingStrategySelectionRuleSet2UniqueIdForDg,  
  
   ssrsTest.Name as TestStrategySelectionRuleSetName,  
   ssrsTest.Id as TestStrategySelectionRuleSetId,  
   ssrsTest.UniqueIdForDg as TestStrategySelectionRuleSetUniqueIdForDg,  
  
    rs.LocalBargainThreshold,  
    rs.LocationMoveFixedCostPerMove,  
    rs.LocationMovePoundPerMile,  
    rs.UpdatePricesAutomatically,  
    rs.MinimumAutoPriceDecrease,  
    rs.MinimumAutoPriceIncrease,  
   rs.MinimumAutoPricePercentDecrease * 100 as MinimumAutoPricePercentDecrease,  
    rs.MinimumAutoPricePercentIncrease * 100 as MinimumAutoPricePercentIncrease,  
    rs.UpdatePricesMon,  
    rs.UpdatePricesTue,  
    rs.UpdatePricesWed,  
    rs.UpdatePricesThu,  
    rs.UpdatePricesFri,  
    rs.UpdatePricesSat,  
    rs.UpdatePricesSun,  
    rs.UpdatePricesPubHolidays,  
    rs.WhenToActionChangesEachDay,  
   rs.LocalBargainsSearchRadius,  
   rs.LocalBargainsMinRetailRating,  
    rs.MaximumOptOutDays,  
   rs.IncludeUnPublishedAds AS IncludeUnPublishedAdsInEmailReport,  
   rs.CompetitorPlateRange,  
   rs.CompetitorSearchRange,  
   --rs.TargetAdditionalMech,  
   --rs.TargetMargin,  
   --rs.TargetPaintPrep,  
   --rs.TargetAuctionFee,  
   --rs.TargetDelivery,  
   --rs.TargetOtherCost,  
   rs.OverUnderThreshold,  
   rs.VeryThreshold,  
   rs.OverUnderIsPercent as OverUnderThresholdIsPercentage,  
   rs.VeryThresholdIsPercent as VeryOverUnderThresholdIsPercentage  
   FROM [autoprice].[RetailerSites] rs  
   INNER JOIN #siteIdsEligibleForUser seu on seu.Id = rs.Site_Id  
   INNER JOIN autoprice.StrategySelectionRuleSets ssrs on ssrs.id = rs.StrategySelectionRuleSet_Id  
      LEFT JOIN autoprice.StrategySelectionRuleSets ssrsBuying on ssrsBuying.id = rs.BuyingStrategySelectionRuleSet_Id  
      LEFT JOIN autoprice.StrategySelectionRuleSets ssrsBuying2 on ssrsBuying2.id = rs.BuyingStrategySelectionRuleSet2_Id  
   LEFT JOIN autoprice.StrategySelectionRuleSets ssrsTest on ssrsTest.id = rs.TestStrategySelectionRuleSet_Id  
   WHERE rs.DealerGroup_Id = @dealerGroupId  
  
END 
GO