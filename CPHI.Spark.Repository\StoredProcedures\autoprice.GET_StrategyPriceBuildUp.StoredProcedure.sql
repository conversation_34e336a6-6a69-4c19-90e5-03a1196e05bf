CREATE OR ALTER PROCEDURE [autoprice].[GET_StrategyPriceBuildUp]
(
    @snapshotDate DATE,
    @advertId INT
)
AS
BEGIN
    SET NOCOUNT ON;

    -- Select the ID of the closest snapshot >= @snapshotDate
    WITH ClosestSnapshot AS (
        SELECT TOP 1 snaps.Id
        FROM autoprice.VehicleAdvertSnapshots snaps
        WHERE snaps.VehicleAdvert_Id = @advertId
        AND snaps.SnapshotDate >= @snapshotDate
        AND (snaps.ValuationMktAvRetail IS NOT NULL OR snaps.ValuationMktAvRetailExVat IS NOT NULL)
        AND snaps.RetailRating IS NOT NULL
        ORDER BY snaps.SnapshotDate ASC
    )
    
    SELECT 
        versions.Name AS VersionName,
        versions.LastUpdatedDate AS VersionCreatedDate,
        factors.Name AS FactorName,
        sfi.Label AS FactorItemLabel,
        sfi.Id AS FactorItemId,
        sfi.Value AS FactorItemValue,
        sfi.ValueAmount AS FactorItemValueAmount,
        sfiv.FactorItemValue As AppliedFactorItemValue,
        sfiv.FactorItemValueAmount As AppliedFactorItemValueAmount,
        sfiv.FactorItemLabel As AppliedFactorItemLabel,
        sfiv.SourceValue,
        sfiv.Impact AS Impact,
        sset.Comment AS RuleSetComment,
        sfiv.ExtendedNotes,
        sfiv.IsRelatedToTestStrategy
    FROM autoprice.StrategyFactorItemVehicleWebsiteRatings sfiv
    INNER JOIN autoprice.VehicleAdvertSnapshots snaps ON sfiv.VehicleAdvertSnapshot_Id = snaps.Id
    INNER JOIN autoprice.VehicleAdverts ads ON ads.Id = snaps.VehicleAdvert_Id
    LEFT JOIN autoprice.StrategyFactorItems sfi ON sfi.Id = sfiv.StrategyFactorItem_Id
    LEFT JOIN autoprice.StrategyFactors factors ON factors.Id = sfi.StrategyFactor_Id
    LEFT JOIN autoprice.StrategyVersions versions ON versions.Id = factors.StrategyVersion_Id
    LEFT JOIN autoprice.StrategySelectionRuleSets sset ON sset.Id = sfiv.StrategySelectionRuleSet_Id
    WHERE snaps.Id IN (SELECT Id FROM ClosestSnapshot)
    AND ads.Id = @advertId;
END
GO
