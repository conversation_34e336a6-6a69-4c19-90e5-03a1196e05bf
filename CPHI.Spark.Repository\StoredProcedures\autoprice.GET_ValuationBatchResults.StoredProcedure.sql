CREATE OR ALTER PROCEDURE [autoprice].[GET_ValuationBatchResults]  
(  
 @batchIds varchar(max), -- '13'  
 @onlyShowBestLocation bit = 1,  
 @userEligibleRetailerSites nvarchar(max)  
)    
AS    
BEGIN    
  
SET NOCOUNT ON;  
  
 SELECT Value as Id INTO #batchIds from STRING_SPLIT(@batchIds,',');  
 SELECT  Value as Id INTO #userEligibleRetailerSites from STRING_SPLIT(@userEligibleRetailerSites,',')   
  
   
 SELECT   
 vvrs.Id,   
 vvrs.DaysToSellAtCurrentSelling,  
 vvrs.RetailRating,  
 vvrs.StrategyPrice,  
 vvrs.VehicleValuation_Id,  
 vvrs.PriceScenario,  
 vvrs.TargetMargin,  
 vvrs.TargetAdditionalMech,  
 vvrs.TargetPaintPrep,  
	vvrs.TargetAuctionFee,  
	vvrs.TargetDelivery,  
	vvrs.TargetOtherCost,
	vvrs.Warranty,
 rs.Name as RetailerName,  
 rs.Id as RetailerSiteRetailerId,  
 rs.Id as RetailerSiteId,  
 rs.Site_Id,  
 ROW_NUMBER() OVER (PARTITION BY vvrs.VehicleValuation_Id ORDER BY StrategyPrice DESC, DaysToSellAtCurrentSelling ASC, rs.Name ASC) as rowno  
 INTO #RankedValuations  
 FROM autoprice.VehicleValuationRatingBySites vvrs  
 INNER JOIN autoprice.RetailerSites rs on rs.Id = vvrs.RetailerSite_Id   
 INNER JOIN autoprice.VehicleValuations vv on vvrs.VehicleValuation_Id = vv.Id  
 INNER JOIN #batchIds b on vv.VehicleValuationBatch_Id = b.Id  
 WHERE rs.IsActive = 1  
  
 SELECT  
 vv.BatteryCapacityKWH,  
 vv.BatteryRangeMiles,  
 vv.BodyType,  
 vv.Colour,  
 vv.SpecificColour,  
 vv.Condition,  
 vv.Co2EmissionGPKM,  
 vv.Cylinders,  
 vv.Derivative,  
 vv.Doors,  
 vv.DriveType,  
 vv.Drivetrain,  
 vv.EngineCapacityCC,  
 vv.EnginePowerBHP,  
 vv.FirstRegistered as RegisteredDate,  
 vv.FuelType,  
 vv.Gears,  
 vv.Generation,  
 vv.HasBeenValued,  
 vv.make,  
 vv.Mileage,  
 vv.model,  
 vv.OwnershipCondition as VehicleOwnership,  
 vv.Sector,  
 vv.Seats,  
 vv.StartStop,  
 vv.TopSpeedMPH,  
 vv.TransmissionType,  
 vv.trim AS Trim,  
 vv.Id as ValuationId,  
 vv.ValuationMktAvPartEx,  
 vv.ValuationAdjPartEx,  
 COALESCE(NULLIF(vv.ValuationAdjPartEx,0) ,NULLIF(vv.ValuationMktAvPartEx,0))as PartEx,  
  
 vv.ValuationMktAvPrivate,  
 vv.ValuationAdjPrivate,  
 COALESCE(NULLIF(vv.ValuationAdjPrivate,0),NULLIF(vv.ValuationMktAvPrivate,0)) as PrivateValue,  
   
 vv.ValuationMktAvRetail,  
 COALESCE(NULLIF(vv.ValuationAdjRetail,0),NULLIF(vv.ValuationAdjRetailExVat,0)) as ValuationAdjRetail,  
 COALESCE(NULLIF(vv.ValuationAdjRetail,0),NULLIF(vv.ValuationMktAvRetail,0),NULLIF(vv.ValuationAdjRetailExVat,0))  as Retail,  
   
 vv.ValuationMktAvTrade,  
 COALESCE( NULLIF(vv.ValuationAdjTrade,0), NULLIF(vv.ValuationAdjTradeExVat,0)) as ValuationAdjTrade,  
 COALESCE( NULLIF(vv.ValuationAdjTrade,0),NULLIF(vv.ValuationMktAvTrade,0), NULLIF(vv.ValuationAdjTradeExVat,0)) as Trade,  
   
   
 vv.VehicleExciseDutyWithoutSupplementGBP,  
 vv.VehicleReg as Reg,  
 vv.VehicleType,  
 vv.VehicleValuationBatch_Id,  
 vv.Vin,  
 vv.ZeroToSixtyMPHSeconds,  
 vvb.Id as BatchId,  
 vvb.LastRunDate,  
 p.Name as LastRunBy,  
 rv.RetailerName as RetailerName,  
 rv.Site_Id as SiteId,  
 ISNULL(s.Description,'') AS SiteName,  
 rv.RetailerSiteRetailerId as RetailerId,  
 rv.DaysToSellAtCurrentSelling as DaysToSellAtCurrentSelling,  
 rv.RetailRating as RetailRating,  
 rv.StrategyPrice as StrategyPrice,  
 rv.rowno as LocationRank,  
 vv.SIV,  
 vv.Reference1,  
 vv.Reference2,  
 vv.Reference3,  
 vv.IsVatQualifying,  
 rv.PriceScenario,  
 vv.CapValue,  
 vv.DateOfLastService,  
 vv.EventDate,  
 vv.EventType,  
 vv.Imported,  
 vv.InsuranceCat,  
 vv.Link,  
 vv.Location,  
 vv.LotNumber,  
 vv.MileageWarranty,  
 vv.MotExpiry,  
 vv.Notes,  
 vv.NumberOfKeys,  
 vv.OnFinance,  
 vv.ReserveOrBuyItNow,  
 vv.Seller,  
 vv.ServiceHistory,  
 vv.Services,  
 vv.CapAverage,  
 vv.CapNew,  
 vv.CapBelow,  
 vv.CapRetail,  
 vv.V5Status,  
 vv.RecallStatus,  
 --SPK-4643 new ones  
 STRING_AGG(CAST(vo.Name AS VARCHAR(MAX)), ',') as SpecOptions,  
 vv.VehicleExciseDutyWithoutSupplementGBP as VEDCost,  
 vv.Owners,  
 vv.CapAverage,  
 vv.CapBelow,  
 vv.CapNew,  
 vv.CapRetail,  
 vv.CAPValuation,  
 vv.CapValue,  
 vv.Sales,  
 vv.Valet,  
 vv.SpareKey,  
 vv.MOT,  
 vv.MOTAdvisory,  
 vv.Servicing,  
 rv.TargetPaintPrep as Paint,   
 vv.Tyres,  
 rv.Warranty,  
 vv.Parts,  
 rv.TargetAdditionalMech as AdditionalMech,  
 rv.TargetAuctionFee as Fee,  
 rv.TargetDelivery as Delivery,  
 rv.TargetOtherCost as Other,  
 vv.Cost,  
 rv.TargetMargin as Profit,  
  
 vv.CompetitorCount,  
 vv.CompetitorPricePositions,  
  
 -- Added Nov 24  
 vv.Notes2,  
 vv.Notes3,  
 vv.Notes4,  
 vv.ModelYear,  
 vv.Refurbished,  
 vv.RetailPrice,  
 vv.ServicedWithinSchedule,  
 vv.Upholstery,  
 vv.ApprovedUsed,  
  
 -- Added Feb 25  
 vv.Engine,  
 vv.PreviousUse,  
  
 -- Added Jul 25  
 vv.MOTHistory  
  
 FROM autoprice.VehicleValuations vv  
 INNER JOIN autoprice.VehicleValuationBatches vvb on vvb.Id = vv.VehicleValuationBatch_Id  
 INNER JOIN People p on p.Id = vvb.LastRunBy_Id  
   
 LEFT JOIN #RankedValuations rv   
  on rv.VehicleValuation_Id = vv.Id and   
  (  
   (rv.rowno = 1 AND @onlyShowBestLocation = 1) OR   
   @onlyShowBestLocation = 0  
  )  
 INNER JOIN #batchIds bids on bids.Id = vvb.Id  
 LEFT JOIN Sites s on s.Id = rv.Site_Id  
 INNER JOIN #userEligibleRetailerSites us on us.id = rv.RetailerSiteId  
 LEFT JOIN autoprice.VehicleValuationOptions vvo on vvo.VehicleValuation_Id = vv.id  
 LEFT JOIN autoprice.VehicleOptions vo on vo.id = vvo.VehicleOption_Id  
 INNER JOIN autoprice.RetailerSites rs on rs.id = p.CurrentRetailerSite_Id  
  
 GROUP BY  
 vv.BatteryCapacityKWH,  
 vv.Engine,  
 vv.PreviousUse,  
 vv.MOTHistory,  
 vv.BatteryRangeMiles,  
 vv.BodyType,  
 vv.Colour,  
 vv.SpecificColour,  
 vv.Condition,  
 vv.Co2EmissionGPKM,  
 vv.Cylinders,  
 vv.Derivative,  
 vv.Doors,  
 vv.DriveType,  
 vv.Drivetrain,  
 vv.EngineCapacityCC,  
 vv.EnginePowerBHP,  
 vv.FirstRegistered ,  
 vv.FuelType,  
 vv.Gears,  
 vv.Generation,  
 vv.HasBeenValued,  
 vv.make,  
 vv.Mileage,  
 vv.model,  
 vv.OwnershipCondition ,  
 vv.Sector,  
 vv.Seats,  
 vv.StartStop,  
 vv.TopSpeedMPH,  
 vv.TransmissionType,  
 vv.trim ,  
 vv.Id ,  
 vv.ValuationMktAvPartEx,  
 vv.ValuationAdjPartEx,  
  COALESCE(NULLIF(vv.ValuationAdjPartEx,0) ,NULLIF(vv.ValuationMktAvPartEx,0)),  
  
 vv.ValuationMktAvPrivate,  
 vv.ValuationAdjPrivate,  
 COALESCE(NULLIF(vv.ValuationAdjPrivate,0),NULLIF(vv.ValuationMktAvPrivate,0)) ,  
   
 vv.ValuationMktAvRetail,  
 COALESCE(NULLIF(vv.ValuationAdjRetail,0),NULLIF(vv.ValuationAdjRetailExVat,0)) ,  
 COALESCE(NULLIF(vv.ValuationAdjRetail,0),NULLIF(vv.ValuationMktAvRetail,0),NULLIF(vv.ValuationAdjRetailExVat,0))  ,  
   
 vv.ValuationMktAvTrade,  
 COALESCE( NULLIF(vv.ValuationAdjTrade,0), NULLIF(vv.ValuationAdjTradeExVat,0)) ,  
 COALESCE( NULLIF(vv.ValuationAdjTrade,0),NULLIF(vv.ValuationMktAvTrade,0), NULLIF(vv.ValuationAdjTradeExVat,0)) ,  
   
 vv.VehicleExciseDutyWithoutSupplementGBP,  
 vv.VehicleReg ,  
 vv.VehicleType,  
 vv.VehicleValuationBatch_Id,  
 vv.Vin,  
 vv.ZeroToSixtyMPHSeconds,  
 vvb.Id ,  
 vvb.LastRunDate,  
 p.Name ,  
 rv.RetailerName ,  
 rv.Site_Id ,  
 ISNULL(s.Description,'') ,  
 rv.RetailerSiteRetailerId ,  
 rv.DaysToSellAtCurrentSelling ,  
 rv.RetailRating ,  
 rv.StrategyPrice ,  
 rv.rowno ,  
 vv.SIV,  
 vv.Reference1,  
 vv.Reference2,  
 vv.Reference3,  
 vv.IsVatQualifying,  
 rv.PriceScenario,  
 vv.CapValue,  
 vv.DateOfLastService,  
 vv.EventDate,  
 vv.EventType,  
 vv.Imported,  
 vv.InsuranceCat,  
 vv.Link,  
 vv.Location,  
 vv.LotNumber,  
 vv.MileageWarranty,  
 vv.MotExpiry,  
 vv.Notes,  
 vv.NumberOfKeys,  
 vv.OnFinance,  
 vv.ReserveOrBuyItNow,  
 vv.Seller,  
 vv.ServiceHistory,  
 vv.Services,  
 vv.CapAverage,  
 vv.CapNew,  
 vv.CapBelow,  
 vv.CapRetail,  
 vv.V5Status,  
 vv.RecallStatus,  
 vv.VehicleExciseDutyWithoutSupplementGBP ,  
 vv.Owners,  
 vv.CapAverage,  
 vv.CapBelow,  
 vv.CapNew,  
 vv.CapRetail,  
 vv.CAPValuation,  
 vv.CapValue,  
 vv.Sales,  
 vv.Valet,  
 vv.SpareKey,  
 vv.MOT,  
 vv.MOTAdvisory,  
 vv.Servicing,  
 rv.TargetPaintPrep,  
 vv.Tyres,  
 rv.Warranty,  
 vv.Parts,  
 rv.TargetAdditionalMech,  
 rv.TargetAuctionFee,  
 rv.TargetDelivery,  
 rv.TargetOtherCost,  
 vv.Cost,  
 rv.TargetMargin,  
 vv.CompetitorCount,  
 vv.CompetitorPricePositions,  
 vv.Notes2,  
 vv.Notes3,  
 vv.Notes4,  
 vv.ModelYear,  
 vv.Refurbished,  
 vv.RetailPrice,  
 vv.ServicedWithinSchedule,  
 vv.Upholstery,  
 vv.ApprovedUsed  
   
 DROP TABLE #batchIds  
 DROP TABLE #userEligibleRetailerSites  
 DROP TABLE #RankedValuations  
  
END  
  

GO
