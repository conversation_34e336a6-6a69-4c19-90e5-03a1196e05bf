CREATE OR ALTER PROCEDURE [autoprice].[GET_VehicleAdvertDetails]
(
    @effectiveDate DATE = NULL,
    @dealerGroupId INT,
    @advertId INT
)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Chassis NVARCHAR(100);
    
    -- Get chassis once to avoid multiple lookups
    SELECT @Chassis = Chassis 
    FROM autoprice.VehicleAdverts 
    WHERE Id = @advertId;
    
    ----------------------------------------------
    -- First price position - Simplified with single query
    ----------------------------------------------
    DECLARE @firstPricePosition INT = 
    (
        SELECT TOP 1 TotalPrice
        FROM autoprice.VehicleAdvertSnapshots rats
        WHERE rats.VehicleAdvert_Id = @advertId
        ORDER BY rats.SnapshotDate ASC
    );
    
    ----------------------------------------------
    -- Detected price changes - Using temp table with indexes
    ----------------------------------------------
    CREATE TABLE #priceChanges (Change INT);
    
    INSERT INTO #priceChanges (Change)
    SELECT CurrentPrice - PreviousPrice as Change
    FROM 
    (
        SELECT
            TotalPrice as CurrentPrice,
            LAG(TotalPrice) OVER (ORDER BY SnapshotDate) AS PreviousPrice
        FROM autoprice.VehicleAdvertSnapshots 
        WHERE VehicleAdvert_Id = @advertId
    ) AS pricesWithPreviousPrice
    WHERE CurrentPrice <> PreviousPrice 
      OR PreviousPrice IS NULL;
    
    ----------------------------------------------
    -- Latest snapshot ID - Simplified query
    ----------------------------------------------
    DECLARE @LatestSnapshotId INT;
    
    SELECT @LatestSnapshotId = MAX(r.Id)
    FROM autoprice.VehicleAdvertSnapshots r
    WHERE r.VehicleAdvert_Id = @advertId
      AND (@effectiveDate IS NULL OR CONVERT(DATE, r.SnapshotDate) = @effectiveDate);
    
    ----------------------------------------------
    -- Images table - Adding proper indexing
    ----------------------------------------------
    CREATE TABLE #images (
        ImageUrl NVARCHAR(MAX),
        rn INT
    );
    
    INSERT INTO #images (ImageUrl, rn)
    SELECT 
        value AS ImageUrl,
        ROW_NUMBER() OVER(ORDER BY (SELECT NULL)) AS rn
    FROM 
        autoprice.VehicleAdverts ads
    CROSS APPLY 
        STRING_SPLIT(ads.ImageUrls, '|')
    WHERE ads.id = @advertId
      AND value != ''
      AND value IS NOT NULL;


         ----------------------------------------------
       --Sub table for most recent opt-out per ad
       ----------------------------------------------
       SELECT
       opts.VehicleAdvert_Id,
       opts.Person_Id,
       opts.ActualEndDate,
       opts.CreatedDate,
       opts.AutoOptOutType,
       reas.Reason,
       ROW_NUMBER() OVER (PARTITION BY opts.VehicleAdvert_Id ORDER BY opts.CreatedDate desc) AS RowNumber
       INTO #mostRecentOptOutForAd
       FROM autoprice.VehicleOptOuts opts
       LEFT JOIN autoprice.OptOutReason reas on reas.id = opts.OptOutReasonId
       WHERE CONVERT(date,opts.ActualEndDate) > @effectiveDate
       AND CONVERT(date,opts.CreatedDate) <= CONVERT(date,@effectiveDate)
       AND opts.VehicleAdvert_Id = @advertId
    
    ----------------------------------------------
    -- Stock by chassis - Optimize with direct filter
    ----------------------------------------------
    CREATE TABLE #stockIdsByChassis (
        Chassis NVARCHAR(100),
        Id INT,
        IsVatQ BIT,
        OriginalPurchasePrice DECIMAL(18,2),
        SIV DECIMAL(18,2),
        NonRecoverableCosts DECIMAL(18,2),
        SuperType NVARCHAR(50),
        RowNum INT
    );
    
    INSERT INTO #stockIdsByChassis
    SELECT
        Chassis,
        Stocks.Id,
        IsVatQ,
        OriginalPurchasePrice,
        SIV,
        NonRecoverableCosts,
        VehicleTypes.SuperType,
        ROW_NUMBER() OVER (PARTITION BY Chassis ORDER BY Stocks.Id DESC) as RowNum
    FROM Stocks
    INNER JOIN VehicleTypes ON VehicleTypes.Id = Stocks.VehicleType_Id
    WHERE Chassis = @Chassis AND @Chassis <> '';
    
    ----------------------------------------------
    -- Main query - Optimized with better joins and filters
    ----------------------------------------------
    SELECT
        @firstPricePosition as FirstPricePosition,
        ads.MostRecentDailyPriceMove as LastPriceChange,
        ads.MostRecentDailyPriceMoveDate as LastChangeDate,
        (SELECT COUNT(Change) FROM #priceChanges) as TotalChanges,
        (COALESCE(snaps.SuppliedPrice,0) + COALESCE(snaps.AdminFee,0)) - @firstPricePosition as TotalPriceChange,
        ads.Id as AdId,
        adSites.Name as AdSiteName,
        adSites.RetailerId as RetailerSiteRetailerId,
        ads.RetailerSite_Id as RetailerSiteId,
        ads.VehicleReg,
        ads.Chassis,
        CASE 
            WHEN ads.StockNumber IS NULL OR ads.StockNumber = 'Unknown' 
                THEN ISNULL(stks.StockNumberFull, 'Unknown')
            ELSE ads.StockNumber
        END AS StockNumber,
        ads.RetailerIdentifier,
        ads.WebSiteStockIdentifier,
        ads.WebSiteSearchIdentifier,
        ads.Make,
        ads.CompetitorLink,
        ads.Model,
        ads.Derivative,
        ads.DerivativeId,
        ads.VehicleType,
        ads.Trim,
        ads.BodyType,
        ads.FuelType,
        ads.BadgeEngineSizeLitres,
        ads.EnginePowerBHP,
        ads.Drivetrain,
        ads.Doors,
        ads.TransmissionType,
        ads.FirstRegisteredDate,
        ads.Colour,
        ads.Owners,
        ads.CreatedInSparkDate,
        ads.Colour,
        ads.SpecificColour,
        ads.DateOnForecourt,
        ads.AttentionGrabber,
        ads.Description,
        snaps.OdometerReadingMiles as OdometerReading,
        ads.EngineCapacityCC,
        ads.HasLeft,
        snaps.NationalRetailRating,
        snaps.NationalRetailDaysToSell AS NationalRetailDays,
        snaps.StrategyPrice,
        snaps.TestStrategyPrice,
        --DATEDIFF(DAY, CAST(CAST(firstSnap.SnapshotDate AS DATE) AS DATETIME), CAST(CAST(lastSnap.SnapshotDate AS DATE) AS DATETIME) ) + 1 AS DaysListed,
        firstSnap.snapshotDate as FirstSnapshotDate,
        CEILING(firstSnap.RetailDaysToSellAtValuation) as OriginalPredictedDTS,
        lastSnap.snapshotDate as LastListedSnapshotDate,
        stks.StockDate,  
        CASE
            WHEN stks.StockDate IS NULL THEN 0
            ELSE DATEDIFF(DAY, stks.StockDate, GETDATE()) 
        END as DaysInStock,
        CASE
            WHEN stks.DateSiteArrival IS NULL THEN 0
            ELSE DATEDIFF(DAY,TRY_CONVERT(DATETIME, CAST(stks.DateSiteArrival AS VARCHAR(8)), 112), GETDATE())
        END as DaysBookedIn,
        snaps.ForecourtPrice,
        (COALESCE(snaps.SuppliedPrice,0) + COALESCE(snaps.AdminFee,0)) as AdvertisedPrice,
        CASE
            WHEN snaps.VatStatus IS NULL THEN 1
            WHEN snaps.VatStatus = 'Ex VAT' THEN 0
            ELSE 1
        END as IncludingVat,
        snaps.PriceIndicatorRatingAtCurrentSelling,
        snaps.DaysToSellAtCurrentSelling,

        --Adjusted
		CASE
            WHEN ads.VehicleType = 'Van' THEN snaps.ValuationAdjRetailExVat
			ELSE snaps.ValuationAdjRetail
		END AS ValuationAdjRetail,
        snaps.ValuationAdjPartEx,
        snaps.ValuationAdjTrade,
        snaps.ValuationAdjPrivate,
        
		--Mkt Avg
		CASE
            WHEN ads.VehicleType = 'Van' THEN snaps.ValuationMktAvRetailExVat
			ELSE snaps.ValuationMktAvRetail
		END AS ValuationMktAvRetail,
		snaps.ValuationMktAvPartEx,
        snaps.ValuationMktAvTrade,
        snaps.ValuationMktAvPrivate,
		--snaps.ValuationAdjRetail AS RelevantValuation,
        snaps.VehicleHasOptionsSpecified,
        snaps.RetailRating,
        snaps.SuppliedPrice,
        snaps.AdminFee,
        snaps.PerformanceRatingScore as PerfRatingScore,
        snaps.PerformanceRatingRating as PerfRating,
        snaps.PerformanceSearchViewsYest as SearchViewsYest,
        snaps.PerformanceAdvertViewsYest as AdvertViewsYest,
        snaps.PerformanceSearchViews7Day as SearchViews7Day,
        snaps.PerformanceAdvertViews7Day as AdvertViews7Day,
        snaps.RetailDemand,
        snaps.RetailSupply,
        snaps.RetailMktCondition as RetailMarketCondition,
        adSites.UpdatePricesAutomatically as SiteOptedIntoAutoPricing,
        (SELECT TOP 1 ImageUrl FROM #images ORDER BY rn) as ImageUrl,
        (SELECT COUNT(ImageUrl) FROM #images) as ImagesCount,
        IIF(ads.VideoUrl IS NOT NULL AND ads.VideoURL <> '', 1, 0) as HasVideo,
        CASE
            WHEN st.SuperType = 'New' THEN st.SIV + snaps.TotalPrice * (1/6)
            WHEN st.IsVatQ = 1 THEN st.SIV + snaps.TotalPrice * (1/6)
            ELSE st.SIV + (snaps.TotalPrice - st.OriginalPurchasePrice) / 6
        END as CostPrice,
        st.NonRecoverableCosts as PrepCost,
        	CASE
		--New car, treat as VAT qualifying
		WHEN st.SuperType = 'New' AND st.SIV <= 10 THEN null  -- no cost price, return null
		WHEN st.SuperType = 'New' AND st.SIV > 10 THEN ROUND(snaps.TotalPrice * 5/6 - st.SIV - st.NonRecoverableCosts ,0)
		--Vat Qualifying car
		WHEN st.IsVatQ = 1 AND st.SIV <= 10 THEN null  -- no cost price, return null
		WHEN st.IsVatQ = 1 AND st.SIV > 10 THEN ROUND(snaps.TotalPrice * 5/6 - st.SIV - st.NonRecoverableCosts ,0)
		--Margin car
		WHEN st.SIV <=10 OR st.OriginalPurchasePrice <= 10 THEN null -- no cost price, return null
		ELSE ROUND( snaps.TotalPrice - st.SIV - st.NonRecoverableCosts - ((snaps.TotalPrice - st.OriginalPurchasePrice) / 6)  ,0)
	END AS PricedProfit,
        
        COALESCE(st.IsVatQ, 0) AS VatQualifying,
        COALESCE(st.Id, 0) as StockItemId,
        snaps.AutotraderAdvertStatus,
        snaps.AdvertiserAdvertStatus,
        snaps.LifecycleStatus,
        snaps.NationalRetailMktCondition,
        tp.IsTradePricing,
        tp.MarginPercentage as TradeMarginPercentage,
        tp.MarginAmount as TradeMarginAmount,
        ads.FirstVehicleAdvertSnapshotId as FirstSnapshotId,
        ads.LatestVehicleAdvertSnapshotId as LatestSnapshotId,
        ads.FirstListedSnapshotId as FirstListedSnapshotId,
        ads.LastListedSnapshotId as LastListedSnapshotId,
        CAST(CAST(firstSnap.SnapshotDate AS DATE) AS DATETIME) as FirstListedSnapshotDate,
        CAST(CAST(lastSnap.SnapshotDate AS DATE) AS DATETIME) as LastListedSnapshotDate,

         prog.Description as ProgressCode,
 stks.Buyer,
 stks.CapValue as CapClean,
 stks.CapID as CapId,
 Stks.HaveV5 as V5,
 stks.HaveServiceHistory,
 stks.NextMOTDate,
 stks.KeyTracker,
 opts.Reason as OptOutReason,

 stks.CustomerInterestRating as CustInterestRating,
 stks.CustomerInterest,
 stks.MostRecentInterestDate,
 stks.Appointments,
 stks.MostRecentAppointmentDate,
 stks.SiteVisits,
 stks.MostRecentSiteVisitDate


    FROM autoprice.VehicleAdvertSnapshots snaps 
    INNER JOIN autoprice.VehicleAdverts ads ON ads.id = snaps.VehicleAdvert_Id
    INNER JOIN autoprice.RetailerSites adSites ON adSites.Id = ads.RetailerSite_Id
    LEFT JOIN autoprice.VehicleAdvertSnapshots firstSnap ON firstSnap.Id = ads.FirstVehicleAdvertSnapshotId
    LEFT JOIN autoprice.VehicleAdvertSnapshots lastSnap ON lastSnap.Id = ads.LastListedSnapshotId
    LEFT JOIN Sites s ON s.Id = adSites.Site_Id
    LEFT JOIN #stockIdsByChassis st ON st.RowNum = 1
    LEFT JOIN Stocks stks ON stks.Id = ads.Stock_Id   
    LEFT JOIN StandingValues prog on prog.Id = stks.ProgressCode_Id
    LEFT JOIN autoprice.TradePriceSettings tp on tp.RetailerSiteId = ads.RetailerSite_Id
    LEFT JOIN #mostRecentOptOutForAd opts on opts.VehicleAdvert_Id = ads.id and opts.RowNumber = 1
    WHERE snaps.Id = @LatestSnapshotId
      AND ads.Id = @advertId
      AND adSites.IsActive = 1
      AND adSites.DealerGroup_Id = @dealerGroupId;
    
    -- Clean up temp tables
    DROP TABLE #images;
    DROP TABLE #priceChanges;
    DROP TABLE #stockIdsByChassis;
END
GO
