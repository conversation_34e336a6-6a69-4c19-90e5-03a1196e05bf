CREATE OR ALTER PROCEDURE [autoprice].[UPDATE_Stocks_VehicleAdvertId]
(
    @dealerGroupId INT
)
AS
BEGIN
    SET NOCOUNT ON;

    -----------------------------------------------------------------------
    -- Update by Registration
    -----------------------------------------------------------------------
    UPDATE st
    SET VehicleAdvert_Id = ads.Id
    FROM stocks st
    INNER JOIN Sites si 
        ON si.Id = st.Site_Id 
       AND si.DealerGroup_Id = @dealerGroupId
    INNER JOIN autoprice.VehicleAdverts ads 
        ON ads.VehicleReg = st.Reg
       AND ads.VehicleReg <> ''
    INNER JOIN autoprice.RetailerSites rs 
        ON rs.Id = ads.RetailerSite_Id
       AND rs.DealerGroup_Id = si.DealerGroup_Id 
       AND rs.Site_Id = si.Id
    WHERE st.VehicleAdvert_Id IS NULL
      AND (st.IsRemoved = 0 OR st.RemovedDate > DATEADD(month, -6, GETDATE()))
      AND st.Reg IS NOT NULL;

    -----------------------------------------------------------------------
    -- Update by Chassis
    -----------------------------------------------------------------------
    UPDATE st
    SET VehicleAdvert_Id = ads.Id
    FROM stocks st
    INNER JOIN Sites si 
        ON si.Id = st.Site_Id 
       AND si.DealerGroup_Id = @dealerGroupId
    INNER JOIN autoprice.VehicleAdverts ads 
        ON ads.Chassis = st.Chassis
       AND ads.Chassis <> ''
    INNER JOIN autoprice.RetailerSites rs 
        ON rs.Id = ads.RetailerSite_Id
       AND rs.DealerGroup_Id = si.DealerGroup_Id 
       AND rs.Site_Id = si.Id
    WHERE st.VehicleAdvert_Id IS NULL
      AND (st.IsRemoved = 0 OR st.RemovedDate > DATEADD(month, -6, GETDATE()))
      AND st.Chassis IS NOT NULL;
END
GO
