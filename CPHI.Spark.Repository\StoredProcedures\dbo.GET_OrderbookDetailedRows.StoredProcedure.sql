CREATE OR ALTER PROCEDURE [dbo].[GET_OrderbookDetailedRows]
(
    @DealIdsString varchar(max),
    @ByStockNumber bit,
    @DealerGroupId int
)
AS
BEGIN
    SET NOCOUNT ON;

    ---------------------------------------------------------------------
    -- 1. Put DealIds into a temp table (NOT a table variable)
    ---------------------------------------------------------------------
    CREATE TABLE #dealIds (id int PRIMARY KEY);

    INSERT INTO #dealIds (id)
    SELECT TRY_CAST(value AS int)
    FROM STRING_SPLIT(@DealIdsString, ',')
    WHERE TRY_CAST(value AS int) IS NOT NULL;

    ---------------------------------------------------------------------
    -- 2. Build Comment tables with IF branching (better query plans)
    ---------------------------------------------------------------------
    CREATE TABLE #CommentTableByStock
        (
            StockNumber nvarchar(100) PRIMARY KEY,
            Comments nvarchar(max)
        );
       CREATE TABLE #CommentTableByDeal
        (
            Deal_Id int PRIMARY KEY,
            Comments nvarchar(max)
        );

    IF @ByStockNumber = 1
    BEGIN
        --CREATE TABLE #CommentTableByStock
        --(
        --    StockNumber nvarchar(100) PRIMARY KEY,
        --    Comments nvarchar(max)
        --);

        INSERT INTO #CommentTableByStock (StockNumber, Comments)
        SELECT 
            c.StockNumber,
            STRING_AGG(CONCAT(p.Name, ' on ', CONVERT(VARCHAR(20), c.Date, 6), ': ', c.Text), '; ') AS Comments
        FROM Comments c
        INNER JOIN People p ON p.Id = c.PersonId
        WHERE c.IsRemoved = 0
          AND p.DealerGroup_Id = @DealerGroupId
        GROUP BY c.StockNumber;
    END
    ELSE
    BEGIN
        --CREATE TABLE #CommentTableByDeal
        --(
        --    Deal_Id int PRIMARY KEY,
        --    Comments nvarchar(max)
        --);

        INSERT INTO #CommentTableByDeal (Deal_Id, Comments)
        SELECT 
            c.Deal_Id,
            STRING_AGG(CONCAT(p.Name, ' on ', CONVERT(VARCHAR(20), c.Date, 6), ': ', c.Text), '; ') AS Comments
        FROM Comments c
        INNER JOIN People p ON p.Id = c.PersonId
        WHERE c.IsRemoved = 0
          AND p.DealerGroup_Id = @DealerGroupId
          AND c.Deal_Id IS NOT NULL
        GROUP BY c.Deal_Id;
    END

    ---------------------------------------------------------------------
    -- 3. Main query
    ---------------------------------------------------------------------
    SELECT
        d.DealId,
        d.StockNumber,
        d.Reg,
        d.OemReference,
        d.LastPhysicalLocation,
        d.Model,
        d.ModelYear,
        d.Description,
        DateDiff(day, d.StockDate, CONVERT(date, getDate())) as VehicleAge,
        d.StockDate,
        d.RegisteredDate,
        d.IsInvoiced,
        d.Customer,
        d.FinanceCo,
        d.OrderDate,
        d.InvoiceDate,
        d.IsLateCost,
        d.ActualDeliveryDate,
        d.HandoverDate,
        d.IsDelivered,
        vehclass.Description as VehicleClassDescription,
        d.Units,
        d.Sale,
        d.CoS,
        d.Discount,
        d.FuelSale,
        d.FuelCost,
        d.OemDeliverySale,
        d.OemDeliveryCost,
        d.AccessoriesSale,
        d.AccessoriesCost,
        d.PDICost,
        d.MechPrep,
        d.BodyPrep,
        d.NewBonus1,
        d.NewBonus2,
        d.BrokerCost,
        d.IntroCommission,
        d.Error,
        d.Other,
        d.VatCost,
        d.PartExOverAllowance1,
        d.IsFinanced,
        d.HasServicePlan,
        d.HasTyreInsurance AS HasTyre,
        d.HasAlloyInsurance AS HasAlloy,
        d.HasTyreAndAlloyInsurance AS HasTyreAlloy,
        d.HasWheelGuard,
        d.HasCosmeticInsurance AS HasCosmetic,
        CASE
            WHEN d.HasPaintProtectionAccessory = 1 OR d.HasPaintProtection = 1 THEN 1
            ELSE 0
        END AS HasPaintProtection,
        d.HasGapInsurance AS HasGap,
        d.HasWarranty,
        d.TotalProductCount,
        d.FinanceCommission,
        d.FinanceSubsidy,
        d.SelectCommission,
        d.ProPlusCommission,
        d.RCIFinanceCommission,
        d.ServicePlanSale,
        d.ServicePlanCost,
        d.TyreInsuranceSale,
        d.TyreInsuranceCost,
        d.TyreInsuranceCommission AS TyreCommission,
        d.AlloyInsuranceSale,
        d.AlloyInsuranceCost,
        d.AlloyInsuranceCommission AS AlloyCommission,
        d.TyreAndAlloyInsuranceSale,
        d.TyreAndAlloyInsuranceCost,
        d.TyreAndAlloyInsuranceCommission AS TyreAlloyCommission,
        d.WheelGuardSale,
        d.WheelGuardCost,
        d.WheelGuardCommission,
        d.CosmeticInsuranceSale,
        d.CosmeticInsuranceCost,
        d.CosmeticInsuranceCommission AS CosmeticCommission,
        d.PaintProtectionSale,
        d.PaintProtectionCost,
        d.GapInsuranceSale,
        d.GapInsuranceCost,
        d.GapInsuranceCommission AS GapCommission,
        d.WarrantySale,
        d.WarrantyCost,
        salesman.Id as SalesmanId,
        salesman.DmsId as SalesmanDmsId,
        salesman.Name as SalesmanName,
        s.Description as SiteDescription,
        s.Code as SiteCode,
        s.Id as SiteId,
        IIF(vehclass.Description = 'Van',0,1) as IsCar,
        fran.Description as Franchise,
        otype.Type as OrderType,
        otype.Description as OrderTypeDescription,
        vtype.Type as VehicleType,
        vtype.Code as VehicleTypeCode,
        vtype.Description as VehicleTypeDescription,
        ds.Description as DeliverySiteDescription,
        d.VehicleSource,
        d.AccountingDate,
        d.EnquiryNumber,
        d.IsClosed,
        d.FinanceType,
        d.Sale + d.Discount + d.CoS + d.PartExOverallowance1 + d.VatCost + d.NewBonus1 + d.NewBonus2  AS MetalProfit,
        d.StandardWarrantyCost,
        d.OemDeliverySale,
        d.OemDeliveryCost,
        d.AccessoriesSale + d.AccessoriesCost + d.FuelSale + d.FuelCost + d.BrokerCost + d.IntroCommission + d.OemDeliverySale + d.OemDeliveryCost 
            + d.PDICost + d.MechPrep + d.BodyPrep + d.Other + d.Error + d.StandardWarrantyCost + d.PaintProtectionAccessorySale + d.PaintProtectionAccessoryCost 
            as OtherProfit,
        d.CosmeticInsuranceSale + d.CosmeticInsuranceCost + d.CosmeticInsuranceCommission + d.GapInsuranceSale + d.GapInsuranceCost 
            + d.GapInsuranceCommission  + d.PaintProtectionSale  + d.PaintProtectionCost + d.ServicePlanSale + d.ServicePlanCost 
            + d.WarrantySale + d.WarrantyCost + d.WheelGuardSale + d.WheelGuardCost + d.WheelGuardCommission + d.TyreInsuranceSale 
            + d.TyreInsuranceCost + d.TyreInsuranceCommission + d.AlloyInsuranceSale + d.AlloyInsuranceCost + d.AlloyInsuranceCommission 
            + d.TyreAndAlloyInsuranceSale + d.TyreAndAlloyInsuranceCost + d.TyreAndAlloyInsuranceCommission  
            as AddOnProfit,
        d.FinanceCommission + d.FinanceSubsidy + d.SelectCommission + d.RCIFinanceCommission + d.StandardsCommission + d.ProPlusCommission
            as FinanceProfit,
        d.LastAdvertisedPrice, 
        d.TotalNlProfit as TotalProfit,
        d.PaintProtectionAccessorySale,
        d.PaintProtectionAccessoryCost,
        IIF(dfs.SentDate IS NOT NULL, CONVERT(VARCHAR(20), dfs.SentDate, 7), '') AS DealfileSentDate,
        COALESCE(ct.Comments, ctd.Comments, ' ') AS Comment
    FROM DEALLatests d
    INNER JOIN Sites s ON s.Id = d.Site_Id
    LEFT JOIN Sites ds ON ds.Id = d.DeliverySite_Id
    LEFT JOIN DealfileSentDates dfs ON dfs.StockNumber = d.StockNumber
    INNER JOIN StandingValues fran ON fran.Id = d.Franchise_Id
    INNER JOIN StandingValues vehclass ON vehclass.Id = d.VehicleClass_Id
    INNER JOIN VehicleTypes vtype ON vtype.Id = d.VehicleType_Id
    INNER JOIN OrderTypes otype ON otype.Id = d.OrderType_Id
    INNER JOIN People salesman ON salesman.Id = d.Salesman_Id
    INNER JOIN #dealIds did ON did.id = d.DealId
    LEFT JOIN #CommentTableByStock ct ON @ByStockNumber = 1 AND ct.StockNumber = d.StockNumber
    LEFT JOIN #CommentTableByDeal ctd ON @ByStockNumber = 0 AND ctd.Deal_Id = d.DealId
    WHERE s.DealerGroup_Id = @DealerGroupId;

END
GO
