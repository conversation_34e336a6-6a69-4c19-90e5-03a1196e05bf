{"ngccVersion": "14.3.0", "configFileHash": "6883213aec2e6448f60915f864b10646a78045af10f17ba40f81f18dd7c1b8e7", "lockFileHash": "6bf5ddcc752d456bf48b5b221a879f837f23298bd63ff82c479b05cc4f900c4a", "entryPointPaths": [["@adobe/css-tools", "@adobe/css-tools"], ["@ampproject/remapping", "@ampproject/remapping"], ["@angular/animations", "@angular/animations"], ["@angular/animations", "@angular/animations/browser"], ["@angular/animations", "@angular/animations/browser/testing"], ["@angular/cdk", "@angular/cdk"], ["@angular/cdk", "@angular/cdk/a11y"], ["@angular/cdk", "@angular/cdk/accordion"], ["@angular/cdk", "@angular/cdk/bidi"], ["@angular/cdk", "@angular/cdk/clipboard"], ["@angular/cdk", "@angular/cdk/coercion"], ["@angular/cdk", "@angular/cdk/collections"], ["@angular/cdk", "@angular/cdk/dialog"], ["@angular/cdk", "@angular/cdk/drag-drop"], ["@angular/cdk", "@angular/cdk/keycodes"], ["@angular/cdk", "@angular/cdk/layout"], ["@angular/cdk", "@angular/cdk/listbox"], ["@angular/cdk", "@angular/cdk/menu"], ["@angular/cdk", "@angular/cdk/observers"], ["@angular/cdk", "@angular/cdk/overlay"], ["@angular/cdk", "@angular/cdk/platform"], ["@angular/cdk", "@angular/cdk/portal"], ["@angular/cdk", "@angular/cdk/scrolling"], ["@angular/cdk", "@angular/cdk/stepper"], ["@angular/cdk", "@angular/cdk/table"], ["@angular/cdk", "@angular/cdk/testing"], ["@angular/cdk", "@angular/cdk/testing/selenium-webdriver"], ["@angular/cdk", "@angular/cdk/testing/testbed"], ["@angular/cdk", "@angular/cdk/text-field"], ["@angular/cdk", "@angular/cdk/tree"], ["@angular/cli", "@angular/cli"], ["@angular/common", "@angular/common"], ["@angular/common", "@angular/common/http"], ["@angular/common", "@angular/common/http/testing"], ["@angular/common", "@angular/common/testing"], ["@angular/common", "@angular/common/upgrade"], ["@angular/compiler", "@angular/compiler"], ["@angular/compiler", "@angular/compiler/testing"], ["@angular/compiler-cli", "@angular/compiler-cli"], ["@angular/compiler-cli", "@angular/compiler-cli/linker"], ["@angular/compiler-cli", "@angular/compiler-cli/linker/babel"], ["@angular/compiler-cli", "@angular/compiler-cli/ngcc"], ["@angular/core", "@angular/core"], ["@angular/core", "@angular/core/testing"], ["@angular/forms", "@angular/forms"], ["@angular/language-service", "@angular/language-service"], ["@angular/localize", "@angular/localize"], ["@angular/localize", "@angular/localize/init"], ["@angular/localize", "@angular/localize/tools"], ["@angular/platform-browser", "@angular/platform-browser"], ["@angular/platform-browser", "@angular/platform-browser/animations"], ["@angular/platform-browser", "@angular/platform-browser/testing"], ["@angular/platform-browser-dynamic", "@angular/platform-browser-dynamic"], ["@angular/platform-browser-dynamic", "@angular/platform-browser-dynamic/testing"], ["@angular/platform-server", "@angular/platform-server"], ["@angular/platform-server", "@angular/platform-server/init"], ["@angular/platform-server", "@angular/platform-server/testing"], ["@angular/router", "@angular/router"], ["@angular/router", "@angular/router/testing"], ["@angular/router", "@angular/router/upgrade"], ["@angular-devkit/architect", "@angular-devkit/architect"], ["@angular-devkit/build-angular", "@angular-devkit/build-angular"], ["@angular-devkit/build-webpack", "@angular-devkit/build-webpack"], ["@angular-devkit/core", "@angular-devkit/core"], ["@angular-devkit/core", "@angular-devkit/core/node"], ["@angular-devkit/schematics", "@angular-devkit/schematics"], ["@angular-devkit/schematics", "@angular-devkit/schematics/tasks"], ["@angular-devkit/schematics", "@angular-devkit/schematics/testing"], ["@angular-devkit/schematics", "@angular-devkit/schematics/tools"], ["@angular-eslint/builder", "@angular-eslint/builder"], ["@angular-eslint/bundled-angular-compiler", "@angular-eslint/bundled-angular-compiler"], ["@angular-eslint/eslint-plugin", "@angular-eslint/eslint-plugin"], ["@angular-eslint/eslint-plugin-template", "@angular-eslint/eslint-plugin-template"], ["@angular-eslint/schematics", "@angular-eslint/schematics"], ["@angular-eslint/template-parser", "@angular-eslint/template-parser"], ["@angular-eslint/utils", "@angular-eslint/utils"], ["@assemblyscript/loader", "@assemblyscript/loader"], ["@babel/parser", "@babel/parser"], ["@babel/types", "@babel/types"], ["@biesbjerg/ngx-translate-extract", "@biesbjerg/ngx-translate-extract"], ["@cspotcode/source-map-support", "@cspotcode/source-map-support"], ["@csstools/postcss-cascade-layers", "@csstools/postcss-cascade-layers"], ["@csstools/postcss-color-function", "@csstools/postcss-color-function"], ["@csstools/postcss-font-format-keywords", "@csstools/postcss-font-format-keywords"], ["@csstools/postcss-hwb-function", "@csstools/postcss-hwb-function"], ["@csstools/postcss-ic-unit", "@csstools/postcss-ic-unit"], ["@csstools/postcss-is-pseudo-class", "@csstools/postcss-is-pseudo-class"], ["@csstools/postcss-nested-calc", "@csstools/postcss-nested-calc"], ["@csstools/postcss-normalize-display-values", "@csstools/postcss-normalize-display-values"], ["@csstools/postcss-oklab-function", "@csstools/postcss-oklab-function"], ["@csstools/postcss-progressive-custom-properties", "@csstools/postcss-progressive-custom-properties"], ["@csstools/postcss-stepped-value-functions", "@csstools/postcss-stepped-value-functions"], ["@csstools/postcss-text-decoration-shorthand", "@csstools/postcss-text-decoration-shorthand"], ["@csstools/postcss-trigonometric-functions", "@csstools/postcss-trigonometric-functions"], ["@csstools/postcss-unset-value", "@csstools/postcss-unset-value"], ["@csstools/selector-specificity", "@csstools/selector-specificity"], ["@discoveryjs/json-ext", "@discoveryjs/json-ext"], ["@eslint/eslintrc", "@eslint/eslintrc"], ["@eslint-community/eslint-utils", "@eslint-community/eslint-utils"], ["@fast-csv/format", "@fast-csv/format"], ["@fast-csv/parse", "@fast-csv/parse"], ["@humanwhocodes/module-importer", "@humanwhocodes/module-importer"], ["@jridgewell/gen-mapping", "@jridgewell/gen-mapping"], ["@jridgewell/resolve-uri", "@jridgewell/resolve-uri"], ["@jridgewell/set-array", "@jridgewell/set-array"], ["@jridgewell/source-map", "@jridgewell/source-map"], ["@jridgewell/sourcemap-codec", "@jridgewell/sourcemap-codec"], ["@jridgewell/trace-mapping", "@jridgewell/trace-mapping"], ["@kurkle/color", "@kurkle/color"], ["@leichtgewicht/ip-codec", "@leichtgewicht/ip-codec"], ["@microsoft/applicationinsights-analytics-js", "@microsoft/applicationinsights-analytics-js"], ["@microsoft/applicationinsights-channel-js", "@microsoft/applicationinsights-channel-js"], ["@microsoft/applicationinsights-common", "@microsoft/applicationinsights-common"], ["@microsoft/applicationinsights-core-js", "@microsoft/applicationinsights-core-js"], ["@microsoft/applicationinsights-dependencies-js", "@microsoft/applicationinsights-dependencies-js"], ["@microsoft/applicationinsights-properties-js", "@microsoft/applicationinsights-properties-js"], ["@microsoft/applicationinsights-shims", "@microsoft/applicationinsights-shims"], ["@microsoft/applicationinsights-web", "@microsoft/applicationinsights-web"], ["@microsoft/dynamicproto-js", "@microsoft/dynamicproto-js"], ["@ng-bootstrap/ng-bootstrap", "@ng-bootstrap/ng-bootstrap"], ["@ngtools/webpack", "@ngtools/webpack"], ["@nodelib/fs.scandir", "@nodelib/fs.scandir"], ["@nodelib/fs.stat", "@nodelib/fs.stat"], ["@nodelib/fs.walk", "@nodelib/fs.walk"], ["@parcel/watcher", "@parcel/watcher"], ["@parcel/watcher-win32-x64", "@parcel/watcher-win32-x64"], ["@phenomnomnominal/tsquery", "@phenomnomnominal/tsquery"], ["@popperjs/core", "@popperjs/core"], ["@socket.io/component-emitter", "@socket.io/component-emitter"], ["@tootallnate/once", "@tootallnate/once"], ["@types/body-parser", "@types/body-parser"], ["@types/bonjour", "@types/bonjour"], ["@types/connect", "@types/connect"], ["@types/connect-history-api-fallback", "@types/connect-history-api-fallback"], ["@types/cors", "@types/cors"], ["@types/eslint", "@types/eslint"], ["@types/eslint", "@types/eslint/rules"], ["@types/eslint-scope", "@types/eslint-scope"], ["@types/estree", "@types/estree"], ["@types/express", "@types/express"], ["@types/express-serve-static-core", "@types/express-serve-static-core"], ["@types/googlemaps", "@types/googlemaps"], ["@types/http-errors", "@types/http-errors"], ["@types/http-proxy", "@types/http-proxy"], ["@types/jasmine", "@types/jasmine"], ["@types/jasminewd2", "@types/jasminewd2"], ["@types/jquery", "@types/jquery"], ["@types/json-schema", "@types/json-schema"], ["@types/mime", "@types/mime"], ["@types/node", "@types/node"], ["@types/node-forge", "@types/node-forge"], ["@types/parse-json", "@types/parse-json"], ["@types/qs", "@types/qs"], ["@types/raf", "@types/raf"], ["@types/range-parser", "@types/range-parser"], ["@types/retry", "@types/retry"], ["@types/selenium-webdriver", "@types/selenium-webdriver"], ["@types/semver", "@types/semver"], ["@types/send", "@types/send"], ["@types/serve-index", "@types/serve-index"], ["@types/serve-static", "@types/serve-static"], ["@types/sizzle", "@types/sizzle"], ["@types/sockjs", "@types/sockjs"], ["@types/trusted-types", "@types/trusted-types"], ["@types/ws", "@types/ws"], ["@typescript-eslint/scope-manager", "@typescript-eslint/scope-manager"], ["@typescript-eslint/type-utils", "@typescript-eslint/type-utils"], ["@typescript-eslint/types", "@typescript-eslint/types"], ["@typescript-eslint/typescript-estree", "@typescript-eslint/typescript-estree"], ["@typescript-eslint/utils", "@typescript-eslint/utils"], ["@typescript-eslint/visitor-keys", "@typescript-eslint/visitor-keys"], ["@xtuc/long", "@xtuc/long"], ["abab", "abab"], ["acorn", "acorn"], ["acorn-walk", "acorn-walk"], ["<PERSON>ler-32", "<PERSON>ler-32"], ["ag-grid-angular", "ag-grid-angular"], ["ag-grid-community", "ag-grid-community"], ["ag-grid-enterprise", "ag-grid-enterprise"], ["agent-base", "agent-base"], ["agentkeepalive", "agentkeepalive"], ["ajv", "ajv"], ["ajv-formats", "ajv-formats"], ["ajv-keywords", "ajv-keywords"], ["animate.css", "animate.css"], ["ansi-colors", "ansi-colors"], ["arg", "arg"], ["array-buffer-byte-length", "array-buffer-byte-length"], ["async", "async"], ["autoprefixer", "autoprefixer"], ["available-typed-arrays", "available-typed-arrays"], ["base64-arraybuffer", "base64-arraybuffer"], ["base64-js", "base64-js"], ["bfj", "bfj"], ["big-integer", "big-integer"], ["big.js", "big.js"], ["blocking-proxy", "blocking-proxy"], ["bonjour-service", "bonjour-service"], ["browserslist", "browserslist"], ["buffer", "buffer"], ["call-bind-apply-helpers", "call-bind-apply-helpers"], ["call-bound", "call-bound"], ["canvg", "canvg"], ["cfb", "cfb"], ["chalk", "chalk"], ["chart.js", "chart.js"], ["chart.js", "chart.js/auto"], ["chart.js", "chart.js/helpers"], ["chartjs-plugin-annotation", "chartjs-plugin-annotation"], ["chartjs-plugin-datalabels", "chartjs-plugin-datalabels"], ["chokidar", "chokidar"], ["chrome-trace-event", "chrome-trace-event"], ["cliui", "cliui"], ["codepage", "codepage"], ["colorette", "colorette"], ["commander", "commander"], ["copy-anything", "copy-anything"], ["copy-webpack-plugin", "copy-webpack-plugin"], ["cosmiconfig", "cosmiconfig"], ["crc-32", "crc-32"], ["create-require", "create-require"], ["critters", "critters"], ["cropperjs", "cropperjs"], ["css-blank-pseudo", "css-blank-pseudo"], ["css-has-pseudo", "css-has-pseudo"], ["css-line-break", "css-line-break"], ["css-prefers-color-scheme", "css-prefers-color-scheme"], ["css-select", "css-select"], ["css-what", "css-what"], ["cssdb", "cssdb"], ["dayjs", "dayjs"], ["define-data-property", "define-data-property"], ["dependency-graph", "dependency-graph"], ["dexie", "dexie"], ["dom-serializer", "dom-serializer"], ["domelementtype", "domelementtype"], ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], ["domino", "domino"], ["dompurify", "dompurify"], ["domutils", "domutils"], ["emoji-regex", "emoji-regex"], ["engine.io", "engine.io"], ["engine.io-parser", "engine.io-parser"], ["enhanced-resolve", "enhanced-resolve"], ["entities", "entities"], ["es-define-property", "es-define-property"], ["es-errors", "es-errors"], ["es-module-lexer", "es-module-lexer"], ["es-object-atoms", "es-object-atoms"], ["es6-promise", "es6-promise"], ["esbuild-wasm", "esbuild-wasm"], ["escalade", "escalade"], ["eslint-utils", "eslint-utils"], ["eslint-visitor-keys", "eslint-visitor-keys"], ["espree", "espree"], ["eventemitter-asyncresource", "eventemitter-asyncresource"], ["eventemitter3", "eventemitter3"], ["exceljs", "exceljs"], ["external-editor", "external-editor"], ["fast-csv", "fast-csv"], ["fast-deep-equal", "fast-deep-equal"], ["fast-glob", "fast-glob"], ["fast-json-patch", "fast-json-patch"], ["fast-json-stable-stringify", "fast-json-stable-stringify"], ["fast-uri", "fast-uri"], ["faye-websocket", "faye-websocket"], ["fflate", "fflate"], ["flatted", "flatted"], ["frac", "frac"], ["fraction.js", "fraction.js"], ["get-caller-file", "get-caller-file"], ["get-package-type", "get-package-type"], ["get-proto", "get-proto"], ["gopd", "gopd"], ["graphemer", "graphemer"], ["has", "has"], ["has-symbols", "has-symbols"], ["has-tostringtag", "has-tostringtag"], ["hasown", "hasown"], ["hdr-histogram-js", "hdr-histogram-js"], ["html-entities", "html-entities"], ["html2canvas", "html2canvas"], ["http-parser-js", "http-parser-js"], ["http-proxy-agent", "http-proxy-agent"], ["http-proxy-middleware", "http-proxy-middleware"], ["https-proxy-agent", "https-proxy-agent"], ["human-signals", "human-signals"], ["iconv-lite", "iconv-lite"], ["ieee754", "ieee754"], ["immutable", "immutable"], ["internal-slot", "internal-slot"], ["ipaddr.js", "ipaddr.js"], ["is-array-buffer", "is-array-buffer"], ["is-map", "is-map"], ["is-plain-object", "is-plain-object"], ["is-set", "is-set"], ["is-weakmap", "is-weakmap"], ["is-weakset", "is-weakset"], ["is-what", "is-what"], ["isbinaryfile", "isbinaryfile"], ["isobject", "isobject"], ["jasmine-spec-reporter", "jasmine-spec-reporter"], ["jest-worker", "jest-worker"], ["json5", "json5"], ["jsonc-parser", "jsonc-parser"], ["jspdf", "jspdf"], ["j<PERSON><PERSON>", "j<PERSON><PERSON>"], ["keyv", "keyv"], ["klona", "klona"], ["levn", "levn"], ["license-webpack-plugin", "license-webpack-plugin"], ["lie", "lie"], ["lines-and-columns", "lines-and-columns"], ["log4js", "log4js"], ["lru-cache", "lru-cache"], ["magic-string", "magic-string"], ["make-error", "make-error"], ["memfs", "memfs"], ["mini-css-extract-plugin", "mini-css-extract-plugin"], ["minipass", "minipass"], ["moment", "moment"], ["nanoid", "nanoid"], ["nanoid", "nanoid/async"], ["nanoid", "nanoid/non-secure"], ["nanoid", "nanoid/url-alphabet"], ["ngx-image-cropper", "ngx-image-cropper", ["C:/Users/<USER>/Documents/Spark/sparkrrg/CPHI.Spark.WebApp/Angular/node_modules/@angular/core", "C:/Users/<USER>/Documents/Spark/sparkrrg/CPHI.Spark.WebApp/Angular/node_modules/@angular/common", "C:/Users/<USER>/Documents/Spark/sparkrrg/CPHI.Spark.WebApp/Angular/node_modules/@angular/platform-browser"]], ["ngx-image-cropper/node_modules/tslib", "ngx-image-cropper/node_modules/tslib"], ["ngx-indexed-db", "ngx-indexed-db"], ["npm-packlist", "npm-packlist"], ["npm-pick-manifest", "npm-pick-manifest"], ["npm-registry-fetch", "npm-registry-fetch"], ["nth-check", "nth-check"], ["optionator", "optionator"], ["performance-now", "performance-now"], ["picocolors", "picocolors"], ["piscina", "piscina"], ["piscina", "piscina/dist"], ["possible-typed-array-names", "possible-typed-array-names"], ["postcss", "postcss"], ["postcss-attribute-case-insensitive", "postcss-attribute-case-insensitive"], ["postcss-color-functional-notation", "postcss-color-functional-notation"], ["postcss-color-hex-alpha", "postcss-color-hex-alpha"], ["postcss-color-rebeccapurple", "postcss-color-rebeccapurple"], ["postcss-custom-media", "postcss-custom-media"], ["postcss-custom-properties", "postcss-custom-properties"], ["postcss-custom-selectors", "postcss-custom-selectors"], ["postcss-dir-pseudo-class", "postcss-dir-pseudo-class"], ["postcss-double-position-gradients", "postcss-double-position-gradients"], ["postcss-env-function", "postcss-env-function"], ["postcss-focus-visible", "postcss-focus-visible"], ["postcss-focus-within", "postcss-focus-within"], ["postcss-gap-properties", "postcss-gap-properties"], ["postcss-image-set-function", "postcss-image-set-function"], ["postcss-lab-function", "postcss-lab-function"], ["postcss-logical", "postcss-logical"], ["postcss-nesting", "postcss-nesting"], ["postcss-overflow-shorthand", "postcss-overflow-shorthand"], ["postcss-place", "postcss-place"], ["postcss-preset-env", "postcss-preset-env"], ["postcss-pseudo-class-any-link", "postcss-pseudo-class-any-link"], ["postcss-selector-not", "postcss-selector-not"], ["postcss-selector-parser", "postcss-selector-parser"], ["postcss-value-parser", "postcss-value-parser"], ["prelude-ls", "prelude-ls"], ["protractor", "protractor"], ["queue-microtask", "queue-microtask"], ["readdirp", "readdirp"], ["reflect-metadata", "reflect-metadata"], ["regex-parser", "regex-parser"], ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], ["rfdc", "rfdc"], ["rxjs", "rxjs"], ["rxjs", "rxjs/ajax"], ["rxjs", "rxjs/fetch"], ["rxjs", "rxjs/internal-compatibility"], ["rxjs", "rxjs/operators"], ["rxjs", "rxjs/testing"], ["rxjs", "rxjs/webSocket"], ["safe-buffer", "safe-buffer"], ["sass", "sass"], ["saxes", "saxes"], ["schema-utils", "schema-utils"], ["selfsigned", "selfsigned"], ["set-function-length", "set-function-length"], ["set-function-name", "set-function-name"], ["setprot<PERSON>of", "setprot<PERSON>of"], ["side-channel", "side-channel"], ["side-channel-list", "side-channel-list"], ["side-channel-map", "side-channel-map"], ["side-channel-weakmap", "side-channel-weakmap"], ["smart-buffer", "smart-buffer"], ["socket.io", "socket.io"], ["socket.io-adapter", "socket.io-adapter"], ["socket.io-parser", "socket.io-parser"], ["socks", "socks"], ["socks-proxy-agent", "socks-proxy-agent"], ["source-map", "source-map"], ["source-map-js", "source-map-js"], ["sourcemap-codec", "sourcemap-codec"], ["spdy", "spdy"], ["spdy-transport", "spdy-transport"], ["ssf", "ssf"], ["stop-iteration-iterator", "stop-iteration-iterator"], ["svg-pathdata", "svg-pathdata"], ["symbol-observable", "symbol-observable"], ["tapable", "tapable"], ["terser", "terser"], ["terser", "terser/bin"], ["terser-webpack-plugin", "terser-webpack-plugin"], ["text-segmentation", "text-segmentation"], ["tree-kill", "tree-kill"], ["ts-node", "ts-node"], ["tslib", "tslib"], ["tweetnacl", "tweetnacl"], ["type-check", "type-check"], ["type-fest", "type-fest"], ["typed-assert", "typed-assert"], ["typescript", "typescript"], ["update-browserslist-db", "update-browserslist-db"], ["uri-js", "uri-js"], ["utrie", "utrie"], ["v8-compile-cache-lib", "v8-compile-cache-lib"], ["validate-npm-package-name", "validate-npm-package-name"], ["webdriver-js-extender", "webdriver-js-extender"], ["webdriver-js-extender", "webdriver-js-extender/built"], ["webpack", "webpack"], ["webpack-dev-middleware", "webpack-dev-middleware"], ["webpack-dev-server", "webpack-dev-server"], ["webpack-merge", "webpack-merge"], ["webpack-subresource-integrity", "webpack-subresource-integrity"], ["websocket-driver", "websocket-driver"], ["which-collection", "which-collection"], ["which-typed-array", "which-typed-array"], ["wmf", "wmf"], ["word-wrap", "word-wrap"], ["xlsx", "xlsx"], ["xmlbuilder", "xmlbuilder"], ["xmlchars", "xmlchars"], ["yaml", "yaml"], ["yargs", "yargs"], ["yargs-parser", "yargs-parser"], ["zone.js", "zone.js"]]}