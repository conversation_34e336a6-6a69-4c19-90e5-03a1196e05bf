{"name": "sparkRRG", "version": "3.1.10", "private": true, "scripts": {"start": "ng serve", "start:dev": "ng serve --configuration=development --open --host=0.0.0.0 --port=4200", "build": "ng build", "test": "ng test", "lint": "ng lint", "rrgProd": "ng build --configuration=rrgProd", "prodLocalBuild": "ng build --configuration=rrgProd && brotli-compress.bat"}, "dependencies": {"@angular/animations": "^14.3.0", "@angular/cdk": "^14.2.7", "@angular/common": "^14.3.0", "@angular/compiler": "^14.3.0", "@angular/core": "^14.3.0", "@angular/forms": "^14.3.0", "@angular/localize": "^14.3.0", "@angular/platform-browser": "^14.3.0", "@angular/platform-browser-dynamic": "^14.3.0", "@angular/platform-server": "^14.3.0", "@angular/router": "^14.3.0", "@babel/core": "^7.27.1", "@fortawesome/fontawesome-pro": "^6.7.2", "@microsoft/applicationinsights-web": "^2.8.18", "@ng-bootstrap/ng-bootstrap": "13.1.1", "@popperjs/core": "^2.11.8", "@types/googlemaps": "^3.43.3", "ag-grid-angular": "29.3.5", "ag-grid-community": "29.3.5", "ag-grid-enterprise": "29.3.5", "animate.css": "^3.7.2", "bootstrap": "^5.3.6", "canvg": "^4.0.3", "chart.js": "4.4.9", "chartjs-plugin-annotation": "3.1.0", "chartjs-plugin-datalabels": "^2.2.0", "core-js": "^3.42.0", "cropperjs": "^1.6.2", "dexie": "^2.0.4", "dompurify": "^3.2.5", "exceljs": "^3.10.0", "fast-json-patch": "^3.1.1", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jquery": "^3.7.1", "jspdf": "^3.0.1", "lodash-es": "^4.17.21", "moment": "^2.30.1", "ngx-image-cropper": "^2.1.2", "ngx-indexed-db": "^11.0.2", "rxjs": "^6.6.7", "sass": "^1.88.0", "tslib": "^2.8.1", "xlsx": "^0.18.5", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.13", "@angular-eslint/builder": "^14.0.1", "@angular-eslint/eslint-plugin": "^14.0.1", "@angular-eslint/eslint-plugin-template": "^14.0.1", "@angular-eslint/schematics": "^14.0.1", "@angular-eslint/template-parser": "^14.0.1", "@angular/cli": "^14.2.13", "@angular/compiler-cli": "^14.3.0", "@angular/language-service": "^14.3.0", "@biesbjerg/ngx-translate-extract": "^7.0.4", "@types/jasmine": "~3.10.18", "@types/jasminewd2": "~2.0.13", "@types/jquery": "^3.5.32", "@types/node": "^16.18.55", "brotli": "^1.3.3", "eslint": "^8.0.0", "jasmine-core": "~3.99.1", "jasmine-spec-reporter": "~5.0.2", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~4.0.2", "karma-jasmine-html-reporter": "^1.7.0", "protractor": "~7.0.0", "ts-node": "~10.9.1", "typescript": "^4.8.4", "webpack": "5.99.8", "webpack-bundle-analyzer": "^3.9.0"}}