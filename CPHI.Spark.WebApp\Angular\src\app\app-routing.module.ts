import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {AppComponent} from './app.component';

import {ChangePasswordComponent} from './components/change-password/change-password.component';
import {ForgotpasswordComponent} from './components/forgotpassword/forgotpassword.component';
import {LoginComponent} from './pages/login/login.component';
import {NoWebAccessComponent} from './pages/login/nowebaccess.component';
import {ManageUserComponent} from './components/manage-user/manage-user.component';
import {ResetPasswordComponent} from './components/reset-password/reset-password.component';
//import the components
//other imports
import {LoggedInGuard} from './loggedIn.guard';
import {AlcopaSummaryComponent} from './pages/alcopaSummary/alcopaSummary.component';
import {CitNowwComponent} from './pages/citNow/citNow.component';
import {ImageRatiosComponent} from './pages/imageRatios/imageRatios.component';
import {DashboardComponent} from './pages/dashboard/dashboard.component';
import {DealsDoneThisWeekComponent} from './pages/dealsDoneThisWeek/dealsDoneThisWeek.component';
import {DealsForTheMonthComponent} from './pages/dealsForTheMonth/dealsForTheMonth.component';
import {DebtsComponent} from './pages/debts/debts.component';
import {EvhcComponent} from './pages/evhc/evhc.component';
import {FAndISummaryComponent} from './pages/fAndISummary/fAndISummary.component';
import {GDPRComponent} from './pages/gdpr/gdpr.component';
import {HandoverDiaryComponent} from './pages/handoverDiary/handoverDiary.component';
import {OrderBookComponent} from './pages/orderBook/orderBook.component';
import {PartsStockComponent} from './pages/partsStock/partsStock.component';
import {PartsSummaryComponent} from './pages/partsSummary/partsSummary.component';
import {PerformanceLeagueComponent} from './pages/performanceLeague/performanceLeague.component';
import {RegistrationsPositionComponent} from './pages/registrationsPosition/registrationsPosition.component';
import {ReportingCentreComponent} from './pages/reportingCentre/reportingCentre.component';
import {SalesActivityComponent} from './pages/salesActivity/salesActivity.component';
import {SalesCommissionComponent} from './pages/salesCommission/salesCommission.component';
import {SalesmanEfficiencyComponent} from './pages/salesmanEfficiency/salesmanEfficiency.component';
import {SalesPerformanceComponent} from './pages/salesPerformance/salesPerformance.component';
import {ScratchCardComponent} from './pages/scratchCard/scratchCard.component';
import {ServiceBookingsComponent} from './pages/serviceBookings/serviceBookings.component';
import {ServiceSummaryComponent} from './pages/serviceSummary/serviceSummary.component';
import {StockLandingComponent} from './pages/stockLanding/stockLanding.component';
import {StockListComponent} from './pages/stockList/stockList.component';
import {StockReportComponent} from './pages/stockReport/stockReport.component';
import {SuperCupComponent} from './pages/superCup/superCup.component';
import {SuperCupTwoComponent} from './pages/superCupTwo/superCupTwo.component';
import {WhiteboardComponent} from './pages/whiteboard/whiteboard.component';
import {SalesExecReviewComponent} from './pages/salesExecReview/salesExecReview.component';
import {DistrinetComponent} from './pages/distrinet/distrinet.component';
import {LiveForecastInputComponent} from './pages/liveForecastInput/liveForecastInput.component';
import {LiveForecastStatusComponent} from './pages/liveForecastStatus/liveForecastStatus.component';
import {LiveForecastReviewComponent} from './pages/liveForecastReview/liveForecastReview.component';
import {UpsellsComponent} from './pages/upsells/upsells.component';
import {PerformanceTrendsComponent} from './pages/performanceTrends/performanceTrends.component';
import {FleetOrderbookComponent} from './pages/fleetOrderbook/fleetOrderbook.component';
import {StockInsightComponent} from './pages/autoprice/stockInsight/stockInsight.component';
import {PricingDashboardComponent} from './pages/autoprice/pricingDashboard/pricingDashboard.component';
import {AdvertListingDetailComponent} from './pages/autoprice/advertListingDetail/advertListingDetail.component';
import {LocationOptimiserComponent} from './pages/autoprice/locationOptimiser/locationOptimiser.component';
//import { ApplyStrategyComponent } from './pages/autoprice/applyStrategy/applyStrategy.component';
import {CommissionGuard} from './commission.guard';
import {StockLandingGuard} from './stockLanding.guard';
import {LocalBargainsComponent} from './pages/autoprice/localBargains/localBargains.component';
import {HomeComponent} from './pages/autoprice/home/<USER>';
import {OptOutsComponent} from './pages/autoprice/optOuts/optOuts.component';
import {TodayPricesComponent} from './pages/autoprice/todayPrices/todayPrices.component';
import {SiteSettingsComponent} from './pages/autoprice/siteSettings/siteSettings.component';
import {TeleStatsComponent} from './pages/teleStats/teleStats.component';
import {LeavingVehicleTrendsComponent} from './pages/autoprice/leavingVehicleTrends/leavingVehicleTrends.component';
import {LeavingVehicleDetailComponent} from './pages/autoprice/leavingVehicleDetail/leavingVehicleDetail.component';
import {AdvertSimpleListingComponent} from './pages/autoprice/advertSimpleListing/advertSimpleListing.component';
import {SalesIncentiveComponent} from './pages/salesIncentive/salesIncentive.component';
import {SimpleExamplePageComponent} from './pages/simpleExamplePage/simpleExamplePage.component';
import {OrderBookNewComponent} from './pages/orderBookNew/orderBookNew.component';
import {StatsDashboardComponent} from './pages/autoprice/statsDashboard/statsDashboard.component';
import {StockProfilerComponent} from './pages/autoprice/stockProfiler/stockProfiler.component';
import {SitesLeagueComponent} from './pages/autoprice/sitesLeague/sitesLeague.component';
import {
   LeavingVehicleTrendsOverTimeComponent
} from './pages/autoprice/leavingVehicleTrendsOverTime/leavingVehicleTrendsOverTime.component';
import {ReportSchedulesComponent} from "./pages/reportSchedules/report-schedules.component";
import { UserMaintenanceComponent } from './pages/userMaintenance/userMaintenance.component';
import { BulkValuationComponent } from './pages/autoprice/bulkValuation/bulkValuation.component';
import { WipReportComponent } from './pages/wipReport/wipReport.component';
import { SpainKPIsComponent } from './pages/dashboard/dashboards/dashboardOverviewSpain/dashboardOverviewSpain.component';
import { DashboardSalesComponent } from './pages/dashboard/dashboards/dashboardSalesRRG/dashboardSalesRRG.component';
import { DashboardAfterSalesRRGComponent } from './pages/dashboard/dashboards/dashboardAfterSalesRRG/dashboardAfterSalesRRG.component';
import { DashboardSiteCompareComponent } from './pages/dashboard/dashboards/dashboardSiteCompare/dashboardSiteCompare.component';
import { DashboardOverviewComponent } from './pages/dashboard/dashboards/dashboardOverviewVindis/dashboardOverviewVindis.component';
import { DashboardSalesVindisComponent } from './pages/dashboard/dashboards/dashboardSalesVindis/dashboardSalesVindis.component';
import { DashboardAfterSalesVindisComponent } from './pages/dashboard/dashboards/dashboardAfterSalesVindis/dashboardAfterSalesVindis.component';
import { OrdersBySiteComponent } from './pages/ordersBySite/ordersBySite.component';
import { DashboardNewVOSpainComponent } from './pages/dashboard/dashboards/dashboardNewVNSpain/dashboardNewVNSpain.component';
import { DashboardUsedSpainComponent } from './pages/dashboard/dashboards/dashboardUsedSpain/dashboardUsedSpain.component';
import { DashboardAftersalesSpainComponent } from './pages/dashboard/dashboards/dashboardAftersalesSpain/dashboardAftersalesSpain.component';
import { DashboardAftersalesSpainKPIComponent } from './pages/dashboard/dashboards/dashboardAftersalesSpainKPI/dashboardAftersalesSpainKPI.component';
import { DashboardAftersalesSpainDetailComponent } from './pages/dashboard/dashboards/dashboardAftersalesSpainDetail/dashboardAftersalesSpainDetail.component';
import { DashboardAftersalesSpainDatasetsComponent } from './pages/dashboard/dashboards/dashboardAftersalesSpainDatasets/dashboardAftersalesSpainDatasets.component';
import { StrategyBuildUpPageComponent } from './pages/autoprice/strategyBuildUp/strategyBuildUp.component';
import { UsageReportComponent } from './pages/usageReport/usagereport.component';
import {TagComponent} from "./pages/tag/tag.component";
import { LeavingVehicleExplorerPageComponent } from './pages/autoprice/leavingVehicleExplorer/leavingVehicleExplorer.component';

export enum PageNameRoutes {
   dashboard = 'dashboard',
   dealsDoneThisWeek = 'dealsDoneThisWeek',
   dealsForTheMonth = 'dealsForTheMonth',
   fAndISummary = 'fAndISummary',
   handoverDiary = 'handoverDiary',
   orderBook = 'orderBook',
   orderBookNew = 'orderBookNew',
   fleetOrderbook = 'fleetOrderbook',
   teleStats = 'teleStats',

   partsSummary = 'partsSummary',
   performanceLeague = 'performanceLeague',
   performanceTrends = 'performanceTrends',
   salesmanEfficiency = 'salesmanEfficiency',
   registrationsPosition = 'registrationsPosition',
   reportingCentre = 'reportingCentre',
   serviceSummary = 'serviceSummary',
   stockList = 'stockList',
   stockReport = 'stockReport',

   whiteboard = 'whiteboard',

  debtsSales = 'debtsSales',
  debtsAfterSales = 'debtsAfterSales',
  salesPerformance = 'salesPerformance',
  salesCommission = 'salesCommission',

  superCup = 'superCup',
  superCupTwo = 'superCupTwo',
  //voc = 'voc',
  citnow = 'citnow',
  imageRatios = 'imageRatios',
  evhc = 'evhc',
  partsStock = 'partsStock',
  serviceBookings = 'serviceBookings',
  stockLanding = 'stockLanding',
  scratchCard = 'scratchCard',
  alcopa = 'alcopa',
  login = 'login',
  noWebAccess = 'noWebAccess',
  forgotpassword = 'forgotpassword',
  resetpassword = 'resetpassword',
  changePassword = 'changePassword',
  manageUser = 'manageUser',
  userMaintenance = 'userMaintenance',
  salesActivity = 'salesActivity',
  gdpr = 'gdpr',
  salesExecReview = 'salesExecReview',
  distrinet = 'distrinet',
  liveForecastInput = 'liveForecastInput',
  liveForecastStatus = 'liveForecastStatus',
  liveForecastReview = 'liveForecastReview',
  upsells = 'upsells',

  home = 'home',
  stockInsight = 'stockInsight',
  pricingDashboard = 'pricingDashboard',
  advertListingDetail = 'advertListingDetail',
  advertSimpleListing = 'advertSimpleListing',
  locationOptimiser = 'locationOptimiser',
  //leavingVehicles = 'leavingVehicles',
  leavingVehicleTrends = 'leavingVehicleTrends',
  leavingVehicleTrendsOverTime = 'leavingVehicleTrendsOverTime',
  scheduledReports = 'scheduledReports',
  leavingVehicleDetail = 'leavingVehicleDetail',
  leavingVehicleExplorer = 'leavingVehicleExplorer',
  vehicleValuation = 'vehicleValuation',
  bulkValuation = 'bulkValuation',
  localBargains = 'localBargains',
  optOuts = 'optOuts',
  todaysPrices = 'todaysPrices',
  siteSettings = 'siteSettings',
  salesIncentive = 'salesIncentive',
  simpleExample = 'simpleExample',
  statsDashboard = 'statsDashboard',
  sitesLeague = 'sitesLeague',
  stockProfiler = 'stockProfiler',
  usageReport = 'usageReport',
  wipReport = 'wipReport',
  orderRate = 'orderRate',
  dashboardOverviewSpain = 'dashboardOverviewSpain',
  dashboardSalesRRG = 'dashboardSalesRRG',
  dashboardAfterSalesRRG = 'dashboardAfterSalesRRG',
  dashboardOverviewVindis = 'dashboardOverviewVindis',
  dashboardSalesVindis = 'dashboardSalesVindis',
  dashboardAfterSalesVindis = 'dashboardAfterSalesVindis',
  siteCompare = 'siteCompare',
  dashboardNewKPIs = 'dashboardNewKPIs',
  dashboardUsedKPIs = 'dashboardUsedKPIs',
  dashboardAfterSalesSpain = 'dashboardAfterSalesSpain',
  afterSalesKPIs = 'afterSalesKPIs',
  afterSalesDetail = 'afterSalesDetail',
  afterSalesDatasets = 'afterSalesDatasets',
  strategyPriceBuildUp= 'strategyPriceBuildUp',
  tagSettings = 'tagSettings',
}

const routes: Routes = [
   //{path:'',component:DashboardVindisComponent, canActivate:[LoggedInGuard],},
   //{ path: 'home', component: DashboardVindisComponent,canActivate:[LoggedInGuard],},  //

   {path: '/', component: AppComponent, canActivate: [LoggedInGuard],},
   {path: PageNameRoutes.dashboard, component: DashboardComponent, canActivate: [LoggedInGuard],},

   {path: PageNameRoutes.dealsDoneThisWeek, component: DealsDoneThisWeekComponent, canActivate: [LoggedInGuard],},
   {path: PageNameRoutes.dealsForTheMonth, component: DealsForTheMonthComponent, canActivate: [LoggedInGuard],},
   {path: PageNameRoutes.fAndISummary, component: FAndISummaryComponent, canActivate: [LoggedInGuard],},
   {path: PageNameRoutes.handoverDiary, component: HandoverDiaryComponent, canActivate: [LoggedInGuard],},
   {path: PageNameRoutes.orderBook, component: OrderBookComponent, canActivate: [LoggedInGuard],},
   {path: PageNameRoutes.orderBookNew, component: OrderBookNewComponent, canActivate: [LoggedInGuard],},
   {path: PageNameRoutes.fleetOrderbook, component: FleetOrderbookComponent, canActivate: [LoggedInGuard],},
   {path: PageNameRoutes.teleStats, component: TeleStatsComponent, canActivate: [LoggedInGuard],},

  { path: PageNameRoutes.partsSummary, component: PartsSummaryComponent, canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.performanceLeague, component: PerformanceLeagueComponent, canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.performanceTrends, component: PerformanceTrendsComponent, canActivate:[LoggedInGuard], },
  //{ path: PageNameRoutes.aftersalesLeague, component: AftersalesLeagueComponent, canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.salesmanEfficiency, component: SalesmanEfficiencyComponent, canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.registrationsPosition, component: RegistrationsPositionComponent, canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.reportingCentre, component: ReportingCentreComponent, canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.serviceSummary, component: ServiceSummaryComponent, canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.stockList, component: StockListComponent, canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.stockReport, component: StockReportComponent, canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.whiteboard, component: WhiteboardComponent, canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.debtsSales, component: DebtsComponent,  canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.debtsAfterSales, component: DebtsComponent,  canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.salesPerformance, component: SalesPerformanceComponent,  canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.salesCommission, component: SalesCommissionComponent,  canActivate:[CommissionGuard,LoggedInGuard], },
  
  { path: PageNameRoutes.superCup, component: SuperCupComponent,  canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.superCupTwo, component: SuperCupTwoComponent,  canActivate:[LoggedInGuard], },
  //{ path: PageNameRoutes.voc, component: VocComponent,  canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.citnow, component: CitNowwComponent,  canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.imageRatios, component: ImageRatiosComponent,  canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.evhc, component: EvhcComponent,  canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.partsStock, component: PartsStockComponent,  canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.serviceBookings, component: ServiceBookingsComponent,  canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.stockLanding, component: StockLandingComponent,  canActivate:[StockLandingGuard,LoggedInGuard], },
  { path: PageNameRoutes.scratchCard, component: ScratchCardComponent, canActivate: [LoggedInGuard], },
  { path: PageNameRoutes.alcopa, component: AlcopaSummaryComponent, canActivate: [LoggedInGuard], },
  { path: PageNameRoutes.login, component: LoginComponent },
  { path: PageNameRoutes.noWebAccess, component: NoWebAccessComponent },
  { path: PageNameRoutes.forgotpassword, component: ForgotpasswordComponent },
  { path: PageNameRoutes.resetpassword, component: ResetPasswordComponent },
  { path: PageNameRoutes.changePassword, component: ChangePasswordComponent, canActivate:[LoggedInGuard] },
  { path: PageNameRoutes.manageUser, component: ManageUserComponent, canActivate:[LoggedInGuard] },
  { path: PageNameRoutes.userMaintenance, component: UserMaintenanceComponent, canActivate:[LoggedInGuard], },
  { path: PageNameRoutes.salesActivity, component: SalesActivityComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.gdpr, component: GDPRComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.salesExecReview, component: SalesExecReviewComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.distrinet, component: DistrinetComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.liveForecastInput, component: LiveForecastInputComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.liveForecastStatus, component: LiveForecastStatusComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.liveForecastReview, component: LiveForecastReviewComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.upsells, component: UpsellsComponent, canActivate: [LoggedInGuard] },
  
  { path: PageNameRoutes.home, component: HomeComponent, canActivate: [LoggedInGuard] },    //home     | boolean is: pricingHome
  { path: PageNameRoutes.stockInsight, component: StockInsightComponent, canActivate: [LoggedInGuard] },  //blobs thing for current stock   | boolean is: stockInsight
  { path: PageNameRoutes.pricingDashboard, component: PricingDashboardComponent, canActivate: [LoggedInGuard] }, //top level dashboard   | boolean is: pricingDashboard
  { path: PageNameRoutes.advertListingDetail, component: AdvertListingDetailComponent, canActivate: [LoggedInGuard] },  //The main report   | boolean is: advertListingDetail
  { path: 'reg:regNumber', component: AdvertListingDetailComponent, canActivate: [LoggedInGuard] },  //The main report   | boolean is: advertListingDetail
  { path: PageNameRoutes.advertSimpleListing, component: AdvertSimpleListingComponent, canActivate: [LoggedInGuard] },  //The main report   | boolean is: advertListingDetail
  { path: PageNameRoutes.locationOptimiser, component: LocationOptimiserComponent, canActivate: [LoggedInGuard] },  //locationOptimiser   | boolean is: locationOptimiser
  //{ path: PageNameRoutes.leavingVehicles, component: LeavingVehiclesComponent, canActivate: [LoggedInGuard] }, //leaving vehicles   | boolean is: leavingVehicles
  { path: PageNameRoutes.leavingVehicleTrends, component: LeavingVehicleTrendsComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.leavingVehicleTrendsOverTime, component: LeavingVehicleTrendsOverTimeComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.scheduledReports, component: ReportSchedulesComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.leavingVehicleDetail, component: LeavingVehicleDetailComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.leavingVehicleExplorer, component: LeavingVehicleExplorerPageComponent, canActivate: [LoggedInGuard] },
  //{ path: PageNameRoutes.vehicleValuation, component: VehicleValuationComponent, canActivate: [LoggedInGuard] },  //DEAD NOW   | boolean is: vehicleValuation
  { path: PageNameRoutes.bulkValuation, component: BulkValuationComponent, canActivate: [LoggedInGuard] }, //  | boolean is: bulkValuation
  { path: 'valuation/:id/:reg/:mileage/:condition', component: BulkValuationComponent, canActivate: [LoggedInGuard] },  //The main report   | boolean is: advertListingDetail
  { path: PageNameRoutes.localBargains, component: LocalBargainsComponent, canActivate: [LoggedInGuard] }, //local bargains   | boolean is: localBargains
  { path: PageNameRoutes.optOuts, component: OptOutsComponent, canActivate: [LoggedInGuard] },  //optOuts in place   | boolean is: optOuts
  { path: PageNameRoutes.todaysPrices, component: TodayPricesComponent, canActivate: [LoggedInGuard] },  //todayPriceChanges   | boolean is: todayPriceChanges
  { path: PageNameRoutes.siteSettings, component: SiteSettingsComponent, canActivate: [LoggedInGuard] },  //siteSettings   | boolean is: autoPriceSiteSettings


  { path: PageNameRoutes.salesIncentive, component: SalesIncentiveComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.simpleExample, component: SimpleExamplePageComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.statsDashboard, component: StatsDashboardComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.sitesLeague, component: SitesLeagueComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.stockProfiler, component: StockProfilerComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.usageReport, component: UsageReportComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.wipReport, component: WipReportComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.orderRate, component: OrdersBySiteComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.dashboardOverviewSpain, component: SpainKPIsComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.dashboardSalesRRG, component: DashboardSalesComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.dashboardAfterSalesRRG, component: DashboardAfterSalesRRGComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.siteCompare, component: DashboardSiteCompareComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.dashboardOverviewVindis, component: DashboardOverviewComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.dashboardSalesVindis, component: DashboardSalesVindisComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.dashboardAfterSalesVindis, component: DashboardAfterSalesVindisComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.dashboardNewKPIs, component: DashboardNewVOSpainComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.dashboardUsedKPIs, component: DashboardUsedSpainComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.dashboardAfterSalesSpain, component: DashboardAftersalesSpainComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.afterSalesKPIs, component: DashboardAftersalesSpainKPIComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.afterSalesDetail, component: DashboardAftersalesSpainDetailComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.afterSalesDatasets, component: DashboardAftersalesSpainDatasetsComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.strategyPriceBuildUp, component: StrategyBuildUpPageComponent, canActivate: [LoggedInGuard] },
  { path: PageNameRoutes.tagSettings, component: TagComponent, canActivate: [LoggedInGuard] },

  //catch all
  {path:'**',redirectTo:'/'},


];

@NgModule({
   imports: [RouterModule.forRoot(routes)],
   exports: [RouterModule]
})
export class AppRoutingModule {


}



