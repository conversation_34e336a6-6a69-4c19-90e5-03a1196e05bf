import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
//-----------------------------------------
//libraries used
//-----------------------------------------
import {<PERSON><PERSON><PERSON>cyPipe, DatePipe, DecimalPipe, TitleCasePipe} from '@angular/common';
import {HTTP_INTERCEPTORS, HttpClient, HttpClientModule} from '@angular/common/http';
import {APP_INITIALIZER, E<PERSON>rHand<PERSON>, NgModule} from '@angular/core';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {BrowserModule} from '@angular/platform-browser';
import {NgbActiveModal, NgbModule, NgbPagination} from '@ng-bootstrap/ng-bootstrap';
import {AgGridModule} from 'ag-grid-angular';
import 'ag-grid-enterprise';
import {LicenseManager} from 'ag-grid-enterprise';

import {ApplicationInsights} from '@microsoft/applicationinsights-web';
import {AppRoutingModule} from './app-routing.module';
//-----------------------------------------
//other angular plumbing stuff
//-----------------------------------------
import {AppComponent} from './app.component';
import {PartsSalesByTypeTileComponent} from './components/aftersalesTiles/partsSalesByTypeTile.component';
import {RunChaseTileComponent} from './components/aftersalesTiles/runChaseTile.component';
import {RunRateAndRequirementTileComponent} from './components/aftersalesTiles/runRateAndRequirementTile.component';
import {SalesByDayTileComponent} from './components/aftersalesTiles/salesByDayTile.component';
import {SalesPositionTileComponent} from './components/aftersalesTiles/salesPositionTile.component';
import {ServiceSalesByTypeTileComponent} from './components/aftersalesTiles/serviceSalesByTypeTile.component';
import {WipAgeingSummaryTileComponent} from './components/aftersalesTiles/wipAgeingSummaryTile.component';
import {ChangePasswordComponent} from './components/change-password/change-password.component';
import {RunChaseTileModalComponent} from './components/runChaseTileModal/runChaseTileModal.component';
//-----------------------------------------
// components - shared
//-----------------------------------------
import {CommentEditorComponent} from './components/commentEditor.component';
import {ConfirmModalComponent} from './components/confirmModal.component';
import {InputModalComponent} from './components/inputModal.component';
import {DarkModeToggle} from './components/darkModeToggle.component';
import {DataOriginsUpdatesModalComponent} from './components/dataOriginsUpdatesModal/dataOriginsUpdatesModal.component';
import {DatePickerModalComponent} from './components/datePickerModal/datePickerModal.component';
import {DatePickerMultiSelectComponent} from './components/datePickerMultiSelect/datePickerMultiSelect.component';
import {DealDetailCommentComponent} from './components/dealDetails/dealDetailComment.component';
import {DealDetailsComponent} from './components/dealDetails/dealDetails.component';
import {DealDetailsFileSentDatesComponent} from './components/dealDetails/dealDetailsFileSentDates.component';
import {DealPopoverComponent} from './components/dealPopover.component';
import {DealPopoverRRGComponent} from './components/dealPopoverRRG.component';
import {ForgotpasswordComponent} from './components/forgotpassword/forgotpassword.component';
import {FranchisePickerComponent} from './components/franchisePicker.component';
import {GuageChartComponent} from './components/guageChart/guageChart.component';
import {HintComponent} from './components/hint.component';
import {InitialLoadModalComponent} from './components/initialLoadModal.component';
import {ManageUserComponent} from './components/manage-user/manage-user.component';
import {LoginComponent} from "./pages/login/login.component";
import {NoWebAccessComponent} from "./pages/login/nowebaccess.component";
import {ExecManagerMappingComponent} from './pages/salesExecReview/execManagerMapping/execManagerMapping.component';
import {ModelPickerComponent} from './pages/stockList/modelPicker.component';
//-----------------------------------------
//pages
//-----------------------------------------
import {OrderTypePickerComponent} from './components/orderTypePicker.component';
import {PickerSimpleComponent} from './components/pickerSimple.component';
import {ProfilePicComponent} from './components/profilePic.component';
import {ProfilePicImageComponent} from './components/profilePicImage.component';
import {ResetPasswordComponent} from './components/reset-password/reset-password.component';
import {RunChaseChartComponent} from './components/runChaseChart.component';
import {SitePickerComponent} from './components/sitePicker.component';
import {SitePickerRRGComponent} from './components/sitePickerRRG.component';
import {SourceDataUpdateComponent} from './components/sourceDataUpdate.component';
import {StockFamilyPickerComponent} from './components/stockFamilyPicker.component';
import {StockGroupPickerComponent} from './components/stockGroupPicker.component';
import {PriceChartComponent} from './components/stockItemModal/priceChart.component';
import {StockItemModalComponent} from './components/stockItemModal/stockItemModal.component';
import {StockItemModalBodyComponent} from './components/stockItemModal/stockItemModalBody/stockItemModalBody.component';
import {
   stockItemModalBodyAutoTraderComponent
} from './components/stockItemModal/stockItemModalBodyAutoTrader/stockItemModalBodyAutoTrader.component';
import {ModelChartComponent} from './components/stockReportModal/modelChart.component';
import {StockReportModalComponent} from './components/stockReportModal/stockReportModal.component';
import {StockReportVehicleTableComponent} from './components/stockReportModal/stockReportVehicleTable.component';
import {TinyDonutComponent} from './components/tinyDonut.component';
import {ToastComponent} from './components/toast.component';
import {VehicleTypePickerComponent} from './components/vehicleTypePicker.component';
import {VehicleTypePickerSpainComponent} from './components/vehicleTypePickerSpain.component';
import {CphPipe} from './cph.pipe';
import {ErrorInterceptor} from './error.interceptor';
import {JwtInterceptor} from './jwt.interceptor';
import {LoggedInGuard} from './loggedIn.guard';
import {AftersalesLeagueComponent} from './pages/aftersalesLeague/aftersalesLeague.component';
import {PerformanceLeagueCardAdvisorsComponent} from './pages/aftersalesLeague/performanceLeagueCardAdvisors.component';
import {PerformanceLeagueCardTechsComponent} from './pages/aftersalesLeague/performanceLeagueCardTechs.component';
import {AlcopaSummaryComponent} from './pages/alcopaSummary/alcopaSummary.component';
import {AlcopaTableComponent} from './pages/alcopaSummary/alcopaTable.component';
import {CitNowwComponent} from './pages/citNow/citNow.component';
import {ImageRatiosComponent} from './pages/imageRatios/imageRatios.component';
import {ImageRatiosChartComponent} from './pages/imageRatios/imageRatiosChart.component';
import {CitNowwChartComponent} from './pages/citNow/citNowChart.component';
import {CitNowSiteDetailModalComponent} from './pages/citNow/citNowSiteDetailModal.component';
import {CitNowwTableComponent} from './pages/citNow/citNowTable.component';
import {CitNowVideoBreakdownModalComponent} from './pages/citNow/citNowVideoBreakdownModal.component';
import {CitNowVideoBreakdownTableComponent} from './pages/citNow/citNowVideoBreakdownTable.component';
import {DashboardComponent} from './pages/dashboard/dashboard.component';
import {
   DashboardAfterSalesRRGComponent
} from './pages/dashboard/dashboards/dashboardAfterSalesRRG/dashboardAfterSalesRRG.component';
import {
   DashboardAfterSalesVindisComponent
} from './pages/dashboard/dashboards/dashboardAfterSalesVindis/dashboardAfterSalesVindis.component';
import {
   DashboardOverviewComponent
} from './pages/dashboard/dashboards/dashboardOverviewVindis/dashboardOverviewVindis.component';
import {DashboardSalesComponent} from './pages/dashboard/dashboards/dashboardSalesRRG/dashboardSalesRRG.component';
import {
   DashboardSalesVindisComponent
} from './pages/dashboard/dashboards/dashboardSalesVindis/dashboardSalesVindis.component';
import {
   DashboardSiteCompareComponent
} from './pages/dashboard/dashboards/dashboardSiteCompare/dashboardSiteCompare.component';
import {ActivityLevelsTileComponent} from './pages/dashboard/tiles/activityLevelsTile/activityLevelsTile.component';
import {AgedUsedStockTileComponent} from './pages/dashboard/tiles/agedUsedStockRRG/agedUsedStockRRG.component';
import {AgedWipTileComponent} from './pages/dashboard/tiles/agedWipTile/agedWipTile.component';
import {AlcopasSummaryTileComponent} from './pages/dashboard/tiles/alcopasSummaryTile/alcopasSummaryTile.component';
import {BonusDebtsRRGComponent} from './pages/dashboard/tiles/bonusDebtsRRG/bonusDebtsRRG.component';
import {CashDebtsRRGComponent} from './pages/dashboard/tiles/cashDebtsRRG/cashDebtsRRG.component';
import {CitNowTileComponent} from './pages/dashboard/tiles/citNowTile/citNowTile.component';
import {CommissionsTileComponent} from './pages/dashboard/tiles/commissionsTile/commissionsTile.component';
import {
   DailyOrdersVsLastYearComponent
} from './pages/dashboard/tiles/dailyOrdersVsLastYear/dailyOrdersVsLastYear.component';
import {DeliveryVsOrderDateComponent} from './pages/dashboard/tiles/deliveryVsOrderDate/deliveryVsOrderDate.component';
import {
   DepartmentDealBreakdownComponent
} from './pages/dashboard/tiles/departmentDealBreakdown/departmentDealBreakdown.component';
import {
   DepartmentProfitPerUnitComponent
} from './pages/dashboard/tiles/departmentProfitPerUnit/departmentProfitPerUnit.component';
import {
   DetailedBookingsTileComponent
} from './pages/dashboard/tiles/detailedBookingsTile/detailedBookingsTile.component';
import {DonutTileComponent} from './pages/dashboard/tiles/donut/donut.component';
import {EvhcSoldTileComponent} from './pages/dashboard/tiles/evhcSoldTile/evhcSoldTile.component';
import {EvhcTileComponent} from './pages/dashboard/tiles/evhcTile/evhcTile.component';
import {FAndITileComponent} from './pages/dashboard/tiles/fAndITile/fAndITile.component';
import {PartsGuageTileComponent} from './pages/dashboard/tiles/partsGuage/partsGuageTile.component';
import {
   PartsStockTileOver1YrComponent
} from './pages/dashboard/tiles/partsStockTileOver1Yr/partsStockTileOver1Yr.component';
import {
   PartsStockTile6To12Component
} from './pages/dashboard/tiles/partsStockTileSixToTwelve/partsStockTileSixToTwelve.component';
import {
   PercentageAndFiguresComponent
} from './pages/dashboard/tiles/percentageAndFigures/percentageAndFigures.component';
import {PercentageTileComponent} from './pages/dashboard/tiles/percentageTile/percentageTile.component';
import {ReconditioningTileComponent} from './pages/dashboard/tiles/reconditioningTile/reconditioningTile.component';
import {RegistrationsTileComponent} from './pages/dashboard/tiles/registrationsTile/registrationsTile.component';
import {ServiceBookingsTileComponent} from './pages/dashboard/tiles/serviceBookingsTile/serviceBookingsTile.component';
import {ServiceDetailedTileComponent} from './pages/dashboard/tiles/serviceDetailedTile/serviceDetailedTile.component';
import {ServiceGuageTileComponent} from './pages/dashboard/tiles/serviceGuage/serviceGuageTile.component';
import {SiteCompareComponent} from './pages/dashboard/tiles/siteCompare/siteCompareTile.component';
import {StockOverageTileComponent} from './pages/dashboard/tiles/stockOverageTile/stockOverageTile.component';
import {ThisWeekOrdersComponent} from './pages/dashboard/tiles/thisWeekOrders/thisWeekOrders.component';
import {
   ThisWeekOrdersDetailedTableComponent
} from './pages/dashboard/tiles/thisWeekOrders/thisWeekOrdersDetailedTable.component';
import {ThisWeekOrdersModalComponent} from './pages/dashboard/tiles/thisWeekOrders/thisWeekOrdersModal.component';
import {
   ThisWeekOrdersSitesTableComponent
} from './pages/dashboard/tiles/thisWeekOrders/thisWeekOrdersSitesTable.component';
import {
   ThisWeekOrdersSimpleComponent
} from './pages/dashboard/tiles/thisWeekOrdersSimple/thisweekOrdersSimple.component';
import {TodayMapComponent} from './pages/dashboard/tiles/todayMap/todayMap.component';
import {UnitsAndValueComponent} from './pages/dashboard/tiles/unitsAndValue/unitsAndValue.component';
import {UsedStockHealthTileComponent} from './pages/dashboard/tiles/usedStockHealth/usedStockHealthTile.component';
import {
   UsedStockMerchBySiteTileComponent
} from './pages/dashboard/tiles/usedStockMerchBySiteTile/usedStockMerchBySiteTile.component';
import {
   UsedStockMerchandisingTileComponent
} from './pages/dashboard/tiles/usedStockMerchandising/usedStockMerchandising.component';
import {VocTileComponent} from './pages/dashboard/tiles/vocTile/vocTile.component';
import {WipBarChartComponent} from './pages/dashboard/tiles/wipPositionTile/wipPositionTile.component';
import {DistrinetComponent} from './pages/distrinet/distrinet.component';
import {OrdersBySiteComponent} from './pages/ordersBySite/ordersBySite.component';
//----------------------------------------+
//dashboard subcomponents
//----------------------------------------+
import {DealsDoneThisWeekComponent} from './pages/dealsDoneThisWeek/dealsDoneThisWeek.component';
import {DealsForTheMonthComponent} from './pages/dealsForTheMonth/dealsForTheMonth.component';
import {DebtsComponent} from './pages/debts/debts.component';
import {BonusesTableComponent} from './pages/debts/tables/bonusesTable.component';
import {DebtsTableComponent} from './pages/debts/tables/debtsTable.component';
import {SitesTableComponent} from './pages/debts/tables/sitesTable.component';
//-----------------------------------------
// components - page specific
//-----------------------------------------
import {BarComponent} from './_cellRenderers/bar.component';
import {BiDirectionalBarComponent} from './_cellRenderers/biDirectionalBar.component';
import {CitNowTableEDynamixComponent} from './pages/citNow/citNowTableEDynamix.component';
import {
   DashboardInvoicedDealsTableComponent
} from './pages/dashboard/dashboards/dashboardInvoicedDealsSpain/dashboardInvoicedDealsTable.component';
import {DistrinetModalComponent} from './pages/distrinet/modal/distrinetModal.component';
import {EvhcComponent} from './pages/evhc/evhc.component';
import {EvhcSitesTableComponent} from './pages/evhc/evhcSitesTable.component';
import {evhcSitesTableEDynamixComponent} from './pages/evhc/evhcSitesTableEDynamix.component';
import {EvhcTechsTableComponent} from './pages/evhc/evhcTechsTable.component';
import {FAndISummaryComponent} from './pages/fAndISummary/fAndISummary.component';
import {FandITablePeopleComponent} from './pages/fAndISummary/fAndITablePeople.component';
import {FandITableSitesComponent} from './pages/fAndISummary/fAndITableSites.component';
import {FleetOrderbookComponent} from './pages/fleetOrderbook/fleetOrderbook.component';
import {GDPRComponent} from './pages/gdpr/gdpr.component';
import {GDPRTableComponent} from './pages/gdpr/gdprTable.component';
import {ExecPickerComponent} from './pages/handoverDiary/execPicker.component';
import {HandoverDiaryComponent} from './pages/handoverDiary/handoverDiary.component';
import {LiveForecastSummaryTableComponent} from './pages/liveForecastInput/visuals/summaryTable.component';
import {LiveForecastReviewTableComponent} from './pages/liveForecastReview/liveForecastReviewTable.component';
import {ApproveForecastButtonComponent} from './pages/liveForecastStatus/cellRenderers/approveForecastButton';
import {ClearForecastButtonComponent} from './pages/liveForecastStatus/cellRenderers/clearForecastButton';
import {RejectForecastButtonComponent} from './pages/liveForecastStatus/cellRenderers/rejectForecastButton';
import {OrderBookComponent} from './pages/orderBook/orderBook.component';
import {PartsStockComponent} from './pages/partsStock/partsStock.component';
import {PartsStockDetailedTableComponent} from './pages/partsStock/partsStockDetailedTable.component';
import {PartsStockModalComponent} from './pages/partsStock/partsStockModal.component';
import {PartsStockTableAgeingComponent} from './pages/partsStock/partsStockSitesAgeingTable.component';
import {PartsStockTableCoverTableComponent} from './pages/partsStock/partsStockSitesCoverTable.component';
import {PartsSummaryComponent} from './pages/partsSummary/partsSummary.component';
import {PartsTableComponent} from './pages/partsSummary/partsTable.component';
import {PerformanceLeagueComponent} from './pages/performanceLeague/performanceLeague.component';
import {RegistrationsChartComponent} from './pages/registrationsPosition/registrationsChart.component';
import {RegistrationsModalComponent} from './pages/registrationsPosition/registrationsModal.component';
import {RegistrationsPositionComponent} from './pages/registrationsPosition/registrationsPosition.component';
import {RegistrationsTableComponent} from './pages/registrationsPosition/registrationsTable.component';
import {ReportingCentreComponent} from './pages/reportingCentre/reportingCentre.component';
import {SalesActivityComponent} from './pages/salesActivity/salesActivity.component';
import {SalesActivityTableComponent} from './pages/salesActivity/salesActivityTable.component';
import {SalesCommissionComponent} from './pages/salesCommission/salesCommission.component';
import {SalesCommissionRRGComponent} from './pages/salesCommissionRRG/salesCommissionRRG.component';
import {CommissionSitesTableRRGComponent} from './pages/salesCommissionRRG/tables/commissionSitesTableRRG.component';
import {
   CommissionStatementTableRRGComponent
} from './pages/salesCommissionRRG/tables/commissionStatementTableRRG.component';
import {CommissionReportComponent} from './pages/salesCommissionVindis/commissionReport/commissionReport.component';
import {SalesCommissionVindisComponent} from './pages/salesCommissionVindis/salesCommissionVindis.component';
import {
   CommissionPeopleTableVindisComponent
} from './pages/salesCommissionVindis/tables/commissionPeopleTableVindis.component';
import {
   CommissionSitesTableVindisComponent
} from './pages/salesCommissionVindis/tables/commissionSitesTableVindis.component';
import {
   CommissionStatementTableVindisComponent
} from './pages/salesCommissionVindis/tables/commissionStatementTableVindis/commissionStatementTableVindis.component';
import {
   SalesExecReviewLastSixMonthsTrendComponent
} from './pages/salesExecReview/charts/lastSixMonthsTrendChart.component';
import {
   SalesExecReviewSalesExecByMonthChartComponent
} from './pages/salesExecReview/charts/salesExecsByMonthChart.component';
import {SalesExecReviewFormComponent} from './pages/salesExecReview/form/form.component';
import {SalesExecReviewComponent} from './pages/salesExecReview/salesExecReview.component';
import {SalesExecReviewByMeasureTableComponent} from './pages/salesExecReview/tables/byMeasure/byMeasure.component';
import {SalesExecReviewByMonthTableComponent} from './pages/salesExecReview/tables/byMonth/byMonth.component';

import {TeleStatRowRenderer} from './pages/teleStats/teleStatRowRenderer/teleStatRowRenderer.component';
import {DeleteFonButtonComponent} from './pages/fleetOrderbook/editFONsModal/deleteFonButton.component';
import {HorizontalBarTgtActComponent} from './pages/salesmanEfficiency/horizontalBarTgtAct.component';
import {ProductivityComponent} from './pages/salesmanEfficiency/productivity';
import {SalesmanEfficiencyComponent} from './pages/salesmanEfficiency/salesmanEfficiency.component';
import {SalesmanEfficiencyPeopleEditorComponent} from './pages/salesmanEfficiency/salesmanEfficiencyPeopleEditor';
import {
   SalesmanEfficiencyTableComponent
} from './pages/salesmanEfficiency/salesmanEfficiencyTable/salesmanEfficiencyTable.component';
import {ManagerCardComponent} from './pages/scratchCard/managerCard/managerCard.component';
import {SalesmanCardComponent} from './pages/scratchCard/salesmanCard/salesmanCard.component';
import {ScratchCardComponent} from './pages/scratchCard/scratchCard.component';
import {SiteCardComponent} from './pages/scratchCard/siteCard/siteCard.component';
import {ServiceBookingsComponent} from './pages/serviceBookings/serviceBookings.component';
import {
   ServiceBookingsSiteDetailTableComponent
} from './pages/serviceBookings/serviceBookingsSiteDetailTable.component';
import {
   ServiceBookingsTableComponent
} from './pages/serviceBookings/serviceBookingsTable/serviceBookingsTable.component';
import {ServiceSummaryComponent} from './pages/serviceSummary/serviceSummary.component';
import {BarChartAndRiskOppsComponent} from './pages/stockLanding/barChartAndRiskOpps/barChartAndRiskOpps.component';
import {StockLandingComponent} from './pages/stockLanding/stockLanding.component';
import {StockLandingPanelComponent} from './pages/stockLanding/stockLandingPanel/stockLandingPanel.component';
import {StockListComponent} from './pages/stockList/stockList.component';
import {ChartComponent} from './pages/stockReport/chart.component';
import {StockReportComponent} from './pages/stockReport/stockReport.component';
import {AllStockTableComponent} from './pages/stockReport/tables/allStockTable.component';
import {OverAgeStockTableComponent} from './pages/stockReport/tables/overAgeStockTable.component';
import {UsedStockMerchTableComponent} from './pages/stockReport/tables/usedStockMerchTable.component';
import {UsedStockTableComponent} from './pages/stockReport/tables/usedStockTable.component';
import {TeleStatsTableComponent} from './pages/teleStats/teleStatsTable/teleStatsTable.component';
import {ConfettiComponent} from './pages/superCup/confetti/confetti.component';
import {DealListPopoverComponent} from './pages/superCup/dealList.component';
import {SuperCupComponent} from './pages/superCup/superCup.component';
import {UserMaintenanceComponent} from './pages/userMaintenance/userMaintenance.component';
import {UserSitePickerComponent} from './pages/userMaintenance/userSitePicker.component';
import {VocComponent} from './pages/voc/voc.component';
import {VocCardComponent} from './pages/voc/vocCard.component';
import {VocTableComponent} from './pages/voc/vocTable.component';
import {WhiteboardComponent} from './pages/whiteboard/whiteboard.component';
import {WipReportComponent} from './pages/wipReport/wipReport.component';
import {BroadcastMessageModalComponent} from './components/broadcastMessageModal/broadcastMessageModal.component';
import {WipSitesTableComponent} from './pages/wipReport/wipSitesTable.component';
import {AppConfigService} from './services/appConfig.service';
import {EnvironmentService} from './services/environment.service';
import {CurrencySpainPipe} from './spainCurrency.pipe';
import {DecimalSpainPipe} from './spainDecimal.pipe';
import {RunChaseChartLargeComponent} from './components/runChaseChartLarge.component';
import {TeleStatsComponent} from './pages/teleStats/teleStats.component';

//-----------------------------------------
//components for AgGrid
//-----------------------------------------
import {ImageCropperModule} from 'ngx-image-cropper';
import {AuditPassComponent} from './_cellRenderers/auditPass.component';
import {BarCurrencyComponent} from './_cellRenderers/barCurrency.component';
import {BarCurrencyAndPercentageComponent} from './_cellRenderers/barCurrencyAndPercentage.component';
import {BarsOnRRGSiteComponent} from './_cellRenderers/barsOnRRGSite.component';
import {BarsPreppedComponent} from './_cellRenderers/barsPrepped.component';
import {BookingsBarComponent} from './_cellRenderers/bookingsBar.component';
import {BooleanComponent} from './_cellRenderers/boolean.component';
import {ChassisComponent} from './_cellRenderers/chassis.component';
import {CommentsCellComponent} from './_cellRenderers/commentsCell.component';
import {CurrencyComponent} from './_cellRenderers/currency.component';
import {CustomHeaderComponent} from './_cellRenderers/customHeader.component';
import {DateComponent} from './_cellRenderers/date.component';
import {DebtCommentsComponent} from './_cellRenderers/debtComments.component';
import {DeleteButtonComponent} from './_cellRenderers/deleteButton.component';
import {HorizontalBarComponent} from './_cellRenderers/horizontalBar.component';
import {IndexComponent} from './_cellRenderers/index.component';
import {LabelForPeopleComponent} from './_cellRenderers/labelForPeople.component';
import {LengthComponent} from './_cellRenderers/length.component';
import {NumberComponent} from './_cellRenderers/number.component';
import {PartsStockBarComponent} from './_cellRenderers/partsStockBar.component';
import {PersonPhotoComponent} from './_cellRenderers/personPhoto';
import {ProductsComponent} from './_cellRenderers/products.component';
import {ProductsHeaderComponent} from './_cellRenderers/productsHeader.component';
import {RegPlateComponent} from './_cellRenderers/regPlate.component';
import {RegPlate1Component} from './_cellRenderers/regPlate1.component';
import {OnRRGSiteComponent} from './_cellRenderers/stockDetailsIcon.component';
import {StockTakePhotoComponent} from './_cellRenderers/stockTakePhoto.component';
import {BISlicerTileComponent} from './components/biSlicerTile/biSlicerTile.component';
import {CommissionAdjustmentComponent} from './components/commissionAdjustment/commissionAdjustment.component';
import {DonutComponent} from './components/donut/donut.component';
import {ScreenRotationModalComponent} from './components/screenRotationModal/screenRotationModal.component';
import {SliderSwitchComponent} from './components/sliderSwitch/sliderSwitch.component';
import {
   DashboardAftersalesSpainComponent
} from './pages/dashboard/dashboards/dashboardAftersalesSpain/dashboardAftersalesSpain.component';
import {
   DashboardAftersalesSpainDatasetsComponent
} from './pages/dashboard/dashboards/dashboardAftersalesSpainDatasets/dashboardAftersalesSpainDatasets.component';
import {
   DashboardAftersalesSpainDatasetsTableComponent
} from './pages/dashboard/dashboards/dashboardAftersalesSpainDatasets/table/dashboardAftersalesSpainDatasetsTable.component';
import {
   DashboardAftersalesSpainDetailComponent
} from './pages/dashboard/dashboards/dashboardAftersalesSpainDetail/dashboardAftersalesSpainDetail.component';
import {
   DashboardAftersalesSpainKPIComponent
} from './pages/dashboard/dashboards/dashboardAftersalesSpainKPI/dashboardAftersalesSpainKPI.component';
import {
   DashboardAftersalesSpainKPITableComponent
} from './pages/dashboard/dashboards/dashboardAftersalesSpainKPI/dashboardAftersalesSpainKPITable.component';
import {
   DashboardInvoicedDealsSpainComponent
} from './pages/dashboard/dashboards/dashboardInvoicedDealsSpain/dashboardInvoicedDealsSpain.component';
import {
   DashboardNewVOSpainComponent
} from './pages/dashboard/dashboards/dashboardNewVNSpain/dashboardNewVNSpain.component';
import {
   DashboardNewVNSpainOrdersPageComponent
} from './pages/dashboard/dashboards/dashboardNewVNSpain/ordersPage/ordersPage.component';
import {
   DashboardNewVNSpainStocksPageComponent
} from './pages/dashboard/dashboards/dashboardNewVNSpain/stockPage/stockPage.component';
import {VNStringPickerComponent} from './pages/dashboard/dashboards/dashboardNewVNSpain/vnStringPicker.component';
import {SpainKPIsComponent} from './pages/dashboard/dashboards/dashboardOverviewSpain/dashboardOverviewSpain.component';
import {StockBarChartComponent} from './pages/dashboard/dashboards/dashboardUsedSpain/charts/stockBarChart.component';
import {StockPieChartComponent} from './pages/dashboard/dashboards/dashboardUsedSpain/charts/stockPieChart.component';
import {
   DashboardUsedSpainComponent
} from './pages/dashboard/dashboards/dashboardUsedSpain/dashboardUsedSpain.component';
import {
   SpainDashboardUsedGraphsComponent
} from './pages/dashboard/dashboards/dashboardUsedSpain/usedGraphs/usedGraphs.component';
import {
   StockByAgeTableComponent
} from './pages/dashboard/dashboards/dashboardUsedSpain/usedTables/stockByAgeTable.component';
import {
   SpainDashboardUsedTablesComponent
} from './pages/dashboard/dashboards/dashboardUsedSpain/usedTables/usedTables.component';
import {AssignmentTileComponent} from './pages/dashboard/tiles/assignmentTile/assignmentTile.component';
import {
   DataOriginsUpdatesTileComponent
} from './pages/dashboard/tiles/dataOriginsUpdateTile/dataOriginsUpdateTile.component';
import {InvoicedDonutComponent} from './pages/dashboard/tiles/invoicedDonut/invoicedDonut.component';
import {
   InvoicingPerformanceComponent
} from './pages/dashboard/tiles/invoicingPerformance/invoicingPerformance.component';
import {OrdersDonutComponent} from './pages/dashboard/tiles/ordersDonut/ordersDonut.component';
import {
   TurnoverPerOperativeComponent
} from './pages/dashboard/tiles/turnoverPerOperative/turnoverPerOperative.component';
import {CustomEditorComponent} from './pages/fleetOrderbook/editFONsModal/customEditor.component';
import {
   FleetOrderbookCommentsCellComponent
} from './pages/fleetOrderbook/fleetOrderbookCommentsCell/fleetOrderbookCommentsCell.component';
import {
   FleetOrderbookCommentEditorComponent
} from './pages/fleetOrderbook/fleetOrderbookCommentsEditor/fleetOrderbookCommentEditor.component';
import {FleetOrderbookTableComponent} from './pages/fleetOrderbook/fleetOrderbookTable/fleetOrderbookTable.component';
import {SimpleTextModalComponent} from './pages/fleetOrderbook/simpleTextModal/simpleTextModal.component';
import {LiveForecastInputComponent} from './pages/liveForecastInput/liveForecastInput.component';
import {LiveForecastReviewComponent} from './pages/liveForecastReview/liveForecastReview.component';
import {LiveForecastStatusComponent} from './pages/liveForecastStatus/liveForecastStatus.component';
import {OrdersBySiteBarComponent} from './pages/ordersBySite/horizontalBar.component';
import {OrdersBySiteTableComponent} from './pages/ordersBySite/ordersBySiteTable.component';
import {RatioOrdersForSiteTableComponent} from './pages/ordersBySite/ordersForSiteTable.component';
import {
   BusinessManagersCommissionComponent
} from './pages/salesCommissionRRG/tables/businessManagersCommission/businessManagersCommission.component';
import {ServiceTableComponent} from './pages/serviceSummary/serviceTable.component';
import {SuperCupTwoComponent} from './pages/superCupTwo/superCupTwo.component';
import {UpsellsComponent} from './pages/upsells/upsells.component';
import {UpsellsTableComponent} from './pages/upsells/upsellsTable.component';
import {UserModalComponent} from './pages/userMaintenance/userModal/userModal.component';
import {WipDetailTableComponent} from './pages/wipReport/wipDetailTable.component';
import {ApplicationInsightLoggingService} from './services/applicationInsightLogging.service';
import {ErrorHandlerService} from './services/errorHandler.service';
import {ExtendedFilterComponent} from './_cellRenderers/extendedFilter.component';
import {ExtendedFilterFloatingFilterComponent} from './_cellRenderers/extendedFilterFloatingFilter.component';
import {SliderFloatingFilter} from './_cellRenderers/myFloatingFilter.component';
import {TranslatedText} from './model/translations.model';
import {
   DashboardSalesRRGFleetComponent
} from './pages/fleetOrderbook/dashboardSalesRRGFleet/dashboardSalesRRGFleet.component';
import {EditFONsModalComponent} from './pages/fleetOrderbook/editFONsModal/editFONsModal.component';
import {HorizontalBarNewComponent} from './_cellRenderers/horizontalBarNew.component';

import {PerformanceTrendsTableComponent} from './pages/performanceTrends/performanceTrendsTable.component';
import {ManagerPickerModalComponent} from './pages/salesExecReview/execManagerMapping/managerPickerModal.component';
import {
   SalesPerformanceCustomTableComponent
} from './pages/salesPerformance/customTable/salesPerformanceCustomTable.component';
import {
   SalesPerformanceOrderRateTableComponent
} from './pages/salesPerformance/orderRateTable/salesPerformanceOrderRateTable.component';
import {SalesPerformanceComponent} from './pages/salesPerformance/salesPerformance.component';
import {ProjectedChartComponent} from './pages/salesPerformance/salesProjectionModal/projectedChart.component';
import {
   SalesProjectionModalComponent
} from './pages/salesPerformance/salesProjectionModal/salesProjectionModal.component';
import {SalesPerformanceTableComponent} from './pages/salesPerformance/vsTargetTable/salesPerformanceTable.component';
import {InstructionRowComponent} from './components/instructionRow/instructionRow.component';
import {BIChartTileComponent} from './components/biChartTile/biChartTile.component';
import {DistrinetSourceDataUpdateComponent} from './components/distrinetSourceDataUpdate.component';
import {
   VehiclesAwaitingPrepModalComponent
} from './components/vehiclesAwaitingPrepModal/vehiclesAwaitingPrepModal.component';
import {UsedDeliveredTileComponent} from './pages/dashboard/tiles/usedDeliveredTile/usedDeliveredTile.component';
import {
   AutoTraderPerformanceRatingComponent
} from './components/autoTraderPerformanceRating/autoTraderPerformanceRating.component';
import {
   AdvertListingDetailTableComponent
} from './pages/autoprice/advertListingDetail/advertListingDetailTable/advertListingDetailTable.component';
import {PricingDashboardComponent} from './pages/autoprice/pricingDashboard/pricingDashboard.component';
import {AdvertListingDetailComponent} from './pages/autoprice/advertListingDetail/advertListingDetail.component';
import {LocationOptimiserComponent} from './pages/autoprice/locationOptimiser/locationOptimiser.component';
import {
   AutoTraderPerformanceRatingChartComponent
} from './components/autoTraderPerformanceRatingChart/autoTraderPerformanceRatingChart.component';
import {BlobChartComponent} from './components/blobChart/blobChart.component';
import {BIChartTileAutoTraderComponent} from './components/biChartTileAutoTrader/biChartTileAutoTrader.component';
import {
   AutoTraderPerformanceRatingSimpleComponent
} from './components/autoTraderPerformanceRatingSimple/autoTraderPerformanceRatingSimple.component';
import {CustomHeaderNew} from './components/customHeader/customHeader.component';
import {BasicInfoModalComponent} from './components/basicInfoModal/basicInfoModal.component';
import {DealInputModalComponent} from './components/dealInputModal/dealInputModal.component';
import {LocationsModalComponent} from './components/locationsModal/locationsModal.component';
import {TypeaheadAndDropdownComponent} from './components/typeaheadAndDropdown.component';
import {NgbdTypeaheadFocus} from './components/simpleTypeahead/simpleTypeahead.component';
import {
   DealerGroupSelectionModalComponent
} from './components/dealerGroupSelectionModal/dealerGroupSelectionModal.component';

import {LeavingVehiclesComponent} from './pages/autoprice/leavingVehicles/leavingVehicles.component';
import {AutoPriceInsightsModalComponent} from './components/autoPriceInsightsModal/autoPriceInsightsModal.component';
import {
   AutoTraderStrategyPriceChartComponent
} from './components/autoTraderStrategyPriceChart/autoTraderStrategyPriceChart.component';
import {RecentViewChartComponent} from './pages/autoprice/pricingDashboard/recentViewChart.component';
import {PriceIndicatorComponent} from './_cellRenderers/priceIndicator.component';
import {
   AutoTraderVsStrategyPriceComponent
} from './components/autoTraderVsStrategyPrice/autoTraderVsStrategyPrice.component';
import {
   AutoTraderCellLozengeComponent
} from './_cellRenderers/auto-trader-cell-lozenge/auto-trader-cell-lozenge.component';
import {AutoTraderPortalComponent} from './_cellRenderers/autoTraderPortal.component';
import {AutoTraderPerfRatingChartCellComponent} from './_cellRenderers/autoTraderPerfRatingChartCell.component';
import {AutoTraderPriceIndChartCellComponent} from './_cellRenderers/autoTraderPriceIndChartCell.component';
import {CheckboxInCellComponent} from './_cellRenderers/checkbox.component';
import {RetailerSitePickerComponent} from './components/retailerSitePicker.component';
import {AutoPriceSettingsModalComponent} from './components/autopriceSettingsModal.component';
import {AutoTraderRetailRatingComponent} from './components/autoTraderRetailRating/autoTraderRetailRating.component';
import {AutoTraderRetailRatingCellComponent} from './_cellRenderers/autoTraderRetailRatingCell.component';
import {AutoTraderPerformanceRatingCellComponent} from './_cellRenderers/autoTraderPerformanceRatingCell.component';
import {VehicleValuationComponent} from './pages/autoprice/vehicleValuation/vehicleValuation.component';
import {
   LocationOptimiserTableComponent
} from './pages/autoprice/locationOptimiser/locationOptimiserTable/locationOptimiserTable.component';
import {AutoTraderAdvertImage} from './_cellRenderers/autoTraderAdvertImage.component';
import {BarChartSimpleComponent} from './pages/dashboard/tiles/barChartSimple/barChartSimple.component';
import {
   VehicleDetailsTableComponent
} from './pages/autoprice/stockInsight/tables/vehicleDetails/vehicleDetails.component';
import {
   VehicleDetailsBulkTableComponent
} from './pages/autoprice/stockInsight/tables/vehicleDetailsBulk/vehicleDetailsBulk.component';
import {PerformanceTrendsComponent} from './pages/performanceTrends/performanceTrends.component';
import {CommissionGuard} from './commission.guard';
import {StockInsightComponent} from './pages/autoprice/stockInsight/stockInsight.component';
import {LocalBargainsComponent} from './pages/autoprice/localBargains/localBargains.component';
import {DashboardPricingComponent} from './pages/dashboard/dashboards/dashboardPricing/dashboardPricing.component';
import {AccountDropdownComponent} from './components/accountDropdown/accountDropdown.component';
import {HomeComponent} from './pages/autoprice/home/<USER>';
import {AutoPriceValuationModalComponent} from './components/autoPriceValuationModal/autoPriceValuationModal.component';
import {
   LocalBargainsTableComponent
} from './pages/autoprice/localBargains/localBargainsTable/localBargainsTable.component';
import {AutoTraderListingComponent} from './_cellRenderers/autoTraderListing.component';
import {NotesComponent} from './components/autoPriceInsightsModal/notes/notes.component';
import {OthersInGroupComponent} from './components/autoPriceInsightsModal/othersInGroup/othersInGroup.component';
import {
   OthersInGroupTableComponent
} from './components/autoPriceInsightsModal/othersInGroup/othersInGroupTable/othersInGroupTable.component';
import {PlaceholderComponent} from './components/autoPriceInsightsModal/placeholder/placeholder.component';
import {PricingChartComponent} from './components/autoPriceInsightsModal/pricingChart/pricingChart.component';
import {PricingDetailsComponent} from './components/autoPriceInsightsModal/pricingDetails/pricingDetails.component';
import {PricingHistoryComponent} from './components/autoPriceInsightsModal/pricingHistory/pricingHistory.component';
import {ProfitTileComponent} from './components/autoPriceInsightsModal/profit/profitTile.component';
import {VehicleDetailsComponent} from './components/autoPriceInsightsModal/vehicleDetails/vehicleDetails.component';
import {
   AutoTraderPriceIndicatorChartComponent
} from './components/autoTraderPriceIndicatorChart/autoTraderPriceIndicatorChart.component';
import {
   PricingScenariosComponent
} from './components/autoPriceInsightsModal/pricingScenarios/pricingScenarios.component';
import {
   SearchAdvertViewsChartComponent
} from './components/autoPriceInsightsModal/searchAdvertViewsChart/searchAdvertViewsChart.component';
import {SiteSettingsComponent} from './pages/autoprice/siteSettings/siteSettings.component';
import {SiteSettingsModalComponent} from './pages/autoprice/siteSettings/siteSettingsModal/siteSettingsModal.component';
import {PrepCostsChartComponent} from './components/autoPriceValuationModal/prepCostsChart/prepCostsChart.component';
import {StatusBarComponent} from './components/statusBar/statusBar.component';
import {OptOutsComponent} from './pages/autoprice/optOuts/optOuts.component';
import {TodayPricesComponent} from './pages/autoprice/todayPrices/todayPrices.component';
import {CdkRegCustomFilter} from './pages/fleetOrderbook/fleetOrderbookTable/CdkRegCustomFilter.component';
import {SiteTransferComponent} from './components/autoPriceInsightsModal/siteTransfer/siteTransfer.component';
import {CDK_DRAG_CONFIG, DragDropModule} from '@angular/cdk/drag-drop';
import {OptOutModalComponent} from './components/optOutModal/optOutModal.component';
import {TableLayoutManagementComponent} from './components/tableLayoutManagement/tableLayoutManagement.component';
import {FleetOrderBookModalComponent} from './pages/fleetOrderbook/modal/modal.component';
import {CustomiseColumnsModalComponent} from './components/customiseColumnsModal/customiseColumnsModal.component';
import {CompetitorAnalysisComponent} from './components/competitorAnalysis/competitorAnalysis.component';
import {
   CompetitorAnalysisChartComponent
} from './components/competitorAnalysis/competitorAnalysisChart/competitorAnalysisChart.component';
import {
   CompetitorAnalysisTableComponent
} from './components/competitorAnalysis/competitorAnalysisTable/competitorAnalysisTable.component';
import {BarChartComponent} from './components/barChart/barChart.component';
import {LeavingVehicleTrendsComponent} from './pages/autoprice/leavingVehicleTrends/leavingVehicleTrends.component';
import {LeavingVehicleDetailComponent} from './pages/autoprice/leavingVehicleDetail/leavingVehicleDetail.component';
import {
   BigLeavingChartComponent
} from './pages/autoprice/leavingVehicleTrends/bigLeavingChart/bigLeavingChart.component';
import {ConfirmationModalComponent} from './pages/fleetOrderbook/confirmationModal/confirmationModal.component';
import {SidenavComponent} from './components/sidenav/sidenav.component';
import {PrettyPrintEnumPipe} from './prettyPrintEnum.pipe';
import {NewDropdownComponent} from './components/newDropdown/newDropdown.component';
import {
   VehicleOptionsTableComponent
} from './components/autoPriceValuationModal/vehicleOptionsTable/VehicleOptionsTable/vehicleOptionsTable.component';
import {
   VehicleOptionsModalComponent
} from './components/autoPriceValuationModal/vehicleOptionsModal/vehicleOptionsModal.component';
import {AdvertSimpleListingComponent} from './pages/autoprice/advertSimpleListing/advertSimpleListing.component';
import {
   AutopriceCheckboxComponent
} from './pages/autoprice/advertSimpleListing/autopriceCheckbox/autopriceCheckbox.component';
import {
   AutopriceDropdownComponent
} from './pages/autoprice/advertSimpleListing/autopriceDropdown/autopriceDropdown.component';
import {PaginatedListComponent} from './pages/autoprice/advertSimpleListing/paginatedList/paginatedList.component';
import {
   SimpleAdvertCardComponent
} from './pages/autoprice/advertSimpleListing/simpleAdvertCard/simpleAdvertCard.component';
import {AutoTraderVehicleCardComponent} from './components/autoTraderVehicleCard/autoTraderVehicleCard.component';
import {AccordionComponent} from './pages/autoprice/advertSimpleListing/accordion/accordion.component';
import {FilterSortManagementComponent} from './components/filterSortManagement/filterSortManagement.component';
import {
   FutureValuationChartComponent
} from './components/autoPriceValuationModal/futureValuationChart/futureValuationChart.component';
import {
   RecentlySoldTableComponent
} from './components/autoPriceValuationModal/recentlySoldTable/recentlySoldTable.component';
import {
   CurrentStockTableComponent
} from './components/autoPriceValuationModal/currentStockTable/currentStockTable.component';
import {SalesIncentiveComponent} from './pages/salesIncentive/salesIncentive.component';
import {ImageRatiosTableComponent} from './pages/imageRatios/imageRatiosTable.component';
import {MultiPickerWithCountComponent} from './components/multiPickerWithCount/multiPickerWithCount.component';
import {VehicleHistoryComponent} from './components/autoPriceValuationModal/vehicleHistory/vehicleHistory.component';
import {
   VehicleDetailsInModalComponent
} from './components/autoPriceValuationModal/vehicleDetailsInModal/vehicleDetailsInModal.component';
import {SaleDetailsComponent} from './components/autoPriceValuationModal/saleDetails/saleDetails.component';
import {
   StrategyPriceBuildUpLayersComponent
} from './components/strategyPriceBuildUpLayers/strategyPriceBuildUpLayers.component';
import {CostingTableComponent} from './components/autoPriceValuationModal/costingTable/costingTable.component';
import {
   StockAndCoverComponent
} from './components/autoPriceValuationModal/stockAndCoverTable/stockAndCoverTable.component';
import {
   EachSiteValuationComponent
} from './components/autoPriceValuationModal/eachSiteValuation/eachSiteValuation.component';
import {ValuationComponent} from './components/autoPriceValuationModal/valuation/valuation.component';
import {
   OptionsTableInsightComponent
} from './components/autoPriceInsightsModal/optionsTableInsight/optionsTableInsight/optionsTableInsight.component';
import {InstructionButtonComponent} from './components/instructionButton/instructionButton.component';
import {PricingPolicyBuilderModalComponent} from './components/pricingPolicyModal/pricingPolicyModal.component';
import {StrategyBuilderModalComponent} from './components/strategyBuilderModal/strategyBuilderModal.component';
import {ConfirmModalNewComponent} from './components/confirmModalNew/confirmModalNew.component';
import {NewHintComponent} from './components/newHint/newHint.component';
import {SimpleExamplePageComponent} from './pages/simpleExamplePage/simpleExamplePage.component';
import {
   SimpleExampleItemsTableComponent
} from './pages/simpleExamplePage/simpleExampleItemsTable/simpleExampleItemsTable.component';
import {SpinnerComponent} from './components/spinner/spinner.component';
import {OrderBookNewComponent} from './pages/orderBookNew/orderBookNew.component';
import {OrderBookNewTableComponent} from './pages/orderBookNew/table/orderBookNewTable.component';
import {NewFactorModalComponent} from './components/pricingPolicyModal/newFactorModal/newFactorModal.component';
import {
   MaybeDeleteFactorModalComponent
} from './components/pricingPolicyModal/maybeDeleteFactorModal/maybeDeleteFactorModal.component';
import {
   AutoPriceNewVehicleValuationModalComponent
} from './components/autoPriceNewVehicleValuationModal/autoPriceNewVehicleValuationModal.component';
import {
   ChooseNewVehicleModalComponent
} from './components/autoPriceNewVehicleValuationModal/chooseNewVehicleModal/chooseNewVehicleModal.component';
import {
   NewFutureValuationChartComponent
} from './components/autoPriceNewVehicleValuationModal/newFutureValuationChart/newFutureValuationChart.component';
import {
   FutureValuationTableComponent
} from './components/autoPriceNewVehicleValuationModal/futureValuationTable/futureValuationTable.component';
import {
   HistoricPricesChartComponent
} from './components/autoPriceNewVehicleValuationModal/historicPricesChart/historicPricesChart.component';
import {StatsDashboardComponent} from './pages/autoprice/statsDashboard/statsDashboard.component';
import {
   StatsDashboardTableComponent
} from './pages/autoprice/statsDashboard/statsDashboardTable/statsDashboardTable.component';
import {MultiPickerComponent} from './components/multiPicker/multiPicker.component';
import {UsageReportComponent} from './pages/usageReport/usagereport.component';
import {UsageReportTableComponent} from './pages/usageReport/usageReportTable/usageReportTable.component';
import {PricingVsStrategyBarsComponent} from './_cellRenderers/pricingVsStrategyBars/pricingVsStrategyBars.component';
import {HorizontalDotBoxComponent} from './_cellRenderers/horizontalDotBox/horizontalDotBox.component';
import {RetailRatingBandBarComponent} from './_cellRenderers/retailRatingBandBar/retailRatingBandBar.component';
import {PerformanceRatingBarComponent} from './_cellRenderers/performanceRatingBar/performanceRatingBar.component';
import {DotPlotModalComponent} from './components/dotPlotModal/dotPlotModal.component';
import {
   StockProfilerTableComponent
} from './pages/autoprice/stockProfiler/stockProfilerTable/stockProfilerTable.component';
import {StockProfilerComponent} from './pages/autoprice/stockProfiler/stockProfiler.component';
import {SitesLeagueComponent} from './pages/autoprice/sitesLeague/sitesLeague.component';
import {SitesLeagueTableComponent} from './pages/autoprice/sitesLeague/sitesLeagueTable/sitesLeagueTable.component';
import {OldCustomHeader} from './components/oldCustomHeader/oldCustomHeader.component';
import {CustomHeaderAdDetail} from './components/customHeaderAdDetail/customHeaderAdDetail.component';
import {
   LeavingVehicleTrendsOverTimeComponent
} from './pages/autoprice/leavingVehicleTrendsOverTime/leavingVehicleTrendsOverTime.component';
import {
   ChartVarianceSummaryComponent
} from './pages/autoprice/leavingVehicleTrendsOverTime/chartVarianceSummary/chartVarianceSummary.component';
import {CurrencyInputComponent} from './components/currencyInput.component';
import {AutotraderImageComponent} from './components/autotraderImage/autotraderImage.component';
import {AutotraderImageCellComponent} from './components/autotraderImageCell/autotraderImageCell.component';
import {
   BulkUpdateAdvertPriceModalComponent
} from './components/bulkUpdateAdvertPriceModal/bulkUpdateAdvertPriceModal.component';
import {
   BatchResultsTableComponent
} from './pages/autoprice/bulkValuation/batchResultsTable/batchResultsTable.component';
import {BulkValuationComponent} from './pages/autoprice/bulkValuation/bulkValuation.component';
import {DestinationTableComponent} from './pages/autoprice/bulkValuation/destinationTable/destinationTable.component';
import {ExcelSourceTableComponent} from './pages/autoprice/bulkValuation/excelSourceTable/excelSourceTable.component';
import {
   BulkValuationTableComponent
} from './pages/autoprice/bulkValuation/bulkValuationTable/bulkValuationTable.component';
import {
   BulkUploadSourceHeaderComponent
} from './pages/autoprice/bulkValuation/excelSourceTable/header/excelSourceTableHeader.component';
import {
   BulkUploadDestinationTableHeaderComponent
} from './pages/autoprice/bulkValuation/destinationTable/destinationTableHeader/destinationTableHeader.component';
import {ReportGroupModalComponent} from './components/reportGroupModal/reportGroupModal.component';
import {ReportSchedulesComponent} from './pages/reportSchedules/report-schedules.component';
import {
   TableLayoutSchedulerModalComponent
} from './components/tableLayoutManagement/tableLayoutSchedulerModal.component';
import {TypeaheadSelectComponent} from './components/typeaheadSelect/typeaheadSelect.component';
import {ReportGroupService} from './services/report-group.service';
import {ButtonWithSpinnerComponent} from "./components/buttonWithSpinner/buttonWithSpinner.component";
import {TagComponent} from "./pages/tag/tag.component";
import {TagModalComponent} from "./pages/tag/tagModal/tagModal.component";
import {TagDisplayComponent} from "./components/tag-display/tag-display.component";
import {AdvertTagsComponent} from "./components/advertTags/advertTags.component";
import {
   TypeaheadAndDropdownLabelValueComponent
} from "./components/typeaheadAndDropdownLabelValue/typeaheadAndDropdownLabelValue.component";
import {ToggleSwitchComponent} from "./components/toggleSwitch/toggleSwitch.component";
import { StrategyPriceBuildUpLayersNewComponent } from './components/strategyPriceBuildUpLayersNew/strategyPriceBuildUpLayersNew.component';
import { BlobDisplayerComponent } from './pages/simpleExamplePage/blobDisplayer/blobDisplayer.component';
import { SimpleModalComponent } from './pages/simpleExamplePage/simpleModal/simpleModal.component';
import { StrategyBuildUpTableComponent } from './pages/autoprice/strategyBuildUp/strategyBuildUpTable/strategyBuildUpTable.component';
import { StrategyBuildUpPageComponent } from './pages/autoprice/strategyBuildUp/strategyBuildUp.component';
import { LeavingVehicleExplorerPageComponent } from './pages/autoprice/leavingVehicleExplorer/leavingVehicleExplorer.component';
import { LeavingVehicleExplorerTableComponent } from './pages/autoprice/leavingVehicleExplorer/leavingVehicleExplorerTable/leavingVehicleExplorerTable.component';


LicenseManager.setLicenseKey("Using_this_AG_Grid_Enterprise_key_( AG-043359 )_in_excess_of_the_licence_granted_is_not_permitted___Please_report_misuse_to_( <EMAIL> )___For_help_with_changing_this_key_please_contact_( <EMAIL> )___( CPH Insight Ltd )_is_granted_a_( Multiple Applications )_Developer_License_for_( 3 )_Front-End_JavaScript_developers___All_Front-End_JavaScript_developers_need_to_be_licensed_in_addition_to_the_ones_working_with_AG_Grid_Enterprise___This_key_has_been_granted_a_Deployment_License_Add-on_for_( 1 )_Production_Environment___This_key_works_with_AG_Grid_Enterprise_versions_released_before_( 6 June 2024 )____[v2]_MTcxNzYyODQwMDAwMA==6de549e2287d9c097f8c1e5fbfd9daa7")

@NgModule({
   declarations: [

      AppComponent,
      CphPipe,
      PrettyPrintEnumPipe,
      OptOutModalComponent,
      CurrencySpainPipe,
      DecimalSpainPipe,
      ExtendedFilterComponent,
      ExtendedFilterFloatingFilterComponent,
      SliderFloatingFilter,
      CustomHeaderNew,
      CustomHeaderAdDetail,
      //----------------------------------------+
      //pages
      //----------------------------------------+
      DashboardSalesRRGFleetComponent,
      DashboardSalesComponent,
      DashboardPricingComponent,
      DashboardOverviewComponent,
      DashboardSalesVindisComponent,
      SpainKPIsComponent,
      DashboardAftersalesSpainComponent,
      DashboardAftersalesSpainDetailComponent,
      DashboardAftersalesSpainKPIComponent,
      DashboardAftersalesSpainKPITableComponent,
      DashboardAftersalesSpainDatasetsComponent,
      DashboardAftersalesSpainDatasetsTableComponent,
      DashboardAfterSalesVindisComponent,
      DashboardSiteCompareComponent,
      DealsDoneThisWeekComponent,
      DealsForTheMonthComponent,
      FAndISummaryComponent,
      AlcopaTableComponent,
      AlcopaSummaryComponent,
      SimpleExampleItemsTableComponent,
      BlobDisplayerComponent,
      SimpleExamplePageComponent,
      SimpleModalComponent,
      HandoverDiaryComponent,
      OrderBookComponent,
      OrderBookNewComponent,
      OrderBookNewTableComponent,
      TeleStatsComponent,
      FleetOrderbookComponent,
      FleetOrderbookCommentsCellComponent,
      FleetOrderbookCommentEditorComponent,
      FleetOrderbookTableComponent,
      PartsSummaryComponent,
      PerformanceLeagueComponent,
      AftersalesLeagueComponent,
      ProfitTileComponent,
      SalesmanEfficiencyComponent,
      RegistrationsPositionComponent,
      ReportingCentreComponent,
      ServiceSummaryComponent,
      StockListComponent,
      StockReportComponent,
      SalesCommissionComponent,
      SalesCommissionRRGComponent,
      SalesCommissionVindisComponent,
      CommissionSitesTableRRGComponent,
      CommissionSitesTableVindisComponent,
      CommissionPeopleTableVindisComponent,
      WhiteboardComponent,
      DebtsComponent,
      AutoPriceValuationModalComponent,
      AutoPriceNewVehicleValuationModalComponent,
      NewFutureValuationChartComponent,
      FutureValuationTableComponent,
      HistoricPricesChartComponent,
      ChooseNewVehicleModalComponent,
      SitesTableComponent,
      DebtsTableComponent,
      BonusesTableComponent,
      ServiceTableComponent,
      CustomEditorComponent,
      SalesPerformanceComponent,
      TeleStatsTableComponent,
      SalesPerformanceTableComponent,
      SalesPerformanceOrderRateTableComponent,
      SalesPerformanceCustomTableComponent,
      SalesProjectionModalComponent,
      DistrinetSourceDataUpdateComponent,
      VehiclesAwaitingPrepModalComponent,
      SuperCupComponent,
      SuperCupTwoComponent,
      VocComponent,
      CitNowwTableComponent,
      EvhcComponent,
      PartsStockComponent,
      ServiceBookingsComponent,
      CitNowwComponent,
      ImageRatiosComponent,
      ImageRatiosTableComponent,
      ImageRatiosChartComponent,
      StockLandingComponent,
      ScratchCardComponent,
      SalesActivityComponent,
      GDPRComponent,
      DashboardUsedSpainComponent,
      TeleStatRowRenderer,
      DashboardNewVOSpainComponent,
      DashboardNewVNSpainOrdersPageComponent,
      DashboardNewVNSpainStocksPageComponent,
      DistrinetComponent,
      SalesExecReviewComponent,
      UpsellsComponent,
      UpsellsTableComponent,
      PerformanceTrendsTableComponent,
      DetailedBookingsTileComponent,
      AssignmentTileComponent,
      ServiceDetailedTileComponent,
      BroadcastMessageModalComponent,

      //----------------------------------------+
      //components for agGrid
      //----------------------------------------+
      BarsOnRRGSiteComponent,
      AutoTraderAdvertImage,
      BarsPreppedComponent,
      IndexComponent,
      RegPlateComponent,
      RegPlate1Component,
      ChassisComponent,
      DeleteButtonComponent,
      BooleanComponent,
      CurrencyComponent,
      NumberComponent,
      DateComponent,
      LengthComponent,
      HorizontalBarComponent,
      HorizontalBarNewComponent,
      HorizontalBarTgtActComponent,
      BookingsBarComponent,
      PartsStockBarComponent,
      ProductsComponent,
      ProductsHeaderComponent,
      AuditPassComponent,
      LabelForPeopleComponent,
      DebtCommentsComponent,
      StockTakePhotoComponent,
      OnRRGSiteComponent,
      PersonPhotoComponent,
      ProductivityComponent,
      BarComponent,
      AutoTraderListingComponent,
      AutoTraderPortalComponent,
      PricingVsStrategyBarsComponent,
      HorizontalDotBoxComponent,
      RetailRatingBandBarComponent,
      PerformanceRatingBarComponent,
      BarCurrencyComponent,
      BarCurrencyAndPercentageComponent,
      BiDirectionalBarComponent,
      LeavingVehiclesComponent,
      //----------------------------------------+
      // components
      //----------------------------------------+
      ThisWeekOrdersSitesTableComponent,
      ThisWeekOrdersDetailedTableComponent,
      ThisWeekOrdersModalComponent,
      DatePickerModalComponent,
      InstructionRowComponent,
      SimpleTextModalComponent,
      BasicInfoModalComponent,
      DealDetailsComponent,
      ExecManagerMappingComponent,
      StockItemModalComponent,
      StockItemModalBodyComponent,
      stockItemModalBodyAutoTraderComponent,
      ChartComponent,
      DealPopoverComponent,
      DealPopoverRRGComponent,
      CommentsCellComponent,
      DealDetailCommentComponent,
      ToastComponent,
      HintComponent,
      DeleteFonButtonComponent,
      FandITablePeopleComponent,
      SalesmanEfficiencyTableComponent,
      CommissionStatementTableVindisComponent,
      CommissionStatementTableRRGComponent,
      PartsStockDetailedTableComponent,
      OrdersBySiteTableComponent,
      RegistrationsTableComponent,
      FandITableSitesComponent,
      GuageChartComponent,
      RunChaseChartComponent,
      ServiceBookingsTableComponent,
      ServiceBookingsSiteDetailTableComponent,
      PartsTableComponent,
      RegistrationsChartComponent,
      PriceChartComponent,
      ProjectedChartComponent,
      RunChaseChartLargeComponent,
      CommentEditorComponent,
      InitialLoadModalComponent,
      CustomHeaderComponent,
      ProfilePicComponent,
      ProfilePicImageComponent,
      OrderTypePickerComponent,
      VehicleTypePickerComponent,
      FranchisePickerComponent,
      ExecPickerComponent,
      SitePickerComponent,
      SitePickerRRGComponent,
      PartsStockTableAgeingComponent,
      PartsStockTableCoverTableComponent,
      StockFamilyPickerComponent,
      StockGroupPickerComponent,
      ConfettiComponent,
      DealListPopoverComponent,
      ModelPickerComponent,
      SpinnerComponent,
      SalesmanEfficiencyPeopleEditorComponent,
      UsedDeliveredTileComponent,
      StockReportModalComponent,
      StockReportVehicleTableComponent,
      UsedStockTableComponent,
      AllStockTableComponent,
      OverAgeStockTableComponent,
      UsedStockMerchTableComponent,
      ModelChartComponent,
      CitNowSiteDetailModalComponent,
      CitNowVideoBreakdownModalComponent,
      WipReportComponent,
      WipSitesTableComponent,
      WipDetailTableComponent,
      BarChartAndRiskOppsComponent,
      StockLandingPanelComponent,
      SiteCardComponent,
      SalesmanCardComponent,
      ManagerCardComponent,
      NoWebAccessComponent,
      OrdersBySiteComponent,
      OrdersBySiteBarComponent,
      RatioOrdersForSiteTableComponent,
      SourceDataUpdateComponent,
      RegistrationsModalComponent,
      ConfirmModalNewComponent,
      SalesActivityTableComponent,
      NewHintComponent,
      DatePickerMultiSelectComponent,
      PickerSimpleComponent,
      VNStringPickerComponent,
      GDPRTableComponent,
      StockByAgeTableComponent,
      StockBarChartComponent,
      StockPieChartComponent,
      DataOriginsUpdatesModalComponent,
      CommissionReportComponent,
      SalesExecReviewByMeasureTableComponent,
      SalesExecReviewByMonthTableComponent,
      SalesExecReviewFormComponent,
      SalesExecReviewLastSixMonthsTrendComponent,
      VehicleTypePickerSpainComponent,
      SalesExecReviewSalesExecByMonthChartComponent,
      DistrinetModalComponent,
      LiveForecastSummaryTableComponent,
      LiveForecastReviewTableComponent,
      DarkModeToggle,
      CitNowVideoBreakdownTableComponent,
      DashboardInvoicedDealsTableComponent,
      PercentageAndFiguresComponent,
      ManagerPickerModalComponent,
      UsedStockMerchBySiteTileComponent,
      //----------------------------------------+
      //dashboard subcomponents
      //----------------------------------------+
      DashboardComponent,
      AgedUsedStockTileComponent,
      CashDebtsRRGComponent,
      BonusDebtsRRGComponent,
      ActivityLevelsTileComponent,
      AgedWipTileComponent,
      VocTileComponent,
      PartsStockTileOver1YrComponent,
      PartsStockTile6To12Component,
      DepartmentProfitPerUnitComponent,
      SiteCompareComponent,
      DonutTileComponent,
      DonutComponent,
      OrdersDonutComponent,
      DepartmentDealBreakdownComponent,
      ThisWeekOrdersComponent,
      UsedStockHealthTileComponent,
      TodayMapComponent,
      DashboardAfterSalesVindisComponent,
      DashboardAfterSalesRRGComponent,
      DeliveryVsOrderDateComponent,
      SpainDashboardUsedGraphsComponent,
      SpainDashboardUsedTablesComponent,
      DailyOrdersVsLastYearComponent,
      AlcopasSummaryTileComponent,
      CommissionsTileComponent,
      //----------------------------------------+
      //dashboard subcomponent tiles
      //----------------------------------------+
      CitNowTileComponent,
      PartsStockModalComponent,
      FAndITileComponent,
      StockOverageTileComponent,
      RegistrationsTileComponent,
      UsedStockMerchandisingTileComponent,
      EvhcTileComponent,
      EvhcSoldTileComponent,
      ServiceGuageTileComponent,
      PartsGuageTileComponent,
      PartsStockTileOver1YrComponent,
      WipBarChartComponent,
      ServiceBookingsTileComponent,
      ThisWeekOrdersSimpleComponent,
      DataOriginsUpdatesTileComponent,
      BIChartTileComponent,
      BISlicerTileComponent,
      //----------------------------------------+
      //other sub-components
      //----------------------------------------+
      PerformanceLeagueCardTechsComponent,
      PerformanceLeagueCardAdvisorsComponent,
      TinyDonutComponent,
      UnitsAndValueComponent,
      VocCardComponent,
      VocTableComponent,
      EvhcTechsTableComponent,
      EvhcSitesTableComponent,
      CitNowwTableComponent,
      CitNowwChartComponent,
      RecentViewChartComponent,
      LoginComponent,
      ForgotpasswordComponent,
      ResetPasswordComponent,
      MultiPickerWithCountComponent,
      MultiPickerComponent,
      ManageUserComponent,
      ChangePasswordComponent,
      ButtonWithSpinnerComponent,
      UserMaintenanceComponent,
      UserSitePickerComponent,
      UserModalComponent,
      ConfirmModalComponent,
      InputModalComponent,
      DealDetailsFileSentDatesComponent,
      RunChaseTileComponent,
      RunChaseTileModalComponent,
      RunRateAndRequirementTileComponent,
      SalesByDayTileComponent,
      ServiceSalesByTypeTileComponent,
      PartsSalesByTypeTileComponent,
      SalesPositionTileComponent,
      WipAgeingSummaryTileComponent,
      ReconditioningTileComponent,
      InvoicingPerformanceComponent,
      InvoicedDonutComponent,
      CommissionAdjustmentComponent,
      SliderSwitchComponent,
      LiveForecastInputComponent,
      LiveForecastStatusComponent,
      ApproveForecastButtonComponent,
      RejectForecastButtonComponent,
      ClearForecastButtonComponent,
      LiveForecastReviewComponent,
      DashboardInvoicedDealsSpainComponent,
      evhcSitesTableEDynamixComponent,
      CitNowTableEDynamixComponent,
      ScreenRotationModalComponent,
      PercentageTileComponent,
      TurnoverPerOperativeComponent,
      EditFONsModalComponent,
      DotPlotModalComponent,
      StatsDashboardComponent,
      StockProfilerComponent,
      SitesLeagueComponent,
      StatsDashboardTableComponent,
      StockProfilerTableComponent,
      
      LeavingVehicleExplorerPageComponent,
      LeavingVehicleExplorerTableComponent,
      SitesLeagueTableComponent,
      StockInsightComponent,
      StrategyBuildUpPageComponent,
      StrategyBuildUpTableComponent,
      PriceIndicatorComponent,
      AdvertListingDetailTableComponent,
      BusinessManagersCommissionComponent,
      PerformanceTrendsComponent,
      PricingDashboardComponent,
      PricingPolicyBuilderModalComponent,
      NewFactorModalComponent,
      MaybeDeleteFactorModalComponent,
      StrategyBuilderModalComponent,
      StrategyPriceBuildUpLayersNewComponent,
      AdvertListingDetailComponent,
      AdvertSimpleListingComponent,
      PaginatedListComponent,
      SimpleAdvertCardComponent,
      AutopriceCheckboxComponent,
      AutopriceDropdownComponent,
      LocationOptimiserComponent,
      AutoTraderPerformanceRatingComponent,
      AutoTraderPerformanceRatingSimpleComponent,
      AutoTraderPerformanceRatingChartComponent,
      AutoTraderPriceIndicatorChartComponent,
      AutoTraderStrategyPriceChartComponent,
      BlobChartComponent,
      BIChartTileAutoTraderComponent,
      AutoTraderPerformanceRatingSimpleComponent,
      AutoPriceInsightsModalComponent,
      StrategyPriceBuildUpLayersComponent,
      VehicleDetailsComponent,
      PricingDetailsComponent,
      PricingChartComponent,
      PricingHistoryComponent,
      PricingScenariosComponent,
      CurrencyInputComponent,
      NotesComponent,
      CompetitorAnalysisComponent,
      OptionsTableInsightComponent,
      OthersInGroupComponent,
      PlaceholderComponent,
      AutoTraderVsStrategyPriceComponent,
      AutoTraderCellLozengeComponent,
      OthersInGroupTableComponent,
      CompetitorAnalysisTableComponent,
      AutoTraderPerfRatingChartCellComponent,
      AutoTraderPriceIndChartCellComponent,
      CheckboxInCellComponent,
      RetailerSitePickerComponent,
      AutoPriceSettingsModalComponent,
      AutoTraderRetailRatingComponent,
      AutoTraderRetailRatingCellComponent,
      AutoTraderPerformanceRatingCellComponent,
      VehicleValuationComponent,
      LocalBargainsComponent,
      VehicleOptionsTableComponent,
      VehicleOptionsModalComponent,
      CostingTableComponent,
      VehicleHistoryComponent,
      VehicleDetailsInModalComponent,
      SaleDetailsComponent,
      LocationOptimiserTableComponent,
      BulkValuationComponent,
      BulkUploadDestinationTableHeaderComponent,
      DestinationTableComponent,
      ExcelSourceTableComponent,
      BulkUploadSourceHeaderComponent,
      //BulkValuationTableHeaderComponent,
      BarChartSimpleComponent,
      VehicleDetailsTableComponent,
      VehicleDetailsBulkTableComponent,
      BatchResultsTableComponent,
      DealInputModalComponent,
      LocationsModalComponent,
      TypeaheadAndDropdownComponent,
      TypeaheadSelectComponent,
      NgbdTypeaheadFocus,
      StatusBarComponent,
      DealerGroupSelectionModalComponent,
      AccountDropdownComponent,
      NewDropdownComponent,
      LocalBargainsTableComponent,
      HomeComponent,
      SearchAdvertViewsChartComponent,
      PrepCostsChartComponent,
      OptOutsComponent,
      TodayPricesComponent,
      CompetitorAnalysisChartComponent,
      SiteSettingsComponent,
      SiteSettingsModalComponent,
      CdkRegCustomFilter,
      SiteTransferComponent,
      TableLayoutManagementComponent,
      TableLayoutSchedulerModalComponent,
      FleetOrderBookModalComponent,
      CustomiseColumnsModalComponent,
      BarChartComponent,
      BigLeavingChartComponent,
      LeavingVehicleTrendsComponent,
      LeavingVehicleDetailComponent,
      LeavingVehicleExplorerTableComponent,
      LeavingVehicleExplorerPageComponent,
      ReportSchedulesComponent,
      ConfirmationModalComponent,
      SidenavComponent,
      BulkValuationTableComponent,
      AutoTraderVehicleCardComponent,
      AccordionComponent,
      FilterSortManagementComponent,
      FutureValuationChartComponent,
      ValuationComponent,
      StockAndCoverComponent,
      RecentlySoldTableComponent,
      EachSiteValuationComponent,
      CurrentStockTableComponent,
      SalesIncentiveComponent,
      InstructionButtonComponent,
      UsageReportComponent,
      UsageReportTableComponent,
      LeavingVehicleTrendsOverTimeComponent,
      ChartVarianceSummaryComponent,
      ReportGroupModalComponent,
      AutotraderImageComponent,
      AutotraderImageCellComponent,
      ChartVarianceSummaryComponent,
      BulkUpdateAdvertPriceModalComponent,
      TagComponent,
      TagModalComponent,
      TagDisplayComponent,
      AdvertTagsComponent,
      ToggleSwitchComponent,
      TypeaheadAndDropdownLabelValueComponent
   ],
   imports: [
      DragDropModule,
      NgbModule,
      BrowserModule,
      BrowserAnimationsModule,
      ImageCropperModule,
      BrowserModule,
      AppRoutingModule,
      ReactiveFormsModule,
      HttpClientModule,
      FormsModule,
      AgGridModule,

   ],
   providers: [
      {provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true},
      {provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true},
      CurrencyPipe,
      DecimalPipe,
      DatePipe,
      CphPipe,
      PrettyPrintEnumPipe,
      OldCustomHeader,
      CustomHeaderNew,
      CustomHeaderAdDetail,
      CommissionGuard,
      LoggedInGuard,
      CurrencySpainPipe,
      DecimalSpainPipe,
      EnvironmentService,
      AppConfigService,
      TranslatedText,
      TitleCasePipe,
      {
         provide: APP_INITIALIZER,
         useFactory: AppConfigurationFactory,
         deps: [AppConfigService, HttpClient], multi: true
      },
      ApplicationInsightLoggingService,
      {provide: ErrorHandler, useClass: ErrorHandlerService},
      NgbActiveModal,
      NgbPagination,
      ReportGroupService,
      {
         provide: CDK_DRAG_CONFIG,
         useValue: {
            draggingDisabled: false,
            dragStartThreshold: 5,
            pointerDirectionChangeThreshold: 5,
            zIndex: 10000
         }
      }
   ],
   bootstrap: [AppComponent],
   exports: [
      CphPipe,
      AutotraderImageCellComponent,
      PrettyPrintEnumPipe
   ]
})
export class AppModule {
}


export function AppConfigurationFactory(
   appConfig: AppConfigService) {
   return () => appConfig.ensureInit().then(() => {
      if (appConfig.appInsightKey.length > 0) {
         const appInsights = new ApplicationInsights({
            config: {
               instrumentationKey: appConfig.appInsightKey,
               enableAutoRouteTracking: true, // option to log all route changes
               disableExceptionTracking: false,
               autoTrackPageVisitTime: true,
               enableAjaxErrorStatusText: true,
               enableAjaxPerfTracking: true,
               enableUnhandledPromiseRejectionTracking: true
            }
         });
         appInsights.loadAppInsights();

         appConfig.appInsights = appInsights;
      }
   });
}


