<div style="height: 100%; overflow-y: auto; padding-bottom: 2em; display: flex; flex-direction: column">
   <div class="flex-grow-1">
      <div style="margin-bottom: 1em;">
         <div *ngFor="let adTag of advertTags" class="tag-items" (click)="editTag($event, adTag)">
            <app-tag-display [tag]="adTag" [canEditTags]="canEditTags"
                             (onDeleteTag)="deleteTag($event, adTag)"></app-tag-display>
         </div>
      </div>
   </div>

   <div>
      <typeaheadAndDropdownLabelValue
         [width]="'100%'"
         *ngIf="canEditTags"
         [placeholder]="'Begin typing to select or create a tag'"
         [position]="'right'"
         [searchList]="filteredTypeaheadList"
         [clearInputOnChoiceMade]="true"
         [showCreateOption]="true"
         [createLabel]="'+ Create Tag'"
         (chosenItemEmitter)="chooseTag($event)"
         (createNewItemEmitter)="onCreateNewTag($event)"
      ></typeaheadAndDropdownLabelValue>
   </div>
</div>
