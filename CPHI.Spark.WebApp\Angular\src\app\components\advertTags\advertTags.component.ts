import {
   ChangeDetectionStrategy,
   ChangeDetectorRef,
   Component,
   EventEmitter,
   Input,
   OnChanges,
   OnInit,
   Output,
   SimpleChanges,
   ViewChild
} from "@angular/core";
import {ConstantsService} from "../../services/constants.service";
import {CreateTagDTO, TagDTO} from "../../model/Tag";
import {VehicleAdvertTagService} from "../../services/vehicleAdvertTag.service";
import {TagService} from "../../services/tag.service";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {TagModalComponent} from "../../pages/tag/tagModal/tagModal.component";
import {Subscription} from "rxjs";
import {SelectionsService} from "../../services/selections.service";
import {
   TypeaheadAndDropdownLabelValueComponent
} from "../typeaheadAndDropdownLabelValue/typeaheadAndDropdownLabelValue.component";

@Component({
   selector: "app-advert-tags",
   templateUrl: "./advertTags.component.html",
   styleUrls: ["./advertTags.component.scss"],
   changeDetection: ChangeDetectionStrategy.OnPush
})

export class AdvertTagsComponent implements OnInit, OnChanges {

   @Input() public adId: number;
   @Output() tagsChanged = new EventEmitter<string>();
   @ViewChild(TypeaheadAndDropdownLabelValueComponent) typeaheadComponent: TypeaheadAndDropdownLabelValueComponent;

   private allTags: TagDTO[];
   typeaheadList: { label: string; value: number }[] = [];
   filteredTypeaheadList: { label: string; value: number }[] = [];
   advertTags: {
      Label: string;
      TagId: number;
      Id: number,
      StrategyImpactAmount: number;
      StrategyImpactPct: number;
      Colour: string
   }[] = [];
   private tagLookup: { [key: number]: TagDTO } = [];
   canEditTags: boolean;

   constructor(
      public selections: SelectionsService,
      private constantsService: ConstantsService,
      private advertTagService: VehicleAdvertTagService,
      private tagService: TagService,
      private modalService: NgbModal,
      private cdr: ChangeDetectorRef
   ) {
   }

   ngOnChanges(changes: SimpleChanges) {

      if (changes.adId && this.adId) {
         this.fetchAdvertTags(this.adId);
      }
   }

   private updateFilteredTypeaheadList() {
      this.filteredTypeaheadList = this.typeaheadList.filter(x => !this.advertTags.find(y => y.TagId === x.value));
   }

   ngOnInit() {

      this.allTags = this.constantsService.tags;

      this.typeaheadList = this.allTags.map(x => ({
         label: `<span style='vertical-align: middle; display: inline-block; width: 1em; height: 1em; background-color: ${x.Colour}; margin-right: 5px; border-radius: 2px '></span>` + x.Label,
         value: x.Id
      }));

      this.allTags.forEach(x => {
         this.tagLookup[x.Id] = x;
      });

      this.canEditTags = this.selections.user.permissions.canEditPricingStrategy;
      this.updateFilteredTypeaheadList();
   }

   fetchAdvertTags(adId: number) {

      this.advertTagService.search({vehicleAdvertId: adId}).then(res => {
         this.advertTags = res.map(x => ({
            Label: this.tagLookup[x.TagId]?.Label || '',
            TagId: x.TagId,
            Id: x.Id,
            StrategyImpactAmount: this.tagLookup[x.TagId]?.StrategyImpactAmount || 0,
            StrategyImpactPct: this.tagLookup[x.TagId]?.StrategyImpactPct || 0,
            Colour: this.tagLookup[x.TagId]?.Colour || '#000000',
         }));
         this.updateFilteredTypeaheadList();

         // Emit the updated tag list as a pipe-delimited string
         const tagIdList = this.advertTags.map(x => x.TagId).join('||');
         this.tagsChanged.emit(tagIdList);

         // Manually trigger change detection for OnPush strategy
         this.cdr.markForCheck();
      });

   }


   chooseTag($event: any) {

      if ($event) {
         this.advertTagService.create({tagId: $event.value, vehicleAdvertId: this.adId} as any).then(res => {
            this.fetchAdvertTags(this.adId);
            this.constantsService.toastSuccess('Tag Added')
         });
      }
   }

   deleteTag($event, adTag: { Label: string; TagId: number; Id: number }) {

      event.stopPropagation();

      this.constantsService.confirmModal.showModal('Confirm delete tag', 'Delete tag: ' + adTag.Label + '?');

      const confirmModalSubscription: Subscription = this.selections.confirmModalEmitter.subscribe(res => {
         if (res) {
            this.advertTagService.delete(adTag.Id).then(res => {
               this.fetchAdvertTags(this.adId);
            });

            this.constantsService.toastSuccess('Tag Removed');
         }
         confirmModalSubscription.unsubscribe();
      })
   }

   onCreateNewTag(tagLabel: string) {
      const newTag: CreateTagDTO = {
         Label: tagLabel || '', // Handle empty string case
         IsActive: true,
         StrategyImpactPct: 100,
         StrategyImpactAmount: 0,
         Colour: '#007bff'
      };

      this.openTagModal(newTag, true);
   }

   private openTagModal(tag: TagDTO | CreateTagDTO, isNew: boolean) {

      const modalRef = this.modalService.open(TagModalComponent, {size: 'sm'});

      modalRef.componentInstance.initialiseModal(tag, isNew);

      modalRef.result.then(res => {
         if (res) {
            // Clear the typeahead input when a new tag is created

            if (isNew && this.typeaheadComponent) {
               this.typeaheadComponent.clearSelection();

               this.constantsService.toastSuccess('Tag Created');
            }

            // Refresh the tags list and update the typeahead
            this.refreshTagsData().then(() => {
               // If this was a new tag creation, automatically select it
               if (isNew && res.Id) {
                  this.advertTagService.create({tagId: res.Id, vehicleAdvertId: this.adId} as any).then(() => {
                     this.fetchAdvertTags(this.adId);
                  });
               } else {
                  this.fetchAdvertTags(this.adId);
               }
            });
         }
      }, () => {
      });
   }

   private async refreshTagsData() {
      // Refresh the tags from tag service
      this.allTags = await this.tagService.search();
      this.constantsService.tags = this.allTags; // Update the constants service as well

      this.typeaheadList = this.allTags.map(x => ({
         label: `<span style='vertical-align: middle; display: inline-block; width: 1em; height: 1em; background-color: ${x.Colour}; margin-right: 5px; border-radius: 2px '></span>` + x.Label,
         value: x.Id
      }));

      this.tagLookup = {};
      this.allTags.forEach(x => {
         this.tagLookup[x.Id] = x;
      });
      this.updateFilteredTypeaheadList();
      this.cdr.markForCheck();
   }

   editTag(event, adTag: { Label: string; TagId: number; Id: number; Colour: string }) {

      event.stopPropagation();

      this.openTagModal(this.tagLookup[adTag.TagId], false);

   }
}
