.modal-body {
    height: 87vh;
}

.dashboard-grid {
  height: 100%;
}

#leftSide,
#rightSide {
  width: calc(50% - 1em);
  display: flex;
  flex-direction: column;
}

.opacity-30 {
  opacity: 0.3;
}

.tag-header {
   font-weight: 500;
}

#vehicleDetailsContainer {
  margin-bottom: 2em;

  table {
    width: 100%;

    tr {
      height: 35px;

      td:nth-of-type(2) {
        text-align: right;
      }

      .regInput {
        background-color: var(--numberPlate);
        border-radius: 5px;
        border: 1px solid;
        text-align: center;
        font-weight: 700;
        font-family: 'NumberPlate';

        text-transform: uppercase;
        float: right;
        padding: 0 1em;
      }

      .mileageInput {
        background-color: #000000;
        color: #FFFFFF;
        padding: 0.5em;
        border: none;
        text-align: right;
        letter-spacing: 0.75em;
        width: 130px;
        float: right;
      }

      .autoTraderButtonGroup button {
        min-width: 65px;
        border: none;
        padding: 0.5em;
        background-color: #C6E0B4;

        &.active {
          background-color: #00B050;
        }
      }
    }
  }
}

#optionsTableContainer,
#vehicleOptionsTableContainer {
  flex: 1;
}

#vehicleValuationContainer {
  display: flex;
  flex-direction: column;
  margin-bottom: 2em;

  #saveValuationContainer {
    display: flex;
    justify-content: flex-end;
  }
}

#optimiserInputs {
  display: flex;
  align-items: center;
  margin-bottom: 1em;

  div {
    align-items: center;

    span {
      text-wrap: nowrap;
      margin-right: 0.5em;
    }

    input {
      border: 1px solid var(--grey80);
      border-radius: 5px;
      padding: 0.2em 0.5em;
      text-align: right;
      width: 100%;
    }
  }
}

locationOptimiserTable {
  height: 100%;
}

#pdfPreviewBoard {
  position: relative;
  background-color: #FFFFFF;
  width: 100%;
  padding: 3em;
  aspect-ratio: 1/0.59;
  border: 2px solid black;

  #downloadBoard {
    position: absolute;
    top: 3em;
    right: 3em;
  }

  #boardPrice {

    font-family: Arial, Helvetica, sans-serif;
    font-weight: 700;
    width: 100%;
    height: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  #boardReg {
    font-weight: 500;
    position: absolute;
    left: 3em;
    bottom: 3em;
  }
}

.custom-checkbox {
  float: right;
}

competitorAnalysis {
  display: flex;
  flex: 1;
}
