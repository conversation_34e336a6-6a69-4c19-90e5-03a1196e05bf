import {Component, HostListener, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import {GetVehicleSpecOptionsParams} from 'src/app/model/GetVehicleSpecOptionsParams.model';
import {ValuationPriceSet} from 'src/app/model/ValuationPriceSet.model';
import {VehicleSpecOption} from 'src/app/model/VehicleSpecOption.model';
import {ConstantsService} from 'src/app/services/constants.service';
import {GetDataMethodsService} from 'src/app/services/getDataMethods.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {AutoPriceInsightsModalService} from './autoPriceInsightsModal.service';

import {ValuationResultForAdvert} from 'src/app/model/ValuationResultForAdvert';
import {PriceBoardsService} from 'src/app/services/priceBoards.service';
import '../../../assets/priceBoardFonts/Arial-bold.js';
import '../../../assets/priceBoardFonts/Arial-normal.js';
import {LocationOptimiserService} from '../../pages/autoprice/locationOptimiser/locationOptimiser.service';
import {Subscription} from "rxjs";

@Component({
  selector: 'autoPriceInsightsModal',
  templateUrl: './autoPriceInsightsModal.component.html',
  styleUrls: ['./autoPriceInsightsModal.component.scss']
})
export class AutoPriceInsightsModalComponent implements OnInit, OnDestroy {

  private dataSubscription: Subscription;

  @HostListener("window:resize", [])
  private onresize(event) {
    const isSmall = window.innerWidth <= 1920;
    if (this.service.isSmallScreen !== isSmall) {
      this.service.isSmallScreen = isSmall; // Only update if it actually changes
    }
  }

  vehicleReg: string;

  get valuationPriceSet(): ValuationPriceSet {
    return {
      PartExThisVehicle: this.service.modalItem.AdvertDetail.ValuationAdjPartEx,
      TradeThisVehicle: this.service.modalItem.AdvertDetail.ValuationAdjTrade,
      PrivateThisVehicle: this.service.modalItem.AdvertDetail.ValuationAdjPrivate,
      RetailThisVehicle: this.service.modalItem.AdvertDetail.ValuationAdjRetail,

      PartExAverageSpec: this.service.modalItem.AdvertDetail.ValuationMktAvPartEx,
      TradeAverageSpec: this.service.modalItem.AdvertDetail.ValuationMktAvTrade,
      PrivateAverageSpec: this.service.modalItem.AdvertDetail.ValuationMktAvPrivate,
      RetailAverageSpec: this.service.modalItem.AdvertDetail.ValuationMktAvRetail,

    }
  }

  lastValuation: ValuationPriceSet;
  vehicleCondition: string = 'Excellent';
  vehicleConditions: string[] = ['Poor', 'Fair', 'Good', 'Great', 'Excellent'];

  mostRecentlySelectedOption: VehicleSpecOption;
  vehicleSpecBuildForAdvert: ValuationResultForAdvert;

  get hasLeft() {
    return this.service?.modalItem?.HasLeft == true;
  }


  constructor(
    public selectionsService: SelectionsService,
    public activeModal: NgbActiveModal,
    public getDataService: GetDataMethodsService,
    public constantsService: ConstantsService,
    public service: AutoPriceInsightsModalService,
    public locationOptimiserService: LocationOptimiserService,
    private priceBoardsService: PriceBoardsService
  ) {

  }

  ngOnInit(): void {
    this.selectionsService.triggerSpinner.next({show: true, message: 'Loading...'});
    this.service.modalView = this.service.modalView ?? 'overview';
    this.service.isSmallScreen = window.innerWidth <= 1920;
    this.service.setAdIndex();

    this.service.getData().then(() => {
      if (this.service.modalView != "overview") {
        this.changeView(this.service.modalView, true);
      }
    });

  }

  ngOnDestroy(): void {
    this.service.modalItem = null;
    this.service.locationOptimiserAdverts = null;
    if (this.dataSubscription) {
      this.dataSubscription.unsubscribe();
    }
  }

  closeModal() {
    this.service.modalItem = null;
    this.activeModal.close();
  }

  onTagsChanged(tagIdList: string) {
    // Update the grid row for this advert
    this.service.updateAdvertTags(this.service.modalItem?.AdvertDetail?.AdId, tagIdList);
  }


  changeView(view: 'overview' | 'valuation' | 'transfer' | 'priceBoard' | 'testStrategy' | 'historicPrices' | 'competitorAnalysis' | 'stockCoverBySite' | 'stockInGroup' | 'lastSixMonths' | 'vehicleHistory', override = false) {

    if (view === this.service.modalView && override == false) return;

    this.service.modalView = view;

    if (this.service.modalView === 'valuation') {
      this.getVehicleSpecOptions()
    }

    if (this.service.modalView === 'transfer') {
      this.locationOptimiserService.initialiseTableLayoutManagement();
      this.service.getLocationOptimiserAdverts(this.service.adId);
    }

    if (this.service.modalView === 'priceBoard') {
      this.priceBoardsService.generatePriceBoard(this.service.modalItem);
    }

    if (this.service.modalView === 'vehicleHistory') {
      this.service.getVehicleHistory();
    }

    if (this.service.modalView === 'lastSixMonths') {
      this.service.getLastSixMonths();
    }

    // Multigroup only
    if (this.service.modalView === 'stockInGroup') {
      this.service.getSameModelAdverts();
    }

    // Multigroup only
    if (this.service.modalView === 'stockCoverBySite') {
      this.service.getStockCover();
      // console.log("stockCoverBySite")
    }
  }


  async getVehicleSpecOptions() {
    const params: GetVehicleSpecOptionsParams = {
      BatchId: null,
      DerivativeId: this.service.modalItem.AdvertDetail.DerivativeId,
      FirstRegisteredDate: this.service.modalItem.AdvertDetail.FirstRegisteredDate
    }

    await this.getDataService.getVehicleSpecOptions(params).then(async (res: VehicleSpecOption[]) => {
      this.service.vehicleOptions = res;
      this.applyPortalOptionsToVehicleOptions();
    }, error => {
      console.error('Error returning vehicle options');
      this.constantsService.toastDanger('Failed to load vehicle options');
    })
  }


  applyPortalOptionsToVehicleOptions() {
    if (this.service.modalItem.AdvertDetail?.VehicleAdvertPortalOptions === null) {
      return;
    }
    this.service.modalItem.AdvertDetail.VehicleAdvertPortalOptions.forEach(po => {
      const vo = this.service.vehicleOptions.find(vo2 => vo2.Name === po);
      if (vo !== undefined) {
        vo.IsChosen = true;
      }
    });
  }


  setChosenOptions() {
    this.service.vehicleOptions.forEach(option => {
      if (this.service.selectedOptions.map(x => x.Name).includes(option.Name)) {
        option.IsChosen = true;
        option.Impact = this.service.selectedOptions.find(x => x.Name === option.Name).Impact;
      }
    })
  }

  redrawRows() {
    this.service.locationOptimiserService.costUpMoves(this.service.locationOptimiserAdverts, this.service.costPerMile, this.service.flatCostPerMove);
    this.service.redrawRowsEmitter.emit(this.service.locationOptimiserAdverts);
  }

  getVehicleDetailsTileSize() {
    if (this.service.isSmallScreen) {
      return 'grid-col-1-4 grid-row-1-7';
    } else {
      return this.constantsService.environment.isSingleSiteGroup ? 'grid-col-1-3 grid-row-1-7' : 'grid-col-1-3 grid-row-1-7';
    }
  }

  getPricingStatusAndHistoryTileSize() {
    if (this.service.isSmallScreen) {
      return 'grid-col-4-6 grid-row-4-7';
    } else {
      return this.constantsService.environment.isSingleSiteGroup ? 'grid-col-1-3 grid-row-7-10' : 'grid-col-3-5 grid-row-7-9'
    }
  }

  getProfitTileSize() {
    if (this.service.isSmallScreen) {
      return 'grid-col-4-6 grid-row-7-10';
    } else {
      return this.constantsService.environment.isSingleSiteGroup ? 'grid-col-3-5 grid-row-7-10' : 'grid-col-3-5 grid-row-9-10'
    }
  }

  async goToNextAd() {
    this.service.currentAdIndex++;
    await this.fetchAdData(this.service.currentAdIndex);
  }

  async goToPreviousAd() {
    this.service.currentAdIndex--;
    await this.fetchAdData(this.service.currentAdIndex);
  }

  async fetchAdData(adIndex: number) {
    this.service.modalItem = null;
    this.service.adId = this.service.allAdIds[adIndex];
    await this.service.getData().then(() => {
      if (this.service.modalView != 'overview') {
        this.changeView(this.service.modalView, true);
      }
    })
  }
}
