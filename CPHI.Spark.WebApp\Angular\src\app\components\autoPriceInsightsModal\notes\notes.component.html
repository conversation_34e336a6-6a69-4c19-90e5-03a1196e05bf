<div class="tile-inner">
   <div class="tile-header">
      Notes
   </div>
   <div class="tile-body">
      <div id="comments-container">
         <span *ngIf="data.length === 0">No notes</span>
         <div *ngFor="let comment of data" (mouseenter)="onHoverComment(comment)"
              (mouseleave)="onCommentHoverout(comment)" class="comment-bubble-container"
              [ngClass]="commentClass(comment)">
            <span class="comment-date">{{ comment.Date | cph:'shortDateWithDayName':0 }}</span>
            <div class="comment-bubble">
               <span class="comment-text">{{ comment.Text }}</span>
               <br>
               <span class="comment-person">{{ comment.PersonName }}</span>
               <ng-container *ngIf="comment.IsMyComment">
                  <button [class.hidden]="!comment.isHovered" class="commentDelete"
                          (click)="maybeDeleteComment(comment.CommentId)">
                     <i class="fas fa-trash"></i>
                  </button>
               </ng-container>
            </div>
         </div>
      </div>
      <div id="add-new-comment">
            <textarea type="text" [(ngModel)]="newComment" placeholder="Add note"
                      (keydown.ENTER)="onEnterPressed($event)" [ngClass]="{ 'canSave': newComment }">
            </textarea>
            <button *ngIf="newComment" id="saveComment" (click)="saveNewComment()">
                <i class="fas fa-long-arrow-up"></i>
            </button>
        </div>
    </div>
</div>
