#comments-container {
   height: calc(100% - 4em);
   overflow-y: auto;
}

.comment-bubble-container {
   display: flex;
   flex-direction: column;
   margin-bottom: 1em;

   &.users-comment {
      align-items: flex-end;

      .comment-bubble {
         background-color: var(--atPrimary);
      }
   }

   &:last-of-type {
      margin-bottom: 0;
   }
}

.comment-bubble {
   border-radius: 5px;
   padding: 0.5em;
   color: #FFFFFF;
   min-width: 50%;
   max-width: 75%;
   background-color: var(--atSecondary);
   position: relative;

   .comment-person {

      color: #D2D2D2;
   }

   .commentDelete {
      transition: ease all 0.3s;
      opacity: 0;
      position: absolute;
      top: 0;
      bottom: 0;
      left: -2em;
      width: 2em;
      color: red;
      background-color: transparent;
      border: none;
   }

   .commentDelete {
      transition: opacity 0.3s ease;
      opacity: 1;
   }

   .commentDelete.hidden {
      opacity: 0;
      pointer-events: none;
      /* Prevents clicking on the invisible button */
   }
}

#add-new-comment {
   width: 100%;
   height: 4em;
   position: absolute;
   bottom: 0;
   display: flex;
   align-items: flex-end;
   background-color: #FFFFFF;

   textarea {
      width: 100%;
      resize: none;
      border: 1px solid var(--grey90);
      border-radius: 0.4em;
      padding: 0.5em;
      height: 3em;

      &.canSave {
         border-radius: 8px 0 0 8px;
      }
   }

   button {
      height: 3em;
      border: 1px solid var(--goodColour);
      background: var(--goodColour);
      width: 15%;
      color: #FFFFFF;
      border-radius: 0 8px 8px 0;
   }
}
