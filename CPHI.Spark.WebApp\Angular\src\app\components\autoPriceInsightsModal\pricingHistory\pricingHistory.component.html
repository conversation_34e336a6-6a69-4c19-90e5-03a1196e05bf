<div class="tile-inner">
   <div class="tile-header">Pricing Status and History</div>
   <div class="tile-body">
      <!-- <div *ngIf="vehicleOptOutStatus" class="text-end">
            <span>Alternatively, you can manually enter a new price here.</span>
            <div class="d-flex justify-content-end my-2">


            </div>
        </div> -->
      <table>
         <!-- Only show if user can action stock prices -->
         <ng-container *ngIf="selectionsService.user.permissions.canActionStockPrices">
            <!-- Site is opted in to autopricing -->
            <ng-container *ngIf="service.modalItem.AdvertDetail.SiteOptedIntoAutoPricing">
               <!-- Pending price change detail -->
               <tr>
                  <td>
                     Pending price change ({{
                        service.modalItem.PriceChangeToday?.ChangeValue | cph : "currency" : 0 : true
                     }})
                  </td>
                  <td>{{ service.modalItem.PriceChangeToday?.NewPrice | cph : "currency" : 0 }}</td>
               </tr>

               <!-- The change status -->
               <tr>
                  <td class="textLeft" colspan="2">{{ service.modalItem.PriceChangeToday?.Status }}</td>
               </tr>

               <!-- Instruction row -->
               <tr>
                  <td>
                     <span *ngIf="service.modalItem.AdvertDetail.SiteOptedIntoAutoPricing">
                        {{ optOutMessage() }}
                     </span>
                  </td>
                  <td class="textRight d-flex justify-content-end align-items-center">
                     <sliderSwitch
                        *ngIf="service.modalItem.AdvertDetail.SiteOptedIntoAutoPricing"
                        [defaultValue]="isItemBeingAutoPriced()"
                        (toggle)="onOptInToggle()"
                        [text]="''"
                        [updateTrigger]="service.pricingStatusSliderTrigger"
                        [blackFont]="true"
                     >
                     </sliderSwitch>
                  </td>
               </tr>
            </ng-container>

            <!-- The option to do a new price manually -->
            <tr *ngIf="showSetNewPriceInput">
               <td>Set new price</td>
               <td>
                  <div id="inputAndGo">
                     <input
                        type="text"
                        class="w-50"
                        [ngModel]="newPrice | cph : 'currency' : 0"
                        (input)="setNewPrice($event)"
                        (click)="select($event)"
                     />
                     <button class="btn btn-success" [disabled]="!newPrice" (click)="maybeUpdatePrice()">Go</button>
                  </div>
               </td>
            </tr>

            <!-- Site is NOT opted in to autopricing -->
            <ng-container
               *ngIf="!service.modalItem.AdvertDetail.SiteOptedIntoAutoPricing && !service.modalItem.HasLeft"
            >
               <!-- Pending price change detail -->
               <tr>
                  <td class="text-nowrap">
                     Suggested price change ({{
                        service.modalItem.PriceChangeToday?.ChangeValue | cph : "currency" : 0 : true
                     }})
                  </td>
                  <td>{{ service.modalItem.PriceChangeToday?.NewPrice | cph : "currency" : 0 }}</td>
               </tr>
            </ng-container>
         </ng-container>

         <tr>
            <td>Days Listed</td>
            <td>{{ service.modalItem.AdvertDetail.DaysListed }}</td>
         </tr>
         <tr>
            <td>Days Since Last Price Change</td>
            <td>
               {{
                  daysSinceLastChangeGetter(
                     service.modalItem.AdvertDetail.LastChangeDate,
                     service.modalItem.AdvertDetail.LeavingSnapshotDate
                  )
               }}
            </td>
         </tr>
         <tr>
            <td>Last Price Change</td>
            <td>
               <span [ngClass]="{ 'text-danger': service.modalItem.AdvertDetail.LastPriceChange < 0 }">
                  {{ service.modalItem.AdvertDetail.LastPriceChange | cph : "currency" : 0 }}
               </span>
            </td>
         </tr>
         <tr>
            <td>Total Price Changes</td>
            <td>
               <span [ngClass]="{ 'text-danger': service.modalItem.AdvertDetail.TotalPriceChange < 0 }">
                  {{ service.modalItem.AdvertDetail.TotalPriceChange | cph : "currency" : 0 }}
               </span>
            </td>
         </tr>
         <tr>
            <td>Number of Price Changes</td>
            <td>{{ service.modalItem.AdvertDetail.TotalChanges }}</td>
         </tr>
      </table>
   </div>
</div>

<!-- Shared opt-out modal component -->
<app-opt-out-modal
   #optOutModalComponent
   [vehicleAdverts]="service.modalItem.AdvertDetail"
   (optOutStatusChanged)="onOptOutStatusChanged($event)"
></app-opt-out-modal>
