import {DatePipe} from '@angular/common';
import {Component, EventEmitter, OnInit, Output, ViewChild} from '@angular/core';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import {Subscription} from 'rxjs';
import {VehicleOptOutStatus} from "src/app/model/VehicleOptOutStatus";
import {UpdatePriceParams} from "src/app/model/UpdatePriceParams";
import {DayToSellAndPriceIndicator} from "src/app/model/DayToSellAndPriceIndicator";
import {ConstantsService} from 'src/app/services/constants.service';
import {GetDataMethodsService} from 'src/app/services/getDataMethods.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {AutoPriceInsightsModalService} from '../autoPriceInsightsModal.service';
import {CphPipe} from 'src/app/cph.pipe';
import {
   GetEstimatedDayToSellAndPriceIndicatorParams
} from "src/app/pages/performanceTrends/GetEstimatedDayToSellAndPriceIndicatorParams";
import {GlobalParamsService} from 'src/app/services/globalParams.service';
import {GlobalParamKey} from 'src/app/model/GlobalParam';
import {OptOutModalComponent} from '../../optOutModal/optOutModal.component';

@Component({
   selector: 'pricingHistory',
   templateUrl: './pricingHistory.component.html',
   styleUrls: ['./pricingHistory.component.scss']
})
export class PricingHistoryComponent implements OnInit {
   @ViewChild('optOutModalComponent', {static: false}) optOutModalComponent: OptOutModalComponent;
   //@Input() data: VehicleAdvertDetail;
   //  get data(): VehicleAdvertDetail {
   //   return this.service.modalItem.AdvertDetail
   // }
   @Output() updateOptOutStatus: EventEmitter<void> = new EventEmitter<void>();
   vehicleOptOutStatus: VehicleOptOutStatus;
   newPrice: number;

   daysToSell: number = 0;
   priceIndicator: string = 'NOANALYSIS';

   constructor(
      public constantsService: ConstantsService,
      private globalParamsService: GlobalParamsService,
      public selectionsService: SelectionsService,
      public datePipe: DatePipe,
      public activeModal: NgbActiveModal,
      public getDataService: GetDataMethodsService,
      public service: AutoPriceInsightsModalService,
      private cphPipe: CphPipe
   ) {
   }

   ngOnInit(): void {
      this.getVehicleOptOutStatus();
   }

   getVehicleOptOutStatus() {
      this.getDataService.getVehicleOptOutStatus(this.service.modalItem.AdvertDetail.AdId).subscribe((res: VehicleOptOutStatus) => {
         if (!res) {
            this.vehicleOptOutStatus = null;
            return;
         }

         if (res.EndDate) {
            res.EndDate = new Date(res.EndDate);
         }
         if (res.CreatedDate) {
            res.CreatedDate = new Date(res.CreatedDate);
         }
         if (res.EndDate < new Date()) {
            this.vehicleOptOutStatus = null;
         } else {
            this.vehicleOptOutStatus = res;
         }
      }, error => {
         console.error('Failed to retrieve vehicle opt-out status', error);
      })
   }

   isOptedOut() {
      return this.vehicleOptOutStatus?.EndDate > new Date();
   }

   public get showSetNewPriceInput() {

      if (this.service.modalItem.HasLeft) {
         return false;
      }

      if (this.constantsService.environment.showChangePriceNowInputAlways) {
         return true;
      }

      if (!this.globalParamsService.getGlobalParam(GlobalParamKey.ShowManualPriceChangeOption)) {
         return false;
      }

      //find out if site is on autopricing
      const site = this.constantsService.RetailerSites.find(x => x.Id === this.service.modalItem.RetailerSiteIdForPostcode);
      if (site) {
         if (!site.UpdatePricesAutomatically) {
            return true;
         } else {
            return this.isOptedOut();
         }
      }

   }

   daysSinceLastChangeGetter(lastChangeDate: Date, comparedTo?: Date): string {
      if (lastChangeDate == null) {
         return '-';
      }

      if (!comparedTo) {
         comparedTo = new Date();
      }

      return this.cphPipe.transform(this.constantsService.differenceInDays(comparedTo, new Date(lastChangeDate)), 'number', 0);
   }

   maybeOptOut() {
      if (this.optOutModalComponent) {
         this.optOutModalComponent.openOptOutModal();
      }
   }

   onOptOutStatusChanged(status: VehicleOptOutStatus) {
      this.vehicleOptOutStatus = status;
      this.service.pricingStatusSliderTrigger.emit();
   }

   maybeOptIn() {
      if (this.optOutModalComponent) {
         this.optOutModalComponent.optIn();
      }
   }

   onOptInToggle() {
      this.isOptedOut() ? this.maybeOptIn() : this.maybeOptOut();
   }

   // Opt-in is now handled by the shared OptOutModalComponent

   setNewPrice(event: any) {
      if (!event.target || (event.target && event.target.value == '')) {
         return;
      }
      this.newPrice = parseFloat(event.target.value.replace('£', '').replace(',', ''));
   }

   getEstimatedDayToSellAndPriceIndicator() {
      const parms: GetEstimatedDayToSellAndPriceIndicatorParams = {
         AdvertiserIds: [this.service.modalItem.AdvertDetail.RetailerSiteRetailerId],
         DerivativeId: this.service.modalItem.AdvertDetail.DerivativeId,
         FirstRegisteredDate: this.service.modalItem.AdvertDetail.FirstRegisteredDate,
         Mileage: this.service.modalItem.AdvertDetail.OdometerReading,
         StrategyPrice: this.newPrice,
         VehicleHasOptionsSpecified: this.service.modalItem.AdvertDetail.VehicleHasOptionsSpecified,
         VehicleAdvertPortalOptions: this.service.modalItem.AdvertDetail.VehicleAdvertPortalOptions,

         AverageValuation: this.service.modalItem.AdvertDetail.ValuationMktAvRetail,
         AdjustedValuation: this.service.modalItem.AdvertDetail.ValuationAdjRetail
      }

      this.getDataService.getAutoPriceEstimatedDayToSellAndPriceIndicator(parms)
         .subscribe((res: DayToSellAndPriceIndicator) => {
            this.daysToSell = res.DaysToSellResults.find(x => x.RetailerSiteRetailerId === this.service.modalItem.AdvertDetail.RetailerSiteRetailerId).DaysToSell;
            this.priceIndicator = res.PriceIndicator;
         }, error => {
            console.error('Failed to retrieve estimated Day to Sell & Price Indicator', error);
         })
   }

   select(event: any) {
      event.target.select();
   }

   maybeUpdatePrice() {
      let modalResultSubscription: Subscription = this.selectionsService.confirmModalEmitter.subscribe(res => {
         if (res) {
            this.updatePrice();
         }
         modalResultSubscription.unsubscribe();
      })

      this.constantsService.confirmModal.showModal('Are you sure? This will update the live AutoTrader price.', null);
   }

   updatePrice() {
      this.selectionsService.triggerSpinner.emit({show: true, message: 'Setting price...'});

      let params: UpdatePriceParams = {
         vehicleAdvertId: this.service.modalItem.AdvertDetail.AdId,
         newPrice: this.newPrice,
         oldPrice: this.service.modalItem.AdvertDetail.AdvertisedPrice
      }

      this.getDataService.updateStockPrice(params).subscribe((res) => {
         this.constantsService.toastSuccess('Successfully set price');
         this.getVehicleOptOutStatus();
         this.selectionsService.triggerSpinner.emit({show: false});
      }, error => {
         this.constantsService.toastDanger('Failed to set price')
         console.error('Failed to set price', error);
         this.selectionsService.triggerSpinner.emit({show: false});
      })
   }

   isItemBeingAutoPriced() {
      if (!this.service.modalItem.AdvertDetail.SiteOptedIntoAutoPricing) {
         return false;
      } //site is not opted into pricing, must be false
      if (!this.isOptedOut()) {
         return true
      }
   }

   optOutMessage(): string {

      if (this.isItemBeingAutoPriced()) {
         return `Auto pricing is enabled.  Click the slider to opt-out.`;
      } else {
         let returnVal = `Vehicle is opted out of auto pricing until ` +
            `${this.cphPipe.transform(this.vehicleOptOutStatus.EndDate, 'date', 0)}. `;

         if (this.vehicleOptOutStatus.OptOutReasonId != null) {
            returnVal += `Reason: ${this.constantsService.OptOutReasons.find(x => x.Id === this.vehicleOptOutStatus.OptOutReasonId).Reason}. `;
         }

         returnVal += `Click the slider to opt-in.`;

         return returnVal;
      }
   }
}
