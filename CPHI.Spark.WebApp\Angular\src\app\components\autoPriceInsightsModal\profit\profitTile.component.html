<div class="tile-inner">
    <div class="tile-header">
        DMS Stock Information
    </div>
    <div class="tile-body">
        <table>
            <tr>
                <td>Vehicle type:</td>
                <td>{{service.modalItem.AdvertDetail.VehicleType}} </td>
            </tr>
            <tr>
                <td>Stock Number</td>
                <td>{{ service.modalItem.AdvertDetail.StockNumber  }}</td>
            </tr>
            <tr>
                <td>Date in stock:</td>
                <td>{{service.modalItem.AdvertDetail.StockDate|cph:'dateShortYear':0}} </td>               
            </tr>
            <tr>
                <td>Days in stock:</td>
                <td>{{ service.modalItem.AdvertDetail.DaysInStock | cph:'number':0 }}</td>
            </tr>
            <tr>
                <td>Cost Price:</td>
                <td>{{ service.modalItem.AdvertDetail.CostPrice | cph:'currency':0 }}</td>
            </tr>
            <tr>
                <td>VAT Qualifying</td>
                <td><div class="value"><input type="checkbox" disabled [ngModel]="service.modalItem.AdvertDetail.VatQualifying" /></div></td>
            </tr>
            <tr>
                <td>Prep Cost:</td>
                <td>{{ service.modalItem.AdvertDetail.PrepCost | cph:'currency':0 }}</td>
            </tr>
            <tr>
                <td>Priced Profit</td>
                <td>{{ service.modalItem.AdvertDetail.PricedProfit | cph:'currency':0 }}</td>
            </tr>


            <ng-container *ngIf="showBigSection">

                 <tr>
                    <td> Dash Link</td>
                    <td > <a  [href]="buildDashLink" target="_blank">
                        (Click here - opens in new tab)
                    </a></td>
                </tr>
                  <!-- ProgressCode: string;
   BoughtFrom: string;
   Buyer: string;
   CapClean: number | null;
   CapId: number | null;
   V5: boolean | null;
   HaveServiceHistory: boolean | null;
   NextMotDate: Date | string | null;
   KeyTracker: string;
   RecommendBuy: boolean | null;
   OptOutReason: string;

   //new cols for Big (performance)
   CustInterestRating: number | null;
   CustomerInterest: number | null;
   MostRecentInterestDate: Date | string | null;
   Appointments: number | null;
   MostRecentAppointmentDate: Date | string | null;
   SiteVisits: number | null;
   MostRecentSiteVisitDate: Date | string | null; -->

                <tr>
                    <td>Progress Code</td>
                    <td>{{ service.modalItem.AdvertDetail.ProgressCode }}</td>
                </tr>
                <tr>
                    <td>Bought From</td>
                    <td>{{ service.modalItem.AdvertDetail.BoughtFrom }}</td>
                </tr>
                <tr>
                    <td>Buyer</td>
                    <td>{{ service.modalItem.AdvertDetail.Buyer }}</td>
                </tr>
                <tr>
                    <td>Cap Clean</td>
                    <td>{{ service.modalItem.AdvertDetail.CapClean }}</td>
                </tr>
                <tr>
                    <td>Cap ID</td>
                    <td>{{ service.modalItem.AdvertDetail.CapId }}</td>
                </tr>
                <tr>
                    <td>V5</td>
                    <td>{{ service.modalItem.AdvertDetail.V5 }}</td>
                </tr>
                <tr>
                    <td>Have Service History</td>
                    <td>{{ service.modalItem.AdvertDetail.HaveServiceHistory }}</td>
                </tr>
                <tr>
                    <td>Next MOT Date</td>
                    <td>{{ service.modalItem.AdvertDetail.NextMotDate|cph:'dateShortYear':0 }}</td>
                </tr>
                <tr>
                    <td>Key Tracker</td>
                    <td>{{ service.modalItem.AdvertDetail.KeyTracker }}</td>
                </tr>
                <tr>
                    <td>Recommend Buy</td>
                    <td>{{ service.modalItem.AdvertDetail.RecommendBuy }}</td>
                </tr>
                <tr>
                    <td>Opt Out Reason</td>
                    <td>{{ service.modalItem.AdvertDetail.OptOutReason }}</td>
                </tr>

                <tr>
                    <td>Customer Interest Rating</td>
                    <td>{{ service.modalItem.AdvertDetail.CustInterestRating }}</td>
                </tr>
                <tr>
                    <td>Customer Interest</td>
                    <td>{{ service.modalItem.AdvertDetail.CustomerInterest }}</td>
                </tr>
                <tr>
                    <td>Most Recent Interest Date</td>
                    <td>{{ service.modalItem.AdvertDetail.MostRecentInterestDate|cph:'dateShortYear':0 }}</td>
                </tr>
                <tr>
                    <td>Appointments</td>
                    <td>{{ service.modalItem.AdvertDetail.Appointments }}</td>
                </tr>
                <tr>
                    <td>Most Recent Appointment Date</td>
                    <td>{{ service.modalItem.AdvertDetail.MostRecentAppointmentDate|cph:'dateShortYear':0 }}</td>
                </tr>
                <tr>
                    <td>Site Visits</td>
                    <td>{{ service.modalItem.AdvertDetail.SiteVisits }}</td>
                </tr>
                <tr>
                    <td>Most Recent Site Visit Date</td>
                    <td>{{ service.modalItem.AdvertDetail.MostRecentSiteVisitDate|cph:'dateShortYear':0 }}</td>
                </tr>


            </ng-container>
        </table>
    </div>
</div>
