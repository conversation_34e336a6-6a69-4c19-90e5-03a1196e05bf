import {Component, OnInit} from '@angular/core';
import {VehicleValuationService} from '../vehicleValuation.service';
import {ValuationCostingFieldEnum} from '../valuationCosting/ValuationCostingDTO';
import {GlobalParamKey, GlobalParamTypeMap} from "../../../model/GlobalParam";

@Component({
   selector: 'costingTable',
   templateUrl: './costingTable.component.html',
   styleUrls: ['./costingTable.component.scss']
})
export class CostingTableComponent implements OnInit {
   flashEmitterSub: any;

   protectPrepCosts?: boolean;

   constructor(
      public service: VehicleValuationService
   ) {
      this.protectPrepCosts = this.service.globalParamsService.getTypedGlobalParam(GlobalParamKey.protectPrepCosts);
   }

   ValuationCostingFieldEnum = ValuationCostingFieldEnum;

   ngOnInit(): void {

      console.log(this.service.valuationCosting, "this.service.valuationCosting!");
      
      this.flashEmitterSub = this.service.valuationCostingFlashEmitter.subscribe(res => {
         this.onFlash(res);
      })

      // this.service.setProfitTo(12345);

   }

   ngOnDestroy() {
      if (this.flashEmitterSub) {
         this.flashEmitterSub.unsubscribe()
      }
   }

   flashState: { [key: string]: boolean } = {};
   infinity = Infinity;

   public handleInputChange(event: Event, fieldName: ValuationCostingFieldEnum) {

      const inputElement = event.target as HTMLInputElement;

      if (this.positiveOnly()) {
         inputElement.value = inputElement.value.replace(/-/g, '');
         this.service.valuationCosting[fieldName] = inputElement.value;
      }

      let numericValue = this.parseCurrency(inputElement.value);

      if (fieldName === ValuationCostingFieldEnum.PricePosition) {
         numericValue = numericValue / 100
      }

      this.service.emitUpdatedCostingValue(fieldName, numericValue)
   }

   public toggleIsVatQualifying(value: boolean) {
      if (value == true) {
         this.service.valuationCosting.VatCost = null;
      }
      this.service.valuationCosting.IsVatQualifying = value;
      this.service.emitUpdatedCostingValue(ValuationCostingFieldEnum.VatCost, this.service.valuationCosting.vatCost);
   }

   showPriceIndicator() {
      return this.service.valuationCosting.pricePosition >= 0.9 && this.service.valuationCosting.pricePosition <= 1.1
   }


   onFlash(enumIndex: ValuationCostingFieldEnum) {
      const fieldName = ValuationCostingFieldEnum[enumIndex].toString();
      this.flashState[fieldName] = true;
      setTimeout(() => {
         this.flashState[fieldName] = false;
      }, 1000); // Flash for 1000 ms
   }


   parseCurrency(formattedValue: string): number {
      // Assuming formattedValue is a string like "$1,000.00"
      return Number(formattedValue.replace(/[^0-9.-]+/g, ""));
   }

   additionalMechDisabled() {
      return this.protectPrepCosts == true;
   }

   positiveOnly() {
      return this.protectPrepCosts == true;
   }
}
