import {EventEmitter, Injectable} from "@angular/core";
import {NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {GridA<PERSON>} from "ag-grid-community";
import {CphPipe} from "src/app/cph.pipe";
import {RetailerSite} from "src/app/model/RetailerSite";
import {CompetitorVehicle} from "src/app/model/CompetitorVehicle";
import {GetValuationModalNewParams} from "src/app/model/GetValuationModalNewParams";
import {GetValuationPriceSetParams} from "src/app/model/GetValuationPriceSetParams.model";
import {NumberPlateYear} from "src/app/model/NumberPlateYear";
import {ValuationModalNew} from "src/app/model/ValuationModalNew";
import {ValuationPriceSet} from "src/app/model/ValuationPriceSet.model";
import {VehicleSpecOption} from "src/app/model/VehicleSpecOption.model";
import {ConstantsService} from "src/app/services/constants.service";
import {GetDataMethodsService} from "src/app/services/getDataMethods.service";
import {SelectionsService} from "src/app/services/selections.service";
import {UserPreferenceService} from "src/app/services/userPreference.service";
import {BubbleChartFields} from "../competitorAnalysis/BubbleChartFields";
import {CompetitorAnalysisService} from "../competitorAnalysis/competitorAnalysis.service";
import {CompetitorAnalysisParams} from "../competitorAnalysis/CompetitorAnalysisParams";
import {FutureValuationChartComponent} from "./futureValuationChart/futureValuationChart.component";
import {ValuationCosting} from "./valuationCosting/valuationCosting";
import {ValuationCostingDTO, ValuationCostingFieldEnum} from "./valuationCosting/ValuationCostingDTO";
import {VehicleOptionsModalComponent} from "./vehicleOptionsModal/vehicleOptionsModal.component";
import {VehicleOptionsTableComponent} from "./vehicleOptionsTable/VehicleOptionsTable/vehicleOptionsTable.component";
import {VehicleRecallStatus} from "src/app/model/VehicleRecallStatus";
import {GlobalParamKey} from "../../model/GlobalParam";
import {GlobalParamsService} from "../../services/globalParams.service";
import {StrategyPriceBuildUpLayersParams} from "../strategyPriceBuildUpLayers/strategyPriceBuildUpLayers.component";

export interface newValue {
   value: number,
   field: ValuationCostingFieldEnum
}

@Injectable({
   providedIn: 'root'
})

export class VehicleValuationService {


   valuationId: number;
   batchId: number;
   valuationCosting: ValuationCosting; //our new costing model

   builtUpLayers: StrategyPriceBuildUpLayersParams;


   //user choices
   vehicleReg: string;
   mileage: number;
   vehicleCondition: string;
   colour: string;
   notes: string;
   chosenRetailerSite: RetailerSite;

   //transient ui thing
   blockMoreOptionChanges: boolean;

   //main data object
   amCalculating: boolean = false;
   valuationModalResultNew: ValuationModalNew;

   //objects for options modal
   optionsModalOptions: VehicleSpecOption[]
   optionsModalValuation: ValuationPriceSet;


   //references to components
   optionsModalRef: NgbModalRef;
   strategyPriceBySiteModalRef: NgbModalRef;
   optionsTableRef: VehicleOptionsTableComponent

   trendedValuationInstance: FutureValuationChartComponent;
   optionsGridApi: GridApi;

   public valuationCostingFlashEmitter = new EventEmitter<ValuationCostingFieldEnum>();
   public valuationCostingChangeEmitter: EventEmitter<newValue> = new EventEmitter();
   public initialDataLoadedEmitter: EventEmitter<void> = new EventEmitter();


   distances: number[] = [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 70, 80, 90, 100, 200];
   chartXYFields: BubbleChartFields[] = [
      {
         x: 'Mileage',
         xAdvert: 'OdometerReading',
         xPretty: 'Mileage',
         xPipe: 'number',
         y: 'PricePosition',
         yPretty: 'Price Position',
         yPipe: 'percent'
      },
      {
         x: 'Mileage',
         xAdvert: 'OdometerReading',
         xPretty: 'Mileage',
         xPipe: 'number',
         y: 'AdvertisedPrice',
         yPretty: 'Price',
         yPipe: 'currency'
      }
   ];
   selectedRadius: number;
   selectedMinPlate: NumberPlateYear;
   selectedMaxPlate: NumberPlateYear;
   numberPlateYear: NumberPlateYear[] = [];
   chartData: BubbleChartFields = {
      x: 'Mileage',
      xAdvert: 'OdometerReading',
      xPretty: 'Mileage',
      xPipe: 'number',
      y: 'PricePosition',
      yPretty: 'Price Position',
      yPipe: 'percent'
   }

   constructor(
      public constantsService: ConstantsService,
      public selectionsService: SelectionsService,
      public getDataService: GetDataMethodsService,
      public modalService: NgbModal,
      public userPrefsService: UserPreferenceService,
      public competitorAnalysisService: CompetitorAnalysisService,
      public cphPipe: CphPipe,
      public globalParamsService: GlobalParamsService
   ) {
   }


   get chosenVehicleLocationStrategyPriceBuild() {
      return this.valuationModalResultNew.LocationStrategyPrices.find(x => x.RetailerSite.Id === this.chosenRetailerSite.Id);
   }


   public getVehicleInformationAndValuation(modalRef: NgbModalRef) {
      this.selectionsService.triggerSpinner.emit({show: true, message: 'Loading...'});

      const vehicleReg = this.vehicleReg != undefined ? this.vehicleReg.replace(/\s+/g, '') : '';
      const mileage = this.mileage ?? 0;
      const condition = this.vehicleCondition == 'Unclassified' ? 'Excellent' : this.vehicleCondition;

      const includeUnPub: boolean = !!(this.globalParamsService.getGlobalParam(GlobalParamKey.webAppShowDefaultShowUnPublishedVehicles) as boolean)

      const parms: GetValuationModalNewParams = {
         vehicleReg: vehicleReg,
         mileage: mileage,
         condition: condition,
         vehicleValuationId: this.valuationId,
         chosenOptions: this.valuationModalResultNew?.VehicleInformation?.ChosenOptions != null ?
            this.valuationModalResultNew.VehicleInformation.ChosenOptions : null,
         includeUnpublishedAds: includeUnPub,
         colour: this.colour
      };

      this.getDataService.getValuationModalNew(parms).subscribe(
         (resNew: ValuationModalNew) => {

            this.valuationModalResultNew = resNew;

            // If we have error message, do not populate modal with further details
            if(this.valuationModalResultNew.ErrorMessage){
               this.selectionsService.triggerSpinner.emit({show: false});
               return;
            }

            this.notes = this.valuationModalResultNew?.Valuation?.Notes;

            // have to properly initialise options
            let properlyInitiatedOptions = [];

            this.valuationModalResultNew.SpecOptions.forEach(op => {
               properlyInitiatedOptions.push(new VehicleSpecOption(op));
            });

            this.valuationModalResultNew.SpecOptions = properlyInitiatedOptions;

            // if we haven't chosen a retailer site yet, set it using users's home site
            if (!this.chosenRetailerSite) {
               this.chosenRetailerSite = resNew.LocationStrategyPrices
                  .find(x => x.RetailerSite.Id === this.selectionsService.user.RetailerSiteId)?.RetailerSite;
            }

            if (this.valuationId &&
               (this.valuationModalResultNew.Valuation.Sales ?? this.valuationModalResultNew.Valuation.StrategyPrice)) {
               // update the costing from the previously saved valuation
               this.updateCostingFromPreviouslySavedValuationValues();
            } else {
               // no valuation id, so we need to use the latest strategy price to set the costing
               this.updateCostingFromStrategyPrice();
            }

            this.competitorAnalysisService.params$.next(this.updateCompetitorAnalysisForOurNewPrice());

            this.selectionsService.triggerSpinner.emit({show: false});

            // Emit that initial data loading is complete
            this.initialDataLoadedEmitter.emit();

         }
         , error => {
            if (modalRef) {
               this.selectionsService.triggerSpinner.emit({show: false});
               modalRef.close();
            }
         });
   }

   public chooseNewSite(retailerSite: RetailerSite) {
      this.chosenRetailerSite = retailerSite;
      this.updateCostingFromStrategyPrice();
   }

   private updateCostingFromStrategyPrice() {

      const sales = this.chosenVehicleLocationStrategyPriceBuild?.StrategyPrice;
      const valn = this.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle;
      const paint = this.chosenVehicleLocationStrategyPriceBuild?.TargetPaintPrep;

      const profit = this.chosenVehicleLocationStrategyPriceBuild?.TargetMargin > 0 ?
         this.chosenVehicleLocationStrategyPriceBuild?.TargetMargin
         :
         this.chosenVehicleLocationStrategyPriceBuild?.TargetMargin;

      const warranty =
         (this.chosenVehicleLocationStrategyPriceBuild?.TargetWarrantyFee) ?
            this.chosenVehicleLocationStrategyPriceBuild?.TargetWarrantyFee :
            this.chosenVehicleLocationStrategyPriceBuild?.TargetWarrantyFee;

      const mech =
         (this.chosenVehicleLocationStrategyPriceBuild?.TargetAdditionalMech) ?
            this.chosenVehicleLocationStrategyPriceBuild?.TargetAdditionalMech :
            this.chosenVehicleLocationStrategyPriceBuild?.TargetAdditionalMech;

      const fee = this.chosenVehicleLocationStrategyPriceBuild?.TargetAuctionFee;
      const delivery = this.chosenVehicleLocationStrategyPriceBuild?.TargetDelivery;
      const other = this.chosenVehicleLocationStrategyPriceBuild?.TargetOtherCost;
      const isVatQualifying = this.valuationModalResultNew.Valuation?.IsVatQualifying;
      const vatCost = this.valuationModalResultNew.Valuation?.VatCost ? this.valuationModalResultNew.Valuation.VatCost : null;

      const state: ValuationCostingDTO =
         new ValuationCostingDTO({
            Valn: valn,
            Sales: sales,
            Profit: profit,
            Valet: 0,
            SpareKey: 0,
            MOT: 0,
            MOTAdvisory: 0,
            Servicing: 0,
            Paint: paint,
            Tyres: 0,
            Warranty: warranty,
            Parts: 0,
            AdditionalMech: mech,
            Fee: fee,
            Delivery: delivery,
            Other: other,
            IsVatQualifying: isVatQualifying,
            VatCost: vatCost
         });

      this.valuationCosting.updateFromSavedState(state);
   }


   private updateCostingFromPreviouslySavedValuationValues() {

      if (!this.valuationModalResultNew.Valuation.IsVatQualifying) {
         this.emitUpdatedCostingValue(ValuationCostingFieldEnum.VatCost, this.valuationModalResultNew.Valuation.VatCost);
      }

      const state: ValuationCostingDTO = new ValuationCostingDTO({
         Valn: this.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle,
         Sales: this.valuationModalResultNew.Valuation.Sales ?? this.valuationModalResultNew.Valuation.StrategyPrice,
         Profit: this.valuationModalResultNew.Valuation.Profit ?? this.chosenVehicleLocationStrategyPriceBuild?.TargetMargin,
         Valet: this.valuationModalResultNew.Valuation.Valet,
         SpareKey: this.valuationModalResultNew.Valuation.SpareKey,
         MOT: this.valuationModalResultNew.Valuation.MOT,
         MOTAdvisory: this.valuationModalResultNew.Valuation.MOTAdvisory,
         Servicing: this.valuationModalResultNew.Valuation.Servicing,
         Paint: this.valuationModalResultNew.Valuation.Paint,
         Tyres: this.valuationModalResultNew.Valuation.Tyres,
         Warranty: this.valuationModalResultNew.Valuation.Warranty,
         Parts: this.valuationModalResultNew.Valuation.Parts,
         AdditionalMech: this.valuationModalResultNew.Valuation.AdditionalMech,
         Fee: this.valuationModalResultNew.Valuation.Fee,
         Delivery: this.valuationModalResultNew.Valuation.Delivery,
         Other: this.valuationModalResultNew.Valuation.Other,
         IsVatQualifying: this.valuationModalResultNew.Valuation.IsVatQualifying,
         VatCost: this.valuationModalResultNew.Valuation.VatCost
      })
      this.valuationCosting.updateFromSavedState(state);
   }

   updateCompetitorAnalysisForOurNewPrice(): CompetitorAnalysisParams {

      const vehicleInformation = this.valuationModalResultNew.VehicleInformation;
      let competitorSummary = this.valuationModalResultNew.CompetitorCheckResult;

      if (competitorSummary != null) {
         // need to remove our old entry
         const ourVehicleIndex = competitorSummary.CompetitorVehicles.findIndex(x => x.IsOurVehicle);
         const ourOldVehicle = competitorSummary.CompetitorVehicles.splice(ourVehicleIndex, 1);

         // then add the new one
         const ourNewVehicle = new CompetitorVehicle(ourOldVehicle[0], this.valuationCosting.sales, this.valuationCosting.pricePosition);
         competitorSummary.CompetitorVehicles.push(ourNewVehicle);

         const prices = (competitorSummary.CompetitorVehicles.map(x => x.AdvertisedPrice as number)).sort((a, b) => a - b);
         const pricePositions = (competitorSummary.CompetitorVehicles.map(x => x.PricePosition as number)).sort((a, b) => a - b);
         competitorSummary.PriceRank = prices.findIndex(x => x === ourNewVehicle.AdvertisedPrice) + 1;
         competitorSummary.ValueRank = pricePositions.findIndex(x => x === ourNewVehicle.PricePosition) + 1;
      }


      return {
         DerivativeId: vehicleInformation.DerivativeId,
         Make: vehicleInformation.Make,
         Model: vehicleInformation.Model,
         Trim: vehicleInformation.Trim,
         RetailerSiteIdForPostcode: this.selectionsService.userRetailerSite.Id,
         TransmissionType: vehicleInformation.Transmission,
         FuelType: vehicleInformation.FuelType,
         BodyType: vehicleInformation.BodyType,
         Drivetrain: vehicleInformation.Drivetrain,
         Doors: parseInt(vehicleInformation.Doors),


         // plus always these:
         VehicleReg: vehicleInformation.VehicleReg,
         OdometerReading: vehicleInformation.Mileage,
         FirstRegisteredDate: new Date(vehicleInformation.FirstRegistered),
         AdvertisedPrice: Math.round(this.valuationCosting.sales),
         PricePosition: this.valuationCosting.pricePosition,
         Valuation: this.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle,

         RetailerSiteRetailerId: this.chosenVehicleLocationStrategyPriceBuild?.RetailerSite.RetailerId,
         CompetitorSummary: competitorSummary,
         ParentType: 'valuationModal',
         ImageURL: null,
         VehicleValuationService: this,
         TradePriceSetting: this.valuationModalResultNew.TradePriceSetting
      };
   }

   chooseAverageSpec() {
      this.valuationModalResultNew.VehicleInformation.ChosenOptions = null;
      // this.userHasChosenOptions = false;
      // this.getNewValuationForAdvert();
   }

   async getNewValuationForAdvert() {
      if (!this.valuationModalResultNew) {
         return;
      }

      const chosenOptions = this.valuationModalResultNew.VehicleInformation.ChosenOptions;
      const params: GetValuationPriceSetParams = {
         //BatchId: this.batchId,
         DerivativeId: this.valuationModalResultNew.VehicleInformation.DerivativeId,
         FirstRegistrationDate: new Date(this.valuationModalResultNew.VehicleInformation.FirstRegistered),
         Mileage: this.mileage,
         Condition: this.vehicleCondition,
         HaveProvidedFeatures: chosenOptions != null,
         Features: chosenOptions
      };


      // Get the valuation
      await this.getDataService.getNewValuationForAdvert(params).then(async (res: ValuationPriceSet) => {
         //await this.onValuationLoad(res);

      }, error => {
         console.error('Error returning valuation price set');
         this.selectionsService.triggerSpinner.emit({show: false});
      })
   }


   async retryRecall() {
      this.valuationModalResultNew.VehicleInformation.RecallStatus = VehicleRecallStatus.Retrying;
      this.getDataService.getRecallStatus(this.valuationModalResultNew.VehicleInformation.VehicleReg).then(async (res: VehicleRecallStatus) => {
         this.valuationModalResultNew.VehicleInformation.RecallStatus = res;
      })
   }


   chooseRetailerSite(retailerSite: RetailerSite) {
      this.chosenRetailerSite = retailerSite;
      //this.getSellingOutlook(retailerSite.RetailerId)
   }

   setSellingPriceTo(newValue: number) {
      this.valuationCosting.Sales = newValue;
      this.emitUpdatedCostingValue(ValuationCostingFieldEnum.Sales, newValue)
   }

   setProfitTo(newValue: number) {
      this.valuationCosting.Profit = newValue;
      this.emitUpdatedCostingValue(ValuationCostingFieldEnum.Profit, newValue)
   }

   emitUpdatedCostingValue(fieldName: ValuationCostingFieldEnum, value: number) {
      const newVal: newValue = {
         value: value,
         field: fieldName
      }
      this.valuationCostingChangeEmitter.emit(newVal);
   }


   async openOptionsModal() {

      if (!this.optionsModalOptions) {
         this.initialiseOptionsModalOptions();
      }


      this.optionsModalRef = this.modalService.open(VehicleOptionsModalComponent, {
         size: 'sm',
         windowClass: 'autoTraderSpecOptionsModal',
         keyboard: false,
         ariaLabelledBy: 'modal-basic-title'
      });
      this.optionsModalRef.result.then((result: string) => {
         // have hit ok.   Need to now copy all properties across from the temporary modal ones to the main ones

         if (result === 'revert') {
            // have chosen to revert to average spec
            this.valuationModalResultNew.VehicleInformation.ChosenOptions = null;
            this.optionsModalOptions = null;
         } else {
            // we have chosen some options
            this.valuationModalResultNew.VehicleInformation.ChosenOptions = [];
            this.optionsModalOptions.forEach(option => {

               const optionInMain: VehicleSpecOption = this.valuationModalResultNew.SpecOptions
                  .find(x => x.Name == option.Name && x.Category == option.Category);

               optionInMain.UpdateProps(option);

               if (option.IsChosen) {
                  this.valuationModalResultNew.VehicleInformation.ChosenOptions.push(option.Name);
               }
            });
         }

         this.getVehicleInformationAndValuation(null);


      }, async () => {
         // just chose to close
      });
   }


   private initialiseOptionsModalOptions() {

      // initialise the options array using all the options from the master list
      this.optionsModalOptions = [];
      this.valuationModalResultNew.SpecOptions.map(x => this.optionsModalOptions.push(new VehicleSpecOption(x)));
      if (this.valuationModalResultNew.VehicleInformation.ChosenOptions) {
         this.valuationModalResultNew.VehicleInformation.ChosenOptions.forEach(option => {
            const optionInModalOptions = this.optionsModalOptions.find(x => x.Name === option);
            if (optionInModalOptions) {
               optionInModalOptions.IsChosen = true;
            }
         });
      }
   }


}
