import {
   Component,
   ElementRef,
   EventEmitter,
   Input,
   OnChanges,
   OnDestroy,
   OnInit,
   SimpleChanges,
   ViewChild
} from '@angular/core';
import {Chart, ChartConfiguration} from 'chart.js';
import {CphPipe} from 'src/app/cph.pipe';
import {BarChartParams} from 'src/app/model/BarChartParams';
import {LeavingVehicleBarChartSet} from 'src/app/model/LeavingVehicleBarChartSet';
import {VNTileParams} from 'src/app/model/VNTileParams';
import {VNTileTableRow} from 'src/app/model/VNTileTableRow';
import {BIChartTileDataType, TileType} from '../biChartTile/biChartTile.component';
import {AutotraderService} from "src/app/services/autotrader.service";
import {DashboardMeasure} from 'src/app/model/DashboardMeasure';
import {SelectionsService} from 'src/app/services/selections.service';
import {Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {WasNowEnum} from "../../pages/autoprice/leavingVehicles/leavingVehicleWasNowEnum";

@Component({
   selector: 'barChart',
   templateUrl: './barChart.component.html',
   styleUrls: ['./barChart.component.scss']
})
export class BarChartComponent implements OnInit, OnDestroy, OnChanges {
   private destroy$ = new Subject<void>();
   private pendingRedraw = false;
   @Input() params: BarChartParams;
   @Input() yMax?: number;
   @Input() newDataEmitter: EventEmitter<LeavingVehicleBarChartSet>;
   @Input() dataKey: string;
   @Input() doesFilter: boolean;
   @Input() wasNow: WasNowEnum;
   @Input() public pageParams: VNTileParams;
   @Input() public dataType: BIChartTileDataType;
   @Input() public fieldName: string;
   @Input() public tileType: TileType;
   @Input() public dashboardParams: any;
   @Input() public showPercentageOfTotal: boolean;
   @Input() public hideZeroLabels: boolean = false;
   @Input() public disableAnimations: boolean = false;
   @ViewChild('barChart', {static: true}) chartCanvas: ElementRef;
   chart: Chart;
   tableRows: VNTileTableRow[];
   maxValue: number;

   constructor(
      public cphPipe: CphPipe,
      public selectionsService: SelectionsService
   ) {
   }

   ngOnInit(): void {
      this.buildChart();
      if (this.doesFilter) {
         this.buildTableRows();
      }

      this.newDataEmitter
         .pipe(takeUntil(this.destroy$)) // Ensures it unsubscribes when component is destroyed
         .subscribe(res => {

            const newData = res[this.dataKey];

            this.params = newData;

            // Don't redraw if this chart is currently selected (to preserve opacity)
            if (this.dashboardParams && this.dashboardParams.parentMethods && this.dashboardParams.parentMethods.isChartSelected) {
               if (this.dashboardParams.parentMethods.isChartSelected(this.dataKey)) {
                  this.updateChartColors();
                  return;
               }
            }

            this.redrawChart();
         });
   }

   ngOnDestroy() {
      this.destroy$.next();
      this.destroy$.complete();

      if (this.chart) {
         this.chart.destroy();
      }
   }

   ngOnChanges(changes: SimpleChanges) {
      if (changes.yMax && this.chart != null) {
         this.chart.options.scales.y.max = changes.yMax.currentValue;
         this.chart.update();
      }
   }

   redrawChart() {
      // If there's already a pending redraw, don't queue another one
      if (this.pendingRedraw) {
         return;
      }

      this.pendingRedraw = true;

      // Use requestAnimationFrame to ensure any ongoing Chart.js event processing is complete
      requestAnimationFrame(() => {
         if (this.chart) {
            this.chart.destroy();
         }
         this.buildChart();
         this.pendingRedraw = false;
      });
   }

   updateChartColors() {
      if (!this.chart || !this.params.StackedDatasets) return;

      // Update the chart data with new colors from params
      const dataset = this.chart.data.datasets[0];
      const newDataset = this.params.StackedDatasets[0];

      if (newDataset) {
         dataset.backgroundColor = [...newDataset.backgroundColor];
         dataset.borderColor = [...newDataset.borderColor];
         dataset.borderWidth = [...newDataset.borderWidth];

         // Update the chart without animation
         this.chart.update('none');
      }
   }

   buildChart() {
      try {
         // Safety check: don't build if component is being destroyed
         if (this.destroy$.closed) {
            return;
         }
         const datasets = this.params.StackedDatasets ?
            this.params.StackedDatasets.map(dataset => ({
               data: dataset.data,
               backgroundColor: dataset.backgroundColor,
               borderColor: dataset.borderColor,
               borderWidth: dataset.borderWidth,
               categoryPercentage: 0.95,
               label: dataset.label
            })) : [{
               data: this.params.Values,
               backgroundColor: this.params.Colours,
               categoryPercentage: 0.95,
            }];

      const config: ChartConfiguration = {
         type: 'bar',
         data: {
            labels: this.params.Labels,
            datasets: datasets
         },
         options: {
            onClick: (c, i) => {
               try {
                  if (!this.doesFilter || !this.chart) {
                     return;
                  }

                  if (!i || i.length === 0 || !i[0] || i[0].index === undefined) {
                     console.warn('Invalid chart click data:', i);
                     return;
                  }

                  const clickedIndex = i[0].index;
                  const clickedLabel = this.chart.data.labels[clickedIndex];

                  // Use requestAnimationFrame to defer the action until after Chart.js completes its event processing
                  requestAnimationFrame(() => {
                     if (this.dashboardParams) {
                        this.dashboardParams.parentMethods.filterTable(
                           clickedLabel,
                           this.dataKey
                        );
                     }
                     if (this.pageParams) {
                        this.pageParams.parentMethods.highlightRow(this.tableRows[clickedIndex], this.fieldName, this.wasNow);
                        this.pageParams.highlightChoiceHasBeenMade.emit();
                     }
                  });
               } catch (error) {
                  console.error('Error in chart onClick handler:', error);
               }
            },
            onHover: (event, chartElement) => {
               const nativeEvent = event.native!;
               const target = nativeEvent.target as HTMLElement;
               if (chartElement.length) {
                  target.style.cursor = 'pointer';
               } else {
                  target.style.cursor = 'default';
               }
            },
            responsive: true,
            maintainAspectRatio: false,
            layout: {
               padding: {
                  top: this.showPercentageOfTotal ? 45 : 15
               }
            },
            hover: {
               mode: this.doesFilter ? 'nearest' : null
            },
            plugins: {
               tooltip: {
                  enabled: false,
               },
               title: {
                  display: false,
                  // text: this.params.Title,
                  // padding: {
                  //   top: 20,
                  //   bottom: 20
                  // }
               },
               legend: {
                  display: false
               },
               datalabels: {
                  anchor: (context) => {
                     // Use labelAnchor if specified, otherwise use default logic
                     if (this.params.labelAnchor) {
                        return this.params.labelAnchor;
                     }
                     if (context.dataset.data[context.dataIndex] >= 0) {
                        return 'end';
                     } else {
                        return 'start';
                     }
                  },
                  align: this.params.labelAlign || 'center',
                  textAlign: 'center',
                  offset: this.params.labelAnchor === 'center' ? 0 : (this.params.labelOffset || 0),
                  color: (context) => {
                     // Use black color when label is centered on the bar
                     if (this.params.labelAnchor === 'center') {
                        return '#000';
                     }
                     // Default color for labels outside bars
                     return '#666';
                  },
                  formatter: (value, context) => {
                     // Don't show labels for zero values if hideZeroLabels is enabled
                     if (value === 0 && this.hideZeroLabels) {
                        return '';
                     }

                     let label: string;
                     const useInlineLabels = this.params.inlinePercentageLabels !== undefined ?
                        this.params.inlinePercentageLabels : false;

                     if (context.dataset.data[context.dataIndex] >= 0) {
                        label = useInlineLabels ?
                           this.cphPipe.transform(value, this.params.DataFormat, 0) :
                           this.cphPipe.transform(value, this.params.DataFormat, 0) + '\n';
                     } else {
                        label = useInlineLabels ?
                           this.cphPipe.transform(value, this.params.DataFormat, 0) :
                           '\n' + this.cphPipe.transform(value, this.params.DataFormat, 0);
                     }

                     if (this.showPercentageOfTotal) {
                        const total: number = this.params.Values.reduce((sum, num) => sum + num, 0);
                        const percent: number = (value / total);
                        if (useInlineLabels) {
                           label += ` (${this.cphPipe.transform(percent, 'percent', 0)})`;
                        } else {
                           label += `${this.cphPipe.transform(percent, 'percent', 0)}\n\n`;
                        }
                     }
                     return label;
                  }
               }
            },
            scales: {
               y: {
                  stacked: !!this.params.StackedDatasets,
                  ticks: {
                     display: this.params.showYAxisLabels !== false
                  },
                  grid: {
                     display: true
                  },
                  title: {
                     display: !!this.params.yAxisTitle,
                     text: this.params.yAxisTitle || ''
                  },
                  min: this.params.yMin,
                  max: this.params.yMax
               },
               x: {
                  stacked: !!this.params.StackedDatasets,
                  grid: {
                     display: false
                  },
                  ticks: {
                     maxRotation: 0,
                     autoSkip: false
                  }
               }
            },
            animation: {
               duration: this.disableAnimations ? 0 : 1000
            }
         },
         plugins: []
      };


      if (this.params.AverageLineValue) {
         config.options.plugins.annotation = {
            annotations: {
               line1: {
                  type: 'line',
                  yMin: this.params.AverageLineValue,
                  yMax: this.params.AverageLineValue,
                  xMin: 0,
                  xMax: this.params.Values.length - 1,
                  borderColor: 'black',
                  borderWidth: 2,
                  borderDash: [2]
               }
            }
         };
      }

      if (this.chart) {
         this.chart.destroy();
      }

         Chart.defaults.font.size = this.selectionsService.getChartFontSize();
         this.chart = new Chart(this.chartCanvas.nativeElement, config);
      } catch (error) {
         console.error('Error building chart:', error);
      }
   }

   buildTableRows() {
      if (this.pageParams) {
         this.tableRows = this.pageParams.parentMethods.buildRows(this.fieldName, this.dataType, this.tileType, this.wasNow);
         this.maxValue = this.tableRows.map(x => x.FilteredTotal).sort((a, b) => b - a)[0];
         const sortOrder = AutotraderService.getSortOrderForDaysBand();
         this.applyCustomSort(this.tableRows, sortOrder);
      }
   }

   private applyCustomSort(tableRows: VNTileTableRow[], sortOrder: string[]) {
      var ordering = {}; // map for efficient lookup of sortIndex
      for (var i = 0; i < sortOrder.length; i++) {
         ordering[sortOrder[i]] = i;
      }
      tableRows = tableRows.sort(function (a, b) {
         return (ordering[a.Label] - ordering[b.Label]) || a.Label.localeCompare(b.Label);
      });
   }

   isHighlightsChosen() {
      if (!this.doesFilter || !this.pageParams) {
         return;
      }
      let choice = this.userChoice();
      if (!choice) {
         console.error('failed finding ', this.fieldName);
      }
      return this.userChoice().ChosenValues.length > 0;
   }

   clearHighlights() {
      let choice = this.pageParams.highlightChoices.find(x => x.FieldName === this.fieldName);
      choice.ChosenValues = [];
      this.pageParams.highlightChoiceHasBeenMade.emit();
   }

   userChoice(): DashboardMeasure {
      let choice = this.pageParams?.highlightChoices?.find(x => x.FieldName === this.fieldName);
      if (!choice) {
         console.error('error finding ', this.fieldName);
      }
      return choice;
   }
}
