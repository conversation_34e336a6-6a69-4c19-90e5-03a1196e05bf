import {ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild} from "@angular/core";
import {CphPipe} from "src/app/cph.pipe";
import {CompetitorSummary} from "src/app/model/CompetitorSummary";
import CompetitorSearchOurVehicle from "src/app/pages/performanceTrends/CompetitorSearchOurVehicle";
import {CompetitorSearchUserChoices} from "src/app/pages/performanceTrends/CompetitorSearchUserChoices";
import {
   CompetitorSearchParams,
   GetValuationModalCompetitorAnalysisParams,
   OurVehicleParams,
} from "src/app/pages/performanceTrends/GetValuationModalCompetitorAnalysisParams";
import {ConstantsService} from "src/app/services/constants.service";
import {GetDataMethodsService} from "src/app/services/getDataMethods.service";
import {SelectionsService} from "src/app/services/selections.service";
import * as utilities from "./../../services/utilityFunctions";
import {BubbleChartFields} from "./BubbleChartFields";
import {CompetitorAnalysisService} from "./competitorAnalysis.service";
import {CompetitorAnalysisParams} from "./CompetitorAnalysisParams";
import {AutoPriceInsightsModalService} from "../autoPriceInsightsModal/autoPriceInsightsModal.service";
import {RetailerSite} from "src/app/model/RetailerSite";
import {GlobalParamsService} from "src/app/services/globalParams.service";
import {GlobalParamKey} from "src/app/model/GlobalParam";
import {NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";


@Component({
   selector: "competitorAnalysis",
   templateUrl: "./competitorAnalysis.component.html",
   styleUrls: ["./competitorAnalysis.component.scss"],
})
export class CompetitorAnalysisComponent implements OnInit {
   @ViewChild('modalRef', {static: true}) modalRef: ElementRef
   @Input() inModal: boolean = false;
   // @Input() private params: CompetitorAnalysisParams //DO NOT REFERENCE THIS, INSTEAD USE THE VERSION IN SERVICE
   // things for the template to render

   public showFilterMenu = false;
   distances: number[] = [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 70, 80, 90, 100, 200];
   chartXYFields: BubbleChartFields[] = [
      {
         x: "Mileage",
         xAdvert: "OdometerReading",
         xPretty: "Mileage",
         xPipe: "number",
         y: "PricePosition",
         yPretty: "Price Position",
         yPipe: "percent",
      },
      {
         x: "Mileage",
         xAdvert: "OdometerReading",
         xPretty: "Mileage",
         xPipe: "number",
         y: "AdvertisedPrice",
         yPretty: "Price",
         yPipe: "currency",
      },
   ];

   public facetFilters: { Facet: string; Choices: string[] }[] = [];
   public selectedFacetChoices: { [key: string]: string[] } = {};
   public selectedFacetsClone: { [key: string]: string[] } = {};
   public selectedSellerTypes: string[] = [];
   private minPlateClone: number;
   private maxPlateClone: number;
   private radiusClone: number;
   private postcodeClone: string;
   private sellerTypesClone: string[];

   public sellerTypes: { label: string, defaultValue: boolean }[] = [
      {label: "Private", defaultValue: true},
      {label: "Independent", defaultValue: true},
      {label: "Franchise", defaultValue: true},
   ];

   tableFilters: {};
   private sellerTypeChanged = false;

   get amWithinInsightsModal(): boolean {
      return this.service.params.ParentType === "insightsModal";
   }

   // the user choices and states
   selectedRadius: number;
   selectedMinPlate: number;
   selectedMaxPlate: number;
   selectedRetailerSite: RetailerSite;
   plateChoices: number[] = [];

   chartData: BubbleChartFields;
   showBlobs: boolean = false;
   GlobalParamKeys = GlobalParamKey;


   private modalInstance: NgbModalRef;

   constructor(
      public service: CompetitorAnalysisService,
      public getDataService: GetDataMethodsService,
      public selectionsService: SelectionsService,
      public constants: ConstantsService,
      private cphPipe: CphPipe,
      public autoPriceInsightsService: AutoPriceInsightsModalService,
      private changeDetectorRef: ChangeDetectorRef,
      public globalParamsService: GlobalParamsService,
      public modalService: NgbModal
   ) {
   }

   ngOnInit(): void {
      // Only initialize if params are available
      if (!this.service.params) {
         return;
      }

      this.initParams();

      // if(this.service.params.ParentType == 'insightsModal' ){
      //   this.service.service.competitorAnalysisRef = this;
      // }
      this.autoPriceInsightsService.competitorAnalysisRef = this;
      this.getCompetitorAnalysisFacets();
   }

   redrawTable(newData: CompetitorSummary, params: CompetitorAnalysisParams) {
      this.initParams();
      if (this.service.chartRef) {
         this.service.chartRef.dealWithNewData(newData);
      }
      if (this.service.tableRef && newData?.CompetitorVehicles) {
         this.service.tableRef.dealWithNewData(params);
      }
   }

   toggleShowFilterMenu() {

      this.showFilterMenu = !this.showFilterMenu;

      this.changeDetectorRef.detectChanges();

      if (this.service.tableRef) {

         requestAnimationFrame(() => {
            this.service.tableRef.gridApi.sizeColumnsToFit();
         });
      }
   }

   initParams() {
      // Prevent initialization if params not ready
      if (!this.service.params || !this.service.params.CompetitorSummary) {
         return;
      }

      this.chartData = this.chartXYFields[0];
      // this.buildMinMaxPlateYear();
      const firstRegDate = this.service.params.FirstRegisteredDate;
      var retailerId = this.service.params.RetailerSiteIdForPostcode;
      this.selectedRetailerSite = this.constants.RetailerSites.find(x => x.Id == retailerId);

      if (!this.selectedRetailerSite) {
         return;
      }

      this.setupPlateYears(firstRegDate, this.selectedRetailerSite);
      this.setPlatesRange();
      this.setRadius(this.service.params.CompetitorSummary.Radius);
      this.selectedFacetsClone = this.constants.clone(this.selectedFacetChoices);
      this.minPlateClone = this.selectedMinPlate;
      this.maxPlateClone = this.selectedMaxPlate;
      this.radiusClone = this.selectedRadius;
      this.postcodeClone = this.selectedRetailerSite.Postcode;

      this.selectedSellerTypes = this.sellerTypes.filter(x => x.defaultValue == true).map(x => x.label);
      this.sellerTypesClone = [...this.sortedSellerTypes(this.selectedSellerTypes)];
   }

   setRadius(radius: number) {
      this.selectedRadius = radius;
   }

   public selectMinPlate(plate: number) {
      this.selectedMinPlate = plate;
   }

   public selectMaxPlate(plate: number) {
      this.selectedMaxPlate = plate;
   }

   public selectRetailerSite(retailerSite: RetailerSite) {
      this.selectedRetailerSite = retailerSite;

   }

   selectRadius(radius: number) {
      this.selectedRadius = radius;
   }

   getAutoPriceCompetitorAnalysis() {
      if (this.service.params.ParentType == "insightsModal") {
         if (this.service.params.WebSiteSearchIdentifier) {
            this.getCompetitorAnalysisForExistingAd();
         } else {

            this.getCompetitorAdvertCreatedFromDmsStock(
               this.selectedFacetChoices,
               this.facetFilters,
               this.selectedSellerTypes
            );
         }
      } else {
         this.getCompetitorAnalysisForNewValuation();
      }
   }

   getCompetitorAnalysisFacets() {
      const retailerSiteId = this.service.params.RetailerSiteRetailerId;
      const derivativeId = this.service.params.DerivativeId;

      this.getDataService.getCompetitorAnalysisFacets(retailerSiteId, derivativeId).then((filter) => {
         this.facetFilters = filter;

         if (this.facetFilters != null) {
            this.facetFilters.forEach((facet) => {
               // Uppercase the first letter of each facet name
               facet.Facet = facet.Facet.charAt(0).toUpperCase() + facet.Facet.slice(1);
               this.selectedFacetChoices[facet.Facet] = [this.service.params[facet.Facet]];
            });
         }
         this.selectedFacetsClone = this.constants.clone(this.selectedFacetChoices);
      });
   }

   private getCompetitorAnalysisForExistingAd() {
      const userChoiceParams: CompetitorSearchUserChoices = {
         MaxPlate: this.selectedMaxPlate,
         MinPlate: this.selectedMinPlate,
         Radius: this.selectedRadius,
         Postcode: this.selectedRetailerSite.Postcode,

         BodyType: this.selectedFacetChoices.BodyType || [],
         FuelType: this.selectedFacetChoices.FuelType || [],
         TransmissionType: this.selectedFacetChoices.TransmissionType || [],
         Trim: this.selectedFacetChoices.Trim || [],
         Drivetrain: this.selectedFacetChoices.Drivetrain || [],
         Doors: this.selectedFacetChoices.Doors || [],
         Segments: this.selectedSellerTypes,
      };

      const plateYear = utilities.dateToNumberPlate(this.service.params.FirstRegisteredDate);
      const thisAdRetailerSite = this.constants.RetailerSites.find(
         (x) => x.RetailerId === this.service.params.RetailerSiteRetailerId
      );
      const thisAdSite = this.constants.Sites.find((x) => x.SiteId === thisAdRetailerSite.Site_Id);

      const ourVehicleParams: CompetitorSearchOurVehicle = {
         VehicleReg: this.service.params.VehicleReg,
         VehicleMileage: this.service.params.OdometerReading,
         VehicleYear: plateYear,
         AdvertisedPrice: this.service.params.AdvertisedPrice,
         PricePosition: this.service.params.PricePosition,
         Valuation: this.service.params.Valuation,
         Postcode: thisAdRetailerSite.Postcode,
         SiteName: thisAdRetailerSite.Name,
         SiteLatitude: thisAdSite.Latitude,
         SiteLongitude: thisAdSite.Longitude,
         ImageURL: this.service.params.ImageURL,
      };

      this.selectionsService.triggerSpinner.emit({show: true, message: "Loading..."});

      this.getDataService.getAutoPriceCompetitorAnalysis(
         this.service.params.AdvertId,
         this.service.params.WebSiteSearchIdentifier,
         this.service.params.RetailerSiteRetailerId,
         userChoiceParams,
         ourVehicleParams,
         this.selectedRetailerSite.Postcode
      )
         .subscribe(
            (res: CompetitorSummary) => {
               this.dealWithNewData(res);
            },
            (error) => {
               console.error("Failed to load modal data", error);
            },
            () => {
               this.selectionsService.triggerSpinner.emit({show: false});
            }
         );
   }

   private getCompetitorAnalysisForNewValuation() {
      const ourVehicleParams: OurVehicleParams = {
         AdvertisedPrice: this.service.params.AdvertisedPrice,
         Valuation: this.service.params.Valuation,

         VehicleReg: this.service.params.VehicleReg,
         Mileage: this.service.params.OdometerReading,
         FirstRegisteredYear: this.service.params.FirstRegisteredDate.getFullYear(),
      };
      const retailerSite = this.constants.RetailerSites.find(
         (x) => x.RetailerId == this.service.params.RetailerSiteRetailerId
      );
      const searchParams: CompetitorSearchParams = {
         standardMake: this.service.params.Make,
         standardModel: this.service.params.Model,
         standardTrim: this.service.params.Trim,
         standardTransmissionType: this.service.params.TransmissionType,
         standardFuelType: this.service.params.FuelType,
         standardBodyType: this.service.params.BodyType,
         standardDrivetrain: this.service.params.Drivetrain,
         doors: this.service.params.Doors.toString(),
         advertiserId: this.service.params.RetailerSiteRetailerId,
         radius: this.selectedRadius,
         minPlate: this.selectedMinPlate,
         maxPlate: this.selectedMaxPlate,
         postcode: this.selectedRetailerSite.Postcode,
         selfRegToExclude: this.service.params.VehicleReg,
         segments: this.selectedSellerTypes,
      };

      let parms: GetValuationModalCompetitorAnalysisParams = {
         SearchParams: searchParams,
         OurVehicleParams: ourVehicleParams,
      };

      this.getDataService.getAutoPriceCompetitorAnalysisForNewValuation(parms).subscribe(
         (res: CompetitorSummary) => {
            this.dealWithNewData(res);
         },
         (error) => {
            console.error("Failed to load modal data", error);
         }
      );
   }

   private getCompetitorAdvertCreatedFromDmsStock(
      selectedFacets: { [key: string]: string[] },
      facetFilters: {
         Facet: string; Choices: string[]

      }[],
      selectedSellerTypes: string[]
   ) {

      let ppToUse = this.service.params.PricePosition;
      if (ppToUse == 0) {
         ppToUse = this.constants.div(
            this.service.params.AdvertDetail.StrategyPrice,
            this.service.params.AdvertDetail.ValuationAdjRetail
         );
      }
      let sellingPrice = this.service.params.AdvertisedPrice;
      if (sellingPrice == 0) {
         sellingPrice = Math.round(this.service.params.AdvertDetail.StrategyPrice);
      }

      const ourVehicleParams: OurVehicleParams = {
         AdvertisedPrice: this.service.params.AdvertisedPrice,
         Valuation: this.service.params.Valuation,

         VehicleReg: this.service.params.VehicleReg,
         Mileage: this.service.params.OdometerReading,
         FirstRegisteredYear: this.service.params.FirstRegisteredDate.getFullYear(),
      };
      const retailerSite = this.constants.RetailerSites.find(
         (x) => x.RetailerId == this.service.params.RetailerSiteRetailerId
      );

      const transmissionChoice: string = this.workoutChoiceValue(selectedFacets, facetFilters, 'TransmissionType');
      const fuelTypeChoice: string = this.workoutChoiceValue(selectedFacets, facetFilters, 'FuelType');
      const drivetrainChoice: string = this.workoutChoiceValue(selectedFacets, facetFilters, 'Drivetrain');
      const bodyTypeChoice: string = this.workoutChoiceValue(selectedFacets, facetFilters, 'Bodytype');
      const doorsChoice: string = this.workoutChoiceValue(selectedFacets, facetFilters, 'Doors');
      const trimChoice: string = this.workoutChoiceValue(selectedFacets, facetFilters, 'Trim');


      const searchParams: CompetitorSearchParams = {
         standardMake: this.service.params.Make,
         standardModel: this.service.params.Model,

         standardTransmissionType: transmissionChoice,
         standardFuelType: fuelTypeChoice,
         standardBodyType: bodyTypeChoice,
         standardDrivetrain: drivetrainChoice,
         doors: doorsChoice,
         standardTrim: trimChoice,

         advertiserId: this.service.params.RetailerSiteRetailerId,
         radius: this.selectedRadius,
         postcode: this.selectedRetailerSite.Postcode,
         minPlate: this.selectedMinPlate,
         maxPlate: this.selectedMaxPlate,
         segments: selectedSellerTypes,
         selfRegToExclude: this.service.params.VehicleReg,
      };

      let parms: GetValuationModalCompetitorAnalysisParams = {
         SearchParams: searchParams,
         OurVehicleParams: ourVehicleParams,
      };
      this.selectionsService.triggerSpinner.emit({show: true, message: 'Loading competitor adverts..'})
      this.getDataService.getAutoPriceCompetitorAnalysisForNewValuation(parms).subscribe(
         (res: CompetitorSummary) => {
            this.selectionsService.triggerSpinner.emit({show: false})
            this.dealWithNewData(res);
         },
         (error) => {
            this.selectionsService.triggerSpinner.emit({show: false})
            console.error("Failed to load modal data", error);
         }
      );
   }

   private workoutChoiceValue(selectedFacets: { [key: string]: string[]; }, facetFilters: {
      Facet: string;
      Choices: string[];
   }[], facetName: string) {

      if (!selectedFacets[facetName]) {
         return null;
      }
      if (selectedFacets[facetName].length == 0) {
         //chosen none, show all
         return null;
      } else if (selectedFacets[facetName].length == facetFilters.find(x => x.Facet == facetName).Choices.length) {
         //chosen all, show all
         return null;
      } else {
         //chosen some or 1, choose first (annoyingly can't choose some from the api)
         return selectedFacets[facetName][0];
      }
   }

   private dealWithNewData(res: CompetitorSummary) {
      this.selectionsService.triggerSpinner.emit({show: false});

      this.service.params.CompetitorSummary = res;

      this.selectedFacetsClone = this.constants.clone(this.selectedFacetChoices);
      this.minPlateClone = this.selectedMinPlate;
      this.maxPlateClone = this.selectedMaxPlate;
      this.radiusClone = this.selectedRadius;
      this.postcodeClone = this.selectedRetailerSite.Postcode;
      this.sellerTypesClone = [...this.selectedSellerTypes];
      this.sellerTypeChanged = false;

      if (this.service.params.VehicleValuationService) {
         this.service.params.VehicleValuationService.valuationModalResultNew.CompetitorCheckResult = res;
      }

      if (this.service.tableRef) {
         this.service.tableRef.dealWithNewData(this.service.params);
      }
      if (this.service.chartRef) {
         this.service.chartRef.dealWithNewData(this.service.params.CompetitorSummary);
      }
   }

   get choiceMade(): boolean {
      //here we will compare the properties
      return (
         this.haveFacetsChanged(this.selectedFacetsClone, this.selectedFacetChoices) ||
         this.minPlateClone != this.selectedMinPlate ||
         this.maxPlateClone != this.selectedMaxPlate ||
         this.radiusClone != this.selectedRadius ||
         this.postcodeClone != this.selectedRetailerSite.Postcode ||
         this.sellerTypeChanged
      );
   }

   public haveFacetsChanged(original, clone): boolean {
      const keysA = Object.keys(original);
      const keysB = Object.keys(clone);

      // First, check if the keys are different
      if (keysA.length !== keysB.length) return true;

      for (const key of keysA) {
         if (!keysB.includes(key)) return true;

         const a = original[key];
         const b = clone[key];

         // Compare arrays by length and elements (unordered comparison)
         if (a.length !== b.length) return true;

         const aSorted = [...a].sort();
         const bSorted = [...b].sort();

         for (let i = 0; i < aSorted.length; i++) {
            if (aSorted[i] !== bSorted[i]) return true;
         }
      }

      return false; // No differences found
   }

   get getDistanceLabel() {
      if (this.selectedRadius === 1000) return "National";
      return `${this.selectedRadius} ${this.selectedRadius === 1 ? "mile" : "miles"}`;
   }

   showChartView(xyFields: BubbleChartFields) {
      this.chartData = xyFields;
      this.showBlobs = true;

      if (this.service.chartRef) {
         this.service.chartRef.selectedXYFields = xyFields;
         this.service.chartRef.dealWithNewData(this.service.params.CompetitorSummary);
      }
   }

   showActiveChartButton(xyFields: BubbleChartFields) {
      return this.chartData.x === xyFields.x && this.chartData.y === xyFields.y;
   }

   setupPlateYears(vehicleFirstRegDate: Date, retailerSite: RetailerSite) {
      const minMax = this.getMinMaxPlateNew(vehicleFirstRegDate, retailerSite.CompetitorPlateRange);
      this.selectedMinPlate = minMax.minPlate;
      this.selectedMaxPlate = minMax.maxPlate;

   }

   getMinMaxPlateNew(firstRegd: Date, plateRange: number): { minPlate: number | null; maxPlate: number | null } {
      const plateYear: number = this.getPlateFromFirstReg(firstRegd);
      const existingIsSep = plateYear > 50;
      const year = existingIsSep ? plateYear - 50 : plateYear;
      const forwardBackEven = plateRange % 2 === 0;
      const halfRange = Math.floor(plateRange / 2);

      let min: number;
      let max: number;

      if (forwardBackEven) {
         // Even range
         min = plateYear - halfRange;
         max = plateYear + halfRange;
      } else {
         // Odd range
         if (existingIsSep) {
            min = plateYear - (plateRange - 1 + 50);
            max = plateYear + (plateRange - 50);
         } else {
            min = year - (plateRange - 50);
            max = year + (plateRange - 1 + 50);
         }
      }

      return {minPlate: min, maxPlate: max};
   }

   getPlateFromFirstReg(firstRegisteredDate: Date | null): number {
      if (!firstRegisteredDate) {
         throw new Error("firstRegisteredDate is required");
      }

      const year = firstRegisteredDate.getFullYear();
      const month = firstRegisteredDate.getMonth() + 1; // getMonth returns 0-11, so add 1 for 1-12

      if (month >= 9) {
         return year - 1950; // e.g., 2021 gives us 71
      } else if (month >= 3) {
         return year - 2000; // e.g., 2021 gives us 21
      } else {
         return year - 1951; // e.g., 2021 gives us 70
      }
   }

   nextLowerPlate(currentPlate: number) {
      if (currentPlate > 50) {
         //we're on a september plate.
         return currentPlate - 50;
      } else {
         return currentPlate - 1 + 50;
      }
   }

   nextHigherPlate(currentPlate: number) {
      if (currentPlate > 50) {
         //we're on a september plate.
         return currentPlate - 50 + 1;
      } else {
         return currentPlate + 50;
      }
   }

   setPlatesRange() {
      let results: number[] = [];

      const highestAllowablePlate: number = this.getHighestAllowablePlate();

      let currentPlate = this.getPlateFromFirstReg(this.service.params.FirstRegisteredDate);

      let lowPlates = [];
      for (let i = 0; i < 6; i++) {
         //step down
         currentPlate = this.nextLowerPlate(currentPlate);
         lowPlates.push(currentPlate);
      }
      lowPlates.reverse();
      results = results.concat(lowPlates);
      let highestPlate = results[results.length - 1];
      for (let i = 0; i < 7; i++) {
         //step up
         highestPlate = this.nextHigherPlate(highestPlate);
         results.push(highestPlate);
         if (highestPlate === highestAllowablePlate) {
            break;
         }
      }

      this.plateChoices = results;
   }

   getHighestAllowablePlate(): number {
      let date: Date = new Date();
      let year: number = date.getFullYear();
      let month: number = date.getMonth() + 1;

      let plateNumber: number = year - 2000;
      if (month >= 9) {
         plateNumber += 50;
      }

      return plateNumber;
   }

   get facetFiltersWithMultipleOptions() {
      if (!this.facetFilters) {
         return [];
      }
      return this.facetFilters.filter((x) => x.Choices.length > 1);
   }

   public getHeight() {
      if (this.service.params.ParentType == "insightsModal") {
         return null;
      }
      const height = 1000;
      return `${height}px`;
   }

   showBlobsAndTableTogether() {
      return this.service.params.ParentType == "valuationModal";
   }

   scrollToThisVehicle() {
      const gridApi = this.service.tableRef.gridApi;

      gridApi.forEachNodeAfterFilterAndSort((node, index) => {
         if (node.data.IsOurVehicle === true) {
            gridApi.ensureIndexVisible(index, "top");
         }
      });
   }

   selectFacet(facet: string, choice: string) {
      if (this.selectedFacetChoices[facet]) {
         if (this.selectedFacetChoices[facet].includes(choice)) {
            this.selectedFacetChoices[facet] = this.selectedFacetChoices[facet].filter((x) => x !== choice);
         } else {
            this.selectedFacetChoices[facet].push(choice);
         }
      } else {
         this.selectedFacetChoices[facet] = [choice];
      }
   }

   openInModal() {
      this.modalInstance = this.modalService
         .open(this.modalRef, {
            keyboard: false,
            ariaLabelledBy: "modal-basic-title",
            size: "lg",
         })
   }

   closeModal() {
      // Close
      this.modalInstance.close();
   }

   toggleSellerType(label: string) {
      if (this.selectedSellerTypes.includes(label)) {
         this.selectedSellerTypes = this.selectedSellerTypes.filter((x) => x !== label);
      } else {
         this.selectedSellerTypes.push(label);
      }

      this.sellerTypeChanged = JSON.stringify(this.sortedSellerTypes(this.selectedSellerTypes)) !== JSON.stringify(this.sortedSellerTypes(this.sellerTypesClone));
   }

   private sortedSellerTypes(selectedSellerTypes: string[]) {
      return selectedSellerTypes.sort((a, b) => a.localeCompare(b));
   }
}
