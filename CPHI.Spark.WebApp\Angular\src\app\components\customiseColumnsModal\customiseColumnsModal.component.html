<div class="modal-header">
   <div class="d-flex w-100">
      <div class="flex-grow-1">
         <h4 class="modal-title" id="modal-basic-title">
            Customise Columns
         </h4>
      </div>
      <div class="flex-shrink-1">
         <button type="button" class="close" aria-label="Close" (click)="onCancelButtonClick()">
            <span aria-hidden="true">&times;</span>
         </button>
      </div>
   </div>
</div>

<div [ngClass]="constants.environment.customer" class="modal-body">
   <div id="columnsContainer">


      <!-- The selected columns -->
      <div class="autotraderCard">
         <div class="tile-inner">

            <!-- Header -->
            <div class="tile-header w-100">
               <div class="d-flex justify-content-between w-100">
                  <div>Selected Columns</div>
                  <button class="deleteColumn" ngbPopover="Remove all columns" triggers="mouseenter:mouseleave"
                          (click)="removeAllSelectedColumns()">
                     <i class="fas fa-trash"></i>
                  </button>
               </div>
            </div>

            <!-- Body -->
            <div class="tile-body">
               <div id="selectedColumns">
                  <div cdkDropList id="selectedList" [cdkDropListData]="selectedColDefs" class="item-list"
                       [cdkDropListOrientation]="'horizontal'" #selected="cdkDropList"
                       (cdkDropListDropped)="drop($event)">

                     <li *ngFor="let column of selectedColDefs" class="item-box" cdkDrag [cdkDragData]="column"
                         (cdkDragStarted)="dragStarted($event, 'selectedList')"
                         (cdkDragReleased)="dragReleased($event)">

                        <div class="w-100">

                           <div class="text-center">
                              {{ column.headerName }}
                           </div>

                           <div class="text-center">
                              <newHint [colDef]="column"></newHint>
                           </div>
                        </div>

                        <button class="deleteColumn individualButtonDelete"
                                (click)="removeSelectedColumn(column.colId); $event.stopPropagation()">
                           <i class="fas fa-times"></i>
                        </button>
                     </li>
                  </div>
               </div>
            </div>
         </div>
      </div>

      <!-- Available columns -->
      <div class="">

         <div class="mb-2 position-relative d-inline-block">
            <input type="text" class="form-control d-inline-block" placeholder="Search columns"
                   [(ngModel)]="columnSearchText">
            <div class="position-absolute" style="top: 8px; right: 10px; z-index: 999; cursor: pointer"
                 (click)="columnSearchText=''">
               <i class="fas fa-times"></i>
            </div>
         </div>

         <!-- overall container -->
         <div id="allColumnsContainer">

            <div *ngFor="let section of filteredColumnSections" class="section drag-container">


               <div class="sectionHeader">
                  {{ section.columnSection }}
               </div>


               <div cdkDropList id="{{section.columnSection}}" [cdkDropListData]="section.items"
                    cdkDropListSortingDisabled
                    [cdkDropListConnectedTo]="selected" class="item-list sectionButtonHolder tile-body"
                    (cdkDropListDropped)="drop($event)">
                  <div class="item-box" *ngFor="let column of section.items" cdkDrag [cdkDragData]="column"
                       (cdkDragStarted)="dragStarted($event, section.columnSection)"
                       (dblclick)="addSelectedColumn(column.colId, section.columnSection)"
                       [ngClass]="{ 'selected': columnIsSelected(column),'hasHint': columnHasHint(column) }">

                     <div class="me-1">
                        {{ column.headerName }}
                     </div>
                     <newHint [position]="'left'" [colDef]="column"></newHint>
                  </div>
               </div>


            </div>


         </div>
      </div>


   </div>
</div>

<div class="modal-footer">
   <button type="button" class="btn btn-success" (click)="onOKButtonClick()">OK</button>
   <button type="button" class="btn btn-primary" (click)="onCancelButtonClick()">Close</button>
</div>
