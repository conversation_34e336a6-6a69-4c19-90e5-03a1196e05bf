import { CdkDragDrop, CdkDragRelease, CdkDragStart, copyArrayItem, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { AfterViewInit, Component } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { GridApi } from 'ag-grid-community';
import { ColDefCph } from 'src/app/model/ColDefCPH';
import { CPHAutoPriceColDef, CPHColDef } from 'src/app/model/CPHColDef';
import { SelectionsService } from 'src/app/services/selections.service';
import { ConstantsService } from '../../services/constants.service';

@Component({
  selector: 'customiseColumnsModal',
  templateUrl: './customiseColumnsModal.component.html',
  styleUrls: ['./customiseColumnsModal.component.scss']
})

export class CustomiseColumnsModalComponent implements AfterViewInit {
  gridApi: GridApi;
  columnSections: { columnSection: string, items: CPHAutoPriceColDef[] }[];
  selectedColDefs: CPHAutoPriceColDef[];
  test = ['Advert']
  draggingFromList: string;
  public sectionOrdering: string[];
  originalColDefs: CPHAutoPriceColDef[];
  columnSearchText: string = "";

  constructor(
    public constants: ConstantsService,
    public modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public selections: SelectionsService
  ) { }

  ngAfterViewInit(): void {
    const columnDefs: CPHAutoPriceColDef[] = this.originalColDefs.filter(x => this.sectionOrdering.includes(x.columnSection));
    console.log(columnDefs)
    this.selectedColDefs = (this.gridApi.getColumnDefs() as CPHAutoPriceColDef[]).filter(x => !x.hide && x.columnSection)//.sort((a, b) => a.columnSection.localeCompare(b.columnSection) || a.headerName.localeCompare(b.headerName));
    this.columnSections = this.groupByColumnSection(columnDefs.filter(x => x.columnSection).sort((a, b) => {
      return this.sectionOrdering.indexOf(a.columnSection) - this.sectionOrdering.indexOf(b.columnSection);
    })) as { columnSection: string, items: CPHAutoPriceColDef[] }[];


  }

  groupByColumnSection(data: ColDefCph[]) {
    const groupedData = {};

    data.forEach(item => {
      const columnSection = item.columnSection;
      if (!groupedData[columnSection]) {
        groupedData[columnSection] = { columnSection, items: [] };
      }
      groupedData[columnSection].items.push(item);
    });

    return Object.values(groupedData);
  }

  onOKButtonClick() {
    this.activeModal.close(this.selectedColDefs);
  }



  onCancelButtonClick() {
    this.activeModal.dismiss();
  }


  drop(event: CdkDragDrop<any[]>) {
    const droppedCol: CPHColDef = event.item.data;


    //  console.log(event)
    if (event.distance.y > 150) {
      this.removeSelectedColumn(droppedCol.colId)
    }



    if (event.previousContainer.id === "selectedList" && event.container.id !== "selectedList") {
      //dragging out of selected, so intend to remove
      this.removeSelectedColumn(droppedCol.colId)
      return;
    }

    //dropping not within selected list
    if (event.previousContainer.id === "selectedList") {
      //we are droppin
    }


    if (event.previousContainer.id === event.container.id) { //&& event.container.id === "selectedList"
      // Reorder items within the same list
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      // Move items between lists
      copyArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
  }

  public removeSelectedColumn(colId: string) {
    this.selectedColDefs = this.selectedColDefs.filter(x => x.colId !== colId);
  }

  dragStarted(event: CdkDragStart<any>, listId: string): void {
    this.draggingFromList = listId;
    //console.log(event)
  }

  dragReleased(event: CdkDragRelease): void {
    if (this.draggingFromList === 'selectedList') {
      console.log('Item dragged from selected list was released');
    }
    // Reset the dragging state
    this.draggingFromList = null;
  }


  removeAllSelectedColumns() {
    this.selectedColDefs = [];
  }



  columnIsSelected(column: CPHColDef) {
    if (this.selectedColDefs.map(x => x.colId).includes(column.colId)) {
      return true;
    }
  }


  columnHasHint(column:CPHAutoPriceColDef):boolean{
    return  !!column.hasExplanation // && column.explanation(column).length>0
    // if(!!column.explanation){
    //   const hint = column.explanation(column);
    //   return hint.length > 0;
    // }
  }

  public addSelectedColumn(colId: any, sectionName: string) {
    const selectedColDef: CPHAutoPriceColDef = this.columnSections.find(x => x.columnSection === sectionName).items.find(x => x.colId === colId);
    this.selectedColDefs.push(selectedColDef);
    //this.selectedColDefs = this.selectedColDefs.filter(x => x.colId !== colId);
  }

  get filteredColumnSections() {
    if (!this.columnSearchText || this.columnSearchText.trim() === '') {
      return this.columnSections;
    }

    const searchLower = this.columnSearchText.toLowerCase();
    return this.columnSections
      .map(section => ({
        ...section,
        items: section.items.filter(column =>
          column.headerName.toLowerCase().includes(searchLower)
        )
      }))
      .filter(section => section.items.length > 0);
  }
}
