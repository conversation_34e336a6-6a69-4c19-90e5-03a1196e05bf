<div class="modal-header">
   <h4 class="modal-title">Choose New Factor</h4>
   <button class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
   </button>
</div>

<div class="modal-body pt-0">

   <div class="d-flex justify-content-between align-items-center mb-3">
      <ul class="nav nav-tabs flex-grow-1 mb-0">
         <li class="nav-item" *ngFor="let tab of tabs">
            <a class="nav-link"
               [class.active]="activeTab === tab"
               (click)="setActiveTab(tab)"
               style="cursor: pointer;">
               {{tab}}
               <span class="badge badge-secondary ms-1">
                  {{categorizedFactors[tab]?.length || 0}}
               </span>
            </a>
         </li>
      </ul>
      <div class="search-container ms-3">
         <div class="input-group">
            <input type="text"
                   class="form-control"
                   placeholder="Search factors..."
                   [(ngModel)]="searchTerm"
                   (ngModelChange)="onSearchChange()">
            <button class="btn btn-outline-secondary"
                    type="button"
                    (click)="clearSearch()">
               <i class="fas fa-times"></i>
            </button>
         </div>
      </div>
   </div>

   <div class="newFactorTable">

      <!-- Show search results grouped by category when searching -->
      <div *ngIf="searchTerm && !activeTab">
         <div *ngIf="getTotalSearchResults() === 0" class="text-center p-4 text-muted">
            <i class="fas fa-search fa-2x mb-2"></i>
            <p>No factors found matching "{{searchTerm}}"</p>
         </div>
         <div *ngFor="let tab of tabs">
            <div *ngIf="filteredCategorizedFactors[tab]?.length > 0">
               <div class="text-muted mt-2 mb-2 tab-section-header">{{tab}}</div>
               <div class="d-flex align-items-center justify-content-between newFactorRow"
                    *ngFor="let factor of filteredCategorizedFactors[tab]">
                  <div class="icon ">
                     <i *ngIf="factor.Name == StrategyFactorName.RR_DL_Matrix" class="fas fa-grid"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RR_DS_Matrix" class="fas fa-grid"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RR_DB_Matrix" class="fas fa-grid"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DTS_DL_Matrix" class="fas fa-grid"></i>
            <i *ngIf="factor.Name == StrategyFactorName.PY_DS_Matrix" class="fas fa-grid"></i>
            <i *ngIf="factor.Name == StrategyFactorName.VB_DS_Matrix" class="fas fa-grid"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RetailRatingBand" class="fas fa-tachometer-average"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RetailRating" class="fas fa-tachometer-alt"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DaysListedBand" class="fas fa-calendar-day"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DaysListed" class="fas fa-calendar-day"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DaysInStockBand" class="fas fa-calendar-day"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DaysInStock" class="fas fa-calendar-day"></i>
            <i *ngIf="factor.Name == StrategyFactorName.OnBrandCheck" class="fas fa-shield-check"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RetailerName" class="fas fa-map-marker"></i>
            <i *ngIf="factor.Name == StrategyFactorName.Brand" class="fas fa-car"></i>
            <i *ngIf="factor.Name == StrategyFactorName.ModelName" class="fas fa-car"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MatchCheapestCompetitor" class="fas fa-ranking-star"></i>
            <i *ngIf="factor.Name == StrategyFactorName.AchieveMarketPositionScore" class="fas fa-ranking-star"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DaysToSell" class="fas fa-calendar-exclamation"></i>
            <i *ngIf="factor.Name == StrategyFactorName.ValuationChangeUntilSell"               class="fas fa-calendar-exclamation"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DailyValuationChange"               class="fas fa-calendar-exclamation"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MinimumProfit" class="fas fa-sterling-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MinimumPricePosition" class="fas fa-percentage"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MaximumPricePosition" class="fas fa-percentage"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RoundToNearestEnding" class="fas fa-sterling-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RoundUpToNearestEnding" class="fas fa-sterling-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RoundDownToNearestEnding" class="fas fa-sterling-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RoundToNearestMultiple" class="fas fa-sterling-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RoundUpToNearestMultiple" class="fas fa-sterling-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RoundToNearestMultiple" class="fas fa-sterling-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RoundToPriceBreak" class="fas fa-sterling-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.SpecificColour" class="fas fa-palette"></i>
            <i *ngIf="factor.Name == StrategyFactorName.Colour" class="fas fa-palette"></i>
            <i *ngIf="factor.Name == StrategyFactorName.AgeAndOwners" class="fas fa-users"></i>
            <i *ngIf="factor.Name == StrategyFactorName.WholesaleAdjustment" class="fa fa-circle-sterling"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RetailRating10sBand" class="fa fa-tachometer-average"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MakeFuelType" class="fa fa-gas-pump"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MakeModel" class="fa fa-car"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MakeAgeBand" class="fa fa-clock-three"></i>
            <i *ngIf="factor.Name == StrategyFactorName.Mileage" class="fa fa-tachometer-average"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MilesPerYear" class="fa fa-tachometer-average"></i>
            <i *ngIf="factor.Name == StrategyFactorName.PerformanceRatingScore" class="fas fa-eyes"></i>
            <i *ngIf="factor.Name == StrategyFactorName.FuelType" class="fa fa-gas-pump"></i>
            <i *ngIf="factor.Name == StrategyFactorName.ValueBand" class="fa fa-pound-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RegYear" class="fa fa-calendar-alt"></i>
            <i *ngIf="factor.Name == StrategyFactorName.LiveMarketCondition" class="fa fa-chart-line"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DateRange" class="fa fa-calendar"></i>
            <i *ngIf="factor.Name == StrategyFactorName.LeavingData" class="fa fa-chart-bar"></i>
            <i *ngIf="factor.Name == StrategyFactorName.SetToAdvertisedPrice" class="fas fa-pound-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.Tag" class="fas fa-tag"></i>
            <i *ngIf="factor.Name == StrategyFactorName.ApplyTagAdjustments" class="fas fa-tags"></i>
                     <i *ngIf="factor.Name == StrategyFactorName.TrackMarketPosition" class="fas fa-pound-sign"></i>
                  </div>
                  <div class="name">
                     {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                  </div>
                  <div class="explanation">
                     <div class="newFactorExplanation">{{ service.factorExplanation(factor) }}</div>
                  </div>
                  <div class="chooseFactor">
                     <button class="btn btn-success" (click)="chooseNewFactor(factor)">Add factor</button>
                  </div>
               </div>
            </div>
         </div>
      </div>

      <!-- Show normal tab view when not searching -->
      <div *ngIf="!searchTerm || activeTab">
         <div *ngIf="activeTab && filteredCategorizedFactors[activeTab]?.length === 0" class="text-center p-4 text-muted">
            <i class="fas fa-search fa-2x mb-2"></i>
            <p>No factors found in this category</p>
         </div>
         <div class="d-flex align-items-center justify-content-between newFactorRow"
              *ngFor="let factor of filteredCategorizedFactors[activeTab]">
            <div class="icon ">
               <i *ngIf="factor.Name == StrategyFactorName.RR_DL_Matrix" class="fas fa-grid"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RR_DS_Matrix" class="fas fa-grid"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RR_DB_Matrix" class="fas fa-grid"></i>
               <i *ngIf="factor.Name == StrategyFactorName.DTS_DL_Matrix" class="fas fa-grid"></i>
               <i *ngIf="factor.Name == StrategyFactorName.PY_DS_Matrix" class="fas fa-grid"></i>
               <i *ngIf="factor.Name == StrategyFactorName.VB_DS_Matrix" class="fas fa-grid"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RetailRatingBand" class="fas fa-tachometer-average"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RetailRating" class="fas fa-tachometer-alt"></i>
               <i *ngIf="factor.Name == StrategyFactorName.DaysListedBand" class="fas fa-calendar-day"></i>
               <i *ngIf="factor.Name == StrategyFactorName.DaysListed" class="fas fa-calendar-day"></i>
               <i *ngIf="factor.Name == StrategyFactorName.DaysInStockBand" class="fas fa-calendar-day"></i>
               <i *ngIf="factor.Name == StrategyFactorName.DaysInStock" class="fas fa-calendar-day"></i>
               <i *ngIf="factor.Name == StrategyFactorName.OnBrandCheck" class="fas fa-shield-check"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RetailerName" class="fas fa-map-marker"></i>
               <i *ngIf="factor.Name == StrategyFactorName.Brand" class="fas fa-car"></i>
               <i *ngIf="factor.Name == StrategyFactorName.ModelName" class="fas fa-car"></i>
               <i *ngIf="factor.Name == StrategyFactorName.MatchCheapestCompetitor" class="fas fa-ranking-star"></i>
               <i *ngIf="factor.Name == StrategyFactorName.AchieveMarketPositionScore" class="fas fa-ranking-star"></i>
               <i *ngIf="factor.Name == StrategyFactorName.DaysToSell" class="fas fa-calendar-exclamation"></i>
               <i *ngIf="factor.Name == StrategyFactorName.ValuationChangeUntilSell" class="fas fa-calendar-exclamation"></i>
               <i *ngIf="factor.Name == StrategyFactorName.DailyValuationChange" class="fas fa-calendar-exclamation"></i>
               <i *ngIf="factor.Name == StrategyFactorName.MinimumProfit" class="fas fa-sterling-sign"></i>
               <i *ngIf="factor.Name == StrategyFactorName.MinimumPricePosition" class="fas fa-percentage"></i>
               <i *ngIf="factor.Name == StrategyFactorName.MaximumPricePosition" class="fas fa-percentage"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RoundToNearestEnding" class="fas fa-sterling-sign"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RoundUpToNearestEnding" class="fas fa-sterling-sign"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RoundDownToNearestEnding" class="fas fa-sterling-sign"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RoundToNearestMultiple" class="fas fa-sterling-sign"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RoundUpToNearestMultiple" class="fas fa-sterling-sign"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RoundDownToNearestMultiple" class="fas fa-sterling-sign"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RoundToPriceBreak" class="fas fa-sterling-sign"></i>
               <i *ngIf="factor.Name == StrategyFactorName.SpecificColour" class="fas fa-palette"></i>
               <i *ngIf="factor.Name == StrategyFactorName.Colour" class="fas fa-palette"></i>
               <i *ngIf="factor.Name == StrategyFactorName.AgeAndOwners" class="fas fa-users"></i>
               <i *ngIf="factor.Name == StrategyFactorName.WholesaleAdjustment" class="fa fa-circle-sterling"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RetailRating10sBand" class="fa fa-tachometer-average"></i>
               <i *ngIf="factor.Name == StrategyFactorName.MakeFuelType" class="fa fa-gas-pump"></i>
               <i *ngIf="factor.Name == StrategyFactorName.MakeModel" class="fa fa-car"></i>
               <i *ngIf="factor.Name == StrategyFactorName.MakeAgeBand" class="fa fa-clock-three"></i>
               <i *ngIf="factor.Name == StrategyFactorName.Mileage" class="fa fa-tachometer-average"></i>
               <i *ngIf="factor.Name == StrategyFactorName.MilesPerYear" class="fa fa-tachometer-average"></i>
               <i *ngIf="factor.Name == StrategyFactorName.PerformanceRatingScore" class="fas fa-eyes"></i>
               <i *ngIf="factor.Name == StrategyFactorName.FuelType" class="fa fa-gas-pump"></i>
               <i *ngIf="factor.Name == StrategyFactorName.ValueBand" class="fa fa-pound-sign"></i>
               <i *ngIf="factor.Name == StrategyFactorName.RegYear" class="fa fa-calendar-alt"></i>
               <i *ngIf="factor.Name == StrategyFactorName.LiveMarketCondition" class="fa fa-chart-line"></i>
               <i *ngIf="factor.Name == StrategyFactorName.DateRange" class="fa fa-calendar"></i>
               <i *ngIf="factor.Name == StrategyFactorName.LeavingData" class="fa fa-chart-bar"></i>
               <i *ngIf="factor.Name == StrategyFactorName.SetToAdvertisedPrice" class="fas fa-pound-sign"></i>
               <i *ngIf="factor.Name == StrategyFactorName.TrackMarketPosition" class="fas fa-pound-sign"></i>
               <i *ngIf="factor.Name == StrategyFactorName.ApplyTagAdjustments" class="fas fa-tags"></i>
               <i *ngIf="factor.Name == StrategyFactorName.Tag" class="fas fa-tag"></i>
            </div>
            <div class="name">
               {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
            </div>
            <div class="explanation">
               <div class="newFactorExplanation">{{ service.factorExplanation(factor) }}</div>
            </div>
            <div class="chooseFactor">
               <button class="btn btn-success" (click)="chooseNewFactor(factor)">Add factor</button>
            </div>
         </div>
      </div>

   </div>
<div class="modal-footer">
   <button class="btn btn-primary" (click)="closeModal()">Close</button>
</div>
