<div class="modal-header">
   <h4 class="modal-title" id="modal-basic-title">Pricing Policy Builder</h4>

   <button type="button" class="close" aria-label="Close" (click)="dismissModal(false)">
      <span aria-hidden="true">&times;</span>
   </button>
</div>
<div class="modal-body" [ngClass]="constants.environment.customer">
   <div class="autotraderCard">
      <div class="cardBody">
         <table id="topTable">
            <tbody>
            <tr>
               <td>
                  <label for="factorId">Policy Id:</label>
               </td>
               <td>
                  <div id="factorId">{{ service.chosenPolicy?.Id }}</div>
               </td>
            </tr>
            <tr>
               <td>
                  <label for="factorName">Policy Name:</label>
               </td>
               <td>
                  <input id="factorName" [(ngModel)]="service.chosenPolicy.Name"/>
               </td>
            </tr>
            <tr>
               <td>
                  <label for="factorComment">Comment:</label>
               </td>
               <td>
                  <input id="factorComment" [(ngModel)]="service.chosenPolicy.Comment"/>
               </td>
            </tr>
            </tbody>
         </table>
      </div>
   </div>

   <instructionRow [message]="instructionMessage"></instructionRow>
   <instructionRow [message]="newStategyInstructionMessage" *ngIf="service.chosenPolicy?.StrategyFactors?.length == 0">
   </instructionRow>

   <!-- The Baseline panel -->
   <div class="panel autotraderCard">
      <div class="cardInner">
         <div class="cardHeader">
            <div class="layerHeader d-flex justify-content-between align-items-center">
               <div class="h2 layerIcon">
                  <i class="fas fa-circle-sterling"></i>
               </div>
               <div class="factorName">Retail Valuation Baseline</div>
               <div class="layerButton"></div>
            </div>
         </div>
         <div class="cardBody"></div>
      </div>
   </div>

   <form #pricingPolicyForm="ngForm">
      <div ngModelGroup="pricingPolicyGroup" #pricingPolicyGroupRef="ngModelGroup">
         <!-- Each of the chosen factors -->
         <ng-container *ngFor="let factor of service.chosenPolicy?.StrategyFactors; let factorIndex = index">
            <div class="panel autotraderCard">
               <div class="factorWorkings">
                  <!-- RetailRatingBand -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.RetailRatingBand">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-tachometer-average"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>

                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>

                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: add 1% above the valuation</td>
                                 <td>101</td>
                                 <td></td>
                              </tr>

                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>{{ factorItem.Label }}</td>
                                    <td>
                                       <input
                                          required
                                          name="RetailRatingBand-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>
                        </div>
                     </div>
                  </div>

                  <!-- DaysInStockBand -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.DaysInStockBand">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-tachometer-average"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>

                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>

                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: add 1% above the valuation</td>
                                 <td>101</td>
                                 <td></td>
                              </tr>

                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>{{ factorItem.Label }}</td>
                                    <td>
                                       <input
                                          required
                                          name="RetailRatingBand-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>


                        </div>
                     </div>
                  </div>

                  <!-- RetailRating10sBand -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.RetailRating10sBand">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-tachometer-average"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>

                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>

                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: add 1% above the valuation</td>
                                 <td>101</td>
                                 <td></td>
                              </tr>

                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>{{ factorItem.Label }}</td>
                                    <td>
                                       <input
                                          required
                                          name="RetailRating10sBand-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>


                        </div>
                     </div>
                  </div>

                  <!-- RetailRating -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.RetailRating">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-car"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th colspan="2">Actions</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td colspan="2">Example: add 1% above the valuation for retail rating up to 20</td>
                                 <td colspan="3"></td>
                              </tr>
                              <tr class="exampleRow">
                                 <td>20</td>
                                 <td>101</td>
                                 <td colspan="3"></td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="d-flex align-items-center">
                                       <div>Excel quick drop &nbsp;</div>
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="pasteEditableLabelNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input (paste)="handlePasteEditableLabel($event, factor)" id="excelQuickDrop"/>
                                 </td>
                                 <td colspan="3"></td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          required
                                          type="text"
                                          name="ModelName-label:X-{{ factorItemIndex }}"
                                          #factorItemLabel="ngModel"
                                          [(ngModel)]="factorItem.Label"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="ModelName-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0 || factorItem.IsReadOnly"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- Mileage -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.Mileage">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-car"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th colspan="2">Actions</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                                 <tr class="exampleRow">
                                    <td colspan="2">Example: add 1% above the valuation for mileage up to 500</td>
                                    <td colspan="3"></td>
                                 </tr>
                                 <tr class="exampleRow">
                                    <td>500</td>
                                    <td>101</td>
                                    <td colspan="3"></td>
                                 </tr>
                                 <tr>
                                    <td>
                                       <div class="d-flex align-items-center">
                                          <div>Excel quick drop &nbsp;</div>
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="pasteEditableLabelNote()"
                                          ></instructionButton>
                                       </div>
                                    </td>
                                    <td>
                                       <input (paste)="handlePasteEditableLabel($event, factor)" id="excelQuickDrop" />
                                    </td>
                                    <td colspan="3"></td>
                                 </tr>
                                 <ng-container
                                    *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          required
                                          type="text"
                                          name="ModelName-label:X-{{ factorItemIndex }}"
                                          #factorItemLabel="ngModel"
                                          [(ngModel)]="factorItem.Label"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="ModelName-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0 || factorItem.IsReadOnly"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>


                  <!-- MilesPerYear -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.MilesPerYear">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-car"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow class="newFactorNotes"
                                        [message]="service.factorExplanation(factor)"></instructionRow>
                        <div class="factorWorkings">
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th colspan="2">Actions</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td colspan="2">Example: go 1% below the valuation when mileage per year is up to 500
                                 </td>
                                 <td colspan="3"></td>

                           </tr>
                           <tr class="exampleRow">
                              <td >500</td>
                              <td>99</td>
                              <td colspan="3"></td>
                           </tr>
                           <tr>
                              <td>
                                 <div class="d-flex align-items-center">
                                    <div>Excel quick drop &nbsp;</div>
                                    <instructionButton
                                       class="newFactorNote"
                                       [note]="pasteEditableLabelNote()"
                                    ></instructionButton>
                                 </div>
                              </td>
                              <td><input (paste)="handlePasteEditableLabel($event, factor)" id="excelQuickDrop" /></td>
                              <td colspan="3"></td>
                           </tr>
                           <ng-container
                              *ngFor="
                                 let factorItem of factor.StrategyFactorItems;
                                 let factorItemIndex = index;
                                 trackBy: trackByIndex
                              "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          required
                                          type="text"
                                          name="ModelName-label:X-{{ factorItemIndex }}"
                                          #factorItemLabel="ngModel"
                                          [(ngModel)]="factorItem.Label"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="ModelName-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0 || factorItem.IsReadOnly"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>
                        </div>
                     </div>
                  </div>


                  <!-- DaysListedBand -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.DaysListedBand">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-calendar-day"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>

                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>

                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: add 1% above the valuation</td>
                                 <td>101</td>
                                 <td></td>
                              </tr>

                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>{{ factorItem.Label }}</td>
                                    <td>
                                       <input
                                          required
                                          name="DaysListedBand-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>


                        </div>
                     </div>
                  </div>

                  <!-- MinimumPricePosition -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.MinimumPricePosition">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-calendar-day"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>

                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>

                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                                 <tr class="exampleRow">
                                     <td>Example: ensure price position is at least 95%</td>
                                    <td>95</td>
                                    <td></td>
                                 </tr>

                                 <ng-container
                                    *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                                 >
                                    <tr>
                                       <td>{{ factorItem.Label }}</td>
                                       <td>
                                          <input
                                             required
                                             name="MinimumPricePosition-value:X-{{ factorItemIndex }}"
                                             #factorItemValue="ngModel"
                                             class="valueInput"
                                             type="number"
                                             [(ngModel)]="factorItem.Value"
                                          />
                                       </td>
                                       <td>
                                          <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                       </td>
                                    </tr>
                                 </ng-container>
                              </tbody>
                           </table>


                        </div>
                     </div>
                  </div>

                  <!-- MaximumPricePosition -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.MaximumPricePosition">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-calendar-day"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>

                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>

                        <div >
                           <table>
                              <thead>
                                 <tr>
                                    <th></th>
                                    <th>Impact %</th>
                                    <th>Optional Notes</th>
                                 </tr>
                              </thead>
                              <tbody>
                                 <tr class="exampleRow">
                                    <td>Example: ensure price position is at most 105%</td>
                                    <td>105</td>
                                    <td></td>
                                 </tr>

                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                                 >
                                    <tr>
                                       <td>{{ factorItem.Label }}</td>
                                       <td>
                                          <input
                                             required
                                             name="MaximumPricePosition-value:X-{{ factorItemIndex }}"
                                             #factorItemValue="ngModel"
                                             class="valueInput"
                                             type="number"
                                             [(ngModel)]="factorItem.Value"
                                          />
                                       </td>
                                       <td>
                                          <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                       </td>
                                    </tr>
                                 </ng-container>
                              </tbody>
                           </table>


                        </div>
                     </div>
                  </div>

                  <!-- OnBrandCheck -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.OnBrandCheck">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-shield-check"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>

                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>

                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: add 1% above the valuation</td>
                                 <td>101</td>
                                 <td></td>
                              </tr>

                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>{{ factorItem.Label }}</td>
                                    <td>
                                       <input
                                          required
                                          name="OnBrandCheck-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>


                        </div>
                     </div>
                  </div>

                  <!-- RetailerName -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.RetailerName">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-map-marker"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th colspan="2">Actions</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: add 1% above the valuation</td>
                                 <td>101</td>
                                 <td colspan="2"></td>
                                 <td></td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>{{ factorItem.Label }}</td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="RetailerName-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0 || factorItem.IsReadOnly"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- Brand -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.Brand">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-car"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th colspan="2">Actions</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: add 1% above the valuation</td>
                                 <td>101</td>
                                 <td colspan="2"></td>
                                 <td></td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="d-flex align-items-center">
                                       <div>Excel quick drop &nbsp;</div>
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="pasteEditableLabelNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input (paste)="handlePasteEditableLabel($event, factor)" id="excelQuickDrop"/>
                                 </td>
                                 <td colspan="3"></td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          required
                                          type="text"
                                          name="Brand-label:X-{{ factorItemIndex }}"
                                          #factorItemLabel="ngModel"
                                          [(ngModel)]="factorItem.Label"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="Brand-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0 || factorItem.IsReadOnly"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- ModelName -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.ModelName">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-car"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th colspan="2">Actions</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: add 1% above the valuation</td>
                                 <td>101</td>
                                 <td colspan="2"></td>
                                 <td></td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="d-flex align-items-center">
                                       <div>Excel quick drop &nbsp;</div>
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="pasteEditableLabelNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input (paste)="handlePasteEditableLabel($event, factor)" id="excelQuickDrop"/>
                                 </td>
                                 <td colspan="3"></td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          required
                                          type="text"
                                          name="ModelName-label:X-{{ factorItemIndex }}"
                                          #factorItemLabel="ngModel"
                                          [(ngModel)]="factorItem.Label"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="ModelName-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0 || factorItem.IsReadOnly"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- DaysInStock -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.DaysInStock">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-car"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th colspan="2">Actions</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td colspan="2">Example: add 1% above the valuation for retail rating up to 20</td>
                                 <td colspan="3"></td>
                              </tr>
                              <tr class="exampleRow">
                                 <td>20</td>
                                 <td>101</td>
                                 <td colspan="3"></td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="d-flex align-items-center">
                                       <div>Excel quick drop &nbsp;</div>
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="pasteEditableLabelNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input (paste)="handlePasteEditableLabel($event, factor)" id="excelQuickDrop"/>
                                 </td>
                                 <td colspan="3"></td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          required
                                          type="text"
                                          name="ModelName-label:X-{{ factorItemIndex }}"
                                          #factorItemLabel="ngModel"
                                          [(ngModel)]="factorItem.Label"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="ModelName-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0 || factorItem.IsReadOnly"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>
                  <!-- DaysListed -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.DaysListed">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-car"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th colspan="2">Actions</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td colspan="2">Example: add 1% above the valuation for retail rating up to 20</td>
                                 <td colspan="3"></td>
                              </tr>
                              <tr class="exampleRow">
                                 <td>20</td>
                                 <td>101</td>
                                 <td colspan="3"></td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="d-flex align-items-center">
                                       <div>Excel quick drop &nbsp;</div>
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="pasteEditableLabelNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input (paste)="handlePasteEditableLabel($event, factor)" id="excelQuickDrop"/>
                                 </td>
                                 <td colspan="3"></td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          required
                                          type="text"
                                          name="ModelName-label:X-{{ factorItemIndex }}"
                                          #factorItemLabel="ngModel"
                                          [(ngModel)]="factorItem.Label"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="ModelName-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0 || factorItem.IsReadOnly"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- MatchCheapestCompetitor -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.MatchCheapestCompetitor">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-ranking-star"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <tbody>
                              <tr>
                                 <td>Competitor match settings</td>
                                 <td></td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Radius: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="radiusNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-radius"
                                       type="number"
                                       [(ngModel)]="factor.getCheapestCompetitorFactorItem('Radius').Value"
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Plate steps: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="plateStepsNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-plateSteps"
                                       type="number"
                                       [(ngModel)]="factor.getCheapestCompetitorFactorItem('PlateSteps').Value"
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Mileage steps :&nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="mileageStepsNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-MileageSteps"
                                       type="number"
                                       [(ngModel)]="factor.getCheapestCompetitorFactorItem('MileageSteps').Value"
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Ranking to achieve: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="rankingToAchieveNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-CompetitorRanking"
                                       type="number"
                                       [(ngModel)]="factor.getCheapestCompetitorFactorItem('Ranking').Value"
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Seller types: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="sellerTypeNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td class="competitorType textRight">
                                    <sliderSwitch
                                       [blackFont]="true"
                                       [width]="150"
                                       [defaultValue]="
                                             factor.getCheapestCompetitorFactorItem('Independent').BoolValue
                                          "
                                       [text]="factor.getCheapestCompetitorFactorItem('Independent').Label"
                                       (toggle)="
                                             toggleFactorItemBoolValue(
                                                factor.getCheapestCompetitorFactorItem('Independent')
                                             )
                                          "
                                    ></sliderSwitch>
                                    <div class="smallGap"></div>
                                    <sliderSwitch
                                       [blackFont]="true"
                                       [width]="150"
                                       [defaultValue]="factor.getCheapestCompetitorFactorItem('Franchise').BoolValue"
                                       [text]="factor.getCheapestCompetitorFactorItem('Franchise').Label"
                                       (toggle)="
                                             toggleFactorItemBoolValue(
                                                factor.getCheapestCompetitorFactorItem('Franchise')
                                             )
                                          "
                                    ></sliderSwitch>
                                    <div class="smallGap"></div>
                                    <sliderSwitch
                                       [blackFont]="true"
                                       [width]="150"
                                       [defaultValue]="
                                             factor.getCheapestCompetitorFactorItem('Supermarket').BoolValue
                                          "
                                       [text]="factor.getCheapestCompetitorFactorItem('Supermarket').Label"
                                       (toggle)="
                                             toggleFactorItemBoolValue(
                                                factor.getCheapestCompetitorFactorItem('Supermarket')
                                             )
                                          "
                                    ></sliderSwitch>
                                    <div class="smallGap"></div>
                                    <sliderSwitch
                                       [blackFont]="true"
                                       [width]="150"
                                       [defaultValue]="factor.getCheapestCompetitorFactorItem('Private').BoolValue"
                                       [text]="factor.getCheapestCompetitorFactorItem('Private').Label"
                                       (toggle)="
                                             toggleFactorItemBoolValue(
                                                factor.getCheapestCompetitorFactorItem('Private')
                                             )
                                          "
                                    ></sliderSwitch>
                                 </td>
                              </tr>
                              <tr>
                                 <td>Comment</td>
                                 <td class="competitorTypeNotes">
                                       <textarea
                                          placeholder="add notes"
                                          [(ngModel)]="factor.getCheapestCompetitorFactorItem('Radius').Comment"
                                       ></textarea>
                                 </td>
                              </tr>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- DaysToSell cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.DaysToSell">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-calendar-exclamation"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Days to sell</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: sell within 30 days</td>
                                 <td>30</td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>{{ factorItem.Label }}</td>
                                    <td>
                                       <input
                                          required
                                          name="{{ factor.Name }}-value:X-{{ factorItemIndex }}"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- DailyValuationChange cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.DailyValuationChange">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-calendar-exclamation"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead></thead>
                              <tbody>
                              <tr class="exampleRow"></tr>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- ValuationChangeUntilSell cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.ValuationChangeUntilSell">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-calendar-exclamation"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead></thead>
                              <tbody>
                              <tr class="exampleRow"></tr>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- LeavingData cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.LeavingData">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-calendar-exclamation"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead></thead>
                              <tbody>
                              <tr class="exampleRow"></tr>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- MinimumProfit cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.MinimumProfit">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-sterling-sign"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Minimum £</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: £1,000 minimum profit</td>
                                 <td>1000</td>
                                 <td></td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>{{ factorItem.Label }}</td>
                                    <td>
                                       <input
                                          required
                                          name="{{ factor.Name }}-value:X-{{ factorItemIndex }}"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- Start Tag cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.Tag">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-tag"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow class="newFactorNotes"
                                        [message]="service.factorExplanation(factor)"></instructionRow>

                        <div class="factorWorkings">

                           <table>
                              <tr>
                                 <th>&nbsp;</th>
                                 <th>Impact %</th>
                                 <th>Impact £</th>
                              </tr>

                              <tr class="exampleRow">
                                 <td colspan="1">Example add 1% to the valuation</td>
                                 <td>101</td>
                                 <td>0</td>
                              </tr>

                              <ng-container *ngFor="
                                 let factorItem of factor.StrategyFactorItems;
                                 let factorItemIndex = index;
                                 trackBy: trackByIndex"
                              >
                                 <tr>
                                    <td>

                                       <app-tag-display *ngIf="factorItem.tag" [tag]="factorItem.tag"
                                                        [canEditTags]="false"></app-tag-display>

                                       <typeaheadAndDropdownLabelValue
                                          *ngIf="!factorItem.tag"
                                          [width]="'100%'"
                                          [position]="'right'"
                                          [placeholder]="'Begin typing to select a tag'"
                                          [searchList]="filteredTypeaheadTagList(factor)"
                                          [showCreateOption]="false"
                                          [clearInputOnChoiceMade]="true"
                                          (chosenItemEmitter)="updateTag($event, factor, factorItem)"
                                       ></typeaheadAndDropdownLabelValue>

                                    </td>

                                    <td>
                                       <input
                                          required
                                          name="{{ factor.Name }}-value:X-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          factorItemIndex
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>

                                    <td>
                                       <input
                                          required
                                          name="{{ factor.Name }}-valueAmount:X-{{ factorItemIndex }}"
                                          #factorItemValueAmount="ngModel"
                                          factorItemIndex
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.ValueAmount"
                                       />
                                    </td>

                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                           </table>
                        </div>
                     </div>
                  </div>
                  <!-- End Tag -->

                  <!-- Start Apply Tag cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.ApplyTagAdjustments">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-tags"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow class="newFactorNotes"
                                        [message]="service.factorExplanation(factor)"></instructionRow>
                     </div>
                  </div>
                  <!-- End Apply Tag -->
                  <!-- RoundToNearestEnding and Multiple variants cardInner -->
                  <div class="cardInner" *ngIf="
                     factor.Name === StrategyFactorName.RoundToNearestEnding ||
                     factor.Name === StrategyFactorName.RoundUpToNearestEnding ||
                     factor.Name === StrategyFactorName.RoundDownToNearestEnding ||
                     factor.Name === StrategyFactorName.RoundToNearestMultiple ||
                     factor.Name === StrategyFactorName.RoundUpToNearestMultiple ||
                     factor.Name === StrategyFactorName.RoundDownToNearestMultiple">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-sterling-sign"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                                 <tr>
                                    <th></th>
                                    <th>
                                       <ng-container *ngIf="factor.Name === StrategyFactorName.RoundToNearestMultiple ">
                                          Round to multiple of £
                                       </ng-container>
                                       <ng-container *ngIf="factor.Name === StrategyFactorName.RoundUpToNearestMultiple ">
                                          Round up to multiple of £
                                       </ng-container>
                                       <ng-container *ngIf="factor.Name === StrategyFactorName.RoundDownToNearestMultiple ">
                                          Round down to multiple of £
                                       </ng-container>

                                       <ng-container *ngIf="factor.Name === StrategyFactorName.RoundToNearestEnding">
                                          Round to nearest price ending £
                                       </ng-container>
                                       <ng-container *ngIf="factor.Name === StrategyFactorName.RoundUpToNearestEnding">
                                          Round up to nearest price ending £
                                       </ng-container>
                                       <ng-container *ngIf="factor.Name === StrategyFactorName.RoundDownToNearestEnding">
                                          Round down to nearest price ending £
                                       </ng-container>
                                    </th>
                                    <th>Optional Notes</th>
                                 </tr>
                              </thead>
                              <tbody>
                                 <tr class="exampleRow">
                                    <td>
                                       <ng-container *ngIf="
                                          factor.Name === StrategyFactorName.RoundToNearestMultiple ||
                                          factor.Name === StrategyFactorName.RoundUpToNearestMultiple ||
                                          factor.Name === StrategyFactorName.RoundDownToNearestMultiple">
                                          Example: multiple of £50
                                       </ng-container>
                                       <ng-container *ngIf="
                                          !(factor.Name === StrategyFactorName.RoundToNearestMultiple ||
                                            factor.Name === StrategyFactorName.RoundUpToNearestMultiple ||
                                            factor.Name === StrategyFactorName.RoundDownToNearestMultiple)">
                                          Example: nearest £50
                                       </ng-container>
                                    </td>
                                    <td>50</td>
                                    <td></td>
                                 </tr>
                                 <ng-container
                                    *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>{{ factorItem.Label }}</td>
                                    <td>
                                       <input
                                          required
                                          name="{{ factor.Name }}-value:X-{{ factorItemIndex }}"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>
                        </div>
                     </div>
                  </div>

                  <!-- RoundToPriceBreak cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.RoundToPriceBreak">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-sterling-sign"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Round if Within £</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: within £50</td>
                                 <td>50</td>
                                 <td></td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>{{ factorItem.Label }}</td>
                                    <td>
                                       <input
                                          required
                                          name="{{ factor.Name }}-value:X-{{ factorItemIndex }}"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- SpecificColour cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.SpecificColour">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-palette"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow class="newFactorNotes"
                                        [message]="service.factorExplanation(factor)"></instructionRow>
                        <div class="factorWorkings">
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th colspan="2">Actions</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRowSpecificColour">
                                 <td>Example: add 1% above the valuation for Urban Grey</td>
                                 <td></td>
                                 <td></td>
                              </tr>
                              <tr class="exampleRowSpecificColour">
                                 <td>Urban Grey</td>
                                 <td>101</td>
                                 <td></td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="d-flex align-items-center">
                                       <div>Excel quick drop &nbsp;</div>
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="pasteEditableLabelNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input (paste)="handlePasteEditableLabel($event, factor)" id="excelQuickDrop"/>
                                 </td>
                                 <td colspan="3"></td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                 let factorItem of factor.StrategyFactorItems;
                                 let factorItemIndex = index;
                                 trackBy: trackByIndex
                              "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          required
                                          type="text"
                                          name="SpecificColour-label:X-{{ factorItemIndex }}"
                                          [(ngModel)]="factorItem.Label"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="SpecificColour-value:X-{{ factorItemIndex }}"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0 || factorItem.IsReadOnly"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>
                        </div>
                     </div>
                  </div>

                  <!-- Colour cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.Colour">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-palette"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow class="newFactorNotes"
                                        [message]="service.factorExplanation(factor)"></instructionRow>
                        <div class="factorWorkings">
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th colspan="2">Actions</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRowColour">
                                 <td>Example: go 1% below the valuation for Red cars</td>
                                 <td></td>
                                 <td></td>
                              </tr>
                              <tr class="exampleRowColour">
                                 <td>Red</td>
                                 <td>99</td>
                                 <td></td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="d-flex align-items-center">
                                       <div>Excel quick drop &nbsp;</div>
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="pasteEditableLabelNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input (paste)="handlePasteEditableLabel($event, factor)" id="excelQuickDrop"/>
                                 </td>
                                 <td colspan="3"></td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                 let factorItem of factor.StrategyFactorItems;
                                 let factorItemIndex = index;
                                 trackBy: trackByIndex
                              "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          required
                                          type="text"
                                          name="Colour-label:X-{{ factorItemIndex }}"
                                          [(ngModel)]="factorItem.Label"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="Colour-value:X-{{ factorItemIndex }}"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0 || factorItem.IsReadOnly"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>
                        </div>
                     </div>
                  </div>

                  <!-- AgeAndOwners cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.AgeAndOwners">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-users"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>Impact %</th>
                                 <th colspan="2">Actions</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRowSpecificColour">
                                 <td>
                                    Example: add 1% above the valuation for 3-5yr old cars with 1 previous owner
                                 </td>
                                 <td></td>
                                 <td></td>
                              </tr>
                              <tr class="exampleRowSpecificColour">
                                 <td style="display: flex; gap: 10em; justify-content: center; align-items: center">
                                    <span>3-5yrs</span>
                                    <span>1</span>
                                 </td>
                                 <td>101</td>
                                 <td></td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>
                                       <select
                                          class="selectDropdown me-5"
                                          required
                                          [ngModel]="factorItem.selectedAgeCategory"
                                          name="{{ factor.Name }}-ageband-dropdown:X-{{ factorItemIndex }}"
                                          (change)="updateAgeAndOwnersLabelForAge($event, factorItem)"
                                       >
                                          <option value="">Select an age category</option>
                                          <option *ngFor="let category of ageCategories" [value]="category">
                                             {{ category }}
                                          </option>
                                       </select>
                                       <select
                                          class="selectDropdown"
                                          required
                                          [ngModel]="factorItem.selectedPreviousOwners"
                                          name="{{ factor.Name }}-previous-owners-dropdown:X-{{ factorItemIndex }}"
                                          (change)="updateAgeAndOwnersLabelForOwners($event, factorItem)"
                                       >
                                          <option value="">Select Previous owners</option>
                                          <option *ngFor="let owner of previousOwners" [value]="owner">
                                             {{ owner }}
                                          </option>
                                       </select>
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          name="{{ factor.Name }}-impact:X-{{ factorItemIndex }}"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          *ngIf="factorItemIndex == factor.StrategyFactorItems.length - 1"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- AchieveMarketPositionScore cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.AchieveMarketPositionScore">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-ranking-star"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <tbody>
                              <tr>
                                 <td>Market position settings</td>
                                 <td></td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Radius: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="radiusNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-PositionRadius"
                                       type="number"
                                       [(ngModel)]="factor.getAchieveMarketPositionScoreFactorItem('Radius').Value"
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Plate steps: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="plateStepsNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-MarketPositionPlateSteps"
                                       type="number"
                                       [(ngModel)]="
                                             factor.getAchieveMarketPositionScoreFactorItem('PlateSteps').Value
                                          "
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Mileage steps :&nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="mileageStepsNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-MarketPositionMileageSteps"
                                       type="number"
                                       [(ngModel)]="
                                             factor.getAchieveMarketPositionScoreFactorItem('MileageSteps').Value
                                          "
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Market Position Score to achieve: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="marketPositionScoreToAchieveNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-MarketPositionRanking"
                                       type="number"
                                       [(ngModel)]="factor.getAchieveMarketPositionScoreFactorItem('Ranking').Value"
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Seller types: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="sellerTypeNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td class="competitorType textRight">
                                    <sliderSwitch
                                       [width]="150"
                                       [blackFont]="true"
                                       [defaultValue]="
                                             factor.getAchieveMarketPositionScoreFactorItem('Independent').BoolValue
                                          "
                                       [text]="factor.getAchieveMarketPositionScoreFactorItem('Independent').Label"
                                       (toggle)="
                                             toggleFactorItemBoolValue(
                                                factor.getAchieveMarketPositionScoreFactorItem('Independent')
                                             )
                                          "
                                    ></sliderSwitch>
                                    <div class="smallGap"></div>
                                    <sliderSwitch
                                       [width]="150"
                                       [blackFont]="true"
                                       [defaultValue]="
                                             factor.getAchieveMarketPositionScoreFactorItem('Franchise').BoolValue
                                          "
                                       [text]="factor.getAchieveMarketPositionScoreFactorItem('Franchise').Label"
                                       (toggle)="
                                             toggleFactorItemBoolValue(
                                                factor.getAchieveMarketPositionScoreFactorItem('Franchise')
                                             )
                                          "
                                    ></sliderSwitch>
                                    <div class="smallGap"></div>
                                    <sliderSwitch
                                       [width]="150"
                                       [blackFont]="true"
                                       [defaultValue]="
                                             factor.getAchieveMarketPositionScoreFactorItem('Supermarket').BoolValue
                                          "
                                       [text]="factor.getAchieveMarketPositionScoreFactorItem('Supermarket').Label"
                                       (toggle)="
                                             toggleFactorItemBoolValue(
                                                factor.getAchieveMarketPositionScoreFactorItem('Supermarket')
                                             )
                                          "
                                    ></sliderSwitch>
                                    <div class="smallGap"></div>
                                    <sliderSwitch
                                       [width]="150"
                                       [blackFont]="true"
                                       [defaultValue]="
                                             factor.getAchieveMarketPositionScoreFactorItem('Private').BoolValue
                                          "
                                       [text]="factor.getAchieveMarketPositionScoreFactorItem('Private').Label"
                                       (toggle)="
                                             toggleFactorItemBoolValue(
                                                factor.getAchieveMarketPositionScoreFactorItem('Private')
                                             )
                                          "
                                    ></sliderSwitch>
                                 </td>
                              </tr>
                              <tr>
                                 <td>Comment</td>
                                 <td class="competitorTypeNotes">
                                       <textarea
                                          placeholder="add notes"
                                          [(ngModel)]="factor.getAchieveMarketPositionScoreFactorItem('Radius').Comment"
                                       ></textarea>
                                 </td>
                              </tr>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>
                  <!-- TrackMarketPosition cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.TrackMarketPosition">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-ranking-star"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <tbody>
                              <tr>
                                 <td>Track market settings</td>
                                 <td></td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Radius: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="radiusNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-PositionRadius"
                                       type="number"
                                       [(ngModel)]="factor.getFactorItem('Radius').Value"
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Plate steps: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="plateStepsNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-MarketPositionPlateSteps"
                                       type="number"
                                       [(ngModel)]="factor.getFactorItem('PlateSteps').Value"
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Mileage steps :&nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="mileageStepsNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-MarketPositionMileageSteps"
                                       type="number"
                                       [(ngModel)]="factor.getFactorItem('MileageSteps').Value"
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Price Position % to achieve vs competitor average: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="pricePositionNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-MarketPositionRanking"
                                       type="number"
                                       [(ngModel)]="factor.getFactorItem('PP%Variance').Value"
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Seller types: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="sellerTypeNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td class="competitorType textRight">
                                    <sliderSwitch
                                       [width]="150"
                                       [blackFont]="true"
                                       [defaultValue]="factor.getFactorItem('Independent').BoolValue"
                                       [text]="factor.getFactorItem('Independent').Label"
                                       (toggle)="toggleFactorItemBoolValue(factor.getFactorItem('Independent'))"
                                    ></sliderSwitch>
                                    <div class="smallGap"></div>
                                    <sliderSwitch
                                       [width]="150"
                                       [blackFont]="true"
                                       [defaultValue]="factor.getFactorItem('Franchise').BoolValue"
                                       [text]="factor.getFactorItem('Franchise').Label"
                                       (toggle)="toggleFactorItemBoolValue(factor.getFactorItem('Franchise'))"
                                    ></sliderSwitch>
                                    <div class="smallGap"></div>
                                    <sliderSwitch
                                       [width]="150"
                                       [blackFont]="true"
                                       [defaultValue]="factor.getFactorItem('Supermarket').BoolValue"
                                       [text]="factor.getFactorItem('Supermarket').Label"
                                       (toggle)="toggleFactorItemBoolValue(factor.getFactorItem('Supermarket'))"
                                    ></sliderSwitch>
                                    <div class="smallGap"></div>
                                    <sliderSwitch
                                       [width]="150"
                                       [blackFont]="true"
                                       [defaultValue]="factor.getFactorItem('Private').BoolValue"
                                       [text]="factor.getFactorItem('Private').Label"
                                       (toggle)="toggleFactorItemBoolValue(factor.getFactorItem('Private'))"
                                    ></sliderSwitch>
                                 </td>
                              </tr>
                              <tr>
                                 <td>Comment</td>
                                 <td class="competitorTypeNotes">
                                       <textarea
                                          placeholder="add notes"
                                          [(ngModel)]="factor.getFactorItem('Radius').Comment"
                                       ></textarea>
                                 </td>
                              </tr>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- WholesaleAdjustment cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.WholesaleAdjustment">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-circle-sterling"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <tbody>
                              <tr>
                                 <td style="white-space: nowrap">
                                    <div class="showInformationInline">
                                       Adjustment %: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="wholesaleAdjustmentPctNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td class="text-start">
                                    <input
                                       class="valueInput"
                                       required
                                       name="WholesaleAdjustmentPct"
                                       type="number"
                                       min="80"
                                       max="120"
                                       oninput="this.value = Math.max(0, this.value)"
                                       [(ngModel)]="factor.getWholesaleAdjustmentFactorItem('AdjustmentPct').Value"
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">
                                       Adjustment £: &nbsp;
                                       <instructionButton
                                          class="newFactorNote"
                                          [note]="wholesaleAdjustmentValueNote()"
                                       ></instructionButton>
                                    </div>
                                 </td>
                                 <td class="text-start">
                                    <input
                                       class="valueInput"
                                       required
                                       type="number"
                                       name="WholesaleAdjustmentValue"
                                       [(ngModel)]="factor.getWholesaleAdjustmentFactorItem('AdjustmentValue').Value"
                                    />
                                 </td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="showInformationInline">Comment:</div>
                                 </td>
                                 <td class="text-start">
                                       <textarea
                                          placeholder="add notes"
                                          [(ngModel)]="factor.getWholesaleAdjustmentFactorItem('AdjustmentPct').Comment"
                                       ></textarea>
                                 </td>
                              </tr>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- MakeFuelType cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.MakeFuelType">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-gas-pump"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow class="newFactorNotes"
                                        [message]="service.factorExplanation(factor)"></instructionRow>
                        <div class="factorWorkings">
                           <table>
                              <thead>
                              <tr>
                                 <th>Brand</th>
                                 <th>Fuel Type</th>
                                 <th>Impact %</th>
                                 <th colspan="2"></th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td colspan="2">Example: Ford Electric take off 1%</td>
                                 <td>99</td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                 let factorItem of factor.StrategyFactorItems;
                                 let factorItemIndex = index;
                                 trackBy: trackByIndex
                              "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          type="text"
                                          required
                                          name="{{ factor.Name }}-brand:X-{{ factorItemIndex }}"
                                          [ngModel]="factorItem.selectedMake"
                                          (change)="updateMakeForMakeAndFuelType($event, factorItem)"
                                       />
                                    </td>
                                    <td>
                                       <select
                                          class="selectDropdown"
                                          [ngModel]="factorItem.selectedFuelType"
                                          required
                                          name="{{ factor.Name }}-fuelType:X-{{ factorItemIndex }}"
                                          (change)="updateFuelType($event, factorItem)"
                                       >
                                          <option value="">Select a fuel type</option>
                                          <option *ngFor="let fuelType of fuelTypes" [value]="fuelType">
                                             {{ fuelType }}
                                          </option>
                                       </select>
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="{{ factor.Name }}-impact:X-{{ factorItemIndex }}"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>
                        </div>
                     </div>
                  </div>

              <!-- MakeModel cardInner -->
            <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.MakeModel">
               <div class="cardHeader">
                  <div class="layerHeader d-flex justify-content-between align-items-center">
                     <div class="h2 layerIcon">
                        <i class="fas fa-gas-pump"></i>
                     </div>
                     <div class="factorName">
                        {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                     </div>
                     <div class="layerButton"></div>
                  </div>
               </div>
               <div class="cardBody">
                  <instructionRow class="newFactorNotes" [message]="service.factorExplanation(factor)"></instructionRow>
                  <div class="factorWorkings">
                     <table>
                        <thead>
                           <tr>
                              <th>Make</th>
                              <th>Model</th>
                              <th>Impact %</th>
                              <th colspan="2"></th>
                              <th>Optional Notes</th>
                           </tr>
                        </thead>
                        <tbody>
                           <tr class="exampleRow">
                              <td colspan="2">Example: Ford Kuga take off 1%</td>
                              <td>99</td>
                           </tr>
                           <tr>
                              <td>
                                 <div class="d-flex align-items-center">
                                    <div>Excel quick drop &nbsp;</div>
                                    <instructionButton
                                       class="newFactorNote"
                                       [note]="pasteEditableLabelNote()"
                                    ></instructionButton>
                                 </div>
                              </td>
                              <td>
                                 <input (paste)="handlePasteEditableLabel($event, factor)" id="excelQuickDrop" />
                              </td>
                              <td colspan="4"></td>
                           </tr>
                           <ng-container
                              *ngFor="
                                 let factorItem of factor.StrategyFactorItems;
                                 let factorItemIndex = index;
                                 trackBy: trackByIndex
                              "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          type="text"
                                          required
                                          name="{{ factor.Name }}-brand:X-{{ factorItemIndex }}"
                                          [ngModel]="factorItem.selectedMake"
                                          (change)="updateMakeForMakeAndModel($event, factorItem)"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          type="text"
                                          required
                                          name="{{ factor.Name }}-model:X-{{ factorItemIndex }}"
                                          [ngModel]="factorItem.selectedModel"
                                          (change)="updateModel($event, factorItem)"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="{{ factor.Name }}-impact:X-{{ factorItemIndex }}"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>
                        </div>
                     </div>
                  </div>


                  <!-- MakeAgeBand cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.MakeAgeBand">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-clock-three"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th>Brand</th>
                                 <th>Age Band</th>
                                 <th>Impact %</th>
                                 <th colspan="2"></th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td colspan="2">Example: Ford &lt;1 year take off 1%</td>
                                 <td>99</td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          required
                                          name="{{ factor.Name }}-brand:X-{{ factorItemIndex }}"
                                          type="text"
                                          [ngModel]="factorItem.selectedMake"
                                          (change)="updateMakeForMakeAndAgeBand($event, factorItem)"
                                       />
                                    </td>
                                    <td>
                                       <select
                                          class="selectDropdown"
                                          required
                                          name="{{ factor.Name }}-age:X-{{ factorItemIndex }}"
                                          [ngModel]="factorItem.selectedAgeBand"
                                          (change)="updateAgeBand($event, factorItem)"
                                       >
                                          <option value="">Select an age band</option>
                                          <option *ngFor="let ageBand of getAgeBands" [value]="ageBand">
                                             {{ ageBand }}
                                          </option>
                                       </select>
                                    </td>
                                    <td>
                                       <input
                                          required
                                          name="{{ factor.Name }}-impact:X-{{ factorItemIndex }}"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- PerformanceRatingScore cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.PerformanceRatingScore">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-eyes"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th>Rating</th>
                                 <th>Impact %</th>
                                 <th></th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: take off 2% for performance rating up to 25</td>
                                 <td colspan="4"></td>
                              </tr>
                              <tr class="exampleRow">
                                 <td>0-25</td>
                                 <td>98</td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          required
                                          name="{{ factor.Name }}-rating:X-{{ factorItemIndex }}"
                                          type="text"
                                          [(ngModel)]="factorItem.Label"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          name="{{ factor.Name }}-impact:X-{{ factorItemIndex }}"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <!-- Buttons for remove and add -->
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0 || factorItem.IsReadOnly"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- FuelType cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.FuelType">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-gas-pump"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th>Fuel Type</th>
                                 <th>Impact %</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: add 1% above the valuation</td>
                                 <td>101</td>
                                 <td></td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>{{ factorItem.Label }}</td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          name="{{ factor.Name }}-impact:X-{{ factorItemIndex }}"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- ValueBand cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.ValueBand">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-pound-sign"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th>Value Band</th>
                                 <th>Impact %</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td colspan="1">Example: add 1% above the valuation</td>
                                 <td colspan="1">101</td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>
                                       {{ factorItem.Label }}
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                          name="{{ factor.Name }}-impact:X-{{ factorItemIndex }}"
                                       />
                                    </td>

                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- RegYear cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.RegYear">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-pound-sign"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th>Value Band</th>
                                 <th>Impact %</th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td colspan="1">Example: add 1% above the valuation</td>
                                 <td colspan="1">101</td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>
                                       {{ factorItem.Label }}
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                          name="{{ factor.Name }}-impact:X-{{ factorItemIndex }}"
                                       />
                                    </td>

                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- LiveMarketCondition cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.LiveMarketCondition">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-chart-line"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th>Condition %</th>
                                 <th>Impact %</th>
                                 <th></th>
                                 <th></th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td colspan="2">Example: Apply 99% if market condition is up to -50%</td>
                              </tr>
                              <tr class="exampleRow">
                                 <td colspan="1">-50</td>
                                 <td colspan="1">99</td>
                                 <td colspan="3"></td>
                              </tr>
                              <tr>
                                 <td>
                                    <div class="d-flex align-items-center">
                                       <div>Excel quick drop &nbsp;</div>
                                       <instructionButton class="newFactorNote" [note]="pasteEditableLabelNote()">
                                       </instructionButton>
                                    </div>
                                 </td>

                                 <td>
                                    <input (paste)="handlePasteEditableLabel($event, factor)" id="excelQuickDrop"/>
                                 </td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td>
                                       <input
                                          class="valueInput extraWide"
                                          required
                                          type="text"
                                          [(ngModel)]="factorItem.Label"
                                          name="{{ factor.Name }}-label:X-{{ factorItemIndex }}"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                          name="{{ factor.Name }}-impact:X-{{ factorItemIndex }}"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          [disabled]="factorItemIndex == 0"
                                          class="btn btn-danger"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- DateRange cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.DateRange">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-calendar"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>
                           <table>
                              <thead>
                              <tr>
                                 <th></th>
                                 <th>From Date</th>
                                 <th>To Date</th>
                                 <th>Impact %</th>
                                 <th>Impact £</th>
                                 <th></th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <tr class="exampleRow">
                                 <td>Example: Apply 99% and £10 if the current date is in range</td>
                                 <td>01/03/2025</td>
                                 <td>31/03/2025</td>
                                 <td>99</td>
                                 <td>10</td>
                              </tr>
                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <td></td>
                                    <td>
                                       <input
                                          required
                                          name="{{ factor.Name }}-effectiveDateFrom:X-{{ factorItemIndex }}"
                                          class="dateInput"
                                          type="date"
                                          [(ngModel)]="factorItem.effectiveDateFrom"
                                          (change)="updateDateRange($event, factorItem)"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          required
                                          name="{{ factor.Name }}-effectiveDateTo:X-{{ factorItemIndex }}"
                                          class="dateInput"
                                          type="date"
                                          [(ngModel)]="factorItem.effectiveDateTo"
                                          (change)="updateDateRange($event, factorItem)"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          required
                                          name="{{ factor.Name }}-value:X-{{ factorItemIndex }}"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Value"
                                       />
                                    </td>
                                    <td>
                                       <input
                                          required
                                          name="{{ factor.Name }}-valueAmount:X-{{ factorItemIndex }}"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.ValueAmount"
                                       />
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-danger"
                                          [disabled]="factorItemIndex == 0"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Remove
                                       </button>
                                    </td>
                                    <td>
                                       <button
                                          class="btn btn-success"
                                          [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                          (click)="addFactorItem($event, factor, factorItemIndex)"
                                       >
                                          Add
                                       </button>
                                    </td>
                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>

                        </div>
                     </div>
                  </div>

                  <!-- SetToAdvertisedPrice cardInner -->
                  <div class="cardInner" *ngIf="factor.Name === StrategyFactorName.SetToAdvertisedPrice">
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <div class="h2 layerIcon">
                              <i class="fas fa-pound-sign"></i>
                           </div>
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>
                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>
                        <div>

                        </div>
                     </div>
                  </div>

                  <!-- Matrices  -->
                  <div
                     class="cardInner"
                     *ngIf="
                        [
                           StrategyFactorName.RR_DB_Matrix,
                           StrategyFactorName.RR_DL_Matrix,
                           StrategyFactorName.RR_DS_Matrix,
                           StrategyFactorName.DTS_DL_Matrix,
                           StrategyFactorName.PY_DS_Matrix,
                           StrategyFactorName.VB_DS_Matrix
                        ].includes(factor.Name)
                     "
                  >
                     <div class="cardHeader">
                        <div class="layerHeader d-flex justify-content-between align-items-center">
                           <!-- The icon -->
                           <div class="h2 layerIcon">
                              <i *ngIf="factor.Name == StrategyFactorName.RR_DL_Matrix" class="fas fa-grid"></i>
                              <i *ngIf="factor.Name == StrategyFactorName.RR_DS_Matrix" class="fas fa-grid"></i>
                              <i *ngIf="factor.Name == StrategyFactorName.DTS_DL_Matrix" class="fas fa-grid"></i>
                           </div>

                           <!-- The label of the factor -->
                           <div class="factorName">
                              {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                           </div>

                           <div class="layerButton"></div>
                        </div>
                     </div>
                     <div class="cardBody">
                        <instructionRow
                           class="newFactorNotes"
                           [message]="service.factorExplanation(factor)"
                        ></instructionRow>

                        <div>
                           <table>
                              <thead>
                              <!-- ######################################################### -->
                              <!-- If matrix type layer -->
                              <!-- ######################################################### -->

                              <!-- Days listed heading row -->
                              <tr>
                                 <th colspan="2">
                                    <div class="d-flex align-items-center">
                                       <div>Excel quick drop &nbsp;</div>
                                       <instructionButton class="newFactorNote" [note]="pasteMatrixNote()">
                                       </instructionButton>
                                    </div>
                                 </th>
                                 <th [attr.colspan]="factor.horizontalBandLabels.length">
                                       <span *ngIf="factor.Name === StrategyFactorName.RR_DL_Matrix"
                                       >Days Listed up to and including</span
                                       >
                                    <span *ngIf="factor.Name === StrategyFactorName.RR_DS_Matrix"
                                    >Days In Stock up to and including</span
                                    >

                                    <span *ngIf="factor.Name === StrategyFactorName.RR_DB_Matrix"
                                    >Days Booked In up to and including</span
                                    >
                                    <span *ngIf="factor.Name === StrategyFactorName.DTS_DL_Matrix"
                                    >Days Listed up to and including</span
                                    >
                                    <span *ngIf="factor.Name === StrategyFactorName.PY_DS_Matrix"
                                    >Days In Stock up to and including</span
                                    >
                                    <span *ngIf="factor.Name === StrategyFactorName.VB_DS_Matrix"
                                    >Days In Stock up to and including</span
                                    >

                                    <i class="ps-3 fas fa-circle-arrow-right"></i>
                                 </th>
                                 <th></th>
                              </tr>

                              <!-- The row for adding / deleting bands -->
                              <tr>
                                 <th><input (paste)="handlePasteMatrix($event, factor)" id="excelQuickDrop"/></th>
                                 <th></th>
                                 <th
                                    class="buttonHoldingCell"
                                    *ngFor="let label of factor.horizontalBandLabels; let labelIndex = index"
                                 >
                                    <button
                                       placement="left"
                                       popoverClass="vehiclePopUpImage"
                                       container="body"
                                       triggers="mouseenter:mouseleave"
                                       [ngbPopover]="'Remove this days band'"
                                       *ngIf="labelIndex > 0"
                                       class="btn btn-danger addRemoveButton"
                                       (click)="maybeDeleteHorizontalBand($event, factor, labelIndex)"
                                    >
                                       <i class="fas fa-circle-minus"></i>
                                    </button>
                                    <button
                                       placement="left"
                                       popoverClass="vehiclePopUpImage"
                                       container="body"
                                       triggers="mouseenter:mouseleave"
                                       [ngbPopover]="'Add another days band to the right'"
                                       class="btn btn-success"
                                       (click)="addHorizontalBand($event, factor, labelIndex + 1, 10)"
                                    >
                                       <i class="fas fa-circle-plus"></i>
                                    </button>
                                 </th>
                              </tr>
                              <!-- Retail Rating and days  thresholds row -->
                              <tr>
                                 <th></th>
                                 <th></th>
                                 <th *ngFor="let label of factor.horizontalBandLabels; let bandLabelIndex = index">
                                    <input
                                       class="valueInput"
                                       tabindex="1"
                                       required
                                       name="bandLabel-value:{{ factorIndex }}-{{ bandLabelIndex }}"
                                       attr.name="bandLabel-value:{{ factorIndex }}-{{ bandLabelIndex }}"
                                       #bandLabelValue="ngModel"
                                       [disabled]="factor.horizontalBandLabels.length - 1 == bandLabelIndex"
                                       type="number"
                                       [(ngModel)]="label.value"
                                       (ngModelChange)="
                                             onHorizontalBandValueChange(factor, factorIndex, bandLabelIndex, $event)
                                          "
                                       [ngClass]="{
                                             redHighlight: colourHorizontalBandRed(factor, bandLabelIndex, label.value)
                                          }"
                                    />
                                 </th>
                                 <th></th>
                              </tr>

                              <tr>
                                 <th colspan="2">
                                       <span
                                          *ngIf="
                                             factor.Name == StrategyFactorName.RR_DL_Matrix ||
                                             factor.Name == StrategyFactorName.RR_DS_Matrix ||
                                             factor.Name == StrategyFactorName.RR_DB_Matrix
                                          "
                                       >
                                          Retail Rating up to and including</span
                                       >
                                    <span *ngIf="factor.Name === StrategyFactorName.DTS_DL_Matrix">
                                          Days to sell</span
                                    >
                                    <span *ngIf="factor.Name === StrategyFactorName.PY_DS_Matrix"> Plate Year</span>
                                    <span *ngIf="factor.Name === StrategyFactorName.VB_DS_Matrix"> Price Band</span>

                                    <i class="ps-3 fas fa-circle-arrow-down"></i>
                                 </th>
                                 <th
                                    *ngFor="let label of factor.horizontalBandLabels; let bandLabelIndex = index"
                                 ></th>
                                 <th>Optional Notes</th>
                              </tr>
                              </thead>
                              <tbody>
                              <!-- ######################################################### -->
                              <!-- The  row build up showing each item -->
                              <!-- ######################################################### -->

                              <ng-container
                                 *ngFor="
                                       let factorItem of factor.StrategyFactorItems;
                                       let factorItemIndex = index;
                                       trackBy: trackByIndex
                                    "
                              >
                                 <tr>
                                    <!-- Buttons for remove and add -->
                                    <td class="buttonHoldingCell">
                                       <button
                                          *ngIf="factorItemIndex > 0"
                                          class="btn btn-danger"
                                          placement="left"
                                          popoverClass="vehiclePopUpImage"
                                          container="body"
                                          triggers="mouseenter:mouseleave"
                                          [ngbPopover]="'Remove this retail rating band'"
                                          (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                       >
                                          <i class="fas fa-circle-minus"></i>
                                       </button>
                                       <button
                                          class="btn btn-success"
                                          placement="left"
                                          popoverClass="vehiclePopUpImage"
                                          container="body"
                                          triggers="mouseenter:mouseleave"
                                          [ngbPopover]="'Add another retail rating band below this band'"
                                          (click)="addFactorItem($event, factor, factorItemIndex + 1)"
                                       >
                                          <i class="fas fa-circle-plus"></i>
                                       </button>
                                    </td>

                                    <!-- The editable label -->
                                    <td>
                                       <input
                                          [disabled]="shouldDisableRowThreshold(factor, factorItemIndex)"
                                          tabindex="2"
                                          required
                                          name="{{ factor.Name }}-label:X-{{ factorItemIndex }}"
                                          #factorItemLabel="ngModel"
                                          class="valueInput"
                                          type="number"
                                          [(ngModel)]="factorItem.Label"
                                          [ngClass]="{
                                                redHighlight: colourVerticalBandRed(
                                                   factor,
                                                   factorItemIndex,
                                                   factorItem.Label
                                                )
                                             }"
                                       />
                                    </td>

                                    <!-- This iterates out the horizontal  input boxes -->
                                    <td
                                       *ngFor="
                                             let horizontalBand of factorItem.horizontalBands;
                                             let bandIndex = index
                                          "
                                    >
                                       <input
                                          class="valueInput"
                                          type="number"
                                          required
                                          name="{{ factor.Name }}-value:{{ bandIndex }}-{{ factorItemIndex }}"
                                          #factorItemValue="ngModel"
                                          tabindex="3"
                                          [(ngModel)]="horizontalBand.value"
                                          min="80"
                                          max="120"
                                       />
                                    </td>

                                    <td>
                                       <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              </tbody>
                           </table>
                        </div>
                     </div>
                  </div>


                  <!-- Here we will add the sections where we get to confirm if we want to set a min or max impact for the policy -->

                  <!-- OnlyApplyIfImpactOverValue -->
                  <div class="d-flex flex-row justify-content-center align-items-center" id="limitIncreaseDecreaseRow">
                     <div>
                        <sliderSwitch
                           [defaultValue]="factor.OnlyApplyIfImpactOverValue != null"
                           (toggle)="factor.toggleOnlyApplyIfImpactOverValue()"
                        ></sliderSwitch>
                     </div>
                     <div class="overUnderLabel">Only take effect if this factor causes an increase of more than £</div>
                     <input id="overUnderInput" [ngModelOptions]="{ standalone: true }"
                            name="OnlyApplyIfImpactOverValue" [(ngModel)]="factor.OnlyApplyIfImpactOverValue"
                            type="number"/>
                     <instructionButton
                        [note]="'Checking this row and adding a £ value means that this policy layer will only take effect if the calculated impact for the vehicle is more than the £ amount you have chosen.'"></instructionButton>
                  </div>
                  <div class="d-flex flex-row justify-content-center align-items-center" id="limitIncreaseDecreaseRow">
                     <div>
                        <sliderSwitch
                           [defaultValue]="factor.OnlyApplyIfImpactOverPercent != null"
                           (toggle)="factor.toggleOnlyApplyIfImpactOverPercent()"
                        ></sliderSwitch>
                     </div>
                     <div class="overUnderLabel">Only take effect if this factor causes an increase of more than %</div>
                     <input id="overUnderInput" [ngModelOptions]="{ standalone: true }"
                            name="OnlyApplyIfImpactOverPercent" [(ngModel)]="factor.OnlyApplyIfImpactOverPercent"
                            type="number"/>
                     <instructionButton
                        [note]="'Checking this row and adding a % value means that this policy layer will only take effect if the calculated impact for the vehicle is more than the % amount you have chosen.'"></instructionButton>
                  </div>

                  <!-- OnlyApplyIfImpactUnderValue -->
                  <div class="d-flex flex-row justify-content-center align-items-center" id="limitIncreaseDecreaseRow">
                     <div>
                        <sliderSwitch
                           [defaultValue]="factor.OnlyApplyIfImpactUnderValue != null"
                           (toggle)="factor.toggleOnlyApplyIfImpactUnderValue()"
                        ></sliderSwitch>
                     </div>
                     <div class="overUnderLabel">Only take effect if this factor causes an impact of less than £</div>
                     <input id="overUnderInput" [ngModelOptions]="{ standalone: true }"
                            name="OnlyApplyIfImpactUnderValue" [(ngModel)]="factor.OnlyApplyIfImpactUnderValue"
                            type="number"/>
                     <instructionButton
                        [note]="'Checking this row and adding a £ value means that this policy layer will only take effect if the calculated impact for the vehicle is below the £ amount you have chosen.'"></instructionButton>
                  </div>
                  <div class="d-flex flex-row justify-content-center align-items-center" id="limitIncreaseDecreaseRow">
                     <div>
                        <sliderSwitch
                           [defaultValue]="factor.OnlyApplyIfImpactUnderPercent != null"
                           (toggle)="factor.toggleOnlyApplyIfImpactUnderPercent()"
                        ></sliderSwitch>
                     </div>
                     <div class="overUnderLabel">Only take effect if this factor causes an impact of less than %</div>
                     <input id="overUnderInput" [ngModelOptions]="{ standalone: true }"
                            name="OnlyApplyIfImpactUnderPercent" [(ngModel)]="factor.OnlyApplyIfImpactUnderPercent"
                            type="number"/>
                     <instructionButton
                        [note]="'Checking this row and adding a % value means that this policy layer will only take effect if the calculated impact for the vehicle is below the % amount you have chosen.'"></instructionButton>
                  </div>


               </div>

               <!-- Remove layer option -->
               <div class="d-flex justify-content-end" id="removeLayerRow">
                  <button class="btn btn-danger" (click)="maybeDeleteFactor($event, factor)">
                     Remove this adjustment layer
                  </button>
               </div>


            </div>
         </ng-container>
      </div>

      <!-- Add another factor panel -->
      <div class="panel autotraderCard">
         <div class="cardInner">
            <div class="cardHeader">
               <div class="layerHeader d-flex justify-content-between align-items-center">
                  <!-- The icon -->
                  <div class="h2 layerIcon"></div>

                  <div class="layerName"></div>

                  <div class="layerButton">
                     <button class="btn btn-success" (click)="showAddFactorModal()">
                        Add another adjustment layer..
                     </button>
                  </div>
               </div>
            </div>
            <div class="cardBody"></div>
         </div>
      </div>
   </form>
</div>

<div class="modal-footer">
   <div
      *ngIf="showDeleteButton()"
      [ngbPopover]="canDeletePolicy() ? null : 'Policy has been used so cannot be deleted'"
      placement="top"
      container="body"
      triggers="mouseenter:mouseleave"
   >
      <button type="button" class="btn btn-danger" [disabled]="!canDeletePolicy()" (click)="deletePolicy()">
         Delete Pricing Policy
      </button>
   </div>

   <div [ngbTooltip]="formErrorTooltip" [disableTooltip]="pricingPolicyForm.valid">
      <button
         type="button"
         class="btn btn-success"
         [disabled]="pricingPolicyForm.invalid"
         (click)="savePolicy({ saveAsNew: true })"
      >
         Save As New Pricing Policy
      </button>
   </div>

   <div [ngbTooltip]="formErrorTooltip" [disableTooltip]="pricingPolicyForm.valid">
      <button
         type="button"
         class="btn btn-success"
         [disabled]="!changesMade() || pricingPolicyForm.invalid"
         (click)="savePolicy({ saveAsNew: false })"
      >
         Save Pricing Policy
      </button>
   </div>

   <button type="button" class="btn btn-primary" (click)="dismissModal(false)">Close</button>
</div>

<ng-template #formErrorTooltip>
   <div *ngFor="let error of showFormErrors()">
      <div>{{ error }}</div>
   </div>
</ng-template>

<!-- Example row template -->
<ng-template #exampleRow let-factor>
   <!-- <ng-container *ngIf="getTableConfiguration(factor)=='Competitor'">
       <tr class="exampleRow">
           <td>
               Example: search within 50 miles, be the 2nd cheapest within 1 plate step
           </td>
           <td>
               50
           </td>
           <td>
               2
           </td>
           <td>
               1
           </td>
           <td></td>
       </tr>
   </ng-container> -->
   <ng-container
      *ngIf="
         [
            StrategyFactorName.RR_DL_Matrix,
            StrategyFactorName.RR_DS_Matrix,
            StrategyFactorName.RR_DB_Matrix,
            StrategyFactorName.DTS_DL_Matrix,
            StrategyFactorName.PY_DS_Matrix,
            StrategyFactorName.VB_DS_Matrix
         ].includes(factor.Name)
      "
   >
      <tr class="exampleRow"></tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.DaysToSell">
      <tr class="exampleRow">
         <td>Example: sell within 30 days</td>
         <td>30</td>
      </tr>
   </ng-container>

   <ng-container *ngIf="[StrategyFactorName.DaysInStock,StrategyFactorName.DaysListed].includes(factor.Name)">
      <tr class="exampleRow">
         <td>Example: add 1% above the valuation for vehicles up to 10 days</td>
         <td></td>
      </tr>
      <tr class="exampleRow">
         <td>10</td>
         <td>101</td>
      </tr>
   </ng-container>

   <ng-container *ngIf="factor.Name == StrategyFactorName.RetailRating">
      <tr class="exampleRow">
         <td>Example: add 1% above the valuation for retail rating up to 20</td>
         <td></td>
      </tr>
      <tr class="exampleRow">
         <td>20</td>
         <td>101</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.PerformanceRatingScore">
      <tr class="exampleRow">
         <td>Example: take off 2% for performance rating up to 25</td>
         <td></td>
      </tr>
      <tr class="exampleRow">
         <td>0-25</td>
         <td>98</td>
      </tr>
   </ng-container>
   <ng-container
      *ngIf="
         [
            StrategyFactorName.RetailRatingBand,
            StrategyFactorName.DaysListedBand,
            StrategyFactorName.OnBrandCheck,
            StrategyFactorName.RetailerName,
            StrategyFactorName.MatchCheapestCompetitor,
            StrategyFactorName.ValuationChangeUntilSell,
            StrategyFactorName.DaysInStockBand,
            StrategyFactorName.SpecificColour,
            StrategyFactorName.AchieveMarketPositionScore,
            StrategyFactorName.WholesaleAdjustment,
            StrategyFactorName.RetailRating10sBand,
            StrategyFactorName.Brand,
            StrategyFactorName.ModelName,
            StrategyFactorName.FuelType,
            StrategyFactorName.ValueBand,
            StrategyFactorName.RegYear,
            StrategyFactorName.LiveMarketCondition,
            StrategyFactorName.SetToAdvertisedPrice
         ].includes(factor.Name)
      "
   >
      <tr class="exampleRow">
         <td>Example: add 1% above the valuation</td>
         <td>101</td>
         <td></td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.SpecificColour">
      <tr class="exampleRowSpecificColour">
         <td>Example: add 1% above the valuation for Urban Grey</td>
         <td></td>
         <td></td>
      </tr>
      <tr class="exampleRowSpecificColour">
         <td>Urban Grey</td>
         <td>101</td>
         <td></td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.AgeAndOwners">
      <tr class="exampleRowSpecificColour">
         <td>Example: add 1% above the valuation for 3-5yr old cars with 1 previous owner</td>
         <td></td>
         <td></td>
      </tr>
      <tr class="exampleRowSpecificColour">
         <td style="display: flex; gap: 10em; justify-content: center; align-items: center">
            <span>3-5yrs</span>
            <span>1</span>
         </td>
         <td>101</td>
         <td></td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.MinimumProfit">
      <tr class="exampleRow">
         <td>Example: £1,000 minimum profit</td>
         <td>1000</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.MinimumPricePosition">
      <tr class="exampleRow">
         <td>Example: 90% price position</td>
         <td>90.0</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.RoundToNearest">
      <tr class="exampleRow">
         <td>Example: nearest £50</td>
         <td>50</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.RoundToPriceBreak">
      <tr class="exampleRow">
         <td>Example: within £50</td>
         <td>50</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.MakeFuelType">
      <tr class="exampleRow">
         <td colspan="2">Example: Ford Electric take off 1%</td>
         <td>99</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.MakeAgeBand">
      <tr class="exampleRow">
         <td colspan="2">Example: Ford &lt;1 year take off 1%</td>
         <td>99</td>
      </tr>
   </ng-container>


   <ng-container *ngIf="factor.Name == StrategyFactorName.LiveMarketCondition">
      <tr class="exampleRow">
         <td>Example: Apply 99% if market condition is up to -50%</td>
         <td></td>
      </tr>
      <tr class="exampleRow">
         <td>-50</td>
         <td>99</td>
      </tr>
   </ng-container>

   <ng-container *ngIf="factor.Name == StrategyFactorName.DateRange">
      <tr class="exampleRow">
         <td>Example: Apply 99% and £10 if the current date is in range</td>
         <td>01/03/2025</td>
         <td>31/03/2025</td>
         <td>99</td>
         <td>10</td>
      </tr>
   </ng-container>


   <ng-container *ngIf="factor.Name == StrategyFactorName.Tag">
      <tr class="exampleRow">
         <td>Example: Apply 99% and £10 if the vehicle has this tag</td>
         <td>Tag 1</td>
         <td>99%</td>
         <td>£10</td>
      </tr>
   </ng-container>


</ng-template>
