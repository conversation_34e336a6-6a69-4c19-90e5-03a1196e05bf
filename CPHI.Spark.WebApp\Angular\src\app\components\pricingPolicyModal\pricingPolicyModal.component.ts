import {<PERSON><PERSON><PERSON>w<PERSON>nit, Component, ElementRef, OnInit, ViewChild} from "@angular/core";
import {NgbActiveModal, NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {CphPipe} from "src/app/cph.pipe";
import {FactorItemHorizontalBand, StrategyFactorItemVM} from "src/app/model/StrategyFactorItemVM";
import {StrategyFactorName} from "src/app/model/StrategyFactorName";
import {StrategyFactorVM} from "src/app/model/StrategyFactorVM";
import {StrategyVersionVM} from "src/app/model/StrategyVersionVM";
import {SiteSettingsService} from "src/app/pages/autoprice/siteSettings/siteSettings.service";
import {ConstantsService} from "src/app/services/constants.service";
import {GetDataMethodsService} from "src/app/services/getDataMethods.service";
import {SelectionsService} from "src/app/services/selections.service";
import {NewFactorModalComponent} from "./newFactorModal/newFactorModal.component";
import {PricingPolicyModalService} from "./pricingPolicyModal.service";
import {MaybeDeleteFactorModalComponent} from "./maybeDeleteFactorModal/maybeDeleteFactorModal.component";
import {AutotraderService} from "src/app/services/autotrader.service";
import {AbstractControl, FormGroup, NgForm} from "@angular/forms";
import {PrettyPrintEnumPipe} from "src/app/prettyPrintEnum.pipe";
import {TagDTO} from "../../model/Tag";
import {TagService} from "../../services/tag.service";
import {AGGridMethodsService} from "../../services/agGridMethods.service";

@Component({
   selector: "pricingPolicyModal",
   templateUrl: "./pricingPolicyModal.component.html",
   styleUrls: ["./pricingPolicyModal.component.scss"],
   providers: [PrettyPrintEnumPipe],
})
export class PricingPolicyBuilderModalComponent implements OnInit, AfterViewInit {
   @ViewChild("pricingPolicyForm") pricingPolicyForm: NgForm;
   @ViewChild("pricingPolicyForm", {static: false, read: ElementRef}) myFormElement!: ElementRef;

   public StrategyFactorName = StrategyFactorName;
   public chosenStrategyClone: StrategyVersionVM;
   public instructionMessage: string = `The adjustment layers below show how this pricing policy will build up a price for each vehicle.   The policy will start with the retail valuation then apply each subsequent adjustment layer in turn.`;
   public newStategyInstructionMessage: string = `Click 'Add Factor' to add an adjustment layer to this pricing policy.`;

   addFactorModalRef: NgbModalRef;
   // allStrategyFactors: StrategyFactorVM[] //for choosing new factor

   ageCategories: string[] = ["<1yrs", "1-3yrs", "3-5yrs", "5-10yrs", ">10yrs"];
   previousOwners: string[] = ["1", "2", "3", "4+"];
   private allTags: TagDTO[] = [];
   private typeaheadList: { label: string; value: number }[] = [];

   // get additionalFactors() {
   //    const existingNames = this.service.chosenPolicy.StrategyFactors.map((x) => x.Name);
   //    const additional = this.service.allStrategyFactors
   //       .filter((x) => !existingNames.includes(x.Name))
   //       .filter(
   //          (x) =>
   //             x.Name !== StrategyFactorName.WholesaleAdjustment ||
   //             this.constants.environment.showWholesaleAdjustmentOption === true
   //       );
   //    //return additional;
   //    return this.service.allStrategyFactors;
   // }

   get fuelTypes() {
      return AutotraderService.getFuelTypes;
   }

   get getAgeBands() {
      return AutotraderService.getSortOrderForAgeBand;
   }

   constructor(
      public selections: SelectionsService,
      public constants: ConstantsService,
      public siteSettingsService: SiteSettingsService,
      private modalService: NgbModal,
      private tagService: TagService,
      private getDataService: GetDataMethodsService,
      private cphPipe: CphPipe,
      private activeModal: NgbActiveModal,
      public service: PricingPolicyModalService,
      private gridHelperService: AGGridMethodsService,
      private prettyPrintPipe: PrettyPrintEnumPipe
   ) {
   }

   ngOnInit(): void {
      this.service.chosenPolicy?.StrategyFactors.forEach((strategyFactor) => {
         if (strategyFactor.Name == StrategyFactorName.AgeAndOwners) {
            strategyFactor.StrategyFactorItems.forEach((factorItem) => {
               [factorItem.selectedAgeCategory, factorItem.selectedPreviousOwners] = factorItem.Label?.split("|") || [
                  this.ageCategories[0],
                  this.previousOwners[0],
               ];
            });
         } else if (strategyFactor.Name == StrategyFactorName.MakeFuelType) {
            strategyFactor.StrategyFactorItems.forEach((factorItem) => {
               [factorItem.selectedMake, factorItem.selectedFuelType] = factorItem.Label?.split("|") || [
                  "Make",
                  "Fuel Type",
               ];
            });
         } else if (strategyFactor.Name == StrategyFactorName.MakeAgeBand) {
            strategyFactor.StrategyFactorItems.forEach((factorItem) => {
               [factorItem.selectedMake, factorItem.selectedAgeBand] = factorItem.Label?.split("|") || [
                  "Make",
                  "<1yrs",
               ];
            });
         } else if (strategyFactor.Name == StrategyFactorName.DateRange) {
            strategyFactor.StrategyFactorItems.forEach((factorItem) => {
               [factorItem.effectiveDateFrom, factorItem.effectiveDateTo] = factorItem.Label?.split("|");
            });
         } else if (strategyFactor.Name == StrategyFactorName.MakeModel) {
            strategyFactor.StrategyFactorItems.forEach((factorItem) => {
               [factorItem.selectedMake, factorItem.selectedModel] = factorItem.Label?.split("|") || [
                  "Make",
                  "Model",
               ];
            });
         }
      });
   }

   ngAfterViewInit() {
      this.refreshTagsData();
   }

   select(event: any) {
      event.target.select();
   }

   setStrategyValue(strategyFactor: StrategyFactorItemVM, event: any) {
      if (!event.target || (event.target && event.target.value == "")) return;
      let priceFactor: number = parseFloat(event.target.value);
      strategyFactor.Value = priceFactor / 100;
   }

   setCommentValue(strategyFactor: StrategyFactorItemVM, event: any) {
      if (!event.target || (event.target && event.target.value == "")) return;
      strategyFactor.Comment = event.target.value;
   }

   showNewStrategyModal() {
      let message = "Enter pricing policy name";
      this.constants.inputModal.inputModalHeader = message;

      let mySubscription = this.selections.inputModalEmitter.subscribe((text) => {
         if (text) {
            this.createNewStrategy(text);
         }

         mySubscription.unsubscribe();
      });

      this.constants.inputModal.showModal();
   }

   createNewStrategy(name: string) {
      const sv = {Name: name} as StrategyVersionVM;
      this.siteSettingsService.createStrategyVersion(sv);
   }

   async deletePolicy() {
      await this.getDataService.deleteStrategyVersion(this.service.chosenPolicy).toPromise();
      await this.constants.sleep(300);
      this.dismissModal(true);
   }

   savePolicy(options: { saveAsNew: boolean }) {
      // ensure competitor item values have been reassembled into the value
      const chosenPolicyToPersist: StrategyVersionVM = this.constants.clone(this.service.chosenPolicy);

      chosenPolicyToPersist.StrategyFactors.map((factor) => {
         if (
            factor.Name === StrategyFactorName.MatchCheapestCompetitor ||
            factor.Name === StrategyFactorName.AchieveMarketPositionScore
         ) {
            const newStrategyFactorItems: StrategyFactorItemVM[] = [];
            factor.StrategyFactorItems.map((item) => {

               const newValue: number = item.BoolValue != null ? (item.BoolValue ? 1 : 0) : item.Value;
               const newValueAmount: number = item.BoolValue != null ? (item.BoolValue ? 1 : 0) : item.ValueAmount;

               let newFactorItem = new StrategyFactorItemVM(null, item.Label, newValue, newValueAmount);
               newFactorItem.Comment = item.Comment;
               newStrategyFactorItems.push(newFactorItem);
            });

            factor.StrategyFactorItems = newStrategyFactorItems;
         }

         if (factor.Name === StrategyFactorName.RR_DL_Matrix) {
            //need to convert the values into updated labels
            //RR20|DL20
            const newStrategyFactorItems: StrategyFactorItemVM[] = [];

            factor.horizontalBandLabels.forEach((horizontalLabel, labelIndex) => {
               factor.StrategyFactorItems.map((item) => {
                  const daysListed: string = horizontalLabel.value.toString();
                  const newLabel = `RR${item.Label}|DL${daysListed}`;

                  const newValue = item.horizontalBands[labelIndex].value;
                  const newValueAmount = 0;

                  let newFactorItem = new StrategyFactorItemVM(null, newLabel, newValue, newValueAmount);
                  newFactorItem.Comment = item.Comment;
                  newStrategyFactorItems.push(newFactorItem);
               });
            });

            factor.StrategyFactorItems = newStrategyFactorItems;
         } else if (factor.Name === StrategyFactorName.RR_DS_Matrix) {
            //need to convert the values into updated labels
            //RR20|DS20
            const newStrategyFactorItems: StrategyFactorItemVM[] = [];

            factor.horizontalBandLabels.forEach((horizontalLabel, labelIndex) => {
               factor.StrategyFactorItems.map((item) => {
                  const daysInStock: string = horizontalLabel.value.toString();
                  const newLabel = `RR${item.Label}|DS${daysInStock}`;

                  const newValue = item.horizontalBands[labelIndex].value;
                  const newValueAmount = 0;

                  let newFactorItem = new StrategyFactorItemVM(null, newLabel, newValue, newValueAmount);
                  newFactorItem.Comment = item.Comment;
                  newStrategyFactorItems.push(newFactorItem);
               });
            });

            factor.StrategyFactorItems = newStrategyFactorItems;
         } else if (factor.Name === StrategyFactorName.RR_DB_Matrix) {
            //need to convert the values into updated labels
            //RR20|DB20
            const newStrategyFactorItems: StrategyFactorItemVM[] = [];

            factor.horizontalBandLabels.forEach((horizontalLabel, labelIndex) => {
               factor.StrategyFactorItems.map((item) => {
                  const daysBookedIn: string = horizontalLabel.value.toString();
                  const newLabel = `RR${item.Label}|DB${daysBookedIn}`;

                  const newValue = item.horizontalBands[labelIndex].value;
                  const newValueAmount = 0;

                  let newFactorItem = new StrategyFactorItemVM(null, newLabel, newValue, newValueAmount);
                  newFactorItem.Comment = item.Comment;
                  newStrategyFactorItems.push(newFactorItem);
               });
            });

            factor.StrategyFactorItems = newStrategyFactorItems;
         } else if (factor.Name === StrategyFactorName.DTS_DL_Matrix) {
            //need to convert the values into updated labels
            //DTS20|DL20
            const newStrategyFactorItems: StrategyFactorItemVM[] = [];

            factor.horizontalBandLabels.forEach((horizontalLabel, labelIndex) => {
               factor.StrategyFactorItems.map((item) => {
                  const daysListed: string = horizontalLabel.value.toString();
                  const newLabel = `DTS${item.Label}|DL${daysListed}`;

                  const newValue = item.horizontalBands[labelIndex].value;
                  const newValueAmount = 0;

                  let newFactorItem = new StrategyFactorItemVM(null, newLabel, newValue, newValueAmount);
                  newFactorItem.Comment = item.Comment;
                  newStrategyFactorItems.push(newFactorItem);
               });
            });

            factor.StrategyFactorItems = newStrategyFactorItems;
         } else if (factor.Name === StrategyFactorName.PY_DS_Matrix) {
            //need to convert the values into updated labels
            //PY2016|DS10
            const newStrategyFactorItems: StrategyFactorItemVM[] = [];

            factor.horizontalBandLabels.forEach((horizontalLabel, labelIndex) => {
               factor.StrategyFactorItems.map((item) => {
                  const daysInStock: string = horizontalLabel.value.toString();
                  const newLabel = `PY${item.Label}|DS${daysInStock}`;
                  const newValue = item.horizontalBands[labelIndex].value;
                  let newFactorItem = new StrategyFactorItemVM(null, newLabel, newValue, 0);
                  newFactorItem.Comment = item.Comment;
                  newStrategyFactorItems.push(newFactorItem);
               });
            });

            factor.StrategyFactorItems = newStrategyFactorItems;
         } else if (factor.Name === StrategyFactorName.VB_DS_Matrix) {
            //need to convert the values into updated labels
            //VB<£5k|DS10
            const newStrategyFactorItems: StrategyFactorItemVM[] = [];

            factor.horizontalBandLabels.forEach((horizontalLabel, labelIndex) => {
               factor.StrategyFactorItems.map((item) => {
                  const daysInStock: string = horizontalLabel.value.toString();
                  const newLabel = `VB${item.Label}|DS${daysInStock}`;
                  const newValue = item.horizontalBands[labelIndex].value;
                  let newFactorItem = new StrategyFactorItemVM(null, newLabel, newValue, 0);
                  newFactorItem.Comment = item.Comment;
                  newStrategyFactorItems.push(newFactorItem);
               });
            });

            factor.StrategyFactorItems = newStrategyFactorItems;
         } else if (factor.Name == StrategyFactorName.AgeAndOwners) {
            factor.StrategyFactorItems.map((item) => {
               item.Label = item.selectedAgeCategory + "|" + item.selectedPreviousOwners;
            });
         } else if (factor.Name == StrategyFactorName.MakeFuelType) {
            factor.StrategyFactorItems.map((item) => {
               item.Label = item.selectedMake + "|" + item.selectedFuelType;
            });
         } else if (factor.Name == StrategyFactorName.MakeAgeBand) {
            factor.StrategyFactorItems.map((item) => {
               item.Label = item.selectedMake + "|" + item.selectedAgeBand;
            });
         } else if (factor.Name == StrategyFactorName.DateRange) {
            factor.StrategyFactorItems.map((item) => {
               item.Label = item.effectiveDateFrom + "|" + item.effectiveDateTo;
            });
         } else if (factor.Name == StrategyFactorName.MakeModel) {
            factor.StrategyFactorItems.map((item) => {
               item.Label = item.selectedMake + "|" + item.selectedModel;
            });
         }
      });

      this.selections.triggerSpinner.next({show: true, message: "Saving..."});

      chosenPolicyToPersist.SaveAsNew = options?.saveAsNew;

      this.getDataService.saveStrategyVersion(chosenPolicyToPersist).subscribe(
         (res: number | null) => {
            this.constants.toastSuccess("Saved pricing policy");
            this.closeModal({pricingPolicyId: res, savedAsNew: options?.saveAsNew});
            // this.selections.triggerSpinner.next({ show: false });
         },
         (error: any) => {
            console.error("Failed to save pricing policy factor items", error);
            // this.selections.triggerSpinner.next({ show: false });
         }
      );
   }

   public toggleFactorItemBoolValue(factorItem: StrategyFactorItemVM) {
      factorItem.BoolValue = !factorItem.BoolValue;
      factorItem.Value = factorItem.BoolValue ? 1 : 0;
   }

   strategyTextSummary(strategy: StrategyVersionVM) {
      if (!strategy) {
         console.trace;
      }
      const firstUsed: string =
         strategy.FirstUsed != null ? this.cphPipe.transform(new Date(strategy.FirstUsed), "shortDate", 0) : "never";
      const lastUsed: string =
         strategy.LastUsed != null ? this.cphPipe.transform(new Date(strategy.LastUsed), "shortDate", 0) : "never";
      return `Strategy #${strategy.Id}: ${strategy.Name} created ${this.cphPipe.transform(
         new Date(strategy.CreatedDate),
         "dateMed",
         0
      )} by ${strategy.CreatedByName} first used ${firstUsed}, last used ${lastUsed}`;
   }

   public showAddFactorModal() {
      const modalRef = (this.addFactorModalRef = this.modalService.open(NewFactorModalComponent, {
         size: "md",
         keyboard: false,
         ariaLabelledBy: "modal-basic-title",
      }));

      modalRef.componentInstance.additionalFactors = this.service.allStrategyFactors;

      this.addFactorModalRef.result.then(
         () => {
         },
         () => {
            modalRef.close();
         }
      );
   }

   deleteFactor(factor: StrategyFactorVM) {
      this.service.chosenPolicy.StrategyFactors = this.service.chosenPolicy.StrategyFactors.filter(
         (x) => x.Name !== factor.Name
      );
   }

   changesMade() {
      return this.isDifferent(this.service.chosenPolicy, this.chosenStrategyClone);
   }

   canDeletePolicy() {
      return this.service.chosenPolicy.FirstUsed == null;
   }

   showDeleteButton() {
      if (this.service.chosenPolicy.Id == null) {
         return false;
      }
      return true;
   }

   isDifferent(obj1: any, obj2: any): boolean {
      // If both are not objects or are null, compare them directly
      if (typeof obj1 !== "object" || obj1 === null || typeof obj2 !== "object" || obj2 === null) {
         return obj1 !== obj2;
      }

      // Get the keys of both objects
      const keys1 = Object.keys(obj1);
      const keys2 = Object.keys(obj2);

      // If the number of keys is different, objects are different
      if (keys1.length !== keys2.length) {
         return true;
      }

      // Check if any key name is different
      for (const key of keys1) {
         if (!(key in obj2)) {
            return true;
         }
      }

      // Recursively compare the values of the keys
      for (const key of keys1) {
         if (this.isDifferent(obj1[key], obj2[key])) {
            return true;
         }
      }

      // If none of the above checks returned true, objects are the same
      return false;
   }

   closeModal(res: { pricingPolicyId: number; savedAsNew?: boolean } | {}) {
      this.activeModal.close(res);
   }

   public dismissModal(didWeDelete: boolean) {
      this.activeModal.dismiss(didWeDelete);
   }

   strategyChoiceName(factor: StrategyFactorVM) {
      if (
         factor.Name === StrategyFactorName.MatchCheapestCompetitor ||
         factor.Name === StrategyFactorName.AchieveMarketPositionScore
      ) {
         return "Position Number";
      }
      if (factor.Name === StrategyFactorName.DaysToSell) {
         return "No. of Days";
      }
      return "Impact %";
   }

   pasteMatrixNote(): string {
      return `It is possible to copy and paste directly from excel into this matrix. Paste a matrix with headers: first row should contain horizontal thresholds, first column should contain vertical labels, and the rest should contain percentage values. The matrix dimensions will be automatically determined from your pasted data. Right click on this input box and choose 'Paste'.`;
   }

   pasteEditableLabelNote(): string {
      return `It is possible to copy and paste directly from excel into this list, simply right click on this input box and choose 'Paste'.`;
   }

   radiusNote(): string {
      return "The number of miles around the dealer in which to search for competitors.";
   }

   plateStepsNote(): string {
      return "The number of plate steps either side of the advert to search, e.g. 1 plate either way from a 20 would search between 69 and 70.";
   }

   mileageStepsNote(): string {
      return "The amount of odometer miles to search either side of the advert.";
   }

   rankingToAchieveNote(): string {
      return "The desired position you would like to achieve amongst the competitor set.   e.g. 2 would set the strategy price to make you the 2nd cheapest seller.    If, for example, you chose 5 and there are only 2 competitors, this strategy layer will have no effect.";
   }

   marketPositionScoreToAchieveNote(): string {
      return "The desired market position you would like to achieve amongst the competitor set.   For example if there are 50 competitors and you choose to achieve 90 this strategy layer will impact the strategy price down to ensure you are the 5th cheapest seller.";
   }

   pricePositionNote(): string {
      return "The desired PP% you would like to achieve relative to the market average.  For example if when the average competitor PP% was 98.0% you would like to be at 97.5%, enter -0.5 in this box.";
   }

   sellerTypeNote(): string {
      return "Whether to include each of these seller channels when searching for competitors.";
   }

   wholesaleAdjustmentPctNote(): string {
      return "Amount to adjust strategy price. For example entering 94% the strategy price will be multiplied by 94%";
   }

   wholesaleAdjustmentValueNote(): string {
      return "Amount to adjust strategy price. For example entering 700 the strategy price will be increased by £700";
   }

   makeFuelTypeNote(): string {
      return `The make and fuel type of the vehicle that the strategy is being applied to.`;
   }

   makeModelNote(): string {
      return `The make and model of the vehicle that the strategy is being applied to.`;
   }

   makeAgeBandNote(): string {
      return `The make and age band of the vehicle that the strategy is being applied to.`;
   }

   getTableEditableColumnCount(factor: StrategyFactorVM): number {
      if (factor.Name === StrategyFactorName.DaysListed) {
         return 3;
      }
      return 1;
   }

   updateLabel(factorItem: any): void {
      factorItem.Label = `${factorItem.selectedAgeCategory}|${factorItem.selectedPreviousOwners}`;
   }

   updateAgeAndOwnersLabelForAge(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      const selectedText = selectElement.options[selectElement.selectedIndex].text;
      // console.log("UPDATING AGE BAND TO " + selectedText);
      factorItem.selectedAgeCategory = selectedText.toLowerCase().startsWith("select") ? "" : selectedText;
      factorItem.Label = `${factorItem.selectedAgeCategory}|${factorItem.selectedPreviousOwners}`;
   }

   updateAgeAndOwnersLabelForOwners(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      const selectedText = selectElement.options[selectElement.selectedIndex].text;
      factorItem.selectedPreviousOwners = selectedText.toLowerCase().startsWith("select") ? "" : selectedText;
      factorItem.Label = `${factorItem.selectedAgeCategory}|${factorItem.selectedPreviousOwners}`;
   }

   updateFuelType(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      const selectedText = selectElement.options[selectElement.selectedIndex].text;
      factorItem.selectedFuelType = selectedText.toLowerCase().startsWith("select") ? "" : selectedText;
      factorItem.Label = `${factorItem.selectedMake}|${factorItem.selectedFuelType}`;
   }


   updateAgeBand(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      const selectedText = selectElement.options[selectElement.selectedIndex].text;
      factorItem.selectedAgeBand = selectedText.toLowerCase().startsWith("select") ? "" : selectedText;
      factorItem.Label = `${factorItem.selectedMake}|${factorItem.selectedAgeBand}`;
   }

   updateDateRange(event: Event, factorItem: StrategyFactorItemVM) {
      factorItem.Label = `${factorItem.effectiveDateFrom}|${factorItem.effectiveDateTo}`;
   }

   updateTag(event, factor: StrategyFactorVM, factorItem: StrategyFactorItemVM) {

      if (event) {
         const tag = this.allTags.find(x => x.Id == event.value);
         factorItem.tag = tag;
         factorItem.Label = tag.Id.toString();
      }
   }

   chosenTags(factor: StrategyFactorVM): number[] {
      if (!factor || !factor.StrategyFactorItems) {
         return [];
      }
      return factor.StrategyFactorItems.map(x => x.tag).filter(x => x != null).map(x => x.Id);
   }

   updateMakeForMakeAndFuelType(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      factorItem.selectedMake = selectElement.value;
      factorItem.Label = `${factorItem.selectedMake}|${factorItem.selectedFuelType}`;
   }

   updateMakeForMakeAndModel(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      factorItem.selectedMake = selectElement.value;
      factorItem.Label = `${factorItem.selectedMake}|${factorItem.selectedModel}`;
   }

   updateModel(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      factorItem.selectedModel = selectElement.value;
      factorItem.Label = `${factorItem.selectedMake}|${factorItem.selectedModel}`;
   }

   updateMakeForMakeAndAgeBand(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      factorItem.selectedMake = selectElement.value;
      factorItem.Label = `${factorItem.selectedMake}|${factorItem.selectedAgeBand}`;
   }

   currency() {
      return this.constants.environment.displayCurrency;
   }

   public maybeDeleteFactor(event: MouseEvent, factor: StrategyFactorVM) {
      event.stopPropagation();

      const modalRef = this.modalService.open(MaybeDeleteFactorModalComponent, {
         size: "sm",
         keyboard: false,
         ariaLabelledBy: "modal-basic-title",
      });
      const modalComponent: MaybeDeleteFactorModalComponent = modalRef.componentInstance;
      modalComponent.title = "Are you sure you want to remove this adjustment layer?";

      modalRef.result.then(
         () => {
            //chose ok:
            this.deleteFactor(factor);
         },
         () => {
            //chosen cancel:
            //modalRef.nativeElement.close()
            //this.modalService.dismissAll();
         }
      );
   }

   public maybeDeleteFactorItem(event: MouseEvent, factor: StrategyFactorVM, index: number) {
      event.stopPropagation();
      const modalRef = this.modalService.open(MaybeDeleteFactorModalComponent, {
         size: "sm",
         keyboard: false,
         ariaLabelledBy: "modal-basic-title",
      });
      const modalComponent: MaybeDeleteFactorModalComponent = modalRef.componentInstance;
      modalComponent.title = "Are you sure you want to remove this item?";

      modalRef.result.then(
         () => {
            this.deleteFactorItem(factor, index);
         },
         () => {
            //modalRef.nativeElement.close()
            //this.modalService.dismissAll();
         }
      );
   }

   public maybeDeleteHorizontalBand(event: MouseEvent, factor: StrategyFactorVM, index: number) {
      event.stopPropagation();
      //let modalRef = this.maybeDeleteModal;
      const modalRef = this.modalService.open(MaybeDeleteFactorModalComponent, {
         size: "sm",
         keyboard: false,
         ariaLabelledBy: "modal-basic-title",
      });
      const modalComponent: MaybeDeleteFactorModalComponent = modalRef.componentInstance;
      modalComponent.title = "Are you sure you want to remove this band?";
      modalRef.result.then(
         () => {
            this.deleteHorizontalBand(factor, index);
         },
         () => {
            //modalRef.nativeElement.close()
            //this.modalService.dismissAll();
         }
      );
   }

   deleteFactorItem(factor: StrategyFactorVM, index: number) {
      factor.StrategyFactorItems.splice(index, 1);
   }

   deleteHorizontalBand(factor: StrategyFactorVM, index: number) {
      factor.horizontalBandLabels.splice(index, 1);
      factor.StrategyFactorItems.map((item) => {
         item.horizontalBands.splice(index, 1);
      });
   }

   addHorizontalBand(event: MouseEvent, factor: StrategyFactorVM, index: number, increment: number) {
      const lastVal = factor.horizontalBandLabels[index - 1].value;
      let nextVal = lastVal + increment;
      if (index == factor.horizontalBandLabels.length) {
         nextVal = 999;
      }
      factor.horizontalBandLabels.splice(index, 0, {value: nextVal});
      factor.StrategyFactorItems.map((item) => {
         item.horizontalBands.splice(index, 0, {id: null, value: 100});
      });
   }

   addFactorItem(event: MouseEvent, factor: StrategyFactorVM, index: number) {
      if (
         factor.Name === StrategyFactorName.RR_DL_Matrix ||
         factor.Name === StrategyFactorName.RR_DS_Matrix ||
         factor.Name === StrategyFactorName.RR_DB_Matrix ||
         factor.Name === StrategyFactorName.DTS_DL_Matrix ||
         factor.Name === StrategyFactorName.PY_DS_Matrix ||
         factor.Name === StrategyFactorName.VB_DS_Matrix
      ) {
         const newBandingValues: FactorItemHorizontalBand[] = [];
         factor.horizontalBandLabels.forEach((label) => {
            newBandingValues.push({id: null, value: 100});
         });

         //workout the label
         const lastLabel = parseInt(factor.StrategyFactorItems[index - 1].Label);
         let nextLabel = lastLabel + 10;
         if (index == factor.StrategyFactorItems.length) {
            nextLabel = 100;
         }

         const newItem: StrategyFactorItemVM = new StrategyFactorItemVM(
            factor.Id,
            nextLabel.toString(),
            100,
            0, // currencyValue
            newBandingValues
         );
         factor.StrategyFactorItems.splice(index, 0, newItem);
      } else if (factor.Name === StrategyFactorName.DaysListed || factor.Name === StrategyFactorName.DaysInStock) {
         //workout the label
         const lastLabel = parseInt(factor.StrategyFactorItems[index].Label);
         let nextLabel = lastLabel + 10;

         const lastValue = factor.StrategyFactorItems[index].Value;

         const newItem: StrategyFactorItemVM = new StrategyFactorItemVM(
            factor.Id,
            nextLabel.toString(),
            lastValue,
            0, // lastValueAmount
         );

         factor.StrategyFactorItems.splice(index + 1, 0, newItem);
      } else if (factor.Name === StrategyFactorName.Mileage) {
         //workout the label
         const lastLabel = parseInt(factor.StrategyFactorItems[index].Label.replace(/\D/g, '')); //remove non-digits
         let nextLabel = `<${lastLabel + 10}`;

         const lastValue = factor.StrategyFactorItems[index].Value;

         const newItem: StrategyFactorItemVM = new StrategyFactorItemVM(factor.Id, nextLabel, lastValue, 0);
         factor.StrategyFactorItems.splice(index + 1, 0, newItem);
      } else if (factor.Name === StrategyFactorName.RetailRating) {
         //workout the label - similar to DaysListed but for retail rating (1-100)
         const lastLabel = parseInt(factor.StrategyFactorItems[index].Label);
         let nextLabel = Math.min(lastLabel + 10, 100); // Cap at 100 for retail rating

         const lastValue = factor.StrategyFactorItems[index].Value;

         const newItem: StrategyFactorItemVM = new StrategyFactorItemVM(
            factor.Id,
            nextLabel.toString(),
            lastValue,
            0 // Last CurrencyValue
         );

         factor.StrategyFactorItems.splice(index + 1, 0, newItem);
      } else {
         const newItem: StrategyFactorItemVM = new StrategyFactorItemVM(
            factor.Id,
            null,
            100,
            0, // currencyValue
         );
         factor.StrategyFactorItems.push(newItem);
      }
      factor.limitMaximumFactorItem();
   }

   showWarningWrongFinishPoint(factor: StrategyFactorVM) {
      return factor.horizontalBandLabels[factor.horizontalBandLabels.length - 1].value !== 999;
   }

   handlePasteDaysListed(event: ClipboardEvent, factor: StrategyFactorVM) {
      event.preventDefault();

      // Get the clipboard data as a string
      const {gridData, expectedColumns} = this.interpretDroppedDataForDayListed(event, factor);

      // Validate the size of the pasted data
      if (gridData[0].length !== expectedColumns) {
         this.constants.toastDanger("It was not possible to update the grid based on the pasted in data");
         return;
      }
      factor.StrategyFactorItems = [];
      // Replace the grid data with the pasted data
      for (let i = 0; i < gridData.length; i++) {
         factor.StrategyFactorItems.push(new StrategyFactorItemVM(
            factor.Id,
            gridData[i][0],
            parseFloat(gridData[i][1]),
            0));
      }
      const minValue = Math.min(...factor.StrategyFactorItems.map((item) => item.Value));
      factor.StrategyFactorItems.push(new StrategyFactorItemVM(factor.Id, '999', minValue, 0, null, true));

      this.constants.toastSuccess("Grid percentages have been updated");
   }

   handlePasteLiveMarketCondition(event: ClipboardEvent, factor: StrategyFactorVM) {
      event.preventDefault();

      // Get the clipboard data as a string
      const {gridData, expectedColumns} = this.interpretDroppedDataForLiveMarketCondition(event, factor);

      // Validate the size of the pasted data (should be 2 columns)
      if (gridData.some(row => row.length !== expectedColumns)) {
         this.constants.toastDanger("Each row must have exactly 2 columns (label and value)");
         return;
      }

      // Validate that all values in the second column are numeric
      const invalidRows: number[] = [];
      gridData.forEach((row, index) => {
         const value = parseFloat(row[1]);
         if (isNaN(value)) {
            invalidRows.push(index + 1);
         }
      });

      if (invalidRows.length > 0) {
         this.constants.toastDanger(`Invalid numeric values found in rows: ${invalidRows.join(', ')}. All values must be numeric.`);
         return;
      }

      // Clear existing items and add new ones
      factor.StrategyFactorItems = [];

      gridData.forEach((row) => {
         const label = row[0];
         const value = parseFloat(row[1]);
         factor.StrategyFactorItems.push(new StrategyFactorItemVM(factor.Id, label, value, 0));
      });

      this.constants.toastSuccess("Live Market Condition rows have been updated");
   }

   handlePasteRetailRating(event: ClipboardEvent, factor: StrategyFactorVM) {
      event.preventDefault();

      // Get the clipboard data as a string
      const {gridData, expectedColumns} = this.interpretDroppedDataForRetailRating(event, factor);

      // Validate the size of the pasted data
      if (gridData[0].length !== expectedColumns) {
         this.constants.toastDanger("It was not possible to update the grid based on the pasted in data");
         return;
      }
      factor.StrategyFactorItems = [];
      // Replace the grid data with the pasted data
      for (let i = 0; i < gridData.length; i++) {
         factor.StrategyFactorItems.push(new StrategyFactorItemVM(factor.Id, gridData[i][0], parseFloat(gridData[i][1]), 0));
      }
      // const minValue = Math.min(...factor.StrategyFactorItems.map((item) => item.Value));
      // factor.StrategyFactorItems.push(new StrategyFactorItemVM(factor.Id, '100', minValue , null, true));

      this.constants.toastSuccess("Grid percentages have been updated");
   }

   // Method to handle the paste event
   handlePasteMatrix(event: ClipboardEvent, factor: StrategyFactorVM) {
      event.preventDefault();

      // Check if this is a matrix type that needs enhanced parsing with headers
      if (factor.Name === StrategyFactorName.VB_DS_Matrix ||
         factor.Name === StrategyFactorName.PY_DS_Matrix ||
         factor.Name === StrategyFactorName.RR_DL_Matrix ||
         factor.Name === StrategyFactorName.RR_DS_Matrix ||
         factor.Name === StrategyFactorName.RR_DB_Matrix ||
         factor.Name === StrategyFactorName.DTS_DL_Matrix) {
         this.handlePasteMatrixWithLabels(event, factor);
         return;
      }

      // Get the clipboard data as a string
      const {gridData, expectedRows, expectedColumns} = this.interpretDroppedData(event, factor);

      // Validate the size of the pasted data
      if (gridData.length !== expectedRows || gridData[0].length !== expectedColumns) {
         this.constants.toastDanger("It was not possible to update the grid based on the pasted in data");
         return;
      }

      // Replace the grid data with the pasted data
      for (let i = 0; i < expectedRows; i++) {
         for (let j = 0; j < expectedColumns; j++) {
            if (factor.StrategyFactorItems[i].horizontalBands) {
               factor.StrategyFactorItems[i].horizontalBands[j].value = parseFloat(gridData[i][j]);
            } else {
               factor.StrategyFactorItems[i].Value = parseFloat(gridData[i][j]);
            }
         }
      }

      this.constants.toastSuccess("Grid percentages have been updated");
   }

   // Enhanced method to handle paste for matrix types with labels
   private handlePasteMatrixWithLabels(event: ClipboardEvent, factor: StrategyFactorVM) {
      const clipboardData = event.clipboardData;
      const pastedText = clipboardData.getData("text");

      // Split the clipboard data into rows and cells
      const rows = pastedText.split("\n").filter((row) => row.trim().length > 0);
      const gridData = rows.map((row) => {
         const cells = row.split("\t");
         // Clean up the data
         return cells.map(cell => {
            let cleanedCell = cell.trim();
            // Strip out % and , symbols
            cleanedCell = cleanedCell.replace(/[%,]/g, '');
            // Convert standalone '-' to '0'
            if (cleanedCell === '-') {
               cleanedCell = '0';
            }
            return cleanedCell;
         });
      });

      // Validate minimum size (at least 2x2 including headers)
      if (gridData.length < 2 || gridData[0].length < 2) {
         this.constants.toastDanger("Matrix must be at least 2x2 including headers");
         return;
      }

      // Extract headers and data
      const headerRow = gridData[0]; // First row contains horizontal thresholds
      const horizontalThresholds = headerRow.slice(1); // Skip first cell (empty corner)

      // Extract vertical labels and percentage data
      const matrixRows = gridData.slice(1); // Skip header row
      const verticalLabels = matrixRows.map(row => row[0]); // First column
      const percentageData = matrixRows.map(row => row.slice(1)); // Skip first column

      // Validate data dimensions
      if (percentageData.some(row => row.length !== horizontalThresholds.length)) {
         this.constants.toastDanger("All data rows must have the same number of columns as the header");
         return;
      }

      // Update horizontal band labels based on matrix type
      factor.horizontalBandLabels = horizontalThresholds.map(threshold => ({
         value: parseInt(threshold) || 0
      }));

      // Update strategy factor items with vertical labels and percentage data
      factor.StrategyFactorItems = verticalLabels.map((label, rowIndex) => {
         const horizontalBands = percentageData[rowIndex].map(percentage => ({
            id: null,
            value: parseFloat(percentage) || 100
         }));

         return new StrategyFactorItemVM(null, label, 0, 0, horizontalBands);
      });

      // Determine success message based on matrix type
      let matrixType = "";
      if (factor.Name === StrategyFactorName.RR_DL_Matrix) {
         matrixType = "Retail Rating vs Days Listed";
      } else if (factor.Name === StrategyFactorName.RR_DS_Matrix) {
         matrixType = "Retail Rating vs Days In Stock";
      } else if (factor.Name === StrategyFactorName.RR_DB_Matrix) {
         matrixType = "Retail Rating vs Days Booked In";
      } else if (factor.Name === StrategyFactorName.DTS_DL_Matrix) {
         matrixType = "Days To Sell vs Days Listed";
      } else if (factor.Name === StrategyFactorName.VB_DS_Matrix) {
         matrixType = "Price Band vs Days In Stock";
      } else if (factor.Name === StrategyFactorName.PY_DS_Matrix) {
         matrixType = "Plate Year vs Days In Stock";
      }

      this.constants.toastSuccess(`${matrixType} matrix labels and percentages have been updated`);
   }

   // Method to handle the paste event
   handlePasteEditableLabel(event: ClipboardEvent, factor: StrategyFactorVM) {
      event.preventDefault();

      // Use specific interpretation for LiveMarketCondition
      if (factor.Name === StrategyFactorName.LiveMarketCondition) {
         this.handlePasteLiveMarketCondition(event, factor);
         return;
      }

      // Use specific interpretation for MakeModel
      if (factor.Name === StrategyFactorName.MakeModel) {
         this.handlePasteMakeModel(event, factor);
         return;
      }

      // Get the clipboard data as a string
      const clipboardData = event.clipboardData; // || window.clipboardData;
      const pastedText = clipboardData.getData("text");

      // Split the clipboard data into rows and cells
      const rows = pastedText.split("\n").filter((row) => row.trim().length > 0);
      const gridData = rows.map((row) => row.split("\t"));

      // Validate the size of the pasted data
      factor.StrategyFactorItems = [];

      gridData.forEach((row) => {
         factor.StrategyFactorItems.push(
            new StrategyFactorItemVM(
               factor.Id,
               row[0],
               parseFloat(row[1]),
               parseFloat(row[2]),
            ));
      });

      this.constants.toastSuccess("Rows have been updated");
   }

   // Method to handle paste event specifically for MakeModel factor
   handlePasteMakeModel(event: ClipboardEvent, factor: StrategyFactorVM) {
      // Get the clipboard data as a string
      const clipboardData = event.clipboardData;
      const pastedText = clipboardData.getData("text");

      // Split the clipboard data into rows and cells
      const rows = pastedText.split("\n").filter((row) => row.trim().length > 0);
      const gridData = rows.map((row) => row.split("\t"));

      // Clear existing items
      factor.StrategyFactorItems = [];

      // Process each row: expecting Make, Model, Impact%
      gridData.forEach((row) => {
         if (row.length >= 3) {
            const make = row[0]?.trim() || '';
            const model = row[1]?.trim() || '';
            const impact = parseFloat(row[2]) || 100;

            const factorItem = new StrategyFactorItemVM(factor.Id, `${make}|${model}`, impact, null);
            factorItem.selectedMake = make;
            factorItem.selectedModel = model;

            factor.StrategyFactorItems.push(factorItem);
         }
      });

      this.constants.toastSuccess("Make and model rows have been updated");
   }

   private interpretDroppedData(event: ClipboardEvent, factor: StrategyFactorVM) {
      const clipboardData = event.clipboardData; // || window.clipboardData;
      const pastedText = clipboardData.getData("text");

      // Split the clipboard data into rows and cells
      const rows = pastedText.split("\n").filter((row) => row.trim().length > 0);
      const gridData = rows.map((row) => row.split("\t"));

      const expectedRows = factor.StrategyFactorItems.length;
      const expectedColumns = factor.StrategyFactorItems[0]?.horizontalBands?.length || 1;
      return {gridData, expectedRows, expectedColumns};
   }

   private interpretDroppedDataForDayListed(event: ClipboardEvent, factor: StrategyFactorVM) {
      const clipboardData = event.clipboardData; // || window.clipboardData;
      const pastedText = clipboardData.getData("text");

      // Split the clipboard data into rows and cells
      const rows = pastedText.split("\n").filter((row) => row.trim().length > 0);
      const gridData = rows.map((row) => row.split("\t"));
      const expectedColumns = 2
      return {gridData, expectedColumns};
   }

   private interpretDroppedDataForRetailRating(event: ClipboardEvent, factor: StrategyFactorVM) {
      const clipboardData = event.clipboardData; // || window.clipboardData;
      const pastedText = clipboardData.getData("text");

      // Split the clipboard data into rows and cells
      const rows = pastedText.split("\n").filter((row) => row.trim().length > 0);
      const gridData = rows.map((row) => row.split("\t"));
      const expectedColumns = 2;
      return {gridData, expectedColumns};
   }

   private interpretDroppedDataForLiveMarketCondition(event: ClipboardEvent, factor: StrategyFactorVM) {
      const clipboardData = event.clipboardData; // || window.clipboardData;
      const pastedText = clipboardData.getData("text");

      // Split the clipboard data into rows and cells
      const rows = pastedText.split("\n").filter((row) => row.trim().length > 0);
      const gridData = rows.map((row) => {
         const cells = row.split("\t");
         // Clean up the data
         return cells.map(cell => {
            let cleanedCell = cell.trim();
            // Strip out % and , symbols
            cleanedCell = cleanedCell.replace(/[%,]/g, '');
            // Convert standalone '-' to '0'
            if (cleanedCell === '-') {
               cleanedCell = '0';
            }
            return cleanedCell;
         });
      });
      const expectedColumns = 2
      return {gridData, expectedColumns};
   }

   disableAddFactorButtonForEditableFactorItem(factorItem: StrategyFactorItemVM) {
      try {
         const factorMax = factorItem.Label.split("-")[1];
         if (factorMax == "999" || factorItem.IsReadOnly === true) {
            return true;
         }
      } catch (error) {
      }

      return false;
   }

   // Method to check if DaysInStock/DaysListed label should be readonly
   shouldDaysLabelBeReadonly(factorItem: StrategyFactorItemVM): boolean {
      if (!factorItem || !factorItem.Label) {
         return false;
      }

      const label = factorItem.Label.toString().trim();

      // Check if label is exactly "999" or ends with "-999"
      return label === "999" || label.endsWith("-999");
   }

   colourHorizontalBandRed(factor: StrategyFactorVM, bandLabelIndex: number, labelValue: number) {
      if (bandLabelIndex == 0) {
         return false;
      }
      if (isNaN(labelValue)) {
         return true;
      }
      const lastLabelValue = factor.horizontalBandLabels[bandLabelIndex - 1].value;
      return lastLabelValue >= labelValue;
   }

   colourVerticalBandRed(factor: StrategyFactorVM, bandLabelIndex: number, labelValue: string) {
      if (bandLabelIndex == 0) {
         return false;
      }
      const labelAsNumber = parseInt(labelValue);
      // if(this.constants.hasAnyNonNumericCharacter(labelValue)){return true;}
      if (isNaN(labelAsNumber)) {
         return true;
      }
      const lastLabelValue = parseInt(factor.StrategyFactorItems[bandLabelIndex - 1].Label);
      return lastLabelValue >= labelAsNumber;
   }

   public showFormErrors(): string[] {
      if (!this.pricingPolicyForm) {
         return [];
      }

      if (this.pricingPolicyForm.valid) {
         return [];
      }

      const ppf = this.pricingPolicyForm.controls.pricingPolicyGroup as FormGroup;

      const errors: string[] = [];

      Object.keys(ppf.controls).forEach((controlName) => {
         const control = ppf.controls[controlName];

         if (control.errors) {
            const controlErrors = this.getErrorMessages(controlName, control);

            errors.push(...controlErrors);
         }
      });

      return errors;
   }

   private getErrorMessages(controlName: string, control: AbstractControl): string[] {
      const errors = control.errors;
      const messages: string[] = [];

      const controlNameItems = controlName.split(":");

      let name = controlNameItems[0].replace(/(-|_)/g, " ").trim();

      let grid = "";

      if (controlNameItems.length > 1) {
         let gridItems = [];

         const errorCell = controlNameItems[1].split("-");

         if (errorCell.length > 1 && errorCell[1] != "X") {
            gridItems.push("row " + (parseInt(errorCell[1], 10) + 1));
         }

         if (errorCell.length > 0 && errorCell[0] != "X") {
            gridItems.push("column " + (parseInt(errorCell[0], 10) + 1));
         }

         if (gridItems.length > 0) {
            grid = " (" + gridItems.join(", ") + ") ";
         }
      }

      name = this.prettyPrintPipe.transform(name, true);

      if (errors.required) {
         messages.push(name + grid + " is required.");
      } else if (errors.min) {
         messages.push(name + grid + " must be greater than or equal to " + errors.min.min);
      } else if (errors.max) {
         messages.push(name + grid + " must be less than or equal to " + errors.max.max);
      } else {
         messages.push(name + grid + " invalid value");
      }

      return messages;
   }

   // This applies the same logic as the class that colours cells, but also sets an error attribute
   // to the "offending" input to render the form invalid
   onHorizontalBandValueChange(factor, factorIndex, bandLabelIndex, labelValue) {
      const controlName = "bandLabel-value:" + factorIndex + "-" + bandLabelIndex;

      const control = (this.pricingPolicyForm.controls.pricingPolicyGroup as FormGroup).controls[controlName];

      if (bandLabelIndex == 0) {
         control.setErrors(null);
         return false;
      }
      if (isNaN(labelValue)) {
         control.setErrors({notANumber: true});
         return true;
      }

      const lastLabelValue = factor.horizontalBandLabels[bandLabelIndex - 1].value;

      if (lastLabelValue >= labelValue) {
         control.setErrors({notANumber: true});
         return false;
      } else {
         control.setErrors(null);
         return true;
      }
   }

   // This applies the same logic as the class that colours cells, but also sets an error attribute
   // to the "offending" input to render the form invalid
   onVerticalBandValueChange(factor: StrategyFactorVM, bandLabelIndex: number, labelValue: string) {
      const controlName = factor.Name + "-label:" + bandLabelIndex;

      const control = (this.pricingPolicyForm.controls.pricingPolicyGroup as FormGroup).controls[controlName];

      if (bandLabelIndex == 0) {
         control.setErrors(null);
         return false;
      }
      const labelAsNumber = parseInt(labelValue);

      if (isNaN(labelAsNumber)) {
         control.setErrors({notANumber: true});
         return true;
      }

      const lastLabelValue = parseInt(factor.StrategyFactorItems[bandLabelIndex - 1].Label);

      if (lastLabelValue >= labelAsNumber) {
         control.setErrors({notANumber: true});
         return false;
      } else {
         control.setErrors(null);
         return true;
      }
   }

   trackByIndex(index: number, _item: any): number {
      return index;
   }

   shouldDisableRowThreshold(factor: StrategyFactorVM, index: number): boolean {
      if (factor.Name == StrategyFactorName.PY_DS_Matrix) {
         return false;
      } else if (factor.Name == StrategyFactorName.VB_DS_Matrix) {
         return false;
      } else {
         return factor.StrategyFactorItems.length - 1 == index;
      }

   }

   protected readonly HTMLInputElement = HTMLInputElement;
   selectedTag: TagDTO;

   private refreshTagsData() {

      // Refresh the tags from tag service
      this.tagService.search().then((tags) => {

         this.allTags = tags;//.filter(x => x.StrategyImpactAmount == 0 && x.StrategyImpactPct == 100);

         this.constants.tags = this.allTags; // Update the constants service as well

         this.typeaheadList = this.allTags.map(x => ({
            label: this.tagService.renderSingleTag(x.Id),
            value: x.Id
         }));

         this.service.chosenPolicy?.StrategyFactors.forEach(factor => {
            if (factor.Name == StrategyFactorName.Tag) {
               factor.StrategyFactorItems.forEach(factorItem => {
                  factorItem.tag = this.allTags.find(x => x.Id == parseInt(factorItem.Label));
               });
            }
         });
      });
   }

   filteredTypeaheadTagList(factor: StrategyFactorVM) {

      // TODO: If we're allowing multiple tag selection, remove those already chosen
      if (!this.typeaheadList || !factor) {
         return [];
      }

      return this.typeaheadList.filter(x => !this.chosenTags(factor).includes(x.value));
   }
}
