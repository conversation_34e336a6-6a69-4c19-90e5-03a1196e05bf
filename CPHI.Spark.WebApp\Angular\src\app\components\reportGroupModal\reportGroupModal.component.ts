import {Component, OnInit, ViewChild, TemplateRef} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {AutoPriceReportGroup} from '../../model/AutoPriceReportGroup';
import {ReportGroupService} from '../../services/report-group.service';
import {ConstantsService} from '../../services/constants.service';
import {ConfirmationModalComponent} from '../../pages/fleetOrderbook/confirmationModal/confirmationModal.component';
import {UserAndEmail} from '../../model/UserAndEmail';
import {BehaviorSubject} from 'rxjs';
import { UserMaintenanceService } from 'src/app/pages/userMaintenance/userMaintenance.service';

@Component({
   selector: 'app-report-group-modal',
   templateUrl: './reportGroupModal.component.html',
   styleUrls: ['./reportGroupModal.component.scss']
})
export class ReportGroupModalComponent implements OnInit {
   @ViewChild('groupDetailsModal') groupDetailsModal: TemplateRef<any>;

   reportGroups: AutoPriceReportGroup[] = [];
   people: UserAndEmail[] = [];
   selectedGroup: AutoPriceReportGroup | null = null;
   groupForm: FormGroup;
   isEditing = false;
   isLoading = false;
   detailsModalRef: any;
   peopleList: { label: string, value: number }[] = [];

   selectedPeople$: BehaviorSubject<number[]> = new BehaviorSubject<number[]>([]);
   // peopleChosen: UserAndEmail[] = [];
   peopleChosen$: BehaviorSubject<UserAndEmail[]> = new BehaviorSubject<UserAndEmail[]>([]);

   constructor(
      private reportGroupService: ReportGroupService,
      private userMaintenanceService: UserMaintenanceService,
      private constantsService: ConstantsService,
      private modalService: NgbModal,
      private fb: FormBuilder,
      public activeModal: NgbActiveModal
   ) {
      this.groupForm = this.fb.group({
         groupName: ['', [Validators.required, Validators.maxLength(60)]],
         selectedPeople: [[]]
      });
   }

   ngOnInit(): void {
      this.loadReportGroups();
      this.loadPeople();

      this.selectedPeople$.subscribe((selectedPeople) => {
         this.peopleChosen$.next(this.people.filter(p => selectedPeople.includes(p.Id)));
         this.groupForm.patchValue({selectedPeople});
      });
   }

   loadReportGroups(): void {
      this.isLoading = true;
      this.reportGroupService.getReportGroups()
         .subscribe(
            (groups) => {
               this.reportGroups = groups;
               this.isLoading = false;
            },
            (error) => {
               console.error('Error loading report groups:', error);
               this.constantsService.toastDanger('Failed to load report groups');
               this.isLoading = false;
            }
         );
   }

   loadPeople(): void {
      this.userMaintenanceService.getUsersAndEmailObservable().subscribe(
         (people) => {
            this.people = people;
            this.peopleList = people.map(p => ({label: p.Name, value: p.Id})).sort((a, b) => a.label.localeCompare(b.label));
         },
         (error) => {
            console.error('Error loading users:', error);
            this.constantsService.toastDanger('Failed to load users');
         }
      );
   }

   openGroupDetailsModal(group: AutoPriceReportGroup | null): void {
      if (group) {
         // Edit existing group
         this.selectedGroup = group;
         this.isEditing = true;

         // Set form values
         this.groupForm.patchValue({
            groupName: group.GroupName,
         });

         this.selectedPeople$.next(group.People?.map(u => u.PersonId));

      } else {
         // Create new group
         this.createNewGroup();
      }

      // Open the modal
      this.detailsModalRef = this.modalService.open(this.groupDetailsModal, {
        // backdrop: 'static',
         keyboard: false,
         size: 'sm'
      });
   }

   createNewGroup(): void {
      this.selectedGroup = null;
      this.isEditing = false;
      this.selectedPeople$.next([]);
      this.groupForm.reset({
         groupName: '',
         selectedPeople: []
      });
   }

   saveGroup(): void {
      if (this.groupForm.invalid) {
         this.constantsService.toastDanger('Please fill in all required fields');
         return;
      }

      const formValues = this.groupForm.value;
      const groupData = {
         groupName: formValues.groupName,
         personIds: formValues.selectedPeople || []
      };

      this.isLoading = true;

      if (this.isEditing && this.selectedGroup) {
         // Update existing group
         this.reportGroupService.updateReportGroup(this.selectedGroup.Id, groupData)
            .subscribe(
               () => {
                  this.constantsService.toastSuccess('Report group updated successfully');
                  this.loadReportGroups();
                  this.isLoading = false;
                  if (this.detailsModalRef) {
                     this.detailsModalRef.close();
                  }
               },
               (error) => {
                  console.error('Error updating report group:', error);
                  this.constantsService.toastDanger('Failed to update report group');
                  this.isLoading = false;
               }
            );
      } else {
         // Create new group
         this.reportGroupService.createReportGroup(groupData)
            .subscribe(
               () => {
                  this.constantsService.toastSuccess('Report group created successfully');
                  this.loadReportGroups();
                  this.isLoading = false;
                  if (this.detailsModalRef) {
                     this.detailsModalRef.close();
                  }
               },
               (error) => {
                  console.error('Error creating report group:', error);
                  this.constantsService.toastDanger('Failed to create report group');
                  this.isLoading = false;
               }
            );
      }
   }

   deleteGroup(group: AutoPriceReportGroup): void {

      const modalRef = this.modalService.open(ConfirmationModalComponent);
      modalRef.componentInstance.header = 'Delete Report Group';
      modalRef.componentInstance.body = `Are you sure you want to delete the report group "${group.GroupName}"?`;
      modalRef.componentInstance.okButtonClass = 'btn-danger';

      modalRef.result.then(
         () => {
            this.isLoading = true;
            this.reportGroupService.deleteReportGroup(group.Id)
               .subscribe(
                  () => {
                     this.constantsService.toastSuccess('Report group deleted successfully');
                     this.loadReportGroups();
                     if (this.selectedGroup && this.selectedGroup.Id === group.Id) {
                        this.selectedGroup = null;
                        this.createNewGroup();
                     }
                     this.isLoading = false;
                  },
                  (error) => {
                     console.error('Error deleting report group:', error);
                     this.constantsService.toastDanger('Failed to delete report group');
                     this.isLoading = false;
                  }
               );
         }
      );
   }

   close(): void {
      this.activeModal.close(this.reportGroups);
   }

   dismiss(): void {
      this.activeModal.dismiss();
   }

   personChosen($event: number) {

      if ($event != null) {
         this.selectedPeople$.next([...this.selectedPeople$.value, $event]);
      }
   }

   removePerson(personId: number) {
      this.selectedPeople$.next(this.selectedPeople$.value.filter(p => p !== personId));
   }
}
