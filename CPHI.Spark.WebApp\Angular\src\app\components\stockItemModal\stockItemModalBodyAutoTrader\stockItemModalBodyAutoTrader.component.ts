import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import { VehicleOptOutParams } from "src/app/model/VehicleOptOutParams";
import { VehicleOptOutStatus } from "src/app/model/VehicleOptOutStatus";
import { UpdatePriceParams } from "src/app/model/UpdatePriceParams";
import { VehicleAdvertWithRating } from 'src/app/model/VehicleAdvertWithRating';
import { VehicleAdvertWithRatingDTO } from "src/app/model/VehicleAdvertWithRatingDTO";
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { ConstantsService } from '../../../services/constants.service'
import { SelectionsService } from '../../../services/selections.service'
import { StockItemModalService } from '../stockItemModal.service';
import { GetVehicleAdvertsWithRatingsParams } from 'src/app/model/GetVehicleAdvertWithRatingsParams';
import { OptOutModalComponent } from '../../optOutModal/optOutModal.component';

@Component({
  selector: 'app-stockItemModalBodyAutoTrader',
  templateUrl: './stockItemModalBodyAutoTrader.component.html',
  styleUrls: ['./stockItemModalBodyAutoTrader.component.scss']
})

export class stockItemModalBodyAutoTraderComponent implements OnInit {
  @ViewChild('optOutModalComponent', { static: false }) optOutModalComponent: OptOutModalComponent;

  brokenImageLinks: boolean = false;
  vehicleAdvert: VehicleAdvertWithRating;
  vehicleOptOutStatus: VehicleOptOutStatus;
  newPrice: number;
  stockCheckPhotoToDisplay: number = 0;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public service: StockItemModalService,
    public activeModal: NgbActiveModal,
    public datePipe: DatePipe,
    public getDataMethods: GetDataMethodsService
  ) { }

  ngOnInit(): void {
    this.getAutoTraderData();
  }

  async getAutoTraderData() {
    let params: GetVehicleAdvertsWithRatingsParams = {
      Reg: this.service.givenStockItem.Reg,
      Vin: this.service.givenStockItem.Chassis,
      RetailerSiteIds: null,
      UserEligibleSites: [this.service.givenStockItem.SiteId].join(','),
      EffectiveDate: this.constants.todayStart.toISOString(),
      IncludeNewVehicles: true,
      IncludeUnPublishedAdverts: true,
      LifecycleStatuses: null,// ["DUE_IN","FORECOURT","IN STOCK NOT ON PORTAL","SALE_IN_PROGRESS"],
      VehicleTypes: null,
      UseTestStrategy: false,
      IncludeLCVs: true,
      IncludeOptedOutVehicles: true,
      IncludeNonOptedOutVehicles: true
    }
    if (this.service.givenRetailerSiteId) {
      params.RetailerSiteIds = [this.service.givenRetailerSiteId].join(',')
    }

    try {

      const res: VehicleAdvertWithRatingDTO[] = await this.getDataMethods.getVehicleAdvertWithRatings(params)
      if (res.length > 0) {
        this.vehicleAdvert = new VehicleAdvertWithRating( res[res.length - 1]);
        this.newPrice = this.vehicleAdvert.AdvertisedPrice;
      }

      if (this.vehicleAdvert) {
        this.getDataMethods.getVehicleOptOutStatus(this.vehicleAdvert.AdId).subscribe((res: VehicleOptOutStatus) => {
          if (res && new Date(res.EndDate) < new Date()) {
            this.vehicleOptOutStatus = null;
          } else {
            this.vehicleOptOutStatus = res;
          }
        }, error => {
          console.error('Failed to retrieve vehicle opt-out status', error);
        })
      }
    }
    catch (error)  {
      console.error('Failed to retrieve vehicle advert', error);
    }
  }

  updateUrl(event: any) {
    let imageElement: HTMLImageElement = document.getElementById('rrgWebsiteImage') as HTMLImageElement;
    imageElement.src = '/assets/imgs/brokenImageUrlPlaceholder.jpg';
    this.brokenImageLinks = true;
  }

  maybeOptOut() {
    if (this.optOutModalComponent) {
      this.optOutModalComponent.openOptOutModal();
    }
  }

  onOptOutStatusChanged(status: VehicleOptOutStatus) {
    this.vehicleOptOutStatus = status;
    this.getAutoTraderData();
  }

  maybeOptIn() {
    if (this.optOutModalComponent) {
      this.optOutModalComponent.optIn();
    }
  }

  // Opt-in is now handled by the shared OptOutModalComponent

  setNewPrice(event: any) {
    if (!event.target || (event.target && event.target.value == '')) return;
    this.newPrice = parseFloat(event.target.value.replace('£', '').replace(',', ''));

  }

  select(event: any) {
    event.target.select();
  }

  maybeUpdatePrice() {
    let modalResultSubscription: Subscription = this.selections.confirmModalEmitter.subscribe(res => {
      if (res) { this.updatePrice(); }
      modalResultSubscription.unsubscribe();
    })

    this.constants.confirmModal.showModal('Are you sure? This will update the live AutoTrader price.', null);
  }

  updatePrice() {
    this.selections.triggerSpinner.emit({ show: true, message: 'Setting price...' });

    let params: UpdatePriceParams = {
      vehicleAdvertId: this.vehicleAdvert.AdId,
      newPrice: this.newPrice,
      oldPrice: this.vehicleAdvert.AdvertisedPrice
    }

    this.getDataMethods.updateStockPrice(params).subscribe((res) => {
      //console.log(res);
      this.constants.toastSuccess('Successfully set price');
      this.selections.triggerSpinner.emit({ show: false });
    }, error => {
      this.constants.toastDanger('Failed to set price')
      console.error('Failed to set price', error);
      this.selections.triggerSpinner.emit({ show: false });
    })
  }

  viewScan(increment?: boolean) {
    this.stockCheckPhotoToDisplay = increment ? this.stockCheckPhotoToDisplay + 1 : this.stockCheckPhotoToDisplay - 1;
  }

  goToListing() {
    let url: string = this.constants.buildAdUrl(this.vehicleAdvert.WebSiteSearchIdentifier, this.vehicleAdvert.VehicleType);
    window.open(url, '_blank').focus();
  }
}
