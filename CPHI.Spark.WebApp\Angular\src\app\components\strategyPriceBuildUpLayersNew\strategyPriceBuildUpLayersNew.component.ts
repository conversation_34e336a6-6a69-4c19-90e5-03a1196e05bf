import {Component, Input, OnInit} from '@angular/core';
import {ConstantsService} from 'src/app/services/constants.service';
import {StrategyPriceBuildUp} from 'src/app/model/StrategyPriceBuildUp';
import {AutopriceRendererService} from 'src/app/services/autopriceRenderer.service';


@Component({
   // tslint:disable-next-line:component-selector
   selector: 'strategyPriceBuildUpLayersNew',
   templateUrl: './strategyPriceBuildUpLayersNew.component.html',
   styleUrls: ['./strategyPriceBuildUpLayersNew.component.scss']
})
export class StrategyPriceBuildUpLayersNewComponent implements OnInit {

   @Input() public buildUpRows: StrategyPriceBuildUp[];
   //@Input() public params: StrategyPriceBuildUpLayersParams;
   hoverInfo: {
      VersionName?: string;
      FactorName?: string;
      FactorItemLabel?: string;
      FactorItemValue?: number;
      RuleSetComment?: string;
      Impact?: number;
      SourceValue?: string;
      ExtendedNotes?: string;
      IsRelatedToTestStrategy?: boolean;
      impact?: number;
   } = {};

   constructor(
      private constants: ConstantsService,
      private autopriceRendererService: AutopriceRendererService
   ) {
   }

   ngOnInit(): void {
   }

   measuredValue(row: StrategyPriceBuildUp) {

      if (row.ExtendedNotes) {
         return row.ExtendedNotes;
      } else {
         return row.SourceValue;
      }
   }


   // get ageAndOwnersString() {
   //    return this.autopriceRendererService.provideAgeAndOwnersString(this.params.ageAndOwners);
   // }

   dateRangeDisplay(FactorItemLabel: string) {

      if (!FactorItemLabel || typeof FactorItemLabel !== 'string') {
         return '';
      }

      try {
         const dateParts = FactorItemLabel.split('|');
         if (dateParts.length !== 2) {
            return FactorItemLabel; // Return original if not in expected format
         }

         return dateParts.map(datePart => {
            try {
               const dateObj = new Date(datePart);
               // Check if date is valid
               if (isNaN(dateObj.getTime())) {
                  return datePart; // Return original part if invalid date
               }
               return dateObj.toLocaleDateString('en-GB', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric'
               });
            } catch (err) {
               return datePart; // Return original part on error
            }
         }).join(' - ');
      } catch (err) {
         return FactorItemLabel; // Return original on any error
      }
   }
}
