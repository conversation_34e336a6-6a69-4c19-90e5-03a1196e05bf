<table>
   <tbody>
   <ng-container *ngFor="let row of params.buildUpData">
      <!-- Extra row at top if we have a rule set comment -->
      <!-- Unified Row Template -->
      <tr>

           <td colspan="2">
               {{row.ExtendedNotes}}
            </td>

            <!-- Impact -->
            <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                        row.Impact | cph : "currency" : 0 : true
                     }}</span>
            </td>



         <!-- Matrix type row -->
         <ng-container *ngIf="row.FactorName === 'RR_DL_Matrix'">
            <!-- Factor Name -->
            <td>
               <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
               Retail Rating: {{ params.retailRating | cph : "number" : 0 : false }}, Days Listed:
               {{ params.daysListed | cph : "number" : 0 : false }}
            </td>
            <td class="percentage-column">
               {{ row.FactorItemValue / 100 | cph : "percent" : 1 }}
            </td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Days in stock matrix row -->
            <ng-container *ngIf="row.FactorName === 'RR_DS_Matrix'">
               <!-- Factor Name -->
               <td>
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Retail Rating: {{ params.retailRating | cph : "number" : 0 : false }}, Days In Stock:
                  {{ params.daysInStock | cph : "number" : 0 : false }}:
               </td>
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>
               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Days booked in matrix row -->
            <ng-container *ngIf="row.FactorName === 'RR_DB_Matrix'">
               <!-- Factor Name -->
               <td>
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Retail Rating: {{ params.retailRating | cph : "number" : 0 : false }}, Days Booked In ({{
                     params.daysBookedIn | cph : "number" : 0 : false
                  }})
               </td>
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>
               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Days in stock matrix row -->
            <ng-container *ngIf="row.FactorName === 'DTS_DL_Matrix'">
               <!-- Factor Name -->
               <td>
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Days to sell: {{ params.daysToSell | cph : "number" : 0 : false }}, Days Listed:
                  {{ params.daysListed | cph : "number" : 0 : false }}:
               </td>
               <td class="percentage-column">
                  {{ row.FactorItemValue / 100 | cph : "percent" : 1 }}
               </td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Plate Year vs Days In Stock matrix row -->
            <ng-container *ngIf="row.FactorName === 'PY_DS_Matrix'">
               <!-- Factor Name -->
               <td>
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Plate Year: {{ row.FactorItemLabel.split("|")[0].replace("PY", "") }}, Days In Stock:
                  {{ params.daysInStock | cph : "number" : 0 : false }}:
               </td>
               <td class="percentage-column">
                  {{ row.FactorItemValue / 100 | cph : "percent" : 1 }}
               </td>

            <!-- Impact -->
            <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                        row.Impact | cph : "currency" : 0 : true
                     }}</span>
            </td>
         </ng-container>

            <!-- Price Band vs Days In Stock matrix row -->
            <ng-container *ngIf="row.FactorName === 'VB_DS_Matrix'">
               <!-- Factor Name -->
               <td>
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Price Band: {{ row.FactorItemLabel.split("|")[0].replace("VB", "") }}, Days In Stock:
                  {{ params.daysInStock | cph : "number" : 0 : false }}:
               </td>
               <td class="percentage-column">
                  {{ row.FactorItemValue / 100 | cph : "percent" : 1 }}
               </td>

            <!-- Impact -->
            <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                        row.Impact | cph : "currency" : 0 : true
                     }}</span>
            </td>
         </ng-container>

            <!-- SpecificColour -->
            <ng-container *ngIf="row.FactorName === 'SpecificColour'">
               <!-- Factor Name -->
               <td>
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Manufacturer Colour: {{ params.specificColour }}
               </td>
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>
     
     
            <!-- Colour -->
            <ng-container *ngIf="row.FactorName === 'Colour'">
               <!-- Factor Name -->
               <td>
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Manufacturer Colour: {{ params.colour }}
               </td>
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Minimum PP% -->
            <ng-container *ngIf="row.FactorName === 'MinimumPricePosition'">
               <!-- Factor Name -->
               <td colspan="2">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Achieve Minimum Price Position of {{ row.FactorItemValue }}%
               </td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Maximum PP% -->
            <ng-container *ngIf="row.FactorName === 'MaximumPricePosition'">
               <!-- Factor Name -->
               <td colspan="2">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Achieve Maximum Price Position of {{ row.FactorItemValue }}%
               </td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Mileage -->
            <ng-container *ngIf="row.FactorName === 'Mileage' || row.FactorName === 'MilesPerYear'">
               <!-- Factor Name -->
               <td>
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Mileage: {{ params.odometer | cph : "number" : 0 : false }}
               </td>
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Performance Rating -->
            <ng-container *ngIf="row.FactorName === 'PerformanceRatingScore'">
               <!-- Factor Name -->
               <td>
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Performance Rating: {{ params.performanceRating | cph : "number" : 0 : false }}
               </td>
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <ng-container *ngIf="row.FactorName === 'AgeAndOwners'">
               <!-- Factor Name -->
               <td>
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Age and owners: {{ ageAndOwnersString }}
               </td>
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Days to sell type row -->
            <ng-container *ngIf="row.FactorName === 'DaysToSell'">
               <!-- Factor Name -->
               <td colspan="2">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  {{ measuredValue(row) }}
               </td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Minimum profit type row -->
            <ng-container *ngIf="row.FactorName === 'MinimumProfit'">
               <!-- Factor Name -->
               <td colspan="2">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  {{ measuredValue(row) }}
               </td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Round to nearest row -->
            <ng-container *ngIf="row.FactorName === 'RoundToNearest'">
               <!-- Factor Name -->
               <td colspan="2">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  {{ measuredValue(row) }}
               </td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Round to price break row -->
            <ng-container *ngIf="row.FactorName === 'RoundToPriceBreak'">
               <!-- Factor Name -->
               <td colspan="2">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  {{ measuredValue(row) }}
               </td>

               <!-- Impact -->
               <td class="textRight">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Limit to competitor type row -->
            <ng-container *ngIf="['MatchCheapestCompetitor', 'AchieveMarketPositionScore'].includes(row.FactorName)">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  {{ row.FactorName }}
               </td>

               <!-- Factor Item Label or Measured Value for 'Match Cheapest Competitor' -->
               <td class="text-center">{{ measuredValue(row) }}</td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <ng-container *ngIf="row.FactorName === 'DateRange'">
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Date {{ dateRangeDisplay(row.FactorItemLabel) }}
                  <span *ngIf="row.SourceValue == 'DateRange-Percent'"> % Adj.</span>
                  <span *ngIf="row.SourceValue == 'DateRange-Currency'"> £ Adj.</span>
               </td>
               <td class="percentage-column" *ngIf="row.SourceValue == 'DateRange-Percent'">
                  {{ row.FactorItemValue / 100 | cph : "percent" : 1 }}
               </td>
               <td class="percentage-column" *ngIf="row.SourceValue == 'DateRange-Currency'">
                  {{ row.FactorItemValueAmount | cph : "currency" : 0 : true }}
               </td>

               <td class="impact-column">
                  {{ row.Impact | cph : "currency" : 0 }}
               </td>
            </ng-container>

            <!-- OnBrand row -->
            <ng-container *ngIf="row.FactorName === 'OnBrandCheck'">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>

                  {{ row.FactorName }}
                  ({{ params.make }}):
                  {{ measuredValue(row) }}
               </td>

               <!-- Factor Item Value (for non-'Match Cheapest Competitor' rows) -->
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="percentage-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- ValuationChangeUntilSell  row -->
            <ng-container *ngIf="row.FactorName === 'ValuationChangeUntilSell'">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>

                  {{ row.FactorName }}
                  ({{ params.make }}):
                  {{ measuredValue(row) }}
               </td>

               <!-- Factor Item Value (for non-'Match Cheapest Competitor' rows) -->
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="percentage-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- DailyValuationChange  row -->
            <ng-container *ngIf="row.FactorName === 'DailyValuationChange'">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>

                  {{ row.FactorName }}
                  {{ measuredValue(row) }}
               </td>

               <!-- Factor Item Value (for non-'Match Cheapest Competitor' rows) -->
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="percentage-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- RetailerName row -->
            <ng-container *ngIf="row.FactorName === 'RetailerName'">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  {{ row.FactorName }}:
                  {{ measuredValue(row) }}
               </td>

               <!-- Factor Item Value (for non-'Match Cheapest Competitor' rows) -->
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Brand row -->
            <ng-container *ngIf="['Brand', 'MakeFuelType', 'MakeAgeBand', 'ModelName', 'MakeModel'].includes(row.FactorName)">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  {{ row.FactorName }}:
                  {{ measuredValue(row) }}
               </td>

               <!-- Factor Item Value (for non-'Match Cheapest Competitor' rows) -->
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- FuelType row -->
            <ng-container *ngIf="row.FactorName === 'FuelType'">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Fuel Type: {{ row.SourceValue }}
               </td>

               <!-- Factor Item Value -->
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- ValueBand row -->
            <ng-container *ngIf="row.FactorName === 'ValueBand'">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Value Band: {{ row.SourceValue }}
               </td>

               <!-- Factor Item Value -->
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- RegYear row -->
            <ng-container *ngIf="row.FactorName === 'RegYear'">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Registration Year: {{ row.SourceValue }}
               </td>

               <!-- Factor Item Value -->
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- LiveMarketCondition row -->
            <ng-container *ngIf="row.FactorName === 'LiveMarketCondition'">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Live Market Condition: {{ row.SourceValue }}
               </td>

               <!-- Factor Item Value -->
               <td class="percentage-column">{{ row.FactorItemValue / 100 | cph : "percent" : 1 }}</td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!-- Retail Rating or Days Listed Type row -->
            <ng-container
               *ngIf="
                  ['RetailRatingBand', 'RetailRating10sBand', 'DaysListedBand', 'DaysInStockBand'].includes(
                     row.FactorName
                  )
               "
         >
            <!-- Factor Name -->
            <!-- <td class="factorNameCol">
               <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
               {{ row.FactorName }}

               <span *ngIf="row.FactorName === 'RetailRating10sBand'">
                     ({{ params.retailRating | cph : "number" : 0 : false }})
                  </span>
                  <span *ngIf="row.FactorName === 'RetailRatingBand'">
                     ({{ params.retailRating | cph : "number" : 0 : false }})
                  </span>
                  <span *ngIf="row.FactorName === 'DaysListedBand'">
                     ({{ params.daysListed | cph : "number" : 0 : false }})
                  </span>
                  <span *ngIf="row.FactorName === 'DaysInStockBand'">
                     ({{ params.daysInStock | cph : "number" : 0 : false }})
                  </span>
            </td> -->

            <td colspan="2">
               {{row.ExtendedNotes}}
            </td>

            <!-- Factor Item Label or Measured Value for 'Match Cheapest Competitor' -->
            <!-- <td class="percentage-column">
               {{ row.FactorItemLabel }}: {{ row.FactorItemValue / 100 | cph : "percent" : 1 }}
            </td> -->

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!--  Days Listed Type row -->
            <ng-container *ngIf="['DaysListed', 'DaysInStock'].includes(row.FactorName)">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  {{ row.FactorName }}
                  <span *ngIf="row.FactorName === 'DaysListed'"
                     >({{ params.daysListed | cph : "number" : 0 : false }})</span
                  >
                  <span *ngIf="row.FactorName === 'DaysInStock'"
                     >({{ params.daysInStock | cph : "number" : 0 : false }})</span
                  >
                  {{ row.FactorItemLabel }}:
               </td>

               <!-- Factor Item Label or Measured Value for 'Match Cheapest Competitor' -->
               <td class="percentage-column">
                  {{ row.FactorItemValue / 100 | cph : "percent" : 1 }}
               </td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!--  Retail Rating row -->
            <ng-container *ngIf="['RetailRating'].includes(row.FactorName)">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  {{ row.FactorName }}
                  ({{ params.retailRating | cph : "number" : 0 : false }}) &le; {{ row.FactorItemLabel }}:
               </td>

               <!-- Factor Item Label or Measured Value for 'Match Cheapest Competitor' -->
               <td class="percentage-column">
                  {{ row.FactorItemValue / 100 | cph : "percent" : 1 }}
               </td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                     row.Impact | cph : "currency" : 0 : true
                  }}</span>
               </td>
            </ng-container>

            <!--  Days Listed Type row -->
            <ng-container *ngIf="row.FactorName == 'WholesaleAdjustment'">
               <!-- Factor Name -->
               <td class="factorNameCol">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Wholesale adjustment
                  {{ row.FactorItemLabel }}
               </td>

               <!-- Factor Item Label or Measured Value for 'Match Cheapest Competitor' -->
               <td class="percentage-column">
                  {{ row.FactorItemValue / 100 | cph : "percent" : 1 }}
               </td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                        row.Impact | cph : "currency" : 0 : true
                     }}</span>
            </td>
         </ng-container>

            <!-- LeavingData row -->
            <ng-container *ngIf="row.FactorName === 'LeavingData'">
               <!-- Factor Name -->
               <td colspan="1">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Leaving Data Factor
               </td>
               <td>
                  {{ row.ExtendedNotes }}
               </td>

            <!-- Impact -->
            <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                        row.Impact | cph : "currency" : 0 : true
                     }}</span>
            </td>
         </ng-container>

         <!-- SetToAdvertisedPrice row -->
         <ng-container *ngIf="row.FactorName === 'SetToAdvertisedPrice'">
            <!-- Factor Name -->
            <td colspan="2">
               <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
               Set To Advertised Price
            </td>

            <!-- Impact -->
            <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                        row.Impact | cph : "currency" : 0 : true
                     }}</span>
            </td>
         </ng-container>


         <!-- TrackMarketPosition row -->
         <ng-container *ngIf="row.FactorName === 'TrackMarketPosition'">
            <!-- Factor Name -->
            <td colspan="2">
               <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
               Track Market Position
            </td>

            <!-- Impact -->
            <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                        row.Impact | cph : "currency" : 0 : true
                     }}</span>
            </td>
         </ng-container>

         <!-- Apply Adjustments that are set on the tags-->
         <ng-container *ngIf="row.FactorName === StrategyFactorName.ApplyTagAdjustments">
            <tr>

               <!-- Factor Name -->
               <td colspan="2">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Set To Advertised Price
               </td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                        row.Impact | cph : "currency" : 0 : true
                     }}</span>
               </td>
            </tr>
         </ng-container>

         <!-- Apply Adjustments on vehicles with a given tag -->
         <ng-container *ngIf="row.FactorName === StrategyFactorName.Tag">
            <tr>

               <!-- Factor Name -->
               <td colspan="2">
                  <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
                  Set To Advertised Price
               </td>

               <!-- Impact -->
               <td class="impact-column">
                  <span [ngClass]="{ 'text-danger': row.Impact < 0 }">{{
                        row.Impact | cph : "currency" : 0 : true
                     }}</span>
               </td>
            </tr>
         </ng-container>
      </tr>
   </ng-container>
   </tbody>
</table>

<ng-template #strategyPopover let-row>
   <div
      style="display: inline-block"
      placement="auto"
      popoverClass="infoPopover"
      triggers="mouseenter:mouseleave"
      container="body"
      (shown)="hoverInfo = row"
      [ngbPopover]="dynamicPopoverContent"
   >
      <i class="fa fa-circle-info strategy-info"></i>
   </div>
</ng-template>

<ng-template #dynamicPopoverContent>
   <table>
      <tr>
         <td>Factor:</td>
         <td>{{ hoverInfo.FactorName }}</td>
      </tr>
      <tr>
         <td>Label:</td>
         <td>{{ hoverInfo.AppliedFactorItemLabel || hoverInfo.FactorItemLabel }}</td>
      </tr>
      <tr>
         <td>Value:</td>
         <td>
            {{ (hoverInfo.AppliedFactorItemValue != null ? hoverInfo.AppliedFactorItemValue : hoverInfo.FactorItemValue) / 100 | percent }}
         </td>
      </tr>
      <tr>
         <td>Value:</td>
         <td>
            {{ (hoverInfo.AppliedFactorItemValueAmount != null ? hoverInfo.AppliedFactorItemValueAmount : hoverInfo.FactorItemValueAmount) | cph: "currency" : 0 : true }}
         </td>
      </tr>
      <tr>
         <td>Impact:</td>
         <td>{{ hoverInfo.Impact | cph : "currency" : 0 : true }}</td>
      </tr>
      <tr>
         <td>Version Name:</td>
         <td>{{ hoverInfo.VersionName }}</td>
      </tr>
   </table>
</ng-template>
