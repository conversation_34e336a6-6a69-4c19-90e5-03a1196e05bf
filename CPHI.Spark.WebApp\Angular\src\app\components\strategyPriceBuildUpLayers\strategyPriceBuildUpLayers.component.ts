import {Component, Input, OnInit} from '@angular/core';
import {ConstantsService} from 'src/app/services/constants.service';
import {StrategyPriceBuildUp} from 'src/app/model/StrategyPriceBuildUp';
import {AutopriceRendererService} from 'src/app/services/autopriceRenderer.service';
import {DomSanitizer} from "@angular/platform-browser";
import {StrategyFactorName} from "../../model/StrategyFactorName";

export interface StrategyPriceBuildUpLayersParams {

   buildUpData: StrategyPriceBuildUp[];
   daysToSell: number;
   daysBookedIn: number;
   retailRating: number;
   daysListed: number;
   daysInStock: number;
   make: string;
   colour:string;
   specificColour: string;
   ageAndOwners: string;
   odometer: number;
   performanceRating: number;
   fuelType: string;
   valueBand: string;
   regYear: string;
   liveMarketCondition: string;
   dateRange: string;
}


@Component({
   // tslint:disable-next-line:component-selector
   selector: 'strategyPriceBuildUpLayers',
   templateUrl: './strategyPriceBuildUpLayers.component.html',
   styleUrls: ['./strategyPriceBuildUpLayers.component.scss']
})
export class StrategyPriceBuildUpLayersComponent implements OnInit {

   @Input() public params: StrategyPriceBuildUpLayersParams;
   hoverInfo: {
      VersionName?: string;
      FactorName?: string;
      FactorItemLabel?: string;
      FactorItemValue?: number;
      FactorItemValueAmount?: number;
      RuleSetComment?: string;
      Impact?: number;
      SourceValue?: string;
      ExtendedNotes?: string;
      IsRelatedToTestStrategy?: boolean;
      impact?: number;
      AppliedFactorItemValue?: number;
      AppliedFactorItemLabel?: string;
      AppliedFactorItemValueAmount?: number;
   } = {};

   constructor(
      private constants: ConstantsService,
      private autopriceRendererService: AutopriceRendererService
   ) {
   }

   ngOnInit(): void {
   }

   measuredValue(row: StrategyPriceBuildUp) {

      if (row.ExtendedNotes) {
         return row.ExtendedNotes;
      } else {
         return row.SourceValue;
      }

   }


   get ageAndOwnersString() {
      return this.autopriceRendererService.provideAgeAndOwnersString(this.params.ageAndOwners);
   }

   dateRangeDisplay(FactorItemLabel: string) {

      if (!FactorItemLabel || typeof FactorItemLabel !== 'string') {
         return '';
      }

      try {
         const dateParts = FactorItemLabel.split('|');
         if (dateParts.length !== 2) {
            return FactorItemLabel; // Return original if not in expected format
         }

         return dateParts.map(datePart => {
            try {
               const dateObj = new Date(datePart);
               // Check if date is valid
               if (isNaN(dateObj.getTime())) {
                  return datePart; // Return original part if invalid date
               }
               return dateObj.toLocaleDateString('en-GB', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric'
               });
            } catch (err) {
               return datePart; // Return original part on error
            }
         }).join(' - ');
      } catch (err) {
         return FactorItemLabel; // Return original on any error
      }
   }

   protected readonly StrategyFactorName = StrategyFactorName;
}
