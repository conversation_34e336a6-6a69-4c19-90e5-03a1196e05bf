
.chooseReport {
   border-radius: 0.375em 0px 0px 0.375em !important;

   #reportName {
      .reportNameLabel {
         width: 15em;
         text-overflow: ellipsis;
         overflow: hidden;
      }
   }


}
.buttons {
   display: flex;
   justify-content: flex-start;
   align-items: center;
   margin-right: 1em;


   #deleteButton {
      border-radius: 0px 0.375em 0.375em 0px !important;
   }

   .dropdown-toggle::after {
      display: none;
   }
}


.reportAndProfilePic {
   display: flex;
   align-items: center;

   .reportName {
      display: flex;
      align-items: center;
      margin-right: 3em;
   }

   .profilePicAndName {
      display: flex;
      align-items: center;
      color: #FFFFFF;
      margin-right: 2em;

      span {
         white-space: nowrap;
      }
   }
}

.sharedByName {
   min-width: 10em;
   max-width: 10em;
   overflow: hidden;
   text-overflow: ellipsis;
}

button.loadReport {
   .label {
      min-width: 20em;
      max-width: 40em;
      overflow: hidden;
      text-overflow: ellipsis;
   }
}

button.containsProfilePic {
   padding-top: 0px;
   padding-bottom: 0px;
}

button.limitReport {
   text-align: center;
   padding: 0 0.3em;
   width: 3.2em;
}

#tableLayoutManagementContainer {
   display: flex;
   align-items: center;
   height: 100%;
   margin-right: 1em;
}

.restricted {
   pointer-events: none;
   background-color: var(--grey70) !important;
   opacity: 0.65;
}

.dropdown-divider {
   margin: 0.25em 0;
}

#tableLayoutsPicker {
   display: flex;
   flex-direction: column;

   button {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.25em 1em;
      border-radius: 0;

      &::after {
         border-top: 0.3em solid transparent;
         border-right: 0.3em solid transparent;
         border-bottom: 0.3em solid transparent;
         border-left: 0.3em solid;
      }
   }
}

.whenSharedLabel {
   text-align: left;
   color: #FFFFFF;
   font-weight: bold;
   padding: 0.25em 1em;
}

.subDropdownMenu {
   padding-top: 0;
   padding-bottom: 0;

   .dropdown-item {
      padding: 0;
      line-height: 2em;
   }
}


@media (max-width: 1920px) and (hover: hover) {
   .dropdown-item {
      min-height: unset !important;
      line-height: 1 !important;
   }
}


.reportName {
   .loadReport {

      min-width: 30em;
      max-width: 30em;
      text-overflow: ellipsis;
   }
}

#setStandardTableStatesModal .tableStateItem, .tableStateItem.cdk-drag-preview {
   background-color: var(--mainAppColour);
   color: #fff;
   border: 0px;
   padding: 0.5em 1.25em;
}

#setStandardTableStatesModal {
   min-height: 60vh;
   display: flex;
   flex-direction: column;

   .dragListContainer {
      width: 50%;
      padding: 1em;

      .columnHeader {

         margin-bottom: 1em;
         border-bottom: 1px solid var(--grey80);
      }

      .cdk-drop-list {
         min-height: 50%;
      }
   }

   .tableStateItem {
      margin-top: 0.5em;

      .reportName {
         margin-right: unset;

         .loadReport {
            min-width: 40em;
            max-width: 40em;
            text-overflow: ellipsis;
         }
      }
   }


   .cdk-drag-placeholder {
      //background:red;
      opacity: 0;
   }

   .cdk-drag-animating {
      transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
   }

   .example-box:last-child {
      border: none;
   }

   .example-list.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
      transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
   }
}
