import {NgbActiveModal, Ngb<PERSON>odal, NgbModalRef} from '@ng-bootstrap/ng-bootstrap';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {Component, Input, OnDestroy, OnInit} from '@angular/core';
import {BehaviorSubject, Observable, Subscription } from 'rxjs';
import {TableLayoutManagementService} from './tableLayoutManagement.service';
import {ApiAccessService} from '../../services/apiAccess.service';
import {ConstantsService} from '../../services/constants.service';
import {AutoPriceReportSchedule} from '../../model/AutoPriceReportSchedule';
import {SelectionsService} from '../../services/selections.service';
import {ConfirmationModalComponent} from '../../pages/fleetOrderbook/confirmationModal/confirmationModal.component';
import {AutoPriceReportGroup} from '../../model/AutoPriceReportGroup';
import {ReportGroupService} from '../../services/report-group.service';
import {ReportGroupModalComponent} from '../reportGroupModal/reportGroupModal.component';
import {UserAndEmail} from '../../model/UserAndEmail';
import { UserMaintenanceService } from 'src/app/pages/userMaintenance/userMaintenance.service';

interface Recipient {
   recipientId?: number; // Maps to personId
   email?: string;
   name?: string;
}

interface ScheduleReportDTO {
   scheduleId?: number;
   reportId: number;
   recipients: Recipient[];
   cron: string;
}

interface DaysSelectDTO {
  label: string;
  value: number;
}

@Component({
   selector: 'tableLayoutSchedulerModal',
   templateUrl: './tableLayoutSchedulerModal.component.html',
   styleUrls: ['./tableLayoutSchedulerModal.component.scss']
})

export class TableLayoutSchedulerModalComponent implements OnInit, OnDestroy {

   @Input() reportId: number;
   @Input() scheduleId: number;

   // For backward compatibility with the timepicker
   public reportTime: { hour: number, minute: number } = {hour: 9, minute: 0};
   public currentPersonId: number;
   public scheduleRequestedById: number | null = null;

   // BehaviorSubjects for recipients
   private searchTermSubject = new BehaviorSubject<string>('');

   // Getter and setter for availableRecipients
   public recipientList: { label: string, value: number }[] = [];
   public reportGroupList: { label: string, value: number }[] = [];

   availableRecipients$: BehaviorSubject<Recipient[]> = new BehaviorSubject<Recipient[]>([]);
   selectedRecipients$: BehaviorSubject<Recipient[]> = new BehaviorSubject<Recipient[]>([]);

   availableReportGroups$: BehaviorSubject<AutoPriceReportGroup[]> = new BehaviorSubject<AutoPriceReportGroup[]>([]);
   selectedReportGroups$: BehaviorSubject<AutoPriceReportGroup[]> = new BehaviorSubject<AutoPriceReportGroup[]>([]);

   get availableRecipients(): Recipient[] {
      return this.availableRecipients$.value;
   }

   set availableRecipients(recipients: Recipient[]) {
      this.availableRecipients$.next(recipients);
   }

   get selectedRecipients(): Recipient[] {
      return this.selectedRecipients$.value;
   }

   set selectedRecipients(recipients: Recipient[]) {
      this.selectedRecipients$.next(recipients);
   }

   get availableReportGroups(): AutoPriceReportGroup[] {
      return this.availableReportGroups$.value;
   }

   set availableReportGroups(reportGroups: AutoPriceReportGroup[]) {
      this.availableReportGroups$.next(reportGroups);
      this.reportGroupList = reportGroups.map(x => ({ value: x.Id, label: x.GroupName }));
   }

   set selectedReportGroups(reportGroups: AutoPriceReportGroup[]) {
      this.selectedReportGroups$.next(reportGroups);
   }

   get selectedReportGroups(): AutoPriceReportGroup[] {
      return this.selectedReportGroups$.value;
   }

   private subscriptions: Subscription[] = [];

   cronForm: FormGroup;
   weekDays = [
      {label: 'Monday', value: 1},
      {label: 'Tuesday', value: 2},
      {label: 'Wednesday', value: 3},
      {label: 'Thursday', value: 4},
      {label: 'Friday', value: 5},
      {label: 'Saturday', value: 6},
      {label: 'Sunday', value: 7},
   ];

   // Initial value for availableRecipients
   private initialRecipients: Recipient[] = [
      {email: 'Loading..'},
   ].sort((a, b) => a.email.localeCompare(b.email));

   buttonSpinner = false;

   constructor(
      public modalService: NgbModal,
      public apiAccessService: ApiAccessService,
      public constantsService: ConstantsService,
      public cronModal: NgbActiveModal,
      private tableLayoutManagementService: TableLayoutManagementService,
      private userMaintenanceService: UserMaintenanceService,
      public selections: SelectionsService,
      private fb: FormBuilder,
      private reportGroupService: ReportGroupService
   ) {
      this.currentPersonId = this.selections.user.PersonId,

         this.cronForm = this.fb.group({
            searchTerm: [''],
            selectedRecipients: [[]],
            customEmail: ['', Validators.email],
            frequency: ['daily'],
            daysOfWeek: this.fb.group({
               1: [false], // Monday
               2: [false], // Tuesday
               3: [false], // Wednesday
               4: [false], // Thursday
               5: [false], // Friday
               6: [false], // Saturday
               7: [false], // Sunday
            }),
            dayOfMonth: [1],
            time: this.fb.group({
               hour: [9],
               minute: [0],
               second: [0],
            }),
         });
   }

   internalTesting() {
      return (this.cronForm.value.customEmail === '<EMAIL>');
   }

   ngOnInit(): void {

      this.availableRecipients = this.initialRecipients;


      this.subscriptions.push(
         this.userMaintenanceService.getUsersAndEmailObservable().subscribe((res: UserAndEmail[]) => {
            this.availableRecipients = res.map(x => ({recipientId: x.Id, email: x.Email, name: x.Name}));
            this.recipientList = this.availableRecipients
                .sort((a, b) => a.name.localeCompare(b.name))
                .map(x => ({ value: x.recipientId, label: x.name }) );

            if (this.scheduleId) {
               this.loadExistingSchedule();
            }
         })
      );

      this.loadReportGroups();

      this.cronForm.get('searchTerm')?.valueChanges.subscribe(value => {
         this.searchTermSubject.next(value || '');
      });
   }

   ngOnDestroy(): void {
      // Clean up subscriptions to prevent memory leaks
      this.subscriptions.forEach(sub => sub.unsubscribe());
   }

   loadReportGroups(): void {
      this.reportGroupService.getReportGroups()
         .subscribe(
            (groups: AutoPriceReportGroup[]) => {
               this.availableReportGroups = groups;
            },
            error => {
               console.error('Error loading report groups:', error);
               this.constantsService.toastDanger('Failed to load report groups');
            }
         );
   }

   openReportGroupModal(): void {
      const modalRef = this.modalService.open(ReportGroupModalComponent, {
         size: 'sm',
        // backdrop: 'static',
         keyboard: false
      });

      modalRef.result.then(
         (updatedGroups: AutoPriceReportGroup[]) => {
            if (updatedGroups) {
               // Update the report groups list
               this.availableReportGroups = updatedGroups;
            }
         },
         () => {
            // Modal dismissed, refresh the groups list anyway in case changes were made
            this.loadReportGroups();
         }
      );
   }

   loadExistingSchedule(): void {
      this.apiAccessService.get('api/Report/' + this.reportId + '/schedule', this.scheduleId.toString()).subscribe(
         (schedule: AutoPriceReportSchedule) => {
            if (schedule) {
               this.scheduleRequestedById = schedule.RequestedById;

               if (schedule.Recipients && schedule.Recipients.length > 0) {
                  this.selectedRecipients = schedule.Recipients.map(r => ({
                     recipientId: r.Recipient?.Id,
                     email: r.Recipient?.Email,
                     name: r.Recipient?.Name
                  }));
               }

               if (schedule.ScheduleGroups && schedule.ScheduleGroups.length > 0) {
                  this.selectedReportGroups = schedule.ScheduleGroups.map(x => x.ReportGroup);
               }

               // Parse the cron expression
               if (schedule.Cron) {
                  const [_, minute, hour, dayMonth, month, dayWeek] = schedule.Cron.split(' ');

                  // Determine frequency
                  let frequency = 'daily';
                  if ((dayMonth === '*' || dayMonth === '?') && (dayWeek !== '*' && dayWeek !== '?')) {
                     frequency = 'weekly';
                  } else if ((dayMonth !== '*' && dayMonth !== '?') && (dayWeek === '*' || dayWeek === '?')) {
                     frequency = 'monthly';
                  }

                  // Reset all days to false
                  const daysOfWeekGroup = this.cronForm.get('daysOfWeek') as FormGroup;
                  for (let i = 0; i < 7; i++) {
                     daysOfWeekGroup.get(i.toString())?.setValue(false);
                  }

                  // If weekly, set the selected days
                  if (frequency === 'weekly' && dayWeek !== '*' && dayWeek !== '?') {
                     // Handle multiple days (comma-separated values)
                     const selectedDays = dayWeek.split(',');
                     selectedDays.forEach(day => {
                        daysOfWeekGroup.get(day)?.setValue(true);
                     });
                  }

                  // First update the main form fields
                  this.cronForm.patchValue({
                     frequency,
                     customEmail: schedule.CustomEmail,
                     dayOfMonth: (dayMonth !== '*' && dayMonth !== '?') ? parseInt(dayMonth, 10) : 1,
                     time: {
                        hour: parseInt(hour, 10),
                        minute: parseInt(minute, 10),
                        second: 0
                     }
                  });

                  // ngb-timepicker is glitched, needs both reactive + template form binding..
                  this.reportTime = {
                     hour: parseInt(hour, 10),
                     minute: parseInt(minute, 10)
                  };
               }
            }
         },
         error => {
            console.error('Error loading schedule:', error);
            this.constantsService.toastDanger('Failed to load schedule details');
         }
      );
   }

   isFormValid(): boolean {
      // We need to use the synchronous methods here since we need an immediate boolean result
      const recipients = this.selectedRecipients;
      const groups = this.selectedReportGroups;

      const frequency = this.cronForm.get('frequency')?.value;

      // Check if at least one day is selected when frequency is weekly
      const weeklyValid = frequency !== 'weekly' || this.hasSelectedDays();

      return (((recipients && recipients.length > 0) || (groups && groups.length > 0)) && this.cronForm.valid && weeklyValid);
   }

   isRecipientSelected(id: number): boolean {
      return this.selectedRecipients.some(recipient => recipient.recipientId == id);
   }

   canChangeVisible(recipient: Recipient): boolean {

      // New schedule
      if (!this.scheduleId) {
         return true;
      }
      // I'm in the schedule
      if (recipient.recipientId == this.currentPersonId) {
         return true;
      }
      // I created the schedule
      if (this.selections.user.PersonId == this.scheduleRequestedById) {
         return true;
      }

      return false;
   }

   get searchTerm(): string {
      const term = this.cronForm.get('searchTerm')?.value?.toLowerCase() || '';
      return term;
   }

   selectedAndVisible(): Recipient[] {

      return this.availableRecipients
         .filter(recipient =>
            this.selectedRecipients.some(x => x.recipientId == recipient.recipientId) &&
            recipient.email.toLowerCase().includes(this.searchTerm)
         )
         .sort((a, b) => a.email.localeCompare(b.email));
   }

   hasSelectedDays(): boolean {
      const daysOfWeekGroup = this.cronForm.get('daysOfWeek') as FormGroup;
      if (!daysOfWeekGroup) {
         return false;
      }

      // Check if at least one day is selected
      return Object.values(daysOfWeekGroup.value).some(value => value === true);
   }

   cronFrequencyChange(frequency?: any) {

       if (frequency) {

          this.cronForm.patchValue({ frequency: frequency || 'daily' });

          if (frequency === 'weekly') {
             // Set Monday as default selected day if no days are selected
             const daysOfWeekGroup = this.cronForm.get('daysOfWeek') as FormGroup;
             if (!this.hasSelectedDays()) {
                daysOfWeekGroup.get('1')?.setValue(true); // Monday
             }
          } else if (frequency === 'monthly') {
             this.cronForm.get('dayOfMonth')?.setValue(1);
          }
       }
   }

   cronSubmit() {

      const {customEmail, frequency, daysOfWeek, dayOfMonth, time} = this.cronForm.value;
      const minute = time.minute || 0;
      const hour = time.hour || 0;
      let cron = '';

      switch (frequency) {
         case 'daily':
            cron = `0 ${minute} ${hour} * * ?`;
            break;
         case 'weekly':
            // Get selected days of week
            const selectedDays = Object.entries(daysOfWeek)
               .filter(([_, selected]) => selected)
               .map(([day, _]) => day)
               .join(',');

            // If no days are selected (shouldn't happen due to validation), default to Monday
            if (!selectedDays) {
               cron = `0 ${minute} ${hour} ? * 1`;
            } else {
               cron = `0 ${minute} ${hour} ? * ${selectedDays}`;
            }
            break;
         case 'monthly':
            cron = `0 ${minute} ${hour} ${dayOfMonth} * ?`;
            break;
      }

      const allRecipients: Recipient[] = [...this.selectedAndVisible()];

      // Don't close the modal yet - wait for the API call to complete
      const scheduleData = {
         scheduleId: this.scheduleId,
         reportId: this.reportId,
         recipients: allRecipients,
         customEmail: this.cronForm.get('customEmail')?.value?.trim(),
         AutoPriceReportGroupIds: this.selectedReportGroups.map(g => g.Id),
         cron
      };

      // Show a loading indicator
      this.buttonSpinner = true;

      // Call scheduleReport and wait for it to complete
      this.scheduleReport(scheduleData).subscribe(
         (_) => {
            // API call succeeded
            this.buttonSpinner = false;
            // Close the modal with the result
            this.cronModal.close({recipients: allRecipients, cron, success: true});
         },
         (error) => {
            // API call failed
            this.buttonSpinner = false;
            // Don't close the modal so the user can try again
            console.error('Failed to save schedule:', error);
         }
      );
   }

   scheduleReport(schedule: ScheduleReportDTO): Observable<any> {
      // Return an Observable that the caller can subscribe to
      return new Observable(observer => {
         if (schedule.scheduleId) {
            // Update existing schedule
            this.apiAccessService.put('api/Report', schedule.reportId + '/schedule/' +
               schedule.scheduleId, schedule).subscribe(
               (res) => {
                  this.constantsService.toastSuccess('Updated report schedule');
                  observer.next(res); // Emit the result
                  observer.complete(); // Complete the Observable
               },
               (error) => {
                  console.error('Error updating schedule:', error);
                  this.constantsService.toastDanger('Failed to update schedule');
                  observer.error(error); // Emit the error
               }
            );
         } else {
            // Create new schedule
            this.apiAccessService.post('api/Report', schedule.reportId + '/schedule', schedule).subscribe(
               (res) => {
                  this.constantsService.toastSuccess('Scheduled report');
                  observer.next(res); // Emit the result
                  observer.complete(); // Complete the Observable
               },
               (error) => {
                  console.error('Error creating schedule:', error);
                  this.constantsService.toastDanger('Failed to create schedule');
                  observer.error(error); // Emit the error
               }
            );
         }
      });
   }

   dismissCron() {
      this.cronModal.dismiss();
   }

   sendTestReport(reportId: number, _recipient: string) {

      this.buttonSpinner = true;

      this.tableLayoutManagementService.testReport(reportId)
         .then(() => {
         })
         .finally(() => {
            this.buttonSpinner = false;
            this.modalService.dismissAll();
         });

   }

   onDeleteSchedule() {
      // Show a confirmation dialog before deleting
      const modalRef: NgbModalRef = this.modalService.open(ConfirmationModalComponent, {size: 'sm'});
      modalRef.componentInstance.header = 'Delete Schedule';
      modalRef.componentInstance.body = 'Are you sure you want to delete this scheduled report? This action cannot be undone.';
      modalRef.componentInstance.okButtonClass = 'btn-danger';

      // Handle the result of the confirmation dialog
      modalRef.result.then(
         () => {
            // User confirmed, proceed with deletion
            // Show a loading indicator
            this.buttonSpinner = true;

            // Call deleteSchedule and wait for it to complete
            this.deleteSchedule(this.reportId, this.scheduleId).subscribe(
               (_) => {
                  // API call succeeded
                  this.buttonSpinner = false;
                  // Close the modal with the result
                  this.cronModal.close({success: true, deleted: true});
               },
               (error) => {
                  // API call failed
                  this.buttonSpinner = false;
                  // Don't close the modal so the user can try again
                  console.error('Failed to delete schedule:', error);
               }
            );
         },
         () => {
            // User cancelled, do nothing
         }
      );
   }

   deleteSchedule(reportId: number, scheduleId: number): Observable<any> {
      // Return an Observable that the caller can subscribe to
      return new Observable(observer => {
         this.apiAccessService.deleteRecord('api/Report', reportId + '/schedule/' + scheduleId).subscribe(
            (res) => {
               this.constantsService.toastSuccess('Deleted report schedule');
               observer.next(res); // Emit the result
               observer.complete(); // Complete the Observable
            },
            (error) => {
               console.error('Error deleting schedule:', error);
               this.constantsService.toastDanger('Failed to delete schedule');
               observer.error(error); // Emit the error
            }
         );
      });
   }

   recipientChosen($event: any) {
       const recipient = this.availableRecipients.find(r => r.recipientId == $event);
       if (recipient === undefined || this.selectedRecipients$.value.some(r => r.recipientId == $event)) {
          return;
       }
       this.selectedRecipients$.next([...(this.selectedRecipients$.value), recipient]);
   }

   removeRecipient(recipient: Recipient) {
      this.selectedRecipients$.next(this.selectedRecipients$.value.filter(r => r.recipientId != recipient.recipientId));
   }

   reportGroupChosen($event: any) {
      const rgc = this.availableReportGroups.find(r => r.Id == $event);
      if (rgc === undefined || this.selectedReportGroups$.value.some(r => r.Id == $event)) {
         return;
      }
      this.selectedReportGroups$.next([...(this.selectedReportGroups$.value), rgc]);
   }

   removeGroup(group: AutoPriceReportGroup) {
      this.selectedReportGroups$.next(this.selectedReportGroups$.value.filter(r => r.Id != group.Id));
   }

   setFrequency(frequency?: string) {
   }
}
