.tag-style {
   border-radius: 0.5em;
   overflow: hidden;
   display: inline-flex;
   gap: 0.3em;
   text-decoration: none;
   -webkit-transition: color 0.2s;
   background-color: var(--tag-colour, orange);
   color: var(--text-colour, white);

   .tag-label {
      padding: 0.2em 1em 0.2em 1em;
   }

   &:has(.delete-tag) {
      .tag-label {
         padding-right: 0.2em;
      }
   }
}


.delete-tag {
   vertical-align: middle;
   height: 2.5em;
   line-height: 2.5em;
   width: 2.5em;
   text-align: center;
   font-size: 0.8em;
   border-left: 1px solid rgba(0, 0, 0, 0.1);
   box-shadow: 0.5px 0px 0 rgba(255, 255, 255, 0.1) inset;

   &:hover {
      background-color: #c00;
   }
}
