import {Component, Input, Output, EventEmitter} from "@angular/core";
import {TagDTO} from "../../model/Tag";
import {TagService} from "../../services/tag.service";
import {DomSanitizer, SafeHtml} from '@angular/platform-browser';

@Component({
   selector: "app-tag-display",
   templateUrl: "./tag-display.component.html",
   styleUrls: ["./tag-display.component.scss"]
})
export class TagDisplayComponent {

   @Input() tag: TagDTO;
   @Input() canEditTags: boolean = false;
   @Input() preview: boolean = false;
   @Output() onDeleteTag = new EventEmitter<any>();

   constructor(
      private tagService: TagService,
      private sanitizer: DomSanitizer
   ) {}

   deleteTag($event: any, tag: any) {
      if (!this.canEditTags) return;
      this.onDeleteTag.emit({event: $event, tag: tag});
   }

   /**
    * Get the formatted tag HTML using the shared method
    */
   getTagHtml(): SafeHtml {
      const html = this.tagService.renderSingleTag(this.tag.Id);
      return this.sanitizer.bypassSecurityTrustHtml(html);
   }

}
