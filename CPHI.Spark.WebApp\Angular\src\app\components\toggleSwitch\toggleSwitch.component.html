<button class="btn btn-primary buttonWithSlider"
        (click)="toggleValue()"
        [disabled]="disabled"
        [class]="(styleClass || '') + ' ' + sizeClass"
        [ngStyle]="{ 'width.em': width || null }">

   <span *ngIf="text" [ngStyle]="{ 'color': blackFont ? 'black' : null }" class="sliderText">{{ text }}</span>
   <div class="switch" style="width: 100%">
      <div class="toggle"
           [ngClass]="{
             'active': value === true,
             'indeterminate': value === null
           }"

           [style]="{
             'transform': value === true ?
                'translateX(calc(' + width + 'em - var(--toggle-circle-size) - (var(--toggle-label-offset) * 2 )))' : ''
           }"
      >
      </div>
      <div class="slider"
           [ngClass]="{ 'indeterminate': value === null }"
           [ngStyle]="{
             'background-color': value === true ? onBgColor : (value === false ? offBgColor : null),
             'border-color': value === true ? onBgColor : (value === false ? offBgColor : null)
           }">
         <span *ngIf="onLabel && value === true" class="toggle-label toggle-label-on"
               [ngStyle]="{ 'color': onColor }">{{ onLabel }}
         </span>
         <span *ngIf="offLabel && value === false" class="toggle-label toggle-label-off"
               [ngStyle]="{ 'color': offColor }">{{ offLabel }}
         </span>
      </div>
   </div>
   <span *ngIf="rightText"
         [ngStyle]="{ 'color': blackFont ? 'black' : null }"
         class="sliderTextRight">{{ rightText }}
   </span>
</button>
