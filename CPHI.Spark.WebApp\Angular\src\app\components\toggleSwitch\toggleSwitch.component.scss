.buttonWithSlider {
   display: flex;
   align-items: center;
   justify-content: space-between;


   span.sliderText {
      margin-right: 0.5em;
   }

   &.no-label-wrap {
      span.sliderTextRight {
         white-space: nowrap;
         overflow: hidden;
         text-overflow: ellipsis;
         display: block;
      }
   }

   span.sliderTextRight {
      margin-left: 0.5em;
   }

   &:hover {
      background-color: inherit !important;
      color: var(--secondary) !important;
   }

   &.btn-primary {

      background-color: transparent;
      height: var(--button-height, 1.7em);

      &:disabled {
         background-color: transparent !important;
      }

      &:not(.btn-dark) {
         .slider {
            border: 1px solid var(--mainAppColour);
            background-color: var(--mainAppColour);
         }

         .switch:has(.toggle.active) {

            .slider {
               border: 1px solid var(--bs-success);
               background-color: var(--bs-success);
            }

         }

         .switch:has(.toggle.indeterminate) {

            .slider {
               border: 1px solid var(--warning);
               background-color: var(--warning);
            }

         }
      }

      &.btn-dark {

         .slider {
            border: 1px solid var(--grey80);
            background-color: var(--mainAppColour);
         }

         .switch:has(.toggle.active) {

            .slider {
               background-color: var(--secondary);
            }
         }

         .switch:has(.toggle.indeterminate) {

            .slider {
               border: 1px solid var(--warning);
               background-color: var(--warning);
            }
         }
      }

      &.btn-alt {

         .slider {
            border: 1px solid var(--grey80);
            background-color: var(--grey80);

         }

         .toggle {
            background-color: #fff !important;
         }

         .switch:has(.toggle.active) {

            .toggle {
               background-color: #fff !important;
            }

            .slider {
               background-color: var(--bs-success);
            }
         }

         .switch:has(.toggle.indeterminate) {

            .toggle {
               background-color: #fff !important;
            }

            .slider {
               background-color: var(--warning);
            }
         }
      }
   }
}

.switch {
   position: relative;
   display: inline-block;
   height: var(--toggle-switch-height, 1.4em);
   width: var(--toggle-switch-width, 2.5em);
   margin-bottom: 0;
}

.slider {
   position: absolute;
   cursor: pointer;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   background-color: var(--primaryDark);
   border: .1em solid white;
   border-radius: 1.8em;
   -webkit-transition: .4s;
   transition: .4s;
}

.toggle {
   position: absolute;
   content: "";
   -webkit-transition: .4s;
   transition: .4s;
   height: var(--toggle-circle-size, 1.1em);
   width: var(--toggle-circle-size, 1.1em);
   left: var(--toggle-circle-offset, 0.16em);
   bottom: var(--toggle-circle-offset, 0.16em);
   border-radius: 50%;
   background-color: white !important;
   z-index: 2; /* Above the labels */
}

.toggle.active {
   left: auto;
}

.toggle.indeterminate {
   -webkit-transform: translateX(var(--toggle-circle-translate-half, 0.55em));
   -ms-transform: translateX(var(--toggle-circle-translate-half, 0.55em));
   transform: translateX(var(--toggle-circle-translate-half, 0.55em));
   background-color: var(--warning) !important;
}

.slider.indeterminate {
   background-color: var(--warning);
   border-color: var(--warning);
}

.toggle-label {
   position: absolute;
   top: 50%;
   transform: translateY(-50%);
   font-size: var(--toggle-label-font-size, 0.75em);
   font-weight: 500;
   white-space: nowrap;
   pointer-events: none;
   z-index: 0; /* Behind the toggle circle */
}

.toggle-label-on {
   left: var(--toggle-label-offset, 0.4em);
}

.toggle-label-off {
   right: var(--toggle-label-offset, 0.4em);
}

.buttonWithSlider {
   padding-left: 0px;
}

.buttonWithSlider.active {
   .slider {
      border: .1em solid var(--buttonColourActive);
   }

   .toggle {
      background-color: var(--buttonColourActive) !important;
   }
}

// Size variants using CSS custom properties
.toggle-size-small {
   --toggle-switch-height: 1.0em;
   --toggle-switch-width: 1.8em;
   --toggle-circle-size: 0.8em;
   --toggle-circle-offset: 0.1em;
   --toggle-label-font-size: 0.6em;
   --toggle-label-offset: 0.4em;
   --button-height: 1.3em;
}

.toggle-size-medium {
   --toggle-switch-height: 1.4em;
   --toggle-switch-width: 2.5em;
   --toggle-circle-size: 1.1em;
   --toggle-circle-offset: 0.16em;
   --toggle-label-font-size: 0.75em;
   --toggle-label-offset: 0.5em;
   --button-height: 1.7em;
}

.toggle-size-large {
   --toggle-switch-height: 1.8em;
   --toggle-switch-width: 3.2em;
   --toggle-circle-size: 1.4em;
   --toggle-circle-offset: 0.2em;
   --toggle-label-font-size: 0.9em;
   --toggle-label-offset: 0.5em;
   --button-height: 2.1em;
}
