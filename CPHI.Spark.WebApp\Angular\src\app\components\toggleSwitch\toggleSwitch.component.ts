import {Component, Input, OnInit, forwardRef, ElementRef, ViewChild} from '@angular/core';
import {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';

export type ToggleSwitchSize = 'small' | 'medium' | 'large';

@Component({
   selector: 'toggleSwitch',
   templateUrl: './toggleSwitch.component.html',
   styleUrls: ['./toggleSwitch.component.scss'],
   providers: [
      {
         provide: NG_VALUE_ACCESSOR,
         useExisting: forwardRef(() => ToggleSwitchComponent),
         multi: true
      }
   ]
})
export class ToggleSwitchComponent implements OnInit, ControlValueAccessor {

   @Input() blackFont: boolean;
   @Input() width: number = 4.5;
   @Input() text!: string;
   @Input() rightText!: string;
   @Input() disabled!: boolean;
   @Input() styleClass: string;
   @Input() onLabel: string;
   @Input() offLabel: string;
   @Input() onBgColor: string;
   @Input() onColor: string;
   @Input() offBgColor: string;
   @Input() offColor: string;
   @Input() size: ToggleSwitchSize = 'medium';

   // Internal value for ControlValueAccessor
   private _value: boolean | null = false;

   // ControlValueAccessor callbacks
   private onChange: (value: boolean | null) => void = () => {
   };
   private onTouched: () => void = () => {
   };

   constructor() {
   }

   ngOnInit() {
   }

   // Getter for the current value
   get value(): boolean | null {
      return this._value;
   }

   // Getter for the size class
   get sizeClass(): string {
      return `toggle-size-${this.size}`;
   }

   // Toggle the value between true and false only (null state is for display only)
   toggleValue(): void {
      if (!this.disabled) {
         // If current value is null, treat it as false for toggling purposes
         if (this._value === null || this._value === false) {
            this._value = true;
         } else { // true
            this._value = false;
         }
         this.onChange(this._value);
         this.onTouched();
      }
   }

   // ControlValueAccessor implementation
   writeValue(value: boolean | null): void {
      this._value = value;
   }

   registerOnChange(fn: (value: boolean | null) => void): void {
      this.onChange = fn;
   }

   registerOnTouched(fn: () => void): void {
      this.onTouched = fn;
   }

   setDisabledState(isDisabled: boolean): void {
      this.disabled = isDisabled;
   }
}
