import { Component, OnInit, Output, EventEmitter, Input, ViewChild, ElementRef } from "@angular/core";
import { Observable, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';
import { ConstantsService } from "../services/constants.service";


@Component({
  selector: 'typeaheadAndDropdown',
  template: `

         <div id="outer" [ngClass]="customClassNames" class="spaceBetween" [ngStyle]="{'width.em':widthEm}">
            <!-- The typeahead -->

            <input #inputBox [ngClass]="{'chosenItem':chosenItem}"
            [ngStyle]="{'width.em':typeaheadWidth}"
            tabindex="{{tabindex}}" placeholder="{{placeholder}}" type="text" class="form-control"
            (keydown.enter)="setChosenAndClearInput($event)" [(ngModel)]="chosenItem" [ngbTypeahead]="search" (selectItem)="setChosenAndClearInput($event)" (click)="clearInitialValue()"
            [disabled]="disabled"
            container="body" id="typeAheadInput" />

            <!-- The dropdown -->
            <div ngbDropdown container="body"  id="dropdownButtonWithCaret"  class= " d-inline-block dropdownButton">

              <button class="dropdownButton btn btn-primary" tabindex="-1" ngbDropdownToggle [disabled]="disabled">

              </button>

              <div ngbDropdownMenu id="dropdownMenuItemsHolder" class="dropdown-menu dropdown-menu-right" [ngClass]="{'dropdown-menu-right':position==='right'}" aria-labelledby="dropdownBasic1">
                <button (click)="chooseNone()" *ngIf="chosenItem && !hideClearButton" ngbDropdownItem>--Clear--</button>

                <!-- ngFor buttons -->
                <button *ngFor="let item of searchListLimited" (click)="onChoiceMade(item)" ngbDropdownItem>
                  {{ item }}
                </button>

                <button disabled ngbDropdownItem *ngIf="searchList?.length===0" >(No Items Found)</button>

              </div>

            </div>

          </div>


   `,

  styles: [`
  #outer.fullWidth{width:100%;}
  #outer.lowHeight .dropdownButton{height:1.7em!important}
  #outer.noBorder input{border:0px!important}
  #outer.lowHeight input{height:1.7em!important}

  #dropdownButtonWithCaret{transform: translateX(-2.7em); z-index:2;}
  .dropdownButton{height:2.7em;border-radius: 0 0.375em 0.375em 0;}
  .autoHeight .dropdownButton { height: auto; }

  input{border: 1px solid var(--grey80) !important; ;height:2.7em!important;width:100%; border-radius: 0.375em 0 0 0.375em;padding-right:2.7em;}
  .autoHeight input { height: unset !important; padding: 0 0 0 0.5em !important; }
  #dropdownMenuItemsHolder{max-height:42vh;overflow:auto}

.autoHeight {
  align-items: stretch;
}
`]
})


export class TypeaheadAndDropdownComponent implements OnInit {
  @Input() public searchList: string[];
  @Input() public keyName: string;
  @Input() public widthEm: number;
  @Input() public placeholder: string;
  @Input() public charToStartSearch: number;
  @Input() public initialValue: string;
  @Input() public position: string;
  @Input() public tabindex: number;
  @Input() public customClasses: string[];
  @Input() public shouldAutoFocus: boolean;
  @Input() public resetDisplayedValued: EventEmitter<boolean>;
  @Input() public resetToInitialEmitter: EventEmitter<boolean>;
  @Input() public setValueEmitter: EventEmitter<string>;
  @Input() public searchListUpdatedEmitter: EventEmitter<string[]>;
  @Input() public disabled: boolean;
  @Input() public hideClearButton: boolean;
  @Input() public clearInputOnChoiceMade: boolean;
  @Input() public focusOnInit: boolean;
  @Output() public chosenItemEmitter: EventEmitter<any> = new EventEmitter()

  @ViewChild('inputBox', { static: true, read: ElementRef }) public inputBox;
  subscription: Subscription;
  subscription2: Subscription;
  subscription3: Subscription;
  searchListUpdatedSubscription: Subscription;

  constructor(
    public constants: ConstantsService
  ) { }


  searchListLimited: string[];
  chosenItem: any;
  typeaheadWidth: number;
  customClassNames: string;

  ngOnInit(): void {

    this.initParams()
    this.customClassNames = ''
    this.customClasses.forEach((customClass, i) => {
      if (i > 0) this.customClassNames += ' '
      this.customClassNames += customClass
    })
    if (this.shouldAutoFocus) this.inputBox.nativeElement.select();

    this.searchListUpdatedSubscription = this.searchListUpdatedEmitter.subscribe((res: string[]) => {
      this.searchList = res;
      this.searchListLimited = this.searchList.slice(0, Math.min(this.searchList.length, 1000));
    })
  }


  ngOnDestroy() {
    if (this.subscription) this.subscription.unsubscribe()
    if (this.subscription2) this.subscription2.unsubscribe()
    if (this.subscription3) this.subscription3.unsubscribe()
    if (this.searchListUpdatedSubscription) this.searchListUpdatedSubscription.unsubscribe();
  }



  initParams() {
    this.searchListLimited = this.searchList.slice(0, Math.min(this.searchList.length, 1000))
    this.typeaheadWidth = this.widthEm - 2.7 - 0.25;
    if (!this.customClasses) this.customClasses = [];

    if (this.focusOnInit) {
      const input = document.getElementsByClassName(this.customClasses[0])[0] as HTMLInputElement;
      input.focus();
    }

    //initially set value
    this.resetToInitialValue();

    if (this.resetToInitialEmitter) {

      this.subscription = this.resetToInitialEmitter.subscribe(res => {
        this.initParams();
        this.resetToInitialValue();
      })
    }

    if (this.setValueEmitter) {
      this.subscription2 = this.setValueEmitter.subscribe(newVal => {
        if (!!newVal) {
          this.chooseItem(newVal);
        } else {
          this.chooseNone()
        }
      })
    }

    if (this.resetDisplayedValued) {
      this.subscription3 = this.resetDisplayedValued.subscribe(res => {
        setTimeout(() => {
          this.resetToInitialValue();
        }, 100)
      })
    }
  }



  private resetToInitialValue() {
    if (this.initialValue) {
      this.chooseItem(this.initialValue);
    } else {
      this.chooseNone();
    }
  }

  chooseItem(item: string) {
    if (!!item) {
      this.chosenItem = item;
    } else {
      this.chooseNone();
    }
  }

  chooseNone() {
    let chosenItem = undefined;
    this.chosenItem = undefined;
    this.chosenItemEmitter.next(chosenItem)
  }


  onFocus() {
    this.inputBox.nativeElement.focus();
  }




  //wtf - straight from https://ng-bootstrap.github.io/#/components/typeahead/examples
  search = (text$: Observable<string>) =>
    text$.pipe
      (
        debounceTime(200),
        distinctUntilChanged(),
        map(term => term.length < this.charToStartSearch ? []
          : this.searchList.filter(v => v.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10))
      )



  //with every keypress and click on item in dropdown, content is passed to this
  onChoiceMade(chosenItem: string) {
    this.chooseItem(chosenItem)
    this.chosenItemEmitter.emit(chosenItem);

    if (this.clearInputOnChoiceMade) {
      this.clearInput();
    }
  }



  clearInitialValue() {
    if (this.initialValue) {
      this.initialValue = null;
      this.chosenItem = null;
    }
  }

  setChosenAndClearInput(event?: any) {
    if (event) {
      this.chosenItem = event.item;
    }
    this.chooseItem(this.chosenItem);
    this.chosenItemEmitter.emit(this.chosenItem);
    if (this.clearInputOnChoiceMade) {
      this.clearInput();
    }
  }

  clearInput() {
    setTimeout(() => {
      this.chosenItem = null;
    }, 1000)
  }
}


