<div id="outer" [ngClass]="customClassNames" class="spaceBetween" [ngStyle]="{'width': width}">
   <input #inputBox
          [ngClass]="{'chosenItem': chosenItem}"
          [ngStyle]="{'width': '100%'}"
          [tabindex]="tabindex"
          [placeholder]="placeholder"
          type="text"

          (keydown.enter)="onTypeaheadSelection($event)"
          [(ngModel)]="displayValue"
          [ngbTypeahead]="search"
          [resultTemplate]="rt"
          [inputFormatter]="inputFormatter"
          (selectItem)="onTypeaheadSelection($event)"
          (click)="onInputClick()"
          (input)="onInputChange($event)"
          [disabled]="disabled"
          container="body"
          id="typeAheadInput"/>

   <!-- Custom template for typeahead results -->
   <ng-template #rt let-r="result" let-t="term">
      <span [innerHTML]="getSafeHtml(r)"></span>
   </ng-template>

   <!-- The dropdown -->
   <div ngbDropdown [placement]="position === 'right' ? 'bottom-right' : 'bottom-left'" container="body" id="dropdownButtonWithCaret" class="d-inline-block dropdownButton">
      <button class="dropdownButton btn btn-primary"
              tabindex="-1"
              ngbDropdownToggle
              [disabled]="disabled">
      </button>

      <div ngbDropdownMenu
           id="dropdownMenuItemsHolder"
           class="dropdown-menu"
           aria-labelledby="dropdownBasic1">

         <!-- Clear button -->
         <button (click)="clearSelection()"
                 *ngIf="chosenItem && !hideClearButton"
                 ngbDropdownItem>
            --Clear--
         </button>

         <!-- List items -->
         <button *ngFor="let item of searchListLimited; trackBy: trackByValue"
                 (click)="onDropdownItemSelected(item)"
                 ngbDropdownItem>
            <span [innerHTML]="getSafeHtml(item.label)" style="pointer-events: none;"></span>
         </button>

         <!-- No items message -->
         <button disabled
                 ngbDropdownItem
                 *ngIf="searchList?.length === 0">
            (No Items Found)
         </button>
      </div>
   </div>
</div>
