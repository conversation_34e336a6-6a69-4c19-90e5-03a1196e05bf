#outer.fullWidth {
   width: 100%;
}

#outer.lowHeight .dropdownButton {
   height: 1.7em !important
}

#outer.noBorder input {
   border: 0px !important
}

#outer.lowHeight input {
   height: 1.7em !important
}

.dropdownButton {
   height: 2.4em;
   border-radius: 0 0.375em 0.375em 0;
}

#typeAheadInput {
   line-height: 2.4em;
   height: 2.4em !important;
}

.autoHeight .dropdownButton {
   height: auto;
}

input {
   border: 1px solid var(--grey80) !important;;
   height: 2.7em !important;
   width: 100%;
   border-radius: 0.375em 0 0 0.375em;
   padding-right: 2.7em;
   padding-left: 0.5em;
}

.autoHeight input {
   height: unset !important;
   padding: 0 0 0 0.5em !important;
}

#dropdownMenuItemsHolder {
   max-height: 42vh;
   overflow: auto
}

.autoHeight {
   align-items: stretch;
}
