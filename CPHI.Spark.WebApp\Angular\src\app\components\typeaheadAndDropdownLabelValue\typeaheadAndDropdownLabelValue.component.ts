import {Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, ViewChild, ChangeDetectionStrategy, ChangeDetectorRef} from "@angular/core";
import {Observable} from 'rxjs';
import {debounceTime, distinctUntilChanged, map} from 'rxjs/operators';
import {DomSanitizer, SafeHtml} from '@angular/platform-browser';

export interface LabelAndValue {
   label: string;
   value: any;
}

// Using type alias instead of duplicate interface
export type SelectedItem = LabelAndValue;

@Component({
   selector: 'typeaheadAndDropdownLabelValue',
   templateUrl: './typeaheadAndDropdownLabelValue.component.html',
   styleUrls: ['./typeaheadAndDropdownLabelValue.component.scss'],
   changeDetection: ChangeDetectionStrategy.OnPush
})
export class TypeaheadAndDropdownLabelValueComponent implements OnInit, OnChanges {
   // Essential inputs only
   @Input() public searchList: LabelAndValue[] = [];
   @Input() public width: string = '20em';
   @Input() public placeholder: string = 'Search...';
   @Input() public charToStartSearch: number = 1;
   @Input() public position: string = 'left';
   @Input() public tabindex: number = 0;
   @Input() public customClasses: string[] = [];
   @Input() public disabled: boolean = false;
   @Input() public hideClearButton: boolean = false;
   @Input() public clearInputOnChoiceMade: boolean = false;
   @Input() public showCreateOption: boolean = true;
   @Input() public createLabel: string = '+ Create';

   // Outputs
   @Output() public chosenItemEmitter: EventEmitter<SelectedItem> = new EventEmitter();
   @Output() public createNewItemEmitter: EventEmitter<string> = new EventEmitter();

   @ViewChild('inputBox', {static: true, read: ElementRef}) public inputBox!: ElementRef;

   // Internal state
   public searchListLimited: LabelAndValue[] = [];
   public chosenItem: LabelAndValue | null = null;
   public displayValue: string = '';
//   public typeaheadWidth: number = 17;
   public customClassNames: string = '';
   private lastInputValue: string = '';
   private isCreatingItem: boolean = false;

   // Performance optimization: cache for sanitized HTML
   private sanitizedHtmlCache = new Map<string, SafeHtml>();
   private searchListMap = new Map<string, LabelAndValue>();

   // DRY: Computed property for create prefix
   private get createPrefix(): string {
      return `${this.createLabel}: `;
   }

   constructor(
      private sanitizer: DomSanitizer,
      private cdr: ChangeDetectorRef
   ) {
   }

   ngOnInit(): void {
      this.initializeComponent();
   }

   ngOnChanges(changes: any): void {
      if (changes.searchList && changes.searchList.currentValue !== changes.searchList.previousValue) {
         this.updateSearchList();
         this.rebuildSearchListMap();
         // Clear sanitized HTML cache when list changes
         this.sanitizedHtmlCache.clear();
         this.cdr.markForCheck();
      }
   }

   private initializeComponent(): void {
      this.updateSearchList();
      this.calculateDimensions();
      this.buildCustomClassNames();
   }

   private updateSearchList(): void {
      this.searchListLimited = this.searchList?.slice(0, 1000) || [];
   }

   private rebuildSearchListMap(): void {
      this.searchListMap.clear();
      if (this.searchList) {
         for (const item of this.searchList) {
            this.searchListMap.set(item.label.toLowerCase(), item);
         }
      }
   }

   private calculateDimensions(): void {
//      this.typeaheadWidth = this.width - 2.7 - 0.25;
   }

   private buildCustomClassNames(): void {
      this.customClassNames = this.customClasses.join(' ');
   }

   public selectItem(item: LabelAndValue | null): void {
      this.chosenItem = item;
      this.displayValue = item?.label || '';

      // Simplified since SelectedItem is now the same as LabelAndValue
      this.chosenItemEmitter.emit(item || undefined);

      if (this.clearInputOnChoiceMade && item) {
         this.clearInput();
      }

      this.cdr.markForCheck();
   }

   public clearSelection(): void {
      this.selectItem(null);
   }

   public search = (text$: Observable<string>) =>
      text$.pipe(
         debounceTime(50),  // Reduced from 200ms to 50ms for faster response
         distinctUntilChanged(),
         map(term => {
            // Store the current input value
            this.lastInputValue = term;

            if (term.length < this.charToStartSearch) {
               return [];
            }

            // Use lowercase term once for performance
            const termLower = term.toLowerCase();
            const termTrimmed = term.trim();

            // Filter and limit in one pass for better performance
            const filteredItems: string[] = [];
            let exactMatch = false;
            const maxResults = 10;

            // Use pre-built map for faster lookups
            exactMatch = this.hasExactMatch(termLower);

            for (const item of this.searchList) {
               const labelLower = item.label.toLowerCase();

               // Use indexOf for slightly better performance on short strings
               if (labelLower.indexOf(termLower) !== -1) {
                  filteredItems.push(item.label);

                  if (filteredItems.length >= maxResults) {
                     break;
                  }
               }
            }

            // Add create option if enabled and no exact match found
            if (this.showCreateOption && termTrimmed !== '' && !exactMatch) {
               filteredItems.push(this.formatCreateOption(termTrimmed));
            }

            return filteredItems;
         })
      );


   public onDropdownItemSelected(chosenItem: LabelAndValue): void {
      this.selectItem(chosenItem);
   }

   public onInputChange(event: any): void {
      const value = event.target.value;

      // If the input was just set to a create option, immediately clear it
      if (this.isCreateOption(value)) {
         this.handleCreateOptionSelection(value, event.target);
      } else if (!this.isCreatingItem) {
         // Update last input value only if we're not in the middle of creating
         this.lastInputValue = value;
      }
   }

   public onTypeaheadSelection(event: any): void {

      if (!event?.item) return;

      const selectedLabel = typeof event.item === 'string' ? event.item : event.item.Label;

      // Handle create new item option
      if (this.isCreateOption(selectedLabel)) {
         const tagLabel = this.extractLabelFromCreateOption(selectedLabel);
         this.createNewItemEmitter.emit(tagLabel);
         return;
      }

      // Handle existing item selection
      const foundItem = this.searchList.find(item => item.label === selectedLabel);

      if (foundItem) {
         this.selectItem(foundItem);
      }
   }

   // Input formatter to prevent create options from being displayed in input
   public inputFormatter = (item: string): string => {
      // If it's a create option, return the original input value immediately
      if (this.isCreateOption(item)) {
         // Set flag to prevent input change tracking
         this.isCreatingItem = true;
         return this.lastInputValue;
      }

      // For regular items, return the item as is
      return item || '';
   }


   public onInputClick(): void {
      // Clear any initial value when user starts typing
      if (this.chosenItem && this.displayValue === this.chosenItem.label) {
         this.displayValue = '';
      }
   }

   private clearInput(): void {
      // Clear input after a short delay due to how ngbTypeahead works
      setTimeout(() => {
         this.selectItem(null);
         this.cdr.markForCheck();
      }, 1);
   }

   public getSafeHtml(html: string): SafeHtml {
      // Check cache first
      let cached = this.sanitizedHtmlCache.get(html);
      if (cached) {
         return cached;
      }

      // Sanitize and cache
      const sanitized = this.sanitizer.bypassSecurityTrustHtml(html);
      this.sanitizedHtmlCache.set(html, sanitized);

      // Limit cache size to prevent memory leaks
      if (this.sanitizedHtmlCache.size > 500) {
         // Remove oldest entries
         const firstKey = this.sanitizedHtmlCache.keys().next().value;
         this.sanitizedHtmlCache.delete(firstKey);
      }

      return sanitized;
   }

   // TrackBy function for ngFor performance
   public trackByValue(index: number, item: LabelAndValue): any {
      return item.value;
   }

   public shouldShowCreateOption(): boolean {
      if (!this.showCreateOption) {
         return false;
      }

      // Always show create option unless there's an exact match
      if (!this.displayValue || this.displayValue.trim() === '') {
         return true;
      }

      const currentInput = this.displayValue.trim().toLowerCase();
      return !this.hasExactMatch(currentInput);
   }

   // DRY: Centralized helper methods
   private isCreateOption(value: string): boolean {
      return value && value.startsWith(this.createPrefix);
   }

   private extractLabelFromCreateOption(value: string): string {
      return value.replace(this.createPrefix, '').trim();
   }

   private formatCreateOption(label: string): string {
      return `${this.createPrefix}${label}`;
   }

   private hasExactMatch(termLower: string): boolean {
      return this.searchListMap.has(termLower);
   }

   private handleCreateOptionSelection(value: string, target: HTMLInputElement): void {
      this.isCreatingItem = true;
      const newItemLabel = this.extractLabelFromCreateOption(value);

      // Immediately restore the original input value
      this.displayValue = this.lastInputValue;
      target.value = this.lastInputValue;

      // Emit the create event
      this.createNewItemEmitter.emit(newItemLabel);

      // Reset flag after a short delay
      setTimeout(() => {
         this.isCreatingItem = false;
         this.cdr.markForCheck();
      }, 100);
   }

   public onCreateNewItem(): void {
      const tagLabel = this.displayValue?.trim() || '';
      if (tagLabel) {
         this.createNewItemEmitter.emit(tagLabel);
      }
   }
}


