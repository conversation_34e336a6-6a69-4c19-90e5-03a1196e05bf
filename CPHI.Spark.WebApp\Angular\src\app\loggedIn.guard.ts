import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';
import { ConstantsService } from './services/constants.service';
import { SelectionsService } from './services/selections.service';
import { PageNameRoutes } from './app-routing.module';


@Injectable({
  providedIn: 'root'
})
export class LoggedInGuard implements CanActivate {


  constructor(
    private selections: SelectionsService,
    public constants: ConstantsService,
    private router: Router

  ) { }

  private waitForUserClaimsToLoad(): Promise<boolean> {
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (this.constants.UserClaims) { // Check if the property is populated
          clearInterval(checkInterval);
          resolve(true);
        }
      }, 100); // Check every 100ms
    });
  }


  async canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Promise<boolean> {

    const loaded = await this.waitForUserClaimsToLoad();


    if (localStorage.getItem('accessToken')) {
      const urlPath = route.url.length > 0 ? route.url[0].path : '';

      //debugger;
      // Menu & Home Page & Routing (New common access check)
      switch(urlPath){

        //Dashboards
        case 'sitesLeague': {
          return this.selections.isPageVisible(PageNameRoutes.sitesLeague);
        }
        case 'statsDashboard': {
          return this.selections.isPageVisible(PageNameRoutes.statsDashboard);
        }
        case 'stockInsight': {
          return this.selections.isPageVisible(PageNameRoutes.stockInsight);
        }

        //Reports
        case 'AdvertSimpleListingSeparate': {
          this.selections.isPageVisible(PageNameRoutes.advertSimpleListing)
        }
        case 'advertListingDetail': {
          return this.selections.isPageVisible(PageNameRoutes.advertListingDetail);
        }
        case 'LeavingVehicleTrendsSeparate': {
          return this.selections.isPageVisible(PageNameRoutes.leavingVehicleTrends)
        }
        case 'LeavingVehicleTrendsOverTime': {
          return this.selections.isPageVisible(PageNameRoutes.leavingVehicleTrendsOverTime);
        }
        case 'LeavingVehicleDetailSeparate': {
          return this.selections.isPageVisible(PageNameRoutes.leavingVehicleDetail);
        }
        case 'LeavingVehicleExplorer': {
          return this.selections.isPageVisible(PageNameRoutes.leavingVehicleExplorer);
        }

        //Sourcing Tool
        case 'BulkValuationSeparate': {
          return this.selections.isPageVisible(PageNameRoutes.bulkValuation);
        }
        case 'LocalBargainsSeparate': {
          return this.selections.isPageVisible(PageNameRoutes.localBargains);
        }
        case 'LocationOptimiserSeparate': {
          return this.selections.isPageVisible(PageNameRoutes.locationOptimiser)
        }
        
        //Pricing Page
        case 'TodaysPricesSeparate': {
          return this.selections.isPageVisible(PageNameRoutes.todaysPrices);
        }
        case 'OptOutsSeparate': {
          return this.selections.isPageVisible(PageNameRoutes.optOuts);
        }

         //Settings
         case 'SiteSettings': {
          return this.selections.isPageVisible(PageNameRoutes.siteSettings)
        }
        case 'UserMaintenance': {
          return this.selections.isPageVisible(PageNameRoutes.userMaintenance);
        }
        case 'UsageReport': {
          return this.selections.isPageVisible(PageNameRoutes.usageReport);
        }


        case 'dashboard': {
          return this.selections.isPageVisible(PageNameRoutes.dashboard);
        }
        case 'orderBook': { 
          return this.selections.isPageVisible(PageNameRoutes.orderBook);
        } 
        case 'dealsDoneThisWeek': { 
          return this.selections.isPageVisible(PageNameRoutes.dealsDoneThisWeek);
        } 
        case 'dealsForTheMonth': { 
          return this.selections.isPageVisible(PageNameRoutes.dealsForTheMonth);
        } 
        case 'whiteboard': { 
          return this.selections.isPageVisible(PageNameRoutes.whiteboard);
        } 
        case 'performanceLeague': { 
          return this.selections.isPageVisible(PageNameRoutes.performanceLeague);
        } 
        case 'performanceTrends': { 
          return this.selections.isPageVisible(PageNameRoutes.performanceTrends);
        } 
        case 'handoverDiary': { 
          return this.selections.isPageVisible(PageNameRoutes.handoverDiary);
        } 
        case 'scratchCard': { 
          return this.selections.isPageVisible(PageNameRoutes.scratchCard);
        } 
        // case 'stockPricing': { 
        //   return this.selections.isPageVisible(PageNameRoutes.stockPricing);
        // } 
        case 'stockList': { 
          return this.selections.isPageVisible(PageNameRoutes.stockList);
        } 
        case 'stockLanding': { //replacement of stocklanding gaurd
          return this.selections.isPageVisible(PageNameRoutes.stockLanding);
        }
        case 'fleetOrderbook': {
          return this.selections.isPageVisible(PageNameRoutes.fleetOrderbook);
        }

        case 'salesCommission': { //replacement of commissiongaurd
          return this.selections.isPageVisible(PageNameRoutes.salesCommission);
        }
       
        
        case 'reportingCentre': {
          return this.selections.isPageVisible(PageNameRoutes.reportingCentre);
        }
       
        case 'stockInsight': {
          return this.selections.isPageVisible(PageNameRoutes.stockInsight);
        }
        // case 'stockOverviewBySite': {
        //   return this.selections.isPageVisible(PageNameRoutes.stockOverview);
        // }
        case 'salesExecReview': {
          return this.selections.isPageVisible(PageNameRoutes.salesExecReview);
        }
        case 'liveForecastStatus': {
          return this.selections.isPageVisible(PageNameRoutes.liveForecastStatus);
        }
        case 'liveForecastInput': {
          return this.selections.isPageVisible(PageNameRoutes.liveForecastInput);
        }
        case 'strategyPriceBuildUp': {
          return this.selections.isPageVisible(PageNameRoutes.strategyPriceBuildUp);
        }

      }



     
      
     
   


   
      return true;
    } else {
      this.router.navigate(['/login']);
    }
    return false;

  }


}

