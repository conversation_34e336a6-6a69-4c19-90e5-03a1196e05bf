import {FormatType} from "../cph.pipe";


export interface BarChartParams {
   Labels: string[];
   Values: number[];
   Colours: string[];
   Title: string;
   AverageLineValue: number;
   DataFormat: FormatType;
   yMin: number;
   yMax: number;
   change?: number;
   percentageValuesWas?: number[];
   percentageValuesNow?: number[];
   average?: number;
   borderWidth?: number;
   labelOffset?: number;
   labelAlign?: 'center' | 'top' | 'bottom' | 'left' | 'right';
   labelAnchor?: 'center' | 'end' | 'start';
   inlinePercentageLabels?: boolean;
   showYAxisLabels: boolean;
   yAxisTitle?: string;

   // For stacked bars - generic structure for multiple datasets
   StackedDatasets?: {
      label: string;
      data: number[];
      backgroundColor: string[];
      borderColor?: string[];
      borderWidth?: number[];
   }[];
}
