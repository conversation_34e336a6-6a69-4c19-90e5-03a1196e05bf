import {ColDef} from "ag-grid-community";


export interface CPHColDef extends ColDef {
   shouldAverage?: boolean;
   shouldAverageIfValue?: boolean;
   shouldTotal?: boolean;
}

export enum GridValueGetterEnum {
   daysCoverGetter = 1,
   AgeAndOwnersGetter = 2,
   formatTestStrategyBandLabel = 3,
   DaysListedGetter = 4,
   LastCommentGetter = 5,
   PriceProfitGetter = 6,
   PerfRating = 7,
   SparklineDataGetter = 8,
   formatStrategyBandLabel = 9,
}

export enum FilterValueGetterEnum {
   AdvertTags = 1
}

export enum GridCellRendererEnum {
   AutoTraderLozengeRenderer = 1,
   CustomImageCellRenderer = 2,
   CustomAdLinkRenderer = 3,
   LastCommentRenderer = 4,
   autoTraderPriceIndicatorRenderer = 5,
   agSparklineCellRenderer = 6,
   AdvertTags = 7
}

export enum GridCellRendererParamsEnum {
   ProvideSparkLineParams = 1
}

export enum GridComparatorEnum {
      getSortOrderForAgeBand = 1,
      getSortOrderForValueBand = 2,
      getSortOrderForDaysBand = 3,
      getSortOrderForVsStrategyBanding = 4,
      getSortOrderForRetailRatingBand = 5,
      getSortOrderForPriceIndicator = 6,
      getSortOrderForPerfRating = 7,
      getSortOrderForMileage = 8,
      getSortOrderForPPBand = 9
}


export enum GridColumnTypeEnum {
   LabelSetFilter = 1,
   Label = 2,
   Number = 3,
   Number1dp = 4,
   Date = 5,
   Boolean = 6,
   Image = 7,
   Special = 8,
   Percent = 9,
   Percent1dp = 10,
   Currency = 11,
   Decimal = 12,
   CurrencyWithPlusMinus = 13
}

export enum GridColumnSectionEnum {
   Site = 1,
   Vehicle = 2,
   StockInformation = 3,
   AdvertDetails = 4,
   VehicleMetrics = 5,
   CompetitorInformation = 6,
   Costings = 7,
   Valuation = 8,
   ValuationAverageSpec = 9,
   AdvertisedPrice = 10,
   AdvertPerformance = 11,
   TodaysPriceChange = 12,
   OptOuts = 13,
   PriceChanges = 14,
   PriceChangesManually = 15,
   BCAInformation = 16,
   Analysis = 17,
   TestStrategy = 18
}

export interface CPHAutoPriceColDef extends CPHColDef {

   columnSection?: string;
   explanation?: ((params: CPHColDef) => string);
   hasExplanation?:boolean;
   children?: CPHColDef[];

   // These are passed from the API
   gridValueGetterEnum?: GridValueGetterEnum;
   gridComparatorEnum?: GridComparatorEnum;
   gridCellRendererEnum?: GridCellRendererEnum;
   gridCellRendererParamsEnum?: GridCellRendererParamsEnum;

   gridColumnTypeEnum?: GridColumnTypeEnum;
   gridColumnSectionEnum?: GridColumnSectionEnum;

   provideExplanation?: boolean;
   filterValueGetterEnum?: FilterValueGetterEnum;
}

