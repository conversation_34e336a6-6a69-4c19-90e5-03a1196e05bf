export class LVExplorerItem {
  // Props derived from LeavingVehicleItem
  FirstSnapId: number;
  LastSnapId: number;
  AdvertId: number;
  Region: string;
  RetailerSiteName: string;
  RetailerId: number;
  RetailerSiteId: number;
  BodyType: string;
  FuelType: string;
  TransmissionType: string;
  Make: string;
  Model: string;
  RegYear: number;
  VehicleReg: string;
  DerivativeId: string;
  Derivative: string;
  MakeModelDerivative: string;
  ListedDate: Date;
  RemovedDate: Date;
  FirstRegisteredDate: Date;
  Mileage: number;
  EarlierOfDoFAndCreatedInSparkDate: Date;
  DaysListed: number;
  FirstPrice: number;
  FirstValuation: number;
  LastPrice: number;
  LastValuation: number;
  LastPriceIndicator: string;
  LastRetailRating: number;
  FirstRetailDaysToSell: number;
  DaysListedVsFirstRetailDaysToSell: number;
  ImageURL: string;
  DaysListedBand: string;
  FirstPP: number;
  FirstPPBand: string;
  LastPP: number;
  LastPPBand: string;
  RetailRatingBand: string;
  MileageBand: string;
  LastPriceBand: string;
  AgeBand: string;
  DaysOnStrategy: number;
  IsOnStrategy: boolean;
  DaysOptedOut?: number;
  PercentDaysOptedOut: number;
  OptedOutPctBand: string;
  AchievedSaleType: string;
  RC_IsSold?: boolean;
  RC_DaysListed?: number;
  RC_Price?: number;
  RC_PPPct?: number;
  RC_SellerName: string;
  RC_Segment: string;
  RC_Valuation?: number;
  RC_SoldDate?: Date;
  DidBeatWholesaleTarget: boolean;
  DateOnForecourt?: Date;
  CreatedInSparkDate: Date;

  // Props derived from OrderbookDetailedRow
  DealId: number;
  StockNumber: string;
  Reg: string;
  OemReference: string;
  LastPhysicalLocation: string;
  ModelYear: number;
  Description: string;
  VehicleAge: number;
  StockDate?: Date;
  RegisteredDate?: Date;
  IsInvoiced: boolean;
  Customer: string;
  FinanceCo: string;
  OrderDate: Date;
  InvoiceDate?: Date;
  IsLateCost: boolean;
  ActualDeliveryDate?: Date;
  HandoverDate?: Date;
  IsDelivered: boolean;
  Units: number;
  Sale: number;
  CoS: number;
  Discount: number;
  FuelSale: number;
  FuelCost: number;
  OemDeliverySale: number;
  OemDeliveryCost: number;
  AccessoriesSale: number;
  AccessoriesCost: number;
  PDICost: number;
  MechPrep: number;
  BodyPrep: number;
  NewBonus1: number;
  NewBonus2: number;
  PaintProtectionAccessorySale: number;
  PaintProtectionAccessoryCost: number;
  BrokerCost: number;
  IntroCommission: number;
  Error: number;
  Other: number;
  VatCost: number;
  PartExOverAllowance1: number;
  FactoryBonus: number;
  RegBonus: number;
  IsFinanced: boolean;
  HasServicePlan: boolean;
  HasTyre: boolean;
  HasAlloy: boolean;
  HasTyreAlloy: boolean;
  HasWheelGuard?: boolean;
  HasCosmetic: boolean;
  HasPaintProtection: boolean;
  HasGap: boolean;
  HasWarranty: boolean;
  TotalProductCount: number;
  FinanceCommission: number;
  FinanceSubsidy: number;
  SelectCommission: number;
  ProPlusCommission: number;
  StandardsCommission: number;
  RCIFinanceCommission: number;
  ServicePlanSale: number;
  ServicePlanCost: number;
  ServicePlanCommission: number;
  TyreSale: number;
  TyreCost: number;
  TyreCommission: number;
  AlloySale: number;
  AlloyCost: number;
  AlloyCommission: number;
  TyreAlloySale: number;
  TyreAlloyCost: number;
  TyreAlloyCommission: number;
  WheelGuardSale: number;
  WheelGuardCost: number;
  WheelGuardCommission: number;
  CosmeticSale: number;
  CosmeticCost: number;
  CosmeticCommission: number;
  PaintProtectionSale: number;
  PaintProtectionCost: number;
  GapSale: number;
  GapCost: number;
  GapCommission: number;
  WarrantySale: number;
  WarrantyCost: number;
  StandardWarrantyCost: number;
  SalesmanId: number;
  SalesmanDmsId: string;
  SalesmanName: string;
  SiteDescription: string;
  SiteId: number;
  SiteCode: number;
  RegionDescription: string;
  IsCar: boolean;
  Franchise: string;
  OrderType: string;
  OrderTypeDescription: string;
  VehicleType: string;
  VehicleTypeCode: string;
  VehicleTypeDescription: string;
  DeliverySiteDescription: string;
  VehicleSource: string;
  VehicleSale: number;
  VehicleCost: number;
  AccountingDate: Date;
  EnquiryNumber: number;
  IsClosed: boolean;
  FinanceType: string;
  MetalProfit: number;
  OtherProfit: number;
  AddOnProfit: number;
  FinanceProfit: number;
  TotalProfit: number;
  LastAdvertisedPrice: number;
  Variant: string;
  VariantTxt: string;
  Delivered: boolean;
  VehicleClassDescription: string;
  DealfileSentDate: string;
  Comment: string;
}


