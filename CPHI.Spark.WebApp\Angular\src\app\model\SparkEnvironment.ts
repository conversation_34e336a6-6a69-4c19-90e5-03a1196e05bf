import { DashboardSection } from "../pages/dashboard/dashboard.component";
import { AgeingOptionsEntityOrAgeingOption } from "../services/environment.service";
import { Channel } from "./sales.model";

export interface SparkEnvironment {

  // Basics
  customer: string;
  production: boolean;
  version: string;

  commissionSchemeName: string; // For the main scheme if applicable
  isSingleSiteGroup: boolean;
  homeIsLandingPage: boolean;
  noDateSuffix: boolean; // st, nd, rd etc.
  
  // URLS
  initialPageURL: string;
  orderBookURL: string;
  fleetOrderbookURL: string;
  dealershipBackgroundImageName: string;

  // Pickers
  languageSelection: boolean;  
  franchisePicker: boolean;
  spainFranchisePickerClass: boolean; // Specific CSS for Spain 
  stockGroupPicker: boolean;
  lateCostPicker: boolean;
  orderTypePicker: boolean;

  // Codes & Channels
  serviceChannels?: (Channel)[] | null;
  partsChannels?: (Channel)[] | null;

  allGroups: string[];
  allFamilyCodes: string[];

  // Currency
  displayCurrency: string;
  displayCurrencySymbol: string;

  // Bookings Bar
  bookingsBar_barStyle1: boolean;
  bookingsBar_barStyle2: boolean;

  // CitNow
  citNoww_eDynamixView: boolean;
  citNoww_excludeAudi: boolean;
  citNoww_hideAftersalesCharts: boolean;
  citNoww_pcOfInvoicedExtWips: boolean;
  citNoww_pcOfInvoicedExtWipsText: string;
  citNoww_pcOfSalesEnquiries: boolean;
  citNoww_pcOfSalesEnquiriesText: string;
  citNoww_showSimpleCitNowPersonDetail: boolean;
  citNoww_showVideosViewed: boolean;

  // Dashboard
  dashboard_canChooseMonth: boolean;
  dashboard_sections: DashboardSection[];
  dashboard_showStockCover: boolean;
  dashboard_includeExtraSpainMenuButtons: boolean;
  dashboard_showZoeSales: boolean;
  dashboard_showHandoverDiarySummary: boolean;
  dashboard_showCashDebts: boolean;
  dashboard_showBonusDebts: boolean;
  dashboard_showRenaultRegistrations: boolean;
  dashboard_showDaciaRegistrations: boolean;
  dashboard_showFleetRegistrations: boolean;
  dashboard_showUsedStockMerchandising: boolean;
  dashboard_showUsedStockOverage: boolean;
  dashboard_showNewStockOverage: boolean;
  dashboard_showTradeStockOverage: boolean;
  dashboard_showCommissions: boolean;
  dashboard_showFinanceAddonPerformance: boolean;
  dashboard_showfAndIPerformanceRRG: boolean;
  dashboard_showVocNPSTile: boolean;
  dashboard_showActivityLevels: boolean;
  dashboard_showActivityOverdues: boolean;
  dashboard_showFleetPerformance: boolean;
  dashboard_showVoC: boolean;
  dashboard_showServiceBookings: boolean;
  dashboard_showCitNow: boolean;
  dashboard_showImageRatios: boolean;
  dashboard_showSalesmanEfficiency: boolean;
  dashboard_showEvhc: boolean;
  dashboard_showFinanceAddons: boolean;
  dashboard_showRegistrations: boolean;
  dashboard_showSimpleDealsByDay: boolean;
  dashboard_excludeTypesFromBreakDown: string[];
  dashboard_showFinanceAddonsAllSites: boolean;
  dashboard_includeDemoStockInStockHealth: boolean;
  dashboard_showVindisSalesDashboard: boolean;
  dashboard_showRRGSalesDashboard: boolean;
  dashboard_showDataOriginsButton: boolean;
  dashboard_showAftersalesDatasets: boolean;
  dashboard_showVindisAftersalesDashboard: boolean;
  dashboard_showRRGAftersalesDashboard: boolean;
  dashboard_showSpainAftersalesDashboard: boolean;

  // DealDetailModal
  dealDetailModal_componentName: string;
  dealDetailModal_currencyDP: number;
  dealDetailModal_costColumnTranslation: string;

  dealDetailModal_dealDetailsSection_showVariant: boolean;
  dealDetailModal_dealDetailsSection_showWebsiteDiscount: boolean;
  dealDetailModal_dealDetailsSection_showFinanceType: boolean;
  dealDetailModal_dealDetailsSection_showOEMReference: boolean;
  dealDetailModal_dealDetailsSection_showQualifyingPartEx: boolean;
  dealDetailModal_dealDetailsSection_showPhysicalLocation: boolean;
  dealDetailModal_dealDetailsSection_showIsClosed: boolean;
  dealDetailModal_dealDetailsSection_showFinanceCo: boolean;
  dealDetailModal_dealDetailsSection_showDescription: boolean;
  dealDetailModal_dealDetailsSection_showUnits: boolean;
  dealDetailModal_dealDetailsSection_showVehicleAge: boolean;
  dealDetailModal_dealDetailsSection_showIsLateCost: boolean;
  dealDetailModal_dealDetailsSection_showAuditPass: boolean;
  dealDetailModal_dealDetailsSection_showInvoiceNo: boolean;

  dealDetailModal_metalProfitSection_headerTranslation: string;
  dealDetailModal_metalProfitSection_showVATCost: boolean;

  dealDetailModal_otherProfitSection_showRegBonus: boolean;
  dealDetailModal_otherProfitSection_showIntroComm: boolean;
  dealDetailModal_otherProfitSection_showBrokerCost: boolean;
  dealDetailModal_otherProfitSection_showAccessories: boolean;
  dealDetailModal_otherProfitSection_showPaintProtectionAccessory: boolean;
  dealDetailModal_otherProfitSection_showFuel: boolean;
  dealDetailModal_otherProfitSection_showDelivery: boolean;
  dealDetailModal_otherProfitSection_showStandardWarranty: boolean;
  dealDetailModal_otherProfitSection_showPdi: boolean;
  dealDetailModal_otherProfitSection_showMechPrep: boolean;
  dealDetailModal_otherProfitSection_showBodyPrep: boolean;
  dealDetailModal_otherProfitSection_showOther: boolean;
  dealDetailModal_otherProfitSection_showError: boolean;
  dealDetailModal_otherProfitSection_showTotal: boolean;

  dealDetailModal_datesSection_showCustomerDestinationDeliveryDate: boolean;
  dealDetailModal_datesSection_showEnterImportCentreDate: boolean;
  dealDetailModal_datesSection_showShipDate: boolean;
  dealDetailModal_datesSection_showExitImportCentreDate: boolean;
  dealDetailModal_datesSection_showAllocationDate: boolean;
  dealDetailModal_datesSection_showDateVehicleRecondition: boolean;
  dealDetailModal_datesSection_showDateFactoryTransportation: boolean;
  dealDetailModal_datesSection_showDateSiteArrival: boolean;
  dealDetailModal_datesSection_showDateSiteTransportation: boolean;

  dealDetailModal_financeProfitSection_show: boolean;
  dealDetailModal_financeProfitSection_rciFinanceCommissionText: string;
  dealDetailModal_financeProfitSection_financeCommissionText: string;
  dealDetailModal_financeProfitSection_showSelectCommission: boolean;
  dealDetailModal_financeProfitSection_showProPlusCommission: boolean;
  dealDetailModal_financeProfitSection_showStandardsCommission: boolean;

  dealDetailModal_addonsSection_showPaintProtection: boolean;
  dealDetailModal_addonsSection_showWarrantyForNewCar: boolean;

  dealDetailModal_showTotalProfitExludingFactoryBonusSection: boolean;
  dealDetailModal_showTotalProfitSection: boolean;
  dealDetailModal_showOtherProfit: boolean;
  dealDetailModal_showAddOnProfit: boolean;
  dealDetailModal_showStockDetailButton: boolean;
  dealDetailModal_showDealFileSentDate: boolean;
  dealDetailModal_showSmallModal: boolean;
  dealDetailModal_enableStockItemModalAutotrader: boolean;
  dealDetailModal_enableSalesExecPicker: boolean; // Can amend exec if deal is delivered (Vindis only)

  // DealDone
  dealDone_showVindisSitePicker: boolean;
  dealDone_showRRGSitePicker: boolean;
  dealDone_showRRGPopoverContent: boolean;
  dealDone_showVindisPopoverContent: boolean;

  // DealsDoneThisWeek
  dealsDoneThisWeek_showPlotOptions: boolean;

  // DealPopover
  dealPopover_showMetalProfit: boolean;
  dealPopover_showOtherProfit: boolean;
  dealPopover_showFinanceProfit: boolean;
  dealPopover_showAddons: boolean;
  dealPopover_showAddonProfit: boolean;

  // DealsForTheMonth
  dealsForTheMonth_showMetal: boolean;
  dealsForTheMonth_showOther: boolean;
  dealsForTheMonth_showFinance: boolean;
  dealsForTheMonth_showAddons: boolean;
  dealsForTheMonth_showGpu: boolean;
  dealsForTheMonth_showBroughtInColumn: boolean;
  dealsForTheMonth_showIncludeExcludeOrders: boolean;
  dealsForTheMonth_showLateCostPicker: boolean;
  dealsForTheMonth_showSpainSpacing: boolean;

  // DepartmentDealBreakdown
  departmentDealBreakDown_RRG: boolean;
  departmentDealBreakDown_Vindis: boolean;

  // Debts
  debts_includeBonuses: boolean;
  debts_showAgedOnPicker: boolean;
  debts_showBonusDebtType: boolean;
  debts_simpleSitesTable: boolean;

  // Donut
  donutTile_ShowLastYearUnits: boolean;
  donutTile_ShowInvoicingTitle: boolean;
  donutTile_SpainAdditionalMeasures: boolean;

  // Evhc
  evhc_showTechTable: boolean;
  evhc_vehiclesCheckedPercent: number,
  evhc_workQuoted: number,
  evhc_workSoldPercent: number,
  evhc_redWorkSoldPercent: number;
  evhc_amberWorkSoldPercent: number;
  evhc_eDynamixView: boolean;

  // F&I
  fAndISummary_hideAlloyColumn: boolean;
  fAndISummary_showManagerTable: boolean;
  fAndISummary_removeFleetFromDefaultOrderTypes: boolean;

  // Handover Diary
  handoverDiary_includeCustomerName: boolean;
  handoverDiary_includeHandoverDate: boolean;
  handoverDiary_includeLastPhysicalLocation: boolean;
  handoverDiary_isConfirmed: boolean;
  handoverDiary_isInvoiced: boolean;
  handoverDiary_showManagerSelector: boolean;

  // Horizontal Bar
  horizontalBar_forRenault: boolean;
  horizontalBar_forVindis: boolean;

  
  // Side Menu
  sideMenu_teleStats: boolean;
  sideMenu_salesPerformance: boolean;
  sideMenu_alcopa: boolean;
  sideMenu_registrationsPosition: boolean;
  sideMenu_fAndISummary: boolean;

  sideMenu_groupTitle: string;
  sideMenu_dashboard: boolean;
  sideMenu_pricingDashboard: boolean;
  sideMenu_orderBook: boolean;
  sideMenu_fleetOrderbook: boolean;
  sideMenu_dealsDoneThisWeek: boolean;
  sideMenu_dealsForTheMonth: boolean;
  sideMenu_whiteboard: boolean;
  sideMenu_performanceLeague: boolean;
  sideMenu_performanceTrends: boolean;
  sideMenu_scratchCard: boolean;
  sideMenu_salesIncentive: boolean;
  sideMenu_superCup: boolean;
  sideMenu_superCupTwo: boolean;
  sideMenu_handoverDiary: boolean;
  sideMenu_distrinet: boolean;
  sideMenu_reportingCentre: boolean;
  sideMenu_stockList: boolean;
  sideMenu_pricingHome: boolean;
  sideMenu_stockInsight: boolean;
  sideMenu_applyStrategy: boolean;
  sideMenu_advertListingDetail: boolean; // new
  sideMenu_leavingVehicles: boolean;
  sideMenu_bulkValuation: boolean; // new
  sideMenu_localBargains: boolean;
  sideMenu_stockLanding: boolean;
  sideMenu_liveForecastInput: boolean;
  sideMenu_liveForecastStatus: boolean;
  sideMenu_liveForecastReview: boolean;
  sideMenu_userMaintenance: boolean;
  sideMenu_debtsSales: boolean;
  sideMenu_debtsAfterSales: boolean;
  sideMenu_citnow: boolean;
  sideMenu_imageRatios: boolean;
  sideMenu_gdpr: boolean;
  sideMenu_serviceSummary: boolean;
  sideMenu_serviceBookings: boolean;
  sideMenu_evhc: boolean;
  sideMenu_upsells: boolean;
  sideMenu_wipReport: boolean;
  sideMenu_partsStock: boolean;
  sideMenu_salesmanEfficiency: boolean;
  sideMenu_salesCommission: boolean;
  sideMenu_salesExecReview: boolean;
  sideMenu_stockReport: boolean;
  sideMenu_todaysPrices: boolean;
  sideMenu_optOuts: boolean;
  sideMenu_locationOptimiser: boolean;
  sideMenu_vehicleValuation: boolean;
  sideMenu_leavingVehicleTrends: boolean;
  sideMenu_leavingVehicleTrendsOverTime: boolean;
  sideMenu_leavingVehicleDetail: boolean;
  sideMenu_leavingVehicleExplorer: boolean;
  sideMenu_siteSettings: boolean;
  sideMenu_scheduledReports:boolean;
  sideMenu_stockReport_showBcaColumns: boolean;
  sideMenu_orderBookNew: boolean;
  sideMenu_simpleExample: boolean;
  sideMenu_salesActivity: boolean;
  sideMenu_partsSummary: boolean;
  sideMenu_statsDashboard: boolean;
  sideMenu_stockProfiler:boolean;
  sideMenu_sitesLeague: boolean;
  sideMenu_advertSimpleListing: boolean;
  sideMenu_home: boolean;
  sideMenu_ShowDetailedMenu: boolean;
  sideMenu_stockReportOld: boolean;
  sideMenu_hasVehiclePricing: boolean;
  sideMenu_showOrderRate: boolean;
  sideMenu_hasOperationReports: boolean;
  sideMenu_dashboardSales: boolean;
  sideMenu_dashboardAfterSales: boolean;
  sideMenu_siteCompare: boolean;
  sideMenu_dashboardNewKPIs: boolean;
  sideMenu_dashboardUsedKPIs: boolean;
  sideMenu_dashboardOverviewSpain: boolean;
  sideMenu_orderRate: boolean;
  sideMenu_dashboardOverviewVindis: boolean;
  sideMenu_strategyPriceBuildUp: boolean;



  
  // Order Book
  orderBook_orderbookTitle: string;
  orderBook_showNewOrderbook: boolean,
  orderBook_showNewDealButton: boolean;
  orderBook_ordersDescription: string;
  orderBook_hideDeliverySiteColumn: boolean;
  orderBook_hideVehicleTypeColumn: boolean;
  orderBook_hideOemReferenceColumn: boolean;
  orderBook_hideVehClassColumn: boolean;
  orderBook_hideModelColumn: boolean;
  orderBook_hideModelYearColumn: boolean;
  orderBook_hideChannelColumn: boolean;
  orderBook_hideTypeColumn: boolean;
  orderBook_hideVehicleSourceColumn: boolean;
  orderBook_hideDaysToDeliverColumn: boolean;
  orderBook_hideDaysToSaleColumn: boolean;
  orderBook_hideLocationColumn: boolean;
  orderBook_hideIsConfirmedColumn: boolean;
  orderBook_hideVehTypeColumn: boolean;
  orderBook_hideIsClosedColumn: boolean;
  orderBook_hideUnitsColumn: boolean;
  orderBook_hideFinanceTypeColumn: boolean;
  orderBook_hideIsLateCostColumn: boolean;
  orderBook_hideAddonsColumn: boolean;
  orderBook_includeAccgDate: boolean;
  orderBook_customDateTypes?: (string)[] | null;
  orderBook_defaultDateType: string;
  orderBook_showLateCost: boolean;
  orderBook_showOrderOptions: boolean;
  orderBook_showAccountingDateButton: boolean;
  orderBook_showDeliveryOptionButtons: boolean;
  orderBook_hideDiscountColumn: boolean;
  orderBook_hideFinanceProfitColumn: boolean;
  orderBook_hideOtherProfitColumn: boolean;
  orderBook_hideMetalColumn: boolean;
  orderBook_hideSalesChannel: boolean;
  orderBook_hideComments: boolean;
  orderBook_hideOrderAllocationDate: boolean;
  orderBook_showMetalSummary: boolean;
  orderBook_showOtherSummary: boolean;
  orderBook_showFinanceSummary: boolean;
  orderBook_showInsuranceSummary: boolean;
  orderBook_siteColumnWidth: number;
  orderBook_customerColumnWidth: number;
  orderBook_vehicleClassColumnWidth: number;
  orderBook_salesExecColumnWidth: number;
  orderBook_descriptionColumnWidth: number;
  orderBook_hideOrderDateSelection: boolean;
  orderBook_hideAuditColumn: boolean;
  orderBook_showManagerSelector: boolean;
  orderBook_hideDateFactoryTransportationColumn: boolean;
  orderBook_hideDateVehicleReconditionColumn: boolean;
  orderBook_hideDateSiteTransportationColumn: boolean;
  orderBook_hideDateSiteArrivalColumn: boolean;
  orderBook_hideReservedDateColumn: boolean;

  ordersDonutTile_SpainSettings: boolean;

  // OrderTypePicker
  orderTypePicker_showRetail: boolean;
  orderTypePicker_showFleet: boolean;
  orderTypePicker_rrgSpainSettings: boolean;
  orderTypePicker_vindisSettings: boolean;

  // Overage Stock Table
  overAgeStockTable_hideDemoColumn: boolean;
  overAgeStockTable_hideTacticalColumn: boolean;
  overAgeStockTable_hideExManagementColumn: boolean;
  overAgeStockTable_hideExDemoColumn: boolean;
  overAgeStockTable_hideTradeColumn: boolean;
  overAgeStockTable_usedColumnName: string;

  // Parts Sales
  partsSales_includeMarginCols: boolean;
  partsSales_showMarginCol: boolean;
  partsSales_showMarginColPerc: boolean;

  partsStockDetailedTable_hideCreatedColumn: boolean;
  partsStockDetailedTable_partStockBarCharts1_headerName: string;
  partsStockDetailedTable_partStockBarCharts1_field: string;
  partsStockDetailedTable_partStockBarCharts1_colId: string;
  partsStockDetailedTable_partStockBarCharts2_headerName: string;
  partsStockDetailedTable_partStockBarCharts2_field: string;
  partsStockDetailedTable_partStockBarCharts2_colId: string;
  partsStockDetailedTable_showPartStockAgeingColumnsForRRG: boolean;
  partsStockDetailedTable_showPartStockAgeingColumnsForVindis: boolean;
  partsStockDetailedTable_hideOfWhichColumn: boolean;
  partsStockDetailedTable_hideDeadValueColumn: boolean;
  partsStockDetailedTable_hideDormantValueColumn: boolean;
  partsStockDetailedTable_hideDeadProvColumn: boolean;
  partsStockDetailedTable_hideDormantProvColumn: boolean;
  partsStockDetailedTable_setClassesForVindis: boolean;
  partsStockDetailedTable_setClassesForRRG: boolean;

  // Parts Stock Sites Cover
  partsStockSitesCoverTable_partStockName: string;

  // Parts Summary
  partsSummary_showTableTypeSelector: boolean;
  partsSummary_tableTypes: string[];

  // Parts Stock
  partsStock_includeOfWhichColumns: boolean;

  // Performance League
  performanceLeague_showExecAndManagerSelector: boolean;
  performanceLeague_hideBadges: boolean;
  performanceLeague_showDeliveredButton: boolean;
  
  // Products
  product_tyreInsurance: string;
  product_tyreAlloyInsurance: string;

  salesmenCanViewWebAppCommission: boolean; // Controls access to SalesCommission page for execs

  // Sales Performance
  salesPerformance_description: string;
  salesPerformance_showFranchisePicker: boolean;
  salesPerformance_showLateCostButtons: boolean;
  salesPerformance_showIncludeExcludeOrders: boolean;
  salesPerformance_showTradeUnitButtons: boolean;
  salesPerformance_showMotabilityButtons: boolean;
  salesPerformance_showOrderRateReportType: boolean;
  salesPerformance_showCustomReportType: boolean;
  salesPerformance_showAllSites: boolean;
  salesPerformance_showRetailSalesTranslation: boolean;

  gdpr_showManagerView: boolean;

  // Selections Service - I don't know if we want to flatten this further or move? Array can contain many entries
  selectionsServiceEnvironment_ageingOptions?: (AgeingOptionsEntityOrAgeingOption)[] | null;
  selectionsServiceEnvironment_ageingOption: AgeingOptionsEntityOrAgeingOption;
  selectionsServiceEnvironment_deliveryDateDateType: string;
  selectionsServiceEnvironment_eligibleForCurrentUserCheck: boolean;

  // Sales Activity
  salesActivity_showManagerTable: boolean;

  // Service Bookings Table
  serviceBookingsTable_clickSiteEnable: boolean;

  // Service Sales Dashboard
  serviceSalesDashboard_onlyLabour: boolean;

  // Service Summary
  serviceSummary_showTableTypeSelector: boolean;
  serviceSummary_defaultTableType: string;
  serviceSummary_tableTypes: string[];
  serviceSummary_defaultTimeOption: string;
  serviceSummary_timeOptions: string[];
  serviceSummary_showTechGroupColumns: boolean;
  serviceSummary_removeNonLabour: boolean;

  showApproveAutoPrices: boolean;
  showChangePriceNowInputAlways: boolean;
  showLatestSnapshotDate: boolean;
  showNestedSideMenu: boolean;
  showNewUsedSummaryBadges: boolean;
  showPrepCostsWhenValuing: boolean;
  showRotationButton: boolean;
  showRunChaseTileModal: boolean;
  showTodayMap: boolean;

  
  



  // Site Compare Tile
  siteCompareTile_includeToday: boolean;
  siteCompareTile_hideReportButtons: boolean;

  // StockInsight
  stockInsight_showPrepCost: boolean;
  stockInsight_showAverageDIS: boolean;

  // Stock Item Modal
  stockItemModal_onlineMerchandising: boolean;

  // Stock List
  stockList_franchises: string[];
  stockList_hideStockDetailsModal: boolean;
  stockList_tableColumns: string[];

  // Stock Overage Tile
  stockOverageTile_SpainThresholds: boolean;
  stockOverageTile_excludeEOM: boolean;

  // Stock Report
  stockReport_showAgePicker: boolean;
  stockReport_hideOnRRGSiteCol: boolean;
  stockReport_initialStockReport: string;
  stockReport_seeUsedStockReport: boolean;
  stockReport_seeAllStockReport: boolean;
  stockReport_seeUsedMerchandisingReport: boolean;
  stockReport_seeOverageStockReport: boolean;
  stockReport_seeStockGraphsReport: boolean;
  stockReport_seeStockByAgeReport: boolean;
  stockReport_includeReservedCarsOption: boolean;

  // Stock Table
  stockTable_hideExDemoColumn: boolean;
  stockTable_hideExManagementColumn: boolean;
  stockTable_hideTacticalColumn: boolean;

  // TodayMap
  todayMap_defaultPositionLat: number;
  todayMap_defaultPositionLong: number;
  todayMap_defaultZoom: number;

  // User Modal
  userModal_doNotRequireCurrentRetailerSite: boolean;
  userModal_userSitePicker_includeInactive: boolean;
  userModal_showRetailSiteErrorMessage: boolean;

  // UsedStockTable
  usedStockTable_exDemo: boolean;
  usedStockTable_exManagementCount: boolean;
  usedStockTable_includeDemoWithUsed: boolean;
  usedStockTable_tactical: boolean;
  usedStockTable_vindisFormatting: boolean;

  userSetup_hideUploadReports: boolean;
  userSetup_hideViewReports: boolean;
  userSetup_hideCommReview: boolean;
  userSetup_hideCommSelf: boolean;
  userSetup_hideSerReviewer: boolean;
  userSetup_hideSerSubmitter: boolean;
  userSetup_hideStockLanding: boolean;
  userSetup_hideSuperCup: boolean;
  userSetup_hideCanEditExecManagerMappings: boolean;
  userSetup_hideIsSalesExec: boolean;
  userSetup_hideAllowReportUpload: boolean;
  userSetup_hideAllowReportCentre: boolean;
  userSetup_hideLiveforecast: boolean;
  userSetup_hideSalesRoles: boolean;
  userSetup_hideTMgr: boolean;
  userSetup_allSalesRoles: string[];
  userSetup_canReviewStockPrices: boolean;
  userSetup_canActionStockPrices: boolean;
  userSetup_canEditStockPriceMatrix: boolean;
  userSetup_showSpanishJobTitles: boolean;
  
  whiteboard_showConfirmed: boolean;
  whiteboard_showNotConfirmed: boolean;
  whiteboard_showFinance: boolean;
  whiteboard_showAddons: boolean;
  whiteboard_showLateCostPicker: boolean;
  whiteboard_showManagerSelector: boolean;
  whiteboard_rrgUKSettings: boolean;

  wipsTable_hideBookingColumn: boolean;
  wipsTable_hideDepartmentColumn: boolean;
  wipsTable_hideAccountColumn: boolean;
  wipsTable_hideDueDateOutColumn: boolean;
  wipsTable_hideWDateInColumn: boolean;
  wipsTable_hideWDateOutColumn: boolean;
  wipsTable_hidePartsColumn: boolean;
  wipsTable_hideOilColumn: boolean;
  wipsTable_hideLabourColumn: boolean;
  wipsTable_hideSubletColumn: boolean;
  wipsTable_hideProvisionColumn: boolean;
  wipsTable_hideCreatedColumn: boolean;
  wipsTable_hideNotesColumn: boolean;

  vehicleTypePicker_showUsed: boolean;
  vehicleTypePicker_showNew: boolean;
  vehicleTypePicker_showAll: boolean;
  vehicleTypePicker_showVehicleTypePickerSpain: boolean;
  vehicleTypePicker_hiddenVehicleTypes: string[];

  showRegionFilterOnSiteDashboard: boolean;
  showWholesaleAdjustmentOption: boolean;
  fAndISummary_includeTargets: boolean;
  usageReport_CommissionPageName: string;
  runRateTile_hideBudget: boolean;
}
