import {ClaimVM} from './ClaimVM';
import {GlobalParam} from './GlobalParam';
import {Department, LastUpdatedDates, SiteVM} from './main.model';
import {RetailerSite} from './RetailerSite';
import {OrderType, VehicleType} from './sales.model';
import {UserPreference} from './UserPreference';
import {OptOutReason} from "./OptOutReason.model";
import { TagDTO } from "./Tag";


export interface StandingDataSet {
   FranchiseCodes: string[];
   Sites: SiteVM[];
   VehicleTypes: VehicleType[];
   OrderTypes: OrderType[];
   GlobalParams: GlobalParam[];
   DealerGroup: string;
   Departments: Department[];
   RetailerSites: RetailerSite[];
   LastUpdatedDates: LastUpdatedDates;
   Blobname: string;
   LatestSnapshotDate: Date;
   UserPreferences: UserPreference[];
   UserClaims: ClaimVM[]
   OptOutReasons: OptOutReason[];
   Tags: TagDTO[];
}
