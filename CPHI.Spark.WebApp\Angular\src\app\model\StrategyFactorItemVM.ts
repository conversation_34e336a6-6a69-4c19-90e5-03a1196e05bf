import {TagDTO} from "./Tag";

export class StrategyFactorItemVM {

   constructor(
      strategyFactorId: number,
      label: string,
      value: number,
      valueAmount: number,
      horizontalBands?: FactorItemHorizontalBand[],
      isReadOnly?: boolean
   ) {
      this.Id = null;
      this.StrategyFactorId = strategyFactorId;
      this.Comment = null;
      this.Label = label;
      this.Value = value;
      this.ValueAmount = valueAmount;

      if (horizontalBands) {
         this.horizontalBands = [];
         horizontalBands.forEach(band => {
            this.horizontalBands.push(band)
         })
      }
      this.IsReadOnly = isReadOnly;
      // if(competitorRadius){this.competitorRadius = competitorRadius}
      // if(competitorCount){this.competitorCount = competitorCount}
   }


   Id: number;
   StrategyFactorId: number;
   Comment: string;
   Label: string;
   Value: number;
   ValueAmount?: number;
   BoolValue: boolean | null = null;


   horizontalBands: FactorItemHorizontalBand[];
   IsReadOnly: boolean | null = null;

   //competitorRadius:number;
   //competitorCount:number;


   //Just used during reconstructing the matrix from a flat list of items
   horizontalLabelNumber?: number;
   horizontalLabelString?: string;
   verticalLabelNumber?: number
   verticalLabelString?: string

    //other things we select which we will combine later to store
    selectedPreviousOwners?: string;
    selectedAgeCategory?: string;
    selectedMake?: string;
    selectedFuelType?: string;
    selectedModel?: string;
    selectedAgeBand?: string;


   //Just used during reconstructing the matrix from a flat list of items
   retailRating?: number;
   retailRatingString?: string;
   daysListed?: number
   daysListedString?: string

   effectiveDateFrom?: string;
   effectiveDateTo?: string;
   tag?: TagDTO;
}

export interface FactorItemHorizontalBand {
   id: number;
   value: number
}
