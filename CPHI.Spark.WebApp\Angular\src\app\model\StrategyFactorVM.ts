import { StrategyFactorItemVM } from "./StrategyFactorItemVM";
import { AutotraderService } from "../services/autotrader.service";
import { StrategyFactorName } from "./StrategyFactorName";
import { RetailerSite } from "./RetailerSite";

export class StrategyFactorVM {
   Id?: number;
   Name: StrategyFactorName;
   StrategyFactorItems: StrategyFactorItemVM[];
   OnlyApplyIfImpactOverValue: number | null;
   OnlyApplyIfImpactOverPercent: number | null;
   OnlyApplyIfImpactUnderValue: number | null;
   OnlyApplyIfImpactUnderPercent: number | null;
   horizontalBandLabels?: StrategyFactorHorizontalBand[];

   constructor(type: StrategyFactorName, retailerSites: RetailerSite[], existingFactor: StrategyFactorVM) {
      if (existingFactor != null) {
         this.Id = existingFactor.Id;
         this.Name = existingFactor.Name;
         this.StrategyFactorItems = existingFactor.StrategyFactorItems;
         this.OnlyApplyIfImpactOverValue = existingFactor.OnlyApplyIfImpactOverValue;
         this.OnlyApplyIfImpactOverPercent = existingFactor.OnlyApplyIfImpactOverPercent;
         this.OnlyApplyIfImpactUnderValue = existingFactor.OnlyApplyIfImpactUnderValue;
         this.OnlyApplyIfImpactUnderPercent = existingFactor.OnlyApplyIfImpactUnderPercent;
      } else {
         //we are constructing a new one
         this.createNew(type, retailerSites);
      }
   }

   toggleOnlyApplyIfImpactOverValue() {
      if(this.OnlyApplyIfImpactOverValue == null){
         this.OnlyApplyIfImpactOverValue = 0;
      }else{
         this.OnlyApplyIfImpactOverValue = null;
      }
   }

   toggleOnlyApplyIfImpactUnderValue() {
      if(this.OnlyApplyIfImpactUnderValue == null){
         this.OnlyApplyIfImpactUnderValue = 0;
      }else{
         this.OnlyApplyIfImpactUnderValue = null;
      }
   }

   toggleOnlyApplyIfImpactOverPercent() {
      if(this.OnlyApplyIfImpactOverPercent == null){
         this.OnlyApplyIfImpactOverPercent = 0;
      }else{
         this.OnlyApplyIfImpactOverPercent = null;
      }
   }

   toggleOnlyApplyIfImpactUnderPercent() {
      if(this.OnlyApplyIfImpactUnderPercent == null){
         this.OnlyApplyIfImpactUnderPercent = 0;
      }else{
         this.OnlyApplyIfImpactUnderPercent = null;
      }
   }

   private createNew(type: StrategyFactorName, retailerSites: RetailerSite[]) {
      this.Id = null;
      this.Name = type;
      this.StrategyFactorItems = [];

      switch (type) {
         case StrategyFactorName.RetailRatingBand:
            this.StrategyFactorItems = AutotraderService.buildRetailRatingFactorItems();
            break;
         case StrategyFactorName.PerformanceRatingScore:
            this.StrategyFactorItems = AutotraderService.buildPerformanceRatingItems();
            break;
         case StrategyFactorName.DaysListedBand:
            this.StrategyFactorItems = AutotraderService.buildDaysListedBandFactorItems();
            break;

         case StrategyFactorName.DaysInStockBand:
            this.StrategyFactorItems = AutotraderService.buildDaysInStockBandFactorItems();
            break;

         case StrategyFactorName.Mileage:
            this.StrategyFactorItems = AutotraderService.buildMileageBandFactorItems();
            break;
         case StrategyFactorName.MilesPerYear:
            this.StrategyFactorItems = AutotraderService.buildMilesPerYearFactorItems();
            break;

         case StrategyFactorName.DaysListed:
         case StrategyFactorName.DaysInStock:
            this.StrategyFactorItems = AutotraderService.buildDaysFactorItems();
            break;

         case StrategyFactorName.RetailRating:
            this.StrategyFactorItems = AutotraderService.buildRetailRatingItems();
            break;

         case StrategyFactorName.OnBrandCheck:
            this.StrategyFactorItems = AutotraderService.buildOnBrandCheckFactorItems();
            break;

         case StrategyFactorName.RetailerName:
            const values = retailerSites.map((x) => x.Name);
            values.forEach((val) => {
               this.StrategyFactorItems.push(new StrategyFactorItemVM(null, val, 100, 0));
            });
            break;

         case StrategyFactorName.FuelType:
            const fuelTypes = AutotraderService.getFuelTypes;
            fuelTypes.forEach((fuelType) => {
               this.StrategyFactorItems.push(new StrategyFactorItemVM(null, fuelType, 100, 0));
            });
            break;

         case StrategyFactorName.ValueBand:
            const valueBands = AutotraderService.getSortOrderForValueBand();
            valueBands.forEach((valueBand) => {
               this.StrategyFactorItems.push(new StrategyFactorItemVM(null, valueBand, 100, 0));
            });
            // Items are already in correct order from getSortOrderForValueBand()
            break;

         case StrategyFactorName.RegYear:
            const regYears = this.generateRegYears();
            regYears.forEach((regYear) => {
               this.StrategyFactorItems.push(new StrategyFactorItemVM(null, regYear, 100, 0));
            });
            // Items are already in correct order from generateRegYears()
            break;

         case StrategyFactorName.LiveMarketCondition:
            const marketConditions = this.generateMarketConditionRanges();
            marketConditions.forEach((condition) => {
               this.StrategyFactorItems.push(new StrategyFactorItemVM(null, condition, 100, 0));
            });
            // Items are already in correct order from generateMarketConditionRanges()
            break;

         case StrategyFactorName.DateRange:
            // TODO: DB: Implement date range
            this.StrategyFactorItems.push(new StrategyFactorItemVM(null, null, 100, 0));
            // Items are already in correct order from generateMarketConditionRanges()
            break;

         case StrategyFactorName.SpecificColour:
         case StrategyFactorName.Colour:
         case StrategyFactorName.AgeAndOwners:
         case StrategyFactorName.MakeFuelType:
         case StrategyFactorName.MakeModel:
         case StrategyFactorName.MakeAgeBand:
            this.StrategyFactorItems.push(new StrategyFactorItemVM(null, "", 100, 0));
            break;

         case StrategyFactorName.MatchCheapestCompetitor:
         case StrategyFactorName.AchieveMarketPositionScore:
            const ranking = type === StrategyFactorName.MatchCheapestCompetitor ? 2 : 60;
            this.StrategyFactorItems = [
               new StrategyFactorItemVM(null, "Radius", 50, null),
               new StrategyFactorItemVM(null, "Ranking", ranking, null),
               new StrategyFactorItemVM(null, "PlateSteps", 1, null),
               new StrategyFactorItemVM(null, "MileageSteps", 30000, null),
               Object.assign(new StrategyFactorItemVM(null, "Independent", 0, null), { BoolValue: true }),
               Object.assign(new StrategyFactorItemVM(null, "Franchise", 0, null), { BoolValue: true }),
               Object.assign(new StrategyFactorItemVM(null, "Supermarket", 0, null), { BoolValue: true }),
               Object.assign(new StrategyFactorItemVM(null, "Private", 0, null), { BoolValue: false }),
            ];
            break;
         case StrategyFactorName.TrackMarketPosition:
            this.StrategyFactorItems = [
               new StrategyFactorItemVM(null, "Radius", 50, null),
               new StrategyFactorItemVM(null, "PP%Variance", 0, null),
               new StrategyFactorItemVM(null, "PlateSteps", 1, null),
               new StrategyFactorItemVM(null, "MileageSteps", 30000, null),
               Object.assign(new StrategyFactorItemVM(null, "Independent", 0, null), { BoolValue: true }),
               Object.assign(new StrategyFactorItemVM(null, "Franchise", 0, null), { BoolValue: true }),
               Object.assign(new StrategyFactorItemVM(null, "Supermarket", 0, null), { BoolValue: true }),
               Object.assign(new StrategyFactorItemVM(null, "Private", 0, null), { BoolValue: false }),
            ];
            break;

         case StrategyFactorName.RR_DL_Matrix:
         case StrategyFactorName.RR_DS_Matrix:
         case StrategyFactorName.RR_DB_Matrix:
            this.StrategyFactorItems = [
               new StrategyFactorItemVM(null, "20", 0, 0, [
                  { id: null, value: 100 },
                  { id: null, value: 99 },
                  {
                     id: null,
                     value: 98,
                  },
               ]),
               new StrategyFactorItemVM(null, "40", 0, 0, [
                  { id: null, value: 101 },
                  { id: null, value: 100 },
                  {
                     id: null,
                     value: 99,
                  },
               ]),
               new StrategyFactorItemVM(null, "60", 0, 0, [
                  { id: null, value: 102 },
                  { id: null, value: 101 },
                  {
                     id: null,
                     value: 100,
                  },
               ]),
               new StrategyFactorItemVM(null, "80", 0, 0, [
                  { id: null, value: 103 },
                  { id: null, value: 102 },
                  {
                     id: null,
                     value: 101,
                  },
               ]),
               new StrategyFactorItemVM(null, "100", 0, 0, [
                  { id: null, value: 104 },
                  { id: null, value: 103 },
                  {
                     id: null,
                     value: 102,
                  },
               ]),
            ];
            this.horizontalBandLabels = [{ value: 20 }, { value: 40 }, { value: 999 }];
            break;

         case StrategyFactorName.DTS_DL_Matrix:
            this.StrategyFactorItems = [
               new StrategyFactorItemVM(null, "20", 0, 0, [
                  { id: null, value: 100 },
                  { id: null, value: 99 },
                  {
                     id: null,
                     value: 98,
                  },
               ]),
               new StrategyFactorItemVM(null, "40", 0, 0, [
                  { id: null, value: 101 },
                  { id: null, value: 100 },
                  {
                     id: null,
                     value: 99,
                  },
               ]),
               new StrategyFactorItemVM(null, "60", 0, 0, [
                  { id: null, value: 102 },
                  { id: null, value: 101 },
                  {
                     id: null,
                     value: 100,
                  },
               ]),
               new StrategyFactorItemVM(null, "90", 0, 0, [
                  { id: null, value: 103 },
                  { id: null, value: 102 },
                  {
                     id: null,
                     value: 101,
                  },
               ]),
            ];
            this.horizontalBandLabels = [{ value: 20 }, { value: 40 }, { value: 999 }];
            break;

         case StrategyFactorName.PY_DS_Matrix:
            this.StrategyFactorItems = [
               new StrategyFactorItemVM(null, "2016", 0, 0, [
                  { id: null, value: 100 },
                  { id: null, value: 99 },
                  {
                     id: null,
                     value: 98,
                  },
               ]),
               new StrategyFactorItemVM(null, "2017", 0, 0, [
                  { id: null, value: 101 },
                  { id: null, value: 100 },
                  {
                     id: null,
                     value: 99,
                  },
               ]),
               new StrategyFactorItemVM(null, "2018", 0, 0, [
                  { id: null, value: 102 },
                  { id: null, value: 101 },
                  {
                     id: null,
                     value: 100,
                  },
               ]),
               new StrategyFactorItemVM(null, "2019", 0, 0, [
                  { id: null, value: 103 },
                  { id: null, value: 102 },
                  {
                     id: null,
                     value: 101,
                  },
               ]),
            ];
            this.horizontalBandLabels = [{ value: 10 }, { value: 20 }, { value: 30 }];
            break;

         case StrategyFactorName.VB_DS_Matrix:
            this.StrategyFactorItems = [
               new StrategyFactorItemVM(null, "5000", 5000, 0, [
                  { id: null, value: 100 },
                  { id: null, value: 99 },
                  {
                     id: null,
                     value: 98,
                  },
               ]),
               new StrategyFactorItemVM(null, "10000", 10000, 0, [
                  { id: null, value: 101 },
                  { id: null, value: 100 },
                  {
                     id: null,
                     value: 99,
                  },
               ]),
               new StrategyFactorItemVM(null, "15000", 15000, 0, [
                  { id: null, value: 102 },
                  { id: null, value: 101 },
                  {
                     id: null,
                     value: 100,
                  },
               ]),
               new StrategyFactorItemVM(null, "999999", 999999, 0, [
                  { id: null, value: 103 },
                  { id: null, value: 102 },
                  {
                     id: null,
                     value: 101,
                  },
               ]),
            ];
            this.horizontalBandLabels = [{ value: 10 }, { value: 20 }, { value: 30 }];
            break;

         case StrategyFactorName.ValuationChangeUntilSell:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, "ValuationChangeUntilSell", 100, 0)];
            break;
         case StrategyFactorName.DailyValuationChange:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, "DailyValuationChange", 100, 0)];
            break;

         case StrategyFactorName.MinimumProfit:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, "Minimum Profit", 1000, 0)];
            break;

         case StrategyFactorName.MinimumPricePosition:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, "Minimum Price Position %", 90.0, 0)];
            break;
         case StrategyFactorName.MaximumPricePosition:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, "Maximum Price Position %", 90.0, 0)];
            break;

         case StrategyFactorName.RoundToNearestEnding:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, "Round to", 50, 0)];
            break;

	        case StrategyFactorName.RoundUpToNearestEnding:
	           this.StrategyFactorItems = [new StrategyFactorItemVM(null, "Round to", 50, 0)];
	           break;

	        case StrategyFactorName.RoundDownToNearestEnding:
	           this.StrategyFactorItems = [new StrategyFactorItemVM(null, "Round to", 50, 0)];
	           break;

	        case StrategyFactorName.RoundToNearestMultiple:
	           this.StrategyFactorItems = [new StrategyFactorItemVM(null, "Multiple of", 50, 0)];
	           break;

	        case StrategyFactorName.RoundUpToNearestMultiple:
	           this.StrategyFactorItems = [new StrategyFactorItemVM(null, "Multiple of", 50, 0)];
	           break;

	        case StrategyFactorName.RoundDownToNearestMultiple:
	           this.StrategyFactorItems = [new StrategyFactorItemVM(null, "Multiple of", 50, 0)];
	           break;


         case StrategyFactorName.RoundToPriceBreak:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, "Round to Price Break", 50, 0)];
            break;

         case StrategyFactorName.DaysToSell:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, "DaysToSell", 30, 0)];
            break;

         case StrategyFactorName.WholesaleAdjustment:
            this.StrategyFactorItems = [
               new StrategyFactorItemVM(null, "AdjustmentPct", 0, 0),
               new StrategyFactorItemVM(null, "AdjustmentValue", 0, 0),
            ];
            break;

         case StrategyFactorName.RetailRating10sBand:
            this.StrategyFactorItems = AutotraderService.buildRetailRating10sFactorItems();
            break;

         case StrategyFactorName.Brand:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, "", 100, 0)];
            break;
         case StrategyFactorName.ModelName:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, "", 100, 0)];
            break;

         case StrategyFactorName.LeavingData:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, "Leaving Data Factor", 100, 0)];
            break;

         case StrategyFactorName.SetToAdvertisedPrice:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, "SetToAdvertisedPrice", 100, 0)];
            break;

         case StrategyFactorName.Tag:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, 'Tag', 100,0)];
            break;

         case StrategyFactorName.ApplyTagAdjustments:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, 'ApplyTagAdjustments', 100,0)];
            break;

         default:
            this.StrategyFactorItems = [];
            break;
      }
   }

   private generateRegYears(): string[] {
      const currentYear = new Date().getFullYear();
      const years: string[] = ["<2010"];

      for (let year = 2010; year <= currentYear; year++) {
         years.push(year.toString());
      }

      return years;
   }

   private generateMarketConditionRanges(): string[] {
      // Generate default market condition ranges: -200%, -100%, 0%, 100%, 200%
      return ["-200", "-100", "0", "100", "200"];
   }

   limitMaximumFactorItem() {
      // if (this.Name === StrategyFactorName.DaysListed) {
      //    const penultimateItem = this.StrategyFactorItems[this.StrategyFactorItems.length - 2];
      //    let finalItemStart = 0;
      //    if (penultimateItem) {
      //       try {
      //          finalItemStart = parseInt(penultimateItem.Label) + 1;
      //       } catch (error) {
      //       }
      //    }

      //    this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Label = `999`;
      // }
      if (this.Name === StrategyFactorName.PerformanceRatingScore) {
         const penultimateItem = this.StrategyFactorItems[this.StrategyFactorItems.length - 2];
         let finalItemStart = 0;
         if (penultimateItem) {
            try {
               finalItemStart = parseInt(penultimateItem.Label.split("-")[1]) + 1;
            } catch (error) {}
         }
         finalItemStart = Math.min(finalItemStart, 99);
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Label = `${finalItemStart}-99`;
      }
      // if (this.Name === StrategyFactorName.DaysInStock) {
      //    const penultimateItem = this.StrategyFactorItems[this.StrategyFactorItems.length - 2];
      //    let finalItemStart = 0;
      //    if (penultimateItem) {
      //       try {
      //          finalItemStart = parseInt(penultimateItem.Label.split('-')[1]) + 1;
      //       } catch (error) {
      //       }
      //    }
      //    finalItemStart = Math.min(finalItemStart, 999);
      //    this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Label = `${finalItemStart}-999`;
      // }
      if (this.Name === StrategyFactorName.RR_DL_Matrix) {
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Label = "99";
         if (this.horizontalBandLabels) {
            this.horizontalBandLabels[this.horizontalBandLabels.length - 1].value = 999;
         }
      }
      if (this.Name === StrategyFactorName.RR_DS_Matrix) {
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Value = 99;
         if (this.horizontalBandLabels) {
            this.horizontalBandLabels[this.horizontalBandLabels.length - 1].value = 999;
         }
      }
      if (this.Name === StrategyFactorName.RR_DB_Matrix) {
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Value = 99;
         if (this.horizontalBandLabels) {
            this.horizontalBandLabels[this.horizontalBandLabels.length - 1].value = 999;
         }
      }
      if (this.Name === StrategyFactorName.DTS_DL_Matrix) {
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Label = "999";
         if (this.horizontalBandLabels) {
            this.horizontalBandLabels[this.horizontalBandLabels.length - 1].value = 999;
         }
      }
      if (this.Name === StrategyFactorName.PY_DS_Matrix) {
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Label = "2030";
         if (this.horizontalBandLabels) {
            this.horizontalBandLabels[this.horizontalBandLabels.length - 1].value = 999;
         }
      }
      if (this.Name === StrategyFactorName.VB_DS_Matrix) {
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Label = ">£50k";
         if (this.horizontalBandLabels) {
            this.horizontalBandLabels[this.horizontalBandLabels.length - 1].value = 999;
         }
      }
   }

   getCheapestCompetitorFactorItem(itemLabel: string) {
      if (this.Name != "MatchCheapestCompetitor") {
         return null;
      }
      return this.StrategyFactorItems.find((item) => item.Label === itemLabel);
   }

   getAchieveMarketPositionScoreFactorItem(itemLabel: string) {
      if (this.Name != "AchieveMarketPositionScore") {
         return null;
      }
      return this.StrategyFactorItems.find((item) => item.Label === itemLabel);
   }

   getTrackMarketAvPricePositionFactorItem(itemLabel: string) {
      if (this.Name != "TrackMarketPosition") {
         return null;
      }
      return this.StrategyFactorItems.find((item) => item.Label === itemLabel);
   }
   getFactorItem(itemLabel: string) {
      return this.StrategyFactorItems.find((item) => item.Label === itemLabel);
   }

   getWholesaleAdjustmentFactorItem(itemLabel: string) {
      if (this.Name != "WholesaleAdjustment") {
         return null;
      }
      return this.StrategyFactorItems.find((item) => item.Label === itemLabel);
   }
}

export interface StrategyFactorHorizontalBand {
   value: number;
}
