import {StrategyFactorName} from "./StrategyFactorName";
import {StrategyPriceBuildUpItem} from "./StrategyPriceBuildUpItem";

export class StrategyPriceBuildUp {
   //build a constructor using a StrategyPriceBuildUpItem:
   constructor(itemIn: StrategyPriceBuildUpItem) {
      this.VersionName = itemIn.VersionName;
      this.FactorName = itemIn.FactorName;
      this.FactorItemLabel = itemIn.FactorItemLabel;
      this.FactorItemValue = itemIn.FactorItemValue;
      this.FactorItemValueAmount = itemIn.FactorItemValueAmount;
      this.RuleSetComment = itemIn.RuleSetComment;
      this.Impact = itemIn.Impact;
      this.SourceValue = itemIn.SourceValue;
      this.ExtendedNotes = itemIn.ExtendedNotes;
   }

   VersionName: string;
   FactorName: StrategyFactorName;
   FactorItemLabel: string;
   FactorItemValue: number;
   SourceValue: string;
   Impact: number;
   RuleSetComment: string;

   ExtendedNotes: string;

   FactorItemValueAmount?: number;  //this IS used

   // normal strategy build ups
   IsRelatedToTestStrategy?: boolean;  //this IS used

   // Sometimes we derive our adjustments from sources outside factor tables, so we need to store them
   AppliedFactorItemValue?: number;
   AppliedFactorItemValueAmount?: number;
}
