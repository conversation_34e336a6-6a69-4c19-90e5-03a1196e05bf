export interface TagDTO {
   Id?: number;
   DealerGroupId?: number;
   Label: string;
   IsActive?: boolean;
   StrategyImpactPct?: number;
   StrategyImpactAmount?: number;
   Colour: string;
   CreatedById?: number;
   UpdatedById?: number;
   CreatedDate?: Date;
   UpdatedDate?: Date;
}

export interface TagSearchDTO {
   DealerGroupName?: number;
   IsActive?: boolean;
}

export interface CreateTagDTO {
   Id?: number;
   Label: string;
   IsActive: boolean;
   StrategyImpactPct: number;
   StrategyImpactAmount: number;
   Colour: string;
}
