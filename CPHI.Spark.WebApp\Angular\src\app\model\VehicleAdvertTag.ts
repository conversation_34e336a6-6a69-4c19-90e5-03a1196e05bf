import {TagDTO} from './Tag';
import {VehicleAdvertDetail} from './VehicleAdvertDetail';

export interface VehicleAdvertTagPersonDTO {
   Id: number;
   Name: string;
}

export interface VehicleAdvertTagDTO {
   Id: number;
   TagId: number;
   Tag?: TagDTO;
   VehicleAdvertId: number;
   VehicleAdvert?: VehicleAdvertDetail;
   CreatedDate: Date;
   UpdatedDate: Date;
   CreatedById?: number;
   CreatedBy?: VehicleAdvertTagPersonDTO;
   UpdatedById?: number;
   UpdatedBy?: VehicleAdvertTagPersonDTO;
}

export interface VehicleAdvertTagSearchDTO {
   id?: number;
   vehicleAdvertId?: number;
   tagId?: number;
   isActive?: boolean;
   dealerGroupName?: number;
}

export interface CreateVehicleAdvertTagDTO extends VehicleAdvertTagDTO {
   dealerGroupName?: number;
}
