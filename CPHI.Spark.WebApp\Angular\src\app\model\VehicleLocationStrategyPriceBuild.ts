import { RetailerSite } from "./RetailerSite";
import { StrategyPriceBuildUpItem } from "./StrategyPriceBuildUpItem";



export class VehicleLocationStrategyPriceBuild {

    constructor(itemIn: VehicleLocationStrategyPriceBuild) {
        this.RetailerSite = new RetailerSite(itemIn.RetailerSite);
        this.RetailRating = itemIn.RetailRating;
        this.DaysToSell = itemIn.DaysToSell;
        this.StrategyPrice = itemIn.StrategyPrice;
        this.BuildUpItems = itemIn.BuildUpItems;
        this.TargetMargin = itemIn.TargetMargin;
        this.TargetAdditionalMech = itemIn.TargetAdditionalMech;
        this.TargetPaintPrep = itemIn.TargetPaintPrep;
        this.TargetAuctionFee = itemIn.TargetAuctionFee;
        this.TargetDelivery = itemIn.TargetDelivery;
        this.TargetWarrantyFee = itemIn.TargetWarrantyFee;
        this.TargetOtherCost = itemIn.TargetOtherCost;
    }
    RetailerSite: RetailerSite;
    RetailRating: number;
    DaysToSell: number;
    StrategyPrice: number;
    BuildUpItems: StrategyPriceBuildUpItem[];

      //valuation
   TargetMargin: number;
   TargetAdditionalMech: number;
   TargetPaintPrep: number;
   TargetAuctionFee: number;
   TargetDelivery: number;
   TargetWarrantyFee: number;
   TargetOtherCost: number;
}
