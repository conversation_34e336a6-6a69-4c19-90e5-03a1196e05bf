import {  Component, ElementRef, HostListener, OnInit, ViewChild } from "@angular/core";
import {
   ApplyColumnStateParams,
   ColDef,
   ColumnEverythingChangedEvent,
   ColumnState,
   FilterChangedEvent,
   GetContextMenuItemsParams,
   IAggFuncParams,
   ICellRendererParams,
   IDateFilterParams,
   IRowNode,
   MenuItemDef,
   RowClickedEvent,
   RowDoubleClickedEvent,
   RowHeightParams,
   ValueGetterParams,
} from "ag-grid-community";

import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { RegPlateComponent } from "src/app/_cellRenderers/regPlate.component";
import { BulkUpdateAdvertPriceModalComponent } from "src/app/components/bulkUpdateAdvertPriceModal/bulkUpdateAdvertPriceModal.component";
import { CustomHeaderAdDetail } from "src/app/components/customHeaderAdDetail/customHeaderAdDetail.component";
import { CustomHeaderAdDetailService } from "src/app/components/customHeaderAdDetail/customHeaderAdDetail.service";
import { TableLayoutManagementService } from "src/app/components/tableLayoutManagement/tableLayoutManagement.service";
import { BuildTotalAndAverageRowsParams } from "src/app/model/BuildTotalAndAverageRowsParams";
import { GlobalParamKey } from "src/app/model/GlobalParam";
import { GridOptionsCph } from "src/app/model/GridOptionsCph";
import { StockPricingSitesOverview } from "src/app/model/StockPricingSitesOverview";
import { UpdatePriceParams } from "src/app/model/UpdatePriceParams";
import { VehicleAdvertDetail } from "src/app/model/VehicleAdvertDetail";
import { VehicleAdvertWithRating } from "src/app/model/VehicleAdvertWithRating";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { ConstantsService } from "src/app/services/constants.service";
import { ExcelExportService } from "src/app/services/excelExportService";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { GlobalParamsService } from "src/app/services/globalParams.service";
import { SelectionsService } from "src/app/services/selections.service";
import { GridFormatService } from "../../../../services/gridFormat.service";
import * as utilities from "../../../../services/utilityFunctions";
import { AdvertListingDetailService } from "../advertListingDetail.service";
import { CPHAutoPriceColDef } from "src/app/model/CPHColDef";

@Component({
   selector: "advertListingDetailTable",
   templateUrl: "./advertListingDetailTable.component.html",
   styleUrls: ["./advertListingDetailTable.component.scss"],
   //changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AdvertListingDetailTableComponent implements OnInit {
   allFilteredNodes: {};
   lastUpdatedFilteredNodes: Date;
   pinnedBottomData: VehicleAdvertDetail[];

   @HostListener("window:resize", [])
   private onresize(event) {
      this.selections.screenWidth = window.innerWidth;
      this.selections.screenHeight = window.innerHeight;
      if (this.service.tableLayoutManagementParams.gridApi) {
         this.service.tableLayoutManagementParams.gridApi.resetRowHeights();
      }
   }

   public components: {
      [p: string]: any;
   } = {
      agColumnHeader: CustomHeaderAdDetail,
   };

   @ViewChild("deleteTableStateModal", { static: true }) deleteTableStateModal: ElementRef;
   @ViewChild("shareTableStateModal", { static: true }) shareTableStateModal: ElementRef;
   @ViewChild("setStandardTableStateModal", { static: true }) setStandardTableStateModal: ElementRef;

   constructor(
      public selections: SelectionsService,
      public excel: ExcelExportService,
      public gridHelpersService: AGGridMethodsService,
      public service: AdvertListingDetailService,
      public constants: ConstantsService,
      public getData: GetDataMethodsService,
      private colTypesService: ColumnTypesService,
      private customHeader: CustomHeaderAdDetailService,
      public apiAccess: ApiAccessService,
      public gridFormatService: GridFormatService,
      public tableLayoutManagementService: TableLayoutManagementService,
      private modalService: NgbModal,
      private globalParamsService: GlobalParamsService
   ) {}

   ngOnInit() {
      //this.provideGridOptions();
      //this.colTypes = this.provideColTypes();

      this.service.searchTerm.valueChanges.subscribe((value) => {
         this.service.tableLayoutManagementParams.gridApi.setQuickFilter(this.service.searchTerm.value);
      });
   }

   ngOnDestroy() {
      this.service.tableLayoutManagementParams.gridApi = null;
      this.service.tableLayoutManagementParams.gridColumnApi = null;
   }

   // ---------------------------
   // UI stuff
   // ---------------------------
   clearSearchTerm() {
      this.service.searchTerm.setValue("");
      if (this.service.tableLayoutManagementParams.gridApi) {
         this.service.tableLayoutManagementParams.gridApi.setQuickFilter(this.service.searchTerm.value);
      }
   }

   // ---------------------------
   // Grid setup
   // ---------------------------

   get provideGridOptions(): GridOptionsCph {
      return {
         suppressPropertyNamesCheck: true,
         showOpenedGroup: true,
         onGridReady: (params) => this.onGridReady(params),
         getRowHeight: (params) => {
            return this.calcRowHeight(params);
         },
         headerHeight: this.gridHelpersService.getHeaderHeight(),
         animateRows: true,
         floatingFiltersHeight: this.gridHelpersService.getFloatingFilterHeight(),
         getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
         aggFuncs: {
            avgIfValue: (params) => {
               const nums = params.values.filter((v) => v !== null && v !== undefined && v !== 0);
               return nums.length ? nums.reduce((a, b) => a + b, 0) / nums.length : null;
            },
         },
         suppressAggFuncInHeader: true,
         context: { thisComponent: this },
         defaultColDef: {
            menuTabs: ["generalMenuTab"],
            resizable: true,
            minWidth: 50,
            sortable: true,
            hide: false,
            floatingFilter: true,
            filterParams: {
               applyButton: false,
               clearButton: true,
               applyMiniFilterWhileTyping: true,
               cellHeight: this.gridHelpersService.getFilterListItemHeight(),
            },
            cellClass: "agAlignCentreVertically",
            headerComponentParams: { showPinAndRemoveOptions: true },
            autoHeaderHeight: true,
            floatingFilterComponentParams: { suppressFilterButton: true },
         },
         rowSelection: "multiple",
         getContextMenuItems: (params) => this.getContextMenuItems(params),
         pinnedBottomRowData: this.pinnedBottomData,
         columnTypes: this.provideColTypes(),
         rowGroupPanelShow: "always",
         columnDefs: this.provideColDefs(),
         getRowClass: (params) => {
            if (params.data?.Description === "Total") return "total";
         },
         onRowClicked: (params) => this.onRowClicked(params),
         onSelectionChanged: (params) => this.onRowSelectionChange(),
         onRowDoubleClicked: (params) => this.rowDoubleClicked(params),
         onColumnEverythingChanged: (event) => this.saveGridState(event),
         onFilterChanged: (event) => {
            this.onFilterChanged(event);
         },
         pivotMode: false,
         autoGroupColumnDef: {
            sortable: true,
            filter: true,
            valueGetter: (params) => {
               return params.node.group ? params.node.key : params.data[params.colDef.field];
            },
         },
         onColumnVisible: (event) => this.onColumnVisible(event),
         suppressDragLeaveHidesColumns: true,
      };
   }

   provideColDefs(): ColDef[] {
      const baseColDefs = this.service.baseColDefs;
      const savedState = this.service.tableLayoutManagementParams.loadedTableState;

      if (!savedState?.length) {
         return baseColDefs;
      }

      // Map colId → state + its index
      let merged = this.gridHelpersService.mergeStateIntoDefs(savedState, baseColDefs);
      return merged;
   }

   provideColTypes() {
      let commonTypes = { ...this.colTypesService.provideColTypes([]) };
      return commonTypes;
   }

   saveGridState = utilities.debounce((event: ColumnEverythingChangedEvent) => {
      this.service.tableLayoutManagementParams.loadedTableState = event.columnApi.getColumnState();
   }, 300);

   providePinnedBottomRowData(): VehicleAdvertDetail[] {
      if (!this.service.tableLayoutManagementParams.gridApi) {
         return;
      }
      const params: BuildTotalAndAverageRowsParams = this.buildUpParamsForBottomRows();
      return this.gridHelpersService.buildTotalAndAverageRows(params);
   }
   private buildUpParamsForBottomRows() {
      const params: BuildTotalAndAverageRowsParams = {
         colsToSkipAverageIfZero: [],
         colsToTotalOrAverage: [],
         colsToTotal: [],
         colsToSetToTrue: [],
         selectedCountFieldName: "RetailerSiteName", //where we put the '7 selected'
         labelFieldName: "Derivative", //where we put the 'Average per vehicle'
         itemName: "vehicle", //used in forming the text e.g. 'Average per vehicle'
         api: this.service.tableLayoutManagementParams.gridApi,
         includeTotalRow: false,
         showTotalInAverage: false,
      };

      this.gridHelpersService.extractAverageAndTotalCols(this.service.baseColDefs, params);
      return params;
   }
   provideDefaultBottomRows(): VehicleAdvertWithRating[] {
      return [
         {
            RetailerSiteName: "0 selected",
         } as VehicleAdvertWithRating,
         {} as VehicleAdvertWithRating,
      ];
   }

   // ---------------------------
   // Grid methods
   // ---------------------------
   onColumnRowGroupChanged() {
      // Get the current row group columns
      const rowGroupColumns = this.service.tableLayoutManagementParams.gridColumnApi.getRowGroupColumns();

      if (rowGroupColumns.length > 0) {
         // Retrieve the current column state
         let currentColumnState = this.service.tableLayoutManagementParams.gridColumnApi.getColumnState();

         // Find the maximum sort index
         let maxSortIndex = currentColumnState.reduce(
            (max, column) => (column.sortIndex !== null && column.sortIndex > max ? column.sortIndex : max),
            0
         );

         rowGroupColumns.forEach((rowGroupColumn) => {
            const firstRowGroupColumn = rowGroupColumn;

            // Increment the maxSortIndex for the next column
            maxSortIndex++;

            const sortModel: ApplyColumnStateParams = {
               state: [
                  {
                     colId: firstRowGroupColumn.getColId(),
                     sort: "asc", // or 'desc' for descending
                     sortIndex: maxSortIndex, // Set the next available sort index
                  },
               ],
            };

            this.service.tableLayoutManagementParams.gridColumnApi.applyColumnState(sortModel);
         });
      }
   }

   onColumnVisible(event) {
      if (!event.api) {
         return;
      }
      const visibleColIds = event.columnApi.getAllDisplayedColumns().map((x) => x.getColId());
      event.api.forEachNode((node) => {
         let nodeHeight = this.calcNodeHeight(node, visibleColIds);
         node.setRowHeight(nodeHeight);
      });
      event.api.onRowHeightChanged();
      this.tableLayoutManagementService.setColumnsForTypeahead();
   }

   onFilterChanged(event: FilterChangedEvent<any, any>) {
      this.service.tableLayoutManagementParams.filterModel = event.api.getFilterModel();
      event.api.redrawRows();
      this.onRowSelectionChange(); //to generate pinned bottom rows
   }
   onRowSelectionChange(): void {
      this.updatePinnedBottomData();
   }
   updatePinnedBottomData() {
      if (!this.service.tableLayoutManagementParams.gridApi) {
         return;
      }
      this.pinnedBottomData = this.providePinnedBottomRowData();
      this.service.tableLayoutManagementParams.gridApi.setPinnedBottomRowData(this.pinnedBottomData);
   }

   onRowClicked(params: RowClickedEvent<any, any>): void {
      if (params.api.isSideBarVisible()) {
         params.api.closeToolPanel();
      }
   }
   calcRowHeight(params: RowHeightParams) {
      if (!params.api) {
         return this.gridHelpersService.getStandardHeight();
      }
      const visibleColIds = params.columnApi.getAllDisplayedColumns().map((x) => x.getColId());
      return this.calcNodeHeight(params.node, visibleColIds);
   }

   calcNodeHeight(node: IRowNode, visibleColIds: string[]): number {
      if (node.isRowPinned()) {
         return this.gridHelpersService.getStandardHeight();
      } //bottom row
      if (!node.data) {
         return this.gridHelpersService.getStandardHeight();
      } //grouped row

      const superHighCols: string[] = ["PerfRatingChart", "PricePositionChart"];
      if (superHighCols.some((x) => visibleColIds.includes(x))) {
         return this.gridHelpersService.getRowHeight(70);
      }

      const highCols: string[] = ["ImageURL", "DailySearchViewsLast7", "DailyAdvertViewsLast7"];
      if (highCols.some((x) => visibleColIds.includes(x))) {
         return this.gridHelpersService.getRowHeight(60);
      }
      return this.gridHelpersService.getStandardHeight();
   }

   getContextMenuItems(params: GetContextMenuItemsParams<any, any>): (string | MenuItemDef)[] {
      const selectedNodeIds: string[] = this.service.tableLayoutManagementParams.gridApi
         .getSelectedNodes()
         .map((x) => x.id);

      if (!selectedNodeIds.includes(params.node.id)) {
         this.service.tableLayoutManagementParams.gridApi.forEachLeafNode((node) => {
            node.setSelected(node.id === params.node.id);
         });
      }

      let result = this.gridHelpersService.getContextMenuItems(params, false);

      if (!params.node.isRowPinned()) {
         result.unshift(
            {
               name: "Open modal",
               action: () => {
                  this.service.openModal(params.node.data);
               },
               cssClasses: ["bold"],
            },
            "separator"
         );
      }

      result = result.concat(this.tableLayoutManagementService.getTableContextMenuItems());

      if (selectedNodeIds.length > 0) {
         result.push("separator", {
            icon: "📄",
            name: `Generate ${this.constants.pluralise(selectedNodeIds.length, "Price Board", "Price Boards")}`,
            action: () => {
               this.service.priceBoardsService.generateMultiPriceBoardsPdf(this.service.tableLayoutManagementParams);
            },
         });

         let selectedNodes: IRowNode[] = this.service.tableLayoutManagementParams.gridApi.getSelectedNodes();
         let updatePriceParams: UpdatePriceParams[] = selectedNodes.map((node) => ({
            vehicleAdvertId: node.data.AdId,
            newPrice: 0,
            oldPrice: parseInt(node.data.AdvertisedPrice),
         }));

         if (this.globalParamsService.getGlobalParam(GlobalParamKey.ShowBulkPriceChangeMenu) as boolean) {
            result.push("separator", {
               icon: "💾",
               name: `Bulk Update Advert ${selectedNodeIds.length > 1 ? "Prices" : "Price"}`,
               action: () => {
                  this.openBulkUpdateAdverPriceModal(updatePriceParams);
               },
            });
         }
      }

      return result;
   }

   openBulkUpdateAdverPriceModal(updatePriceParams: UpdatePriceParams[]) {
      const modalRef = this.modalService.open(BulkUpdateAdvertPriceModalComponent, {
         keyboard: true,
         size: "xs",
      });
      modalRef.componentInstance.updatePriceParams = updatePriceParams;
      modalRef.result.then((result) => {});
   }

   // ---------------------------
   // Renderers and getters
   // ---------------------------
   // currencyRenderer(params: ICellRendererParams) {
   //    return this.service.cphPipe.transform(params.value, "currency", 0);
   // }

   // numberRenderer(params: ICellRendererParams) {
   //    return this.service.cphPipe.transform(params.value, "number", 0);
   // }
   // booleanGetter(params: ValueGetterParams<any>): any {
   //    const colId = params.column.getColId();
   //    if (params.data) {
   //       //is a cell
   //       return params.data[params.colDef.field];
   //    } else {
   //       let tot = 0;
   //       params.node.allLeafChildren.forEach((child) => {
   //          if (child.data[params.colDef.field]) tot++;
   //       });
   //       const result: number = tot;
   //    }
   // }
   // daysListedRenderer(params: any) {
   //    if (!params.node.data) {
   //       return "";
   //    }
   //    return params.value;
   // }

   // pricePositionGetter(params: ValueGetterParams<any>): any {
   //    const row: VehicleAdvertWithRating = params.data;
   //    if (!row) {
   //       let valn = 0;
   //       let supplied = 0;
   //       params.node.allLeafChildren.forEach((leaf) => {
   //          if (leaf.data.ValuationMktAvRetail > 0) {
   //             valn += leaf.data.ValuationMktAvRetail;
   //             supplied += leaf.data.AdvertisedPrice;
   //          }
   //       });
   //       return utilities.div(supplied, valn);
   //    }
   //    return row.ValuationMktAvRetail === 0 ? 0 : row.AdvertisedPrice / row.ValuationMktAvRetail;
   // }

   // aggFunc(val: IAggFuncParams<any, any>): any {
   //    let result = 0;
   //    val.values.forEach((v) => {
   //       result += v;
   //    });
   //    return result;
   // }

   // regRenderer(params): any {
   //    const node: IRowNode = params.node;
   //    return node.isRowPinned() ? null : RegPlateComponent;
   // }

   rowDoubleClicked(params: RowDoubleClickedEvent) {
      const row: VehicleAdvertWithRating = params.data;
      this.service.openModal(row);
   }

   onGridReady(params) {
      this.service.tableLayoutManagementService.parent = this.service.tableLayoutManagementParams;
      this.service.tableLayoutManagementParams.gridApi = params.api;
      this.service.tableLayoutManagementParams.gridColumnApi = params.columnApi;
      this.service.tableLayoutManagementParams.originalColDefs = [...this.service.baseColDefs];
      this.updatePinnedBottomData();
      this.selections.triggerSpinner.emit({ show: false });
   }

   excelExport() {
      let tableModel = this.service.tableLayoutManagementParams.gridApi.getModel();
      this.excel.createSheetObject(tableModel, "Stock Pricing - Vehicle Adverts", 1, 1);
   }

   getDateFilterParams() {
      let params: IDateFilterParams = {
         comparator: (filterLocalDateAtMidnight: Date, cellValue: string) => {
            let dateAndTimeAsString: string = cellValue;

            if (dateAndTimeAsString === null) return -1;

            let dateOnly: string = dateAndTimeAsString.split("T")[0];
            let dateParts: string[] = dateOnly.split("-");
            let cellDate = new Date(Number(dateParts[0]), Number(dateParts[1]), Number(dateParts[2]));

            if (filterLocalDateAtMidnight.getTime() === cellDate.getTime()) return 0;

            if (cellDate < filterLocalDateAtMidnight) return -1;

            if (cellDate > filterLocalDateAtMidnight) return 1;

            return 0;
         },
      };

      return params;
   }

   customPortalLinkRenderer(params) {
      const showIcon = params.data && !params.node.isRowPinned();
      const autoTraderListingIdentifier = params.data?.WebSiteStockIdentifier;

      let url: string = this.constants.buildPortalUrl(autoTraderListingIdentifier);

      return `<a *ngIf="${showIcon}" id="iconWrapper" target="_blank" href="${url}">
    <i class="fas fa-car"></i></div>`;
   }
}
