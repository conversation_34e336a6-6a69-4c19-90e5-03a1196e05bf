<nav class="navbar">
   <nav class="generic">
      <div id="pageTitle">
         <div>Leaving Vehiles Explorer</div>
      </div>

      <ng-container *ngIf="service">
         <div class="buttonGroup topDropdownButtons">
            <!-- Site selector for back end-->
            <multiPickerWithCount
               [label]="'Select Sites, we will use to get from back end'"
               [menuItems]="siteNames"
               [chosenItems]="service.chosenSiteNames"
               [itemCount]="provideItemCountForSitePicker.bind(this)"
               [onChosenItemsChange]="onChosenSitesChange.bind(this)"
            ></multiPickerWithCount>
         </div>



         <div class="d-flex align-items-center">
            From:
            <div class="mx-2">
                <input type="date" class="form-control"
                       [value]="formatDateForInput(service.startDate)"
                       (change)="onDateChange($event, true)"
                       >
            </div>
            To:
            <div class="mx-2">
                <input type="date" class="form-control"
                       [value]="formatDateForInput(service.endDate)"
                       (change)="onDateChange($event, false)"
                       >
            </div>
        </div>


      </ng-container>
   </nav>

   <nav class="pageSpecific"></nav>
</nav>

<!-- Main Page -->
<div id="overallHolder" class="single-line-nav">
   <div class="content-new">
      <div class="content-inner-new">
         <div  class="d-flex flex-column h-100">
            <instructionRow [message]="infoMessage"> </instructionRow>
             <leavingVehicleExplorerTable *ngIf="service.items" [params]="tableParams"> </leavingVehicleExplorerTable>
         </div>
      </div>
   </div>
</div>
