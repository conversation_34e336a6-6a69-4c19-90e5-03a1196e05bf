import { Component, OnInit } from "@angular/core";
import { GetLeavingVehicleExplorerItemsParams } from "src/app/model/GetLeavingVehicleExplorerItemsParams";
import { LVExplorerItem } from "src/app/model/LVExplorerItem";
import { SiteVM } from "src/app/model/main.model";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { LeavingVehicleExplorerPageService } from "./leavingVehicleExplorer.service";
import { FilterModel } from "src/app/model/FilterModel";
import { BlobDisplayerParams } from "../../simpleExamplePage/blobDisplayer/blobDisplayer.params";
import { LeavingVehicleExplorerTableParams } from "./leavingVehicleExplorerTable/leavingVehicleExplorerTable.params";
import { SelectionsService } from "src/app/services/selections.service";

@Component({
   selector: "leavingVehicleExplorerPage",
   templateUrl: "./leavingVehicleExplorer.component.html",
   styleUrls: ["./leavingVehicleExplorer.component.scss"],
})
export class LeavingVehicleExplorerPageComponent implements OnInit {
   constructor(
      public service: LeavingVehicleExplorerPageService,
      private getDataMethodsService: GetDataMethodsService,
      private constantsService: ConstantsService,
      private selectionsService: SelectionsService
   ) {}

   showNewDataIndicator: boolean;

   //---------------
   //getters
   //---------------
   // get countChosenItems() {
   //    return this.service.LVExplorerItemsFiltered.filter((x) => x.isChosen).length;
   // }

   get siteNames() {
      return this.service.constantsService.Sites.filter((x) => x.IsActive).map((x) => x.SiteDescription);
   }



   get infoMessage(){
      return 'Table shows all vehicles that have left within the date range selected above.   Drag fields from the top panel to the Row Groups panel to group and review the results.';
   }

   get tableParams(): LeavingVehicleExplorerTableParams {
      const params: LeavingVehicleExplorerTableParams = {
         items: this.service.items,
         newFilteredData: (filteredItems) => {
            this.service.itemsFiltered = filteredItems;
         },
        
         storeGridState: (gridState) => {
            this.service.gridState = gridState;
         },
         loadGridState: () => {
            return this.service.gridState;
         },
         
      };
      return params;
   }

   //---------------
   //lifecycle stuff
   //---------------
   async ngOnInit() {
      if (!this.service.serviceHasBeenInitialised) {
         const allSites = this.service.constantsService.Sites.filter((x) => x.IsActive).map((x) => x.SiteDescription);
         this.service.chosenSiteNames = new Set<string>(allSites); //initially set to all
         this.service.serviceHasBeenInitialised = true;
         if (!this.service.startDate) {
            this.service.startDate = this.constantsService.addMonths(
               this.constantsService.startOfMonth(new Date()),
               -6
            );
         }
         if (!this.service.endDate) {
            this.service.endDate = this.constantsService.endOfMonth(new Date());
         }
      }
      await this.getDataAndDealWithData();
   }

   ngOnDestroy() {}

   //---------------
   // UI stuff
   //---------------
   provideItemCountForSitePicker(item: string): number {
      if (!this.service.items) {
         return 0;
      }
      return this.service.items.filter((x) => x.SiteDescription == item).length;
   }

    formatDateForInput(date: Date): string {
      // Format date as YYYY-MM-DD for input[type="date"]
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
   }

   public async onChosenSitesChange() {
      await this.getDataAndDealWithData();
   }

  

  

   onDateChange(event: Event, isStartDate: boolean) {
      const dateValue = (event.target as HTMLInputElement).value;
      const newDate = new Date(dateValue);

      if (isStartDate) {
         this.service.startDate = newDate;
         // If new start date is greater than end date, update end date
         if (this.service.startDate.getTime() > this.service.endDate.getTime()) {
            this.service.endDate = newDate;
         }
      } else {
         this.service.endDate = newDate;
         // If new end date is less than start date, update start date
         if (this.service.endDate.getTime() < this.service.startDate.getTime()) {
            this.service.startDate = newDate;
         }
      }
      this.getDataAndDealWithData();
   }

   reassignItems(items: LVExplorerItem[]) {
      this.service.items = [...items]; //triggers ag-grid
   }

   //---------------
   // get data
   //---------------

   generateSiteIds(sites: SiteVM[], chosenSiteNames: Set<string>) {
      const ids = sites.filter((x) => chosenSiteNames.has(x.SiteDescription)).map((x) => x.SiteId);
      return ids;
   }
   private constructGetDataParams(): GetLeavingVehicleExplorerItemsParams {
      const siteIds = this.generateSiteIds(this.service.constantsService.Sites, this.service.chosenSiteNames);
      const retailerSiteIds = this.constantsService.RetailerSites.filter((x) => siteIds.includes(x.Site_Id)).map(
         (x) => x.Id
      );
      const vehTypes = this.constantsService.VehicleTypes.filter((x) => x.SuperType == "Used").map((x) => x.Type);
      const orderTypes = ["Retail"];
      const franchies = this.constantsService.FranchiseCodes;
      return {
         SiteIds: siteIds,
         VehicleTypeTypes: vehTypes,
         OrderTypeTypes: orderTypes,
         Franchises: franchies,
         ChosenRetailerSiteIds: retailerSiteIds,
         StartDate: this.service.startDate,
         EndDate: this.service.endDate,
         IncludeNewVehicles: false,
         IncludeUsedVehicles: true,
         IncludeLCVs: true,
      };
   }
   async getDataAndDealWithData() {
      this.selectionsService.triggerSpinner.emit({ show: true, message: "Loading..." });
      try {
         const parms: GetLeavingVehicleExplorerItemsParams = this.constructGetDataParams();
         const res: LVExplorerItem[] = await this.getDataMethodsService.getLeavingVehicleExplorerItems(parms);
         this.service.items = res;
      } catch (error) {
         this.service.constantsService.toastDanger("Error fetching data");
      }
      finally {
         this.selectionsService.triggerSpinner.emit({ show: false });
      }
   }

  
}
