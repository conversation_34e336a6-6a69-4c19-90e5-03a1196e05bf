import { Injectable } from '@angular/core';
import { LVExplorerItem } from 'src/app/model/LVExplorerItem';
import { ConstantsService } from 'src/app/services/constants.service';
import { LeavingVehicleExplorerTableComponent } from './leavingVehicleExplorerTable/leavingVehicleExplorerTable.component';
import { ColumnState } from 'ag-grid-community';

@Injectable({
  providedIn: 'root'
})
export class LeavingVehicleExplorerPageService   {

  //user choices
  chosenSiteNames:Set<string> = new Set<string>();
  items:LVExplorerItem[];
  itemsFiltered:LVExplorerItem[]=[];
  serviceHasBeenInitialised:boolean=false;
   startDate: Date;
  endDate: Date;
  gridState: ColumnState[] = null;

  //references to components


  constructor(
    public constantsService: ConstantsService,
  ) {
  }





}
