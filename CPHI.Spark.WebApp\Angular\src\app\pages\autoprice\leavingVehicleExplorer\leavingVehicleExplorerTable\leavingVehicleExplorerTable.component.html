<div id="gridHolder">

  <!-- <button class="btn btn-primary" id="easyPickCols" (click)="easyPickCols()">Easy pick columnns</button> -->
  
  <!-- Status bar -->
  <div id="gridHeader">
  
    <statusBar  (excelExportClick)="excelExport()" [gridColumnApi]="gridColumnApi" [gridApi]="gridApi" [gridOptions]="gridOptions"></statusBar>

    <!-- <tableLayoutManagement [showCustomiseColumns]="true" *ngIf="params.items" ></tableLayoutManagement> -->

    <!-- <div class="searchBox autoHeight">
      <div class="searchBoxIconContainer">
        <i class="searchBoxIcon fas fa-search"></i>
      </div>
      <form>
        <input placeholder="{{ constants.translatedText.Common_Search }}" class="form-control ml-2" type="text"
          [formControl]="service.searchTerm" />
        <div *ngIf="!!service.searchTerm.value" (click)="clearSearchTerm()" id="searchBarClearButton">
          <i class="fas fa-times-circle"></i>
        </div>
      </form>
    </div> -->
    <!-- <div (click)="excelExport()">
      <img [src]="constants.provideExcelLogo()" [ngStyle]="{'width': '2em'}">
    </div> -->
  </div>
  
  <!-- The Grid -->
  <ag-grid-angular class="ag-theme-balham h-100" 
  [components]="components" 
[rowData]="params.items" 
[gridOptions]="gridOptions">
</ag-grid-angular>
  
</div>
