import { Component, Input, OnInit } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
   ColumnEverythingChangedEvent,
   GridApi,
   GridReadyEvent,
   IAggFuncParams,
   ICellRendererParams,
   ModelUpdatedEvent,
   SideBarDef,
} from "ag-grid-community";
import { CphPipe } from "src/app/cph.pipe";
import { FilterModel } from "src/app/model/FilterModel";
import { SameModelAdvert } from "src/app/model/SameModelAdvert";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { ConstantsService } from "src/app/services/constants.service";
import { localeEs } from "src/environments/locale.es.js";
import { LeavingVehicleExplorerTableParams } from "./leavingVehicleExplorerTable.params";
import { LVExplorerItem } from "src/app/model/LVExplorerItem";
import { ExcelExportService } from "src/app/services/excelExportService";
import * as utilities from "../../../../services/utilityFunctions";
import { LeavingVehicleColDefsService } from "src/app/services/leavingVehicleColDefs.service";
import { CPHAutoPriceColDef, CPHColDef } from "src/app/model/CPHColDef";
import { CustomHeaderAdDetail } from "src/app/components/customHeaderAdDetail/customHeaderAdDetail.component";
import { DetailedOrderbookColDefsService } from "src/app/services/detailedOrderbookColDefs.service";
import { GridOptionsCph } from "src/app/model/GridOptionsCph";

@Component({
   selector: "leavingVehicleExplorerTable",
   templateUrl: "./leavingVehicleExplorerTable.component.html",
   styleUrls: ["./leavingVehicleExplorerTable.component.scss"],
})
export class LeavingVehicleExplorerTableComponent implements OnInit {
   @Input() params: LeavingVehicleExplorerTableParams;
   gridApi: GridApi;
   gridColumnApi: any;
   public components: {
      [p: string]: any;
   } = {
      agColumnHeader: CustomHeaderAdDetail,
   };

   public sideBar: SideBarDef | string | string[] | boolean | null = {
      toolPanels: [
         {
            id: "filters",
            labelDefault: "Filters",
            labelKey: "filters",
            iconKey: "filter",
            toolPanel: "agFiltersToolPanel",
            minWidth: 100,
            width: 200,
            maxWidth: 200,
         },
         {
            id: "columns",
            labelDefault: "Columns",
            labelKey: "columns",
            iconKey: "columns",

            toolPanel: "agColumnsToolPanel",
            minWidth: 100,
            width: 200,
            maxWidth: 200,
            toolPanelParams: {
               suppressPivotMode: false,
               suppressRowGroups: false,
               suppressValues: false,
               suppressPivots: true,
            },
         },
      ],
      position: "left",
      defaultToolPanel: "columns",
   };
   colDefs: CPHAutoPriceColDef[];
   gridOptions: GridOptionsCph;

   constructor(
      public gridHelpersService: AGGridMethodsService,
      public constantsService: ConstantsService,
      public cphPipe: CphPipe,
      private columnTypesService: ColumnTypesService,
      private excel: ExcelExportService,
      private leavingVehicleColDefsService: LeavingVehicleColDefsService,
      private detailedOrderbookColDefsService: DetailedOrderbookColDefsService
   ) {}

   //-----------------
   // lifecyle stuff
   //-----------------
   ngOnInit(): void {
      this.colDefs = this.provideColDefs();
      this.gridOptions = this.getGridDefinitions();
   }

   ngOnDestroy() {}

   myAverageAggFunc = (params: IAggFuncParams) => {
      let sum = 0;
      let count = 0;
      params.values.forEach((v) => {
         if (v != null && !isNaN(v)) {
            sum += v;
            count++;
         }
      });
      return count > 0 ? sum / count : null;
   };

   //-----------------
   // getters
   //-----------------
   getGridDefinitions(): GridOptionsCph {
      return {
         getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
         aggFuncs: {
            myAverage: this.myAverageAggFunc,
         },
         suppressAggFuncInHeader: true,
         getLocaleText: (params: any) =>
            this.constantsService.currentLang == "es"
               ? localeEs[params.key] || params.defaultValue
               : params.defaultValue,
         defaultColDef: {
            enableRowGroup: true,
            enablePivot: false, // 🚫 no columns drag
            enableValue: true,
            resizable: true,
            sortable: true,
            filterParams: {
               applyButton: false,
               clearButton: true,
               cellHeight: this.gridHelpersService.getFilterListItemHeight(),
            },
            //autoHeight: true,
            autoHeaderHeight: true,
            floatingFilter: true,
         },
         onModelUpdated: (event: ModelUpdatedEvent) => this.onModelUpdated(),
         columnTypes: {
            ...this.columnTypesService.provideColTypes([]),
         },
         columnDefs: this.colDefs,
         onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
         getRowHeight: (params) => {
            if (params.node.rowPinned) {
               return this.gridHelpersService.getRowPinnedHeight();
            } else {
               return this.gridHelpersService.getStandardHeight();
            }
         },
         pivotMode: true,
         sideBar: this.sideBar,
         floatingFiltersHeight: this.gridHelpersService.getFloatingFilterHeight(),
      };
   }

   //---------------------
   //grid stuff
   // //---------------------

   provideColDefs(): CPHAutoPriceColDef[] {
      const leavingDefs = this.leavingVehicleColDefsService.provideColDefsLeavingVehicleDetail();
      const orderbookDefs = this.detailedOrderbookColDefsService.provideDetailedOrderbookColDefs();
      const baseColDefs = leavingDefs.concat(orderbookDefs);

      this.gridHelpersService.workoutColWidths(this.params.items, baseColDefs, 10, 6);

      const minWidthColTypes = ["currency", "number", "percent", "percent1dp"];
      baseColDefs
         .filter((x) => minWidthColTypes.includes(x.type as string))
         .map((x) => {
            x.width = 70;
            x.enableValue = true;
         });

      const costCol = baseColDefs.find((x) => x.colId == "CoS");
      costCol.width = 100;

      baseColDefs.map((def) => {
         if (def.shouldAverageIfValue) {
            def.aggFunc = "myAverage";
         } else if (def.shouldAverage) {
            def.aggFunc = "avg";
         } else if (def.shouldTotal) {
            def.aggFunc = "sum";
         }
      });

      //const pivotMeasures = ['currency','number'];
      //baseColDefs.filter((x) => minWidthColTypes.includes((x.type as string)) ).map((x) => (x.enableValue = true));

      let savedState = this.params.loadGridState();
      if (savedState == null) {
         return baseColDefs;
      }

      //else update
      return this.gridHelpersService.mergeStateIntoDefs(savedState, baseColDefs);
   }

   getImage(params: ICellRendererParams) {
      const row: SameModelAdvert = params.data;
      if (!row || !row?.ImageUrl) return "";
      return `<img style="height: 50px; width: 100%;" src=${row.ImageUrl} />`;
   }

   onModelUpdated() {
      if (!this.gridApi) {
         return;
      }
      const filteredItems: LVExplorerItem[] = [];
      this.gridApi.forEachNodeAfterFilter((node) => filteredItems.push(node.data));
      this.params.newFilteredData(filteredItems);
   }

   onGridReady(event: GridReadyEvent) {
      this.gridApi = event.api;
      this.gridColumnApi = event.columnApi;
      this.onModelUpdated();
   }

   public updateFilterModel(filterModel: FilterModel) {
      this.gridApi.setFilterModel(filterModel);
   }

   excelExport() {
      let tableModel = this.gridApi.getModel();
      this.excel.createSheetObject(tableModel, "Strategy Price Build Up Detail", 1, 1);
   }

   saveGridState = utilities.debounce((event: ColumnEverythingChangedEvent) => {
      const colState = event.columnApi.getColumnState();
      this.params.storeGridState(colState);
   }, 300);
}
