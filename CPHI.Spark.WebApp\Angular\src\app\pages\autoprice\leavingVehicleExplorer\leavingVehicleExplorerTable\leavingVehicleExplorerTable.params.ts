


//This describes the properties that a service must commit to having, when it's used with a simpleExampleItemsTable.   
//In any service that wants to use a simpleExampleItemsTable, it must implement this interface

import { ColumnState } from "ag-grid-community";
import { LVExplorerItem } from "src/app/model/LVExplorerItem";

//for example:   export class SimpleExamplePageService implements SimpleExampleItemsServiceInterface  { etc.
export class LeavingVehicleExplorerTableParams {
  //gridRef: SimpleExampleItemsTableComponent;
  items: LVExplorerItem[]; //page uses this to populate the table
  newFilteredData: (filteredItems: LVExplorerItem[]) => void;
  storeGridState: (gridState: ColumnState[]) => void;
  loadGridState: () => ColumnState[]|null;
  //updateItemDescription: (item: LVExplorerItem, newDescription: string) => void;
}
