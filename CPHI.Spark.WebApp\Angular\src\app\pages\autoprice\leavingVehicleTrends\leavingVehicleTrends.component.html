<nav class="navbar">
   <nav class="generic">
      <h4 id="pageTitle">
         Leaving Vehicle Trends&nbsp;
         <sourceDataUpdate *ngIf="!constants.environment.showLatestSnapshotDate"
                           [chosenDate]="constants.LastUpdatedDates.AutotraderAdverts"></sourceDataUpdate>
      </h4>

      <div class="d-flex align-items-center">
         From:
         <div class="mx-2">
            <input type="date" class="form-control"
                   [value]="formatDateForInput(service.startDate)"
                   (change)="onDateChange($event, true)">
         </div>
         To:
         <div class="mx-2">
            <input type="date" class="form-control"
                   [value]="formatDateForInput(service.endDate)"
                   (change)="onDateChange($event, false)">
         </div>
      </div>

      <div>

         <div ngbDropdown class="d-inline-block" autoClose="true">
            <button class="btn btn-primary metricsButton" id="dropdownBasic1" ngbDropdownToggle>
               {{ service.metric?.label }}
            </button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
               <button *ngFor="let metricChoice of service.metricChoices" ngbDropdownItem
                       (click)="changeMetricChoice(metricChoice)">
                  {{ metricChoice.label }}
               </button>
            </div>
         </div>

      </div>


      <div class="buttonGroup">
         <button class="btn btn-primary" [ngClass]="{'active':service.displayChoice==='smallCharts'}"
                 (click)="service.displayChoice='smallCharts'">
            Small Charts
         </button>
         <button class="btn btn-primary" [ngClass]="{'active':service.displayChoice==='bigChart'}"
                 (click)="service.displayChoice='bigChart'">
            Large Chart
         </button>
      </div>

      <!-- Show First PP slider -->
      <ng-container *ngIf="service.displayChoice==='bigChart'">
         <sliderSwitch (toggle)="service.toggleSetShowPricePoints('first') " [disabled]="false"
                       [defaultValue]="service.showFirstPricePoint" [text]="'First Price Position'"></sliderSwitch>


         <!-- Show Last PP slider -->
         <sliderSwitch *ngIf="service.displayChoice==='bigChart'" (toggle)="service.toggleSetShowPricePoints('last') "
                       [disabled]="false" [defaultValue]="service.showLastPricePoint"
                       [text]="'Final Price Position'"></sliderSwitch>

      </ng-container>
   </nav>
</nav>

<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
   <div class="content-new">
      <div class="content-inner-new d-flex small-gap">
         <!-- LEFT 1/3 -->
         <div class="d-flex small-gap left-side flex-wrap">
            <div class="column-container">

               <!-- All the slicers  -->

               <!-- ############################## -->
               <!-- Left hand column -->
               <!-- ############################## -->

               <!-- Total -->
               <div class="trend-tile">
                  <ng-container *ngIf="service.rawDataHighlighted">
                     <div class="tileHeader">
                        <div class="h4 headerWords">
                           Total Vehicles
                        </div>
                     </div>
                     <div class="contentsHolder">
                        <div id="totalCountContent"
                             class="d-flex flex-column align-items-center justify-content-center h-100">
                           <div id="clickToViewDetail" *ngIf="pageIsFiltered()">
                              <instructionRow [message]="'Click number to view detail'"></instructionRow>
                           </div>
                           <h1 class="bigNumber clickable" id="totalCount" (click)="goToLeavingVehiclesAnalysis()">
                              <strong>
                                 {{ service.rawDataHighlighted.length | cph:'number':0 }}
                              </strong>
                           </h1>
                        </div>
                     </div>
                  </ng-container>
               </div>

               <!-- Region -->
               <div class="trend-tile">
                  <biChartTile
                     *ngIf="service.rawData"
                     [dataType]="dataTypes.label"
                     [title]="'Region'"
                     [metric]="service?.metric"
                     [tileType]="'VerticalBar'"
                     [labelWidth]="50"
                     [pageParams]="service.getPageParams()"
                     [fieldName]="'Region'">
                  </biChartTile>
               </div>

               <!-- RegYear -->
               <div class="trend-tile">
                  <biChartTile
                     *ngIf="service.rawData"
                     [dataType]="dataTypes.label"
                     [title]="'Registration Year'"
                     [metric]="service?.metric"
                     [tileType]="'VerticalBar'"
                     [labelWidth]="30"
                     [pageParams]="service.getPageParams()"
                     [fieldName]="'RegYear'"
                     [customSort]="'RegYear'">
                  </biChartTile>
               </div>


               <!-- FuelType -->
               <div class="trend-tile">
                  <biChartTile
                     *ngIf="service.rawData"
                     [metric]="service?.metric"
                     [dataType]="dataTypes.label"
                     [title]="'Fuel Type'"
                     [customSort]="'HighToLow'"
                     [tileType]="'VerticalBar'"
                     [labelWidth]="40"
                     [pageParams]="service.getPageParams()"
                     [fieldName]="'FuelType'">
                  </biChartTile>
               </div>

               <!-- MileageBand -->
               <div class="trend-tile">
                  <biChartTile
                     *ngIf="service.rawData"
                     [dataType]="dataTypes.label"
                     [title]="'Mileage'"
                     [metric]="service?.metric"
                     [tileType]="'VerticalBar'"
                     [labelWidth]="30"
                     [pageParams]="service.getPageParams()"
                     [fieldName]="'MileageBand'"
                     [customSort]="'Mileage'">
                  </biChartTile>
               </div>

               <!-- Make -->
               <div class="trend-tile">
                  <biChartTile
                     *ngIf="service.rawData"
                     [dataType]="dataTypes.label"
                     [title]="'Make'"
                     [metric]="service?.metric"
                     [customSort]="'HighToLow'"
                     [tileType]="'VerticalBar'"
                     [labelWidth]="30"
                     [pageParams]="service.getPageParams()"
                     [fieldName]="'Make'">
                  </biChartTile>
               </div>


            </div>


            <div class="column-container">

               <!-- Retailer Site Name -->
               <div class="trend-tile">
                  <biChartTile
                     *ngIf="service.rawData"
                     [dataType]="dataTypes.label"
                     [title]="'Retailer Site Name'"
                     [metric]="service?.metric"
                     [tileType]="'VerticalBar'"
                     [labelWidth]="60"
                     [pageParams]="service.getPageParams()"
                     [fieldName]="'RetailerSiteName'">
                  </biChartTile>
               </div>

               <!-- BodyType -->
               <div class="trend-tile">
                  <biChartTile
                     *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Body Type'"
                     [metric]="service?.metric"
                     [customSort]="'HighToLow'"
                     [tileType]="'VerticalBar'"
                     [labelWidth]="30"
                     [pageParams]="service.getPageParams()"
                     [fieldName]="'BodyType'">
                  </biChartTile>
               </div>

               <!-- TransmissionType -->
               <div class="trend-tile">
                  <biChartTile
                     *ngIf="service.rawData"
                     [dataType]="dataTypes.label"
                     [title]="'TransmissionType'"
                     [metric]="service?.metric"
                     [tileType]="'VerticalBar'"
                     [labelWidth]="30"
                     [pageParams]="service.getPageParams()"
                     [fieldName]="'TransmissionType'">
                  </biChartTile>
               </div>


               <!-- LastPriceBand -->
               <div class="trend-tile">
                  <biChartTile
                     *ngIf="service.rawData"
                     [dataType]="dataTypes.label"
                     [title]="'Price Band'"
                     [metric]="service?.metric"
                     [tileType]="'VerticalBar'"
                     [labelWidth]="30"
                     [pageParams]="service.getPageParams()"
                     [fieldName]="'LastPriceBand'"
                     [customSort]="'LastPriceBand'">
                  </biChartTile>
               </div>


               <!-- IsOnStrategy -->
               <div class="trend-tile">
                  <biChartTile
                     *ngIf="service.rawData"
                     [dataType]="dataTypes.label"
                     [title]="'On Strategy'"
                     [metric]="service?.metric"
                     [tileType]="'VerticalBar'"
                     [labelWidth]="30"
                     [pageParams]="service.getPageParams()"
                     [fieldName]="'IsOnStrategy'">
                  </biChartTile>
               </div>


               <!-- Model -->
               <div class="trend-tile">
                  <biChartTile
                     *ngIf="service.rawData"
                     [dataType]="dataTypes.label"
                     [title]="'Model'"
                     [metric]="service?.metric"
                     [customSort]="'HighToLow'"
                     [tileType]="'VerticalBar'"
                     [labelWidth]="50"
                     [pageParams]="service.getPageParams()"
                     [fieldName]="'Model'">
                  </biChartTile>
               </div>


            </div>


            <!-- ############################## -->
            <!-- The charts -->
            <!-- ############################## -->

         </div>
         <!-- RIGHT 2/3 -->
         <div class="right-side d-flex small-gap flex-column h-100">

            <div style="flex-grow: 1;">

               <!-- Big chart -->
               <ng-container *ngIf="service.displayChoice==='bigChart'">

                  <!-- Big chart itself -->
                  <div class="h-100">
                     <bigLeavingChart [params]="service.bigChartParams" *ngIf="service.bigChartParams"
                                      [newDataEmitter]="service.newBigChartDataEmitter"></bigLeavingChart>
                  </div>

               </ng-container>

               <div *ngIf="service.displayChoice==='smallCharts'"
                    style="
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px,   1fr));"
                    class="w-100 small-gap h-100">

                  <div class="graph-column">

                     <!-- Sold volume by retail rating -->
                     <div class="graph-tile">
                        <ng-container *ngIf="service.smallChartSetData">
                           <div class="tileHeader">
                              <div class="h4 headerWords">
                                 {{ service.smallChartSetData.rrSoldVolume.Title }}
                              </div>
                           </div>
                           <div class="contentsHolder">
                              <barChart *ngIf="service.smallChartSetData" [dataKey]="'rrSoldVolume'"
                                        [newDataEmitter]="service.newSmallChartDataEmitter"
                                        [params]="service.smallChartSetData.rrSoldVolume" [doesFilter]="true"
                                        [pageParams]="service.getPageParams()" [dataType]="dataTypes.label"
                                        [tileType]="'HorizontalBar'"
                                        [fieldName]="'RetailRatingBand'" [showPercentageOfTotal]="true">
                              </barChart>
                           </div>
                        </ng-container>
                     </div>


                     <!-- Days to sell by Retail Rating -->
                     <div class="graph-tile">
                        <ng-container *ngIf="service.smallChartSetData">
                           <div class="tileHeader">
                              <div class="h4 headerWords">
                                 {{ service.smallChartSetData.rrDaysListed.Title }}
                              </div>
                           </div>
                           <div class="contentsHolder">
                              <barChart *ngIf="service.smallChartSetData" [dataKey]="'rrDaysListed'"
                                        [newDataEmitter]="service.newSmallChartDataEmitter"
                                        [doesFilter]="true" [pageParams]="service.getPageParams()"
                                        [dataType]="dataTypes.label" 
                                        [tileType]="'HorizontalBar'" [fieldName]="'RetailRatingBand'"
                                        [params]="service.smallChartSetData.rrDaysListed">
                              </barChart>
                           </div>
                        </ng-container>
                     </div>

                     <div style="height: 33%;">
                        <!-- intentionally left empty - to make boxes align nicely -->
                     </div>
                  </div>

                  <div class="graph-column">

                     <!-- First price position -->
                     <div class="graph-tile">
                        <ng-container *ngIf="service.smallChartSetData">
                           <div class="tileHeader">
                              <div class="h4 headerWords">
                                 {{ service.smallChartSetData.rrFirstPP.Title }}
                              </div>
                           </div>
                           <div class="contentsHolder">
                              <barChart *ngIf="service.smallChartSetData" [dataKey]="'rrFirstPP'"
                                        [newDataEmitter]="service.newSmallChartDataEmitter"
                                        [doesFilter]="true" [pageParams]="service.getPageParams()"
                                        [dataType]="dataTypes.label"
                                        [tileType]="'HorizontalBar'" [fieldName]="'RetailRatingBand'"
                                        [params]="service.smallChartSetData.rrFirstPP">
                              </barChart>
                           </div>
                        </ng-container>
                     </div>

                     <!-- Final PP -->
                     <div class="graph-tile">
                        <ng-container *ngIf="service.smallChartSetData">
                           <div class="tileHeader">
                              <div class="h4 headerWords">
                                 {{ service.smallChartSetData.rrLastPP.Title }}
                              </div>
                           </div>
                           <div class="contentsHolder">
                              <barChart *ngIf="service.smallChartSetData" [dataKey]="'rrLastPP'"
                                        [newDataEmitter]="service.newSmallChartDataEmitter"

                                        [doesFilter]="true" [pageParams]="service.getPageParams()"
                                        [dataType]="dataTypes.label"
                                        [tileType]="'HorizontalBar'" [fieldName]="'RetailRatingBand'"

                                        [params]="service.smallChartSetData.rrLastPP">
                              </barChart>
                           </div>
                        </ng-container>
                     </div>


                     <!-- PP change -->
                     <div class="graph-tile">
                        <ng-container *ngIf="service.smallChartSetData">
                           <div class="tileHeader">
                              <div class="h4 headerWords">
                                 {{ service.smallChartSetData.rrChangedPP.Title }}
                              </div>
                           </div>
                           <div class="contentsHolder">
                              <barChart *ngIf="service.smallChartSetData" [dataKey]="'rrChangedPP'"
                                        [newDataEmitter]="service.newSmallChartDataEmitter"

                                        [doesFilter]="true" [pageParams]="service.getPageParams()"
                                        [dataType]="dataTypes.label"
                                        [tileType]="'HorizontalBar'" [fieldName]="'RetailRatingBand'"

                                        [params]="service.smallChartSetData.rrChangedPP">
                              </barChart>
                           </div>
                        </ng-container>
                     </div>


                  </div>

                  <div class="graph-column">
                     <!-- Sold volume by Days Listed -->
                     <div class="graph-tile">
                        <ng-container *ngIf="service.smallChartSetData">
                           <div class="tileHeader">
                              <div class="h4 headerWords">
                                 {{ service.smallChartSetData.dlSoldVolume.Title }}
                              </div>
                           </div>
                           <div class="contentsHolder">
                              <barChart *ngIf="service.smallChartSetData" [dataKey]="'dlSoldVolume'"
                                        [newDataEmitter]="service.newSmallChartDataEmitter"
                                        [params]="service.smallChartSetData.dlSoldVolume"
                                        [doesFilter]="true" [pageParams]="service.getPageParams()"
                                        [dataType]="dataTypes.label"
                                        [tileType]="'HorizontalBar'" [fieldName]="'DaysListedBand'"
                                        [showPercentageOfTotal]="true">
                              </barChart>
                           </div>
                        </ng-container>
                     </div>

                     <!-- Average days listed by Days Listed -->
                     <div class="graph-tile">
                        <ng-container *ngIf="service.smallChartSetData">
                           <div class="tileHeader">
                              <div class="h4 headerWords">
                                 {{ service.smallChartSetData.dlDaysListed.Title }}
                              </div>
                           </div>
                           <div class="contentsHolder">
                              <barChart *ngIf="service.smallChartSetData" [dataKey]="'dlDaysListed'"
                                        [newDataEmitter]="service.newSmallChartDataEmitter"
                                        [doesFilter]="true"
                                        [pageParams]="service.getPageParams()"
                                        [fieldName]="'DaysListedBand'"
                                        [params]="service.smallChartSetData.dlDaysListed">
                              </barChart>
                           </div>
                        </ng-container>
                     </div>

                     <!-- Days listed final PP -->
                     <div class="graph-tile">
                        <ng-container *ngIf="service.smallChartSetData">
                           <div class="tileHeader">
                              <div class="h4 headerWords">
                                 {{ service.smallChartSetData.dlLastPP.Title }}
                              </div>
                           </div>
                           <div class="contentsHolder">
                              <barChart *ngIf="service.smallChartSetData" [dataKey]="'dlLastPP'"
                                        [newDataEmitter]="service.newSmallChartDataEmitter"
                                        [doesFilter]="true"
                                        [pageParams]="service.getPageParams()"
                                        [fieldName]="'DaysListedBand'"
                                        [params]="service.smallChartSetData.dlLastPP">
                              </barChart>
                           </div>
                        </ng-container>
                     </div>

                  </div>

               </div>
            </div>

            <div class="d-flex flex-wrap" style="height: 220px; grid-gap: 0.5em;">

               <div class="flex-grow-1" style="flex-basis: 200px;">

                  <div class="d-flex small-gap h-100 w-100">

                     <div class="d-flex flex-column small-gap" style="width: 50%;">

                        <!-- OptedOutPctBand -->
                        <div class="trend-tile">
                           <biChartTile
                              *ngIf="service.rawData"
                              [dataType]="dataTypes.label"
                              [metric]="service?.metric"
                              [title]="'Opted Out Percentage Band'"
                              [tileType]="'VerticalBar'"
                              [labelWidth]="30"
                              [pageParams]="service.getPageParams()"
                              [fieldName]="'OptedOutPctBand'">
                           </biChartTile>
                        </div>


                        <!-- Vehicle Type  -->
                        <div class="trend-tile">
                           <biChartTile
                              *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Vehicle Type'"
                              [metric]="service?.metric"
                              [customSort]="'HighToLow'" [tileType]="'VerticalBar'" [labelWidth]="30"
                              [pageParams]="service.getPageParams()" [fieldName]="'VehicleType'">
                           </biChartTile>
                        </div>
                     </div>

                     <div style="width: 50%;" class="d-flex flex-column small-gap">

                        <!-- FirstPPBand -->
                        <div class="trend-tile">
                           <biChartTile
                              *ngIf="service.rawData"
                              [dataType]="dataTypes.label"
                              [title]="firstPPTitle()"
                              [metric]="service?.metric"
                              [tileType]="'VerticalBar'"
                              [labelWidth]="30"
                              [pageParams]="service.getPageParams()"
                              [fieldName]="'FirstPPBand'"
                              [customSort]="'PPBand'">
                           </biChartTile>
                        </div>

                        <!-- LastPPBand -->
                        <div class="trend-tile">
                           <biChartTile
                              *ngIf="service.rawData"
                              [dataType]="dataTypes.label"
                              [title]="lastPPTitle()"
                              [metric]="service?.metric"
                              [tileType]="'VerticalBar'"
                              [labelWidth]="30"
                              [pageParams]="service.getPageParams()"
                              [fieldName]="'LastPPBand'"
                              [customSort]="'PPBand'">
                           </biChartTile>
                        </div>


                     </div>

                  </div>

               </div>

               <div class="flex-grow-1" style="flex-basis: 200px;">

                  <div class="trend-tile h-100">
                     <table *ngIf="service.summaryStats" id="summaryStatsTable">
                        <thead>
                        <tr>
                           <th></th>
                           <th>All</th>
                           <th>Selected</th>
                           <th>Vs</th>
                           <th>Not selected</th>
                           <th>Vs not selected</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                           <td>Vehicles</td>
                           <td>{{ service.summaryStats.all.vehicleCount|cph:'number':0 }}</td>
                           <td>{{ service.summaryStats.highlighted.vehicleCount|cph:'number':0 }}</td>
                           <td>{{ service.summaryStats.vs.vehicleCount|cph:'number':0 }}</td>
                           <td>{{ service.summaryStats.notHighlighted.vehicleCount|cph:'number':0 }}</td>
                           <td>{{ service.summaryStats.highlightedVsNot.vehicleCount|cph:'number':0 }}</td>
                        </tr>
                        <tr>
                           <td>Retail Rating</td>
                           <td>{{ service.summaryStats.all.retailRating|cph:'number':0 }}</td>
                           <td>{{ service.summaryStats.highlighted.retailRating|cph:'number':0 }}</td>
                           <td>{{ service.summaryStats.vs.retailRating|cph:'number':0 }}</td>
                           <td>{{ service.summaryStats.notHighlighted.retailRating|cph:'number':0 }}</td>
                           <td>{{ service.summaryStats.highlightedVsNot.retailRating|cph:'number':0 }}</td>
                        </tr>
                        <tr>
                           <td>Days Listed</td>
                           <td>{{ service.summaryStats.all.daysListed|cph:'number':0 }}</td>
                           <td>{{ service.summaryStats.highlighted.daysListed|cph:'number':0 }}</td>
                           <td>{{ service.summaryStats.vs.daysListed|cph:'number':0 }}</td>
                           <td>{{ service.summaryStats.notHighlighted.daysListed|cph:'number':0 }}</td>
                           <td>{{ service.summaryStats.highlightedVsNot.daysListed|cph:'number':0 }}</td>
                        </tr>
                        <tr>
                           <td>First Price Position</td>
                           <td>{{ service.summaryStats.all.firstPP|cph:'percent':1 }}</td>
                           <td>{{ service.summaryStats.highlighted.firstPP|cph:'percent':1 }}</td>
                           <td>{{ service.summaryStats.vs.firstPP|cph:'percent':1 }}</td>
                           <td>{{ service.summaryStats.notHighlighted.firstPP|cph:'percent':1 }}</td>
                           <td>{{ service.summaryStats.highlightedVsNot.firstPP|cph:'percent':1 }}</td>
                        </tr>
                        <tr>
                           <td>Final Price Position</td>
                           <td>{{ service.summaryStats.all.lastPP|cph:'percent':1 }}</td>
                           <td>{{ service.summaryStats.highlighted.lastPP|cph:'percent':1 }}</td>
                           <td>{{ service.summaryStats.vs.lastPP|cph:'percent':1 }}</td>
                           <td>{{ service.summaryStats.notHighlighted.lastPP|cph:'percent':1 }}</td>
                           <td>{{ service.summaryStats.highlightedVsNot.lastPP|cph:'percent':1 }}</td>
                        </tr>
                        <tr>
                           <td>Change to Price Position</td>
                           <td>{{ service.summaryStats.all.changePP|cph:'percent':1 }}</td>
                           <td>{{ service.summaryStats.highlighted.changePP|cph:'percent':1 }}</td>
                           <td>{{ service.summaryStats.notHighlighted.changePP|cph:'percent':1 }}</td>
                           <td>{{ service.summaryStats.notHighlighted.changePP|cph:'percent':1 }}</td>
                           <td>{{ service.summaryStats.highlightedVsNot.changePP|cph:'percent':1 }}</td>
                        </tr>
                        </tbody>
                     </table>
                  </div>


               </div>

            </div>
         </div>
      </div>
   </div>
</div>
