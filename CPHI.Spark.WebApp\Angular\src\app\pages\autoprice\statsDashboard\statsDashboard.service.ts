import {EventEmitter, Injectable} from '@angular/core';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {CphPipe} from 'src/app/cph.pipe';

import {Router} from '@angular/router';
import {GridApi} from 'ag-grid-community';
import {GlobalParamKey} from 'src/app/model/GlobalParam';
import {GetStatsDashboardParams} from 'src/app/model/GetStatsDashboardParams';
import {MenuItemNew} from 'src/app/model/main.model';
import {StatsDashboard, StatsDashboardDTO} from 'src/app/model/StatsDashboard';
import {StatsDashboardFiltersForStockReport} from 'src/app/model/StatsDashboardFiltersForStockReport';
import {StatsVehicle} from 'src/app/model/StatsVehicle';
import {ConstantsService} from 'src/app/services/constants.service';
import {GetDataMethodsService} from 'src/app/services/getDataMethods.service';
import {GlobalParamsService} from 'src/app/services/globalParams.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {AdvertListingDetailService} from '../advertListingDetail/advertListingDetail.service';
import {StatsDashboardComponent, StatsDashboardPageComponentType} from './statsDashboard.component';
import {
   StatsDashboardTableComponent,
   StatsDashboardTableParams
} from './statsDashboardTable/statsDashboardTable.component';

// Enum for chart keys to ensure type safety
export enum ChartKey {
   AdvertsByRetailRating = 'advertsByRetailRating',
   AdvertsByDaysListed = 'advertsByDaysListed',
   VsStrategyPrice = 'vsStrategyPrice',
   PerformanceRating = 'performanceRating'
}

// Map chart keys to filter configuration
export const CHART_TO_FILTER_MAP: Record<ChartKey, { fieldName: string, filterType: 'range' | 'set' }> = {
   [ChartKey.AdvertsByRetailRating]: { fieldName: 'RetailRating', filterType: 'range' },
   [ChartKey.AdvertsByDaysListed]: { fieldName: 'DaysListed', filterType: 'range' },
   [ChartKey.VsStrategyPrice]: { fieldName: 'VsStrategyBanding', filterType: 'set' },
   [ChartKey.PerformanceRating]: { fieldName: 'PerfRating', filterType: 'set' }
};

// Centralized filter category definitions
export const CHART_FILTER_CATEGORIES = {
   RetailRating: [
      {range: [0, 19], label: '<20'},
      {range: [20, 39], label: '20 to 39'},
      {range: [40, 59], label: '40 to 59'},
      {range: [60, 79], label: '60 to 79'},
      {range: [80, Infinity], label: '80+'}
   ],
   DaysListed: [
      {range: [0, 19], label: '<20'},
      {range: [20, 39], label: '<40'},
      {range: [40, 59], label: '<60'},
      {range: [60, Infinity], label: '60+'}
   ],
   VsStrategyBanding: [
      {range: null, label: 'V. Underpriced', value: 'VeryUnderPriced', displayValue: 'V. Underpriced'},
      {range: null, label: 'Underpriced', value: 'UnderPriced', displayValue: 'Underpriced'},
      {range: null, label: 'On Strategy', value: 'OnStrategyPrice', displayValue: 'On Strategy'},
      {range: null, label: 'Overpriced', value: 'OverPriced', displayValue: 'Overpriced'},
      {range: null, label: 'V. Overpriced', value: 'VeryOverPriced', displayValue: 'V. Overpriced'},
      {range: null, label: 'No Strat Price', value: 'NoValuation', displayValue: 'No Valuation'},
      {range: null, label: 'No Price', value: 'NoAdvertisedPrice', displayValue: 'No Price'}
   ],
   PerfRating: [
      {range: [0.01, 25], label: 'Low', value: 'LOW'},
      {range: [25.01, 50], label: '< Average', value: 'BELOW_AVERAGE', displayValue: 'Below Avg'},
      {range: [50.01, 75], label: '> Average', value: 'ABOVE_AVERAGE', displayValue: 'Above Avg'},
      {range: [75.01, Infinity], label: 'Excellent', value: 'EXCELLENT'},
      {range: [null, null], label: 'No Rating', value: 'NONE', displayValue: ''}
   ]
};

@Injectable({
   providedIn: 'root'
})
export class StatsDashboardService implements StatsDashboardTableParams {

   chosenSiteNames: Set<string> = new Set<string>();
   chosenRegionNames: Set<string> = new Set<string>();

   serviceHasBeenInitialised: boolean = false;

   useTestStrategy: boolean = false;
   showTestStrategySlider: boolean = false;


   //references to components
   gridRef: StatsDashboardTableComponent;
   pageRef: StatsDashboardComponent;


   newSmallChartDataEmitter: EventEmitter<any> = new EventEmitter();


   filters: StatsDashboardFiltersForStockReport;

   filterModel: any = {};
   previouslySelectedBars: { [key: string]: number[] } = {};

   includeUnPublishedAds: boolean = false;
   includeNewVehicles: boolean = false;
   includeLCVs: boolean = true;

   // Key for the ShowLCVToggle GlobalParam
   showLCVToggleKey: GlobalParamKey = GlobalParamKey.ShowLCVToggle;
   showLCVToggle: boolean = false;


   allLifecycleStatuses: string [] = [
      "DUE_IN",
      "FORECOURT",
      "IN STOCK NOT ON PORTAL",
      "SALE_IN_PROGRESS"
   ];

   chosenLifecycleStatuses: Set<string> = new Set();

   get vehicles() {
      return this.pageRef?.statsDashboard.Vehicles
   }

   gridApi: GridApi;

   constructor(
      public constantsService: ConstantsService,
      public selectionsService: SelectionsService,
      public modalService: NgbModal,
      public getDataMethodsService: GetDataMethodsService,
      public cph: CphPipe,
      public router: Router,
      public advertListingDetailService: AdvertListingDetailService,
      public globalParamsService: GlobalParamsService
   ) {


   }


   get siteNames(): string[] {
      const chosenSitesForRegions = this.constantsService.Sites.filter(x => Array.from(this.chosenRegionNames).includes(x.RegionDescription)).map(x => x.SiteId);
      return this.constantsService.RetailerSites.filter(x => x.IsActive && chosenSitesForRegions.includes(x.Site_Id)).map(x => x.Name);
   };

   get regionNames(): string[] {
      return Array.from(new Set(this.constantsService.Sites.filter(x => x.IsActive).map(x => x.RegionDescription)));
   };


   initParams(): void {

      if (!this.serviceHasBeenInitialised) {
         this.chosenSiteNames = new Set<string>(this.constantsService.RetailerSites.filter(x => x.IsActive).map(x => x.Name));
         this.chosenRegionNames = new Set<string>(this.constantsService.Sites.filter(x => x.IsActive).map(x => x.RegionDescription));
         this.chosenLifecycleStatuses = new Set<string>(this.allLifecycleStatuses);

         // Initialize includeLCVs based on GlobalParam
         if (this.globalParamsService.getGlobalParam(this.showLCVToggleKey) == true) {
            this.showLCVToggle = true;
         }
         ;

         this.serviceHasBeenInitialised = true;
         this.initialiseFilters();
      }

   }


   async getData() {
      try {
         const parms: GetStatsDashboardParams = {
            RetailerSiteIds: this.constantsService.RetailerSites.filter(x => this.chosenSiteNames.has(x.Name)).map(x => x.Id),
            UseTestStrategy: this.useTestStrategy,
            IncludeNewVehicles: this.includeNewVehicles,
            IncludeUnPublishedAdverts: this.includeUnPublishedAds,
            LifecycleStatuses: Array.from(this.chosenLifecycleStatuses),
            IncludeLCVs: this.includeLCVs,
            IncludeOptedOutVehicles: this.constantsService.optedOutVehiclesChoice !== "Exclude Opted Out Vehicles",
            IncludeNonOptedOutVehicles: this.constantsService.optedOutVehiclesChoice !== "Only Opted Out Vehicles"
         };
         const res: StatsDashboardDTO = await this.getDataMethodsService.getStatsDashboard(parms);
         const statsDashboard: StatsDashboard = new StatsDashboard(res);

         this.dealWithNewData(statsDashboard);
      } catch (error) {
         this.constantsService.toastDanger("Error fetching data");
      }
   }


   //do any triggering of the various components
   dealWithNewData(data: StatsDashboard) {

      this.pageRef.dealWithNewData(data);


   }

   dealWithFilteredItems(filteredItems: StatsVehicle[], callingComponent: StatsDashboardPageComponentType) {
      //todo
   }


   initialiseFilters() {
      this.filters = {
         PerformanceRating: false,
         RetailRating: false,
         DaysListed: false,
         PricePosition: false,
         IsLowQuality: false,
         NoAttentionGrabber: false,
         LessThan9Images: false,
         NoImages: false,
         NoVideo: false,
         VsStrategyBandingFilter: false
      }
   }

   getDashboardParams() {
      return {
         parentMethods: {
            filterTable: (fieldName, dataType) => {
               // Get the bar index based on the label
               let barIndex = -1;
               if (dataType === ChartKey.AdvertsByRetailRating) {
                  barIndex = this.pageRef.advertsByRetailRating?.Labels?.indexOf(fieldName) ?? -1;
               } else if (dataType === ChartKey.AdvertsByDaysListed) {
                  barIndex = this.pageRef.advertsByDaysListed?.Labels?.indexOf(fieldName) ?? -1;
               } else if (dataType === ChartKey.VsStrategyPrice) {
                  barIndex = this.pageRef.vsStrategyPrice?.Labels?.indexOf(fieldName) ?? -1;
               } else if (dataType === ChartKey.PerformanceRating) {
                  barIndex = this.pageRef.performanceRating?.Labels?.indexOf(fieldName) ?? -1;
               }

               // Call onBarSelected instead of filterTableFromChartClick
               if (barIndex >= 0 && this.pageRef) {
                  this.pageRef.onBarSelected(dataType, barIndex);
               }
            },
            isChartSelected: (dataKey) => this.pageRef?.selectedBarInfo?.chartKey === dataKey
         }
      }
   }

   enableFilter(field: string) {
      this.filters[field] = true;
      this.filter(field);
      // Chart updates will be triggered by grid's onFilterChanged event
   }

   filterTableFromChartClick(label: any, dataType: any) {
      // Find the bar index based on the label and chart type
      let barIndex = -1;
      if (dataType === ChartKey.AdvertsByRetailRating) {
         barIndex = this.pageRef.advertsByRetailRating?.Labels?.indexOf(label) ?? -1;
         this.filterForRetailRating(label);
      } else if (dataType === ChartKey.AdvertsByDaysListed) {
         barIndex = this.pageRef.advertsByDaysListed?.Labels?.indexOf(label) ?? -1;
         this.filterForDaysListed(label);
      } else if (dataType === ChartKey.VsStrategyPrice) {
         barIndex = this.pageRef.vsStrategyPrice?.Labels?.indexOf(label) ?? -1;
         this.filterForStrategyPrice(label);
      } else if (dataType === ChartKey.PerformanceRating) {
         barIndex = this.pageRef.performanceRating?.Labels?.indexOf(label) ?? -1;
         this.filterForPerformanceRating(label);
      }

      // Trigger opacity update
      if (barIndex >= 0) {
         this.pageRef.onBarSelected(dataType, barIndex);
      }
   }

   filterForRetailRating(label: string) {
      const category = CHART_FILTER_CATEGORIES.RetailRating.find(cat => cat.label === label);
      const filterRange = category ? category.range : [80]; // Default to 80+ if not found

      this.filters.RetailRating = true;
      this.filter('RetailRating', filterRange);
   }

   filterForDaysListed(label: string) {
      const category = CHART_FILTER_CATEGORIES.DaysListed.find(cat => cat.label === label);
      const filterRange = category ? category.range : [60]; // Default to 60+ if not found

      this.filters.DaysListed = true;
      this.filter('DaysListed', filterRange);
   }

   filterForStrategyPrice(label: string) {
      const category = CHART_FILTER_CATEGORIES.VsStrategyBanding.find(cat => cat.label === label);

      this.filters.VsStrategyBandingFilter = true;

      // Handle special case for "No Strat Price" which combines both NoValuation and NoAdvertisedPrice
      if (label === 'No Strat Price') {
         this.filterModel['VsStrategyBandingFilter'] = {
            filterType: 'set',
            values: ['NoValuation', 'NoAdvertisedPrice']
         };
         this.gridApi.setFilterModel(this.filterModel);

         // Update pinned bottom row after applying filters
         if (this.gridRef && this.gridRef.updatePinnedBottomRow) {
            this.gridRef.updatePinnedBottomRow();
         }
      } else {
         const filterValue = category ? category.value : label; // fallback to label if not found
         this.filter('VsStrategyBandingFilter', undefined, filterValue);
      }
   }

   filterForPerformanceRating(label: string) {
      const category = CHART_FILTER_CATEGORIES.PerfRating.find(cat => cat.label === label);
      let filterRange = category ? category.range : [0, 0]; // Default fallback

      // Handle special case for "No Rating" (null values)
      if (label === 'No Rating') {
         filterRange = [0,0]; // Special flag for null filtering
      }

      this.filters.PerformanceRating = true;
      this.filter('PerformanceRating', filterRange);
   }

   filter(field: string, filterRange?: number[], setFilterValue?: string) {
      if (filterRange === null) {
         // Special case for null values (No Rating)
         this.filterModel[field] = {
            filterType: 'number',
            type: 'blank'
         }
      } else if (filterRange) {
         // Number filter
         if (filterRange.length > 1) {
            this.filterModel[field] = {
               filterType: 'number',
               operator: 'AND',
               condition1: {
                  type: 'greaterThanOrEqual',
                  filter: filterRange[0]
               },
               condition2: {
                  type: 'lessThanOrEqual',
                  filter: filterRange[1]
               }
            }
         } else {
            this.filterModel[field] = {
               filterType: 'number',
               type: 'greaterThanOrEqual',
               filter: filterRange[0]
            }
         }
      } else {
         // Set filter
         this.filterModel[field] = {
            filterType: 'set',
            values: setFilterValue ? [setFilterValue] : ['true']
         }
      }

      this.gridApi.setFilterModel(this.filterModel);

      // Update pinned bottom row after applying filters
      if (this.gridRef && this.gridRef.updatePinnedBottomRow) {
         this.gridRef.updatePinnedBottomRow();
      }
   }

   clearFilter(fields: string[]) {
      // Clear filter flags and model in a single loop
      fields.forEach(field => {
         this.filters[field] = false;
         delete this.filterModel[field];
         this.pageRef.clearBarSelection(field as ChartKey);
      });

      this.gridApi.setFilterModel(this.filterModel);

      // Update pinned bottom row after clearing filters
      if (this.gridRef?.updatePinnedBottomRow) {
         this.gridRef.updatePinnedBottomRow();
      }
   }

   clearAllFilters() {
      // Reset all filters and clear chart selections
      this.initialiseFilters();
      this.filterModel = {};
      this.previouslySelectedBars = {};

      // Clear all visual selections (this also updates the grid)
      this.pageRef.clearAllBarSelections();

      // Update pinned bottom row after clearing all filters
      if (this.gridRef?.updatePinnedBottomRow) {
         this.gridRef.updatePinnedBottomRow();
      }
   }

   goToStockReports() {
      // Convert bar selections to proper field filters for the main grid
      const filterModelForStockReports = this.convertBarSelectionsToFieldFilters();

      // Add filter to only show published ads
      filterModelForStockReports['AutotraderAdvertStatus'] = {
         filterType: 'set',
         values: ['PUBLISHED']
      };

      this.advertListingDetailService.setExternalFilterModel(filterModelForStockReports);
      let menuItem: MenuItemNew | undefined = this.constantsService.getMenuItemFromUrl('/advertListingDetail');
      if (menuItem) {
         this.constantsService.navigateByUrl(menuItem);
      } //, 'operationreports'
   }

   private convertBarSelectionsToFieldFilters(): any {
      const filterModel: any = {};

      if (!this.previouslySelectedBars) {
         return filterModel;
      }

      for (const chartKey in this.previouslySelectedBars) {
         if (!this.previouslySelectedBars.hasOwnProperty(chartKey)) continue;

         const selectedBars = this.previouslySelectedBars[chartKey];
         if (!selectedBars || selectedBars.length === 0) continue;

         const filterConfig = CHART_TO_FILTER_MAP[chartKey];
         if (!filterConfig) continue;

         const categories = CHART_FILTER_CATEGORIES[filterConfig.fieldName];
         if (!categories) continue;

         const filter = filterConfig.filterType === 'range'
            ? this.createRangeFilter(selectedBars, categories)
            : this.createSetFilter(selectedBars, categories);

         if (filter) {
            filterModel[filterConfig.fieldName] = filter;
         }
      }

      return filterModel;
   }

   private createRangeFilter(selectedBars: number[], categories: any[]): any {
      const ranges = selectedBars
         .map(barIndex => categories[barIndex]?.range)
         .filter(r => r)
         .map(r => ({ min: r[0], max: r[1] === Infinity ? Number.MAX_SAFE_INTEGER : r[1] }));

      if (ranges.length === 0) return null;

      return {
         filterType: 'number',
         operator: 'OR',
         conditions: ranges.map(range => {
            const isUnbounded = range.max === Number.MAX_SAFE_INTEGER;
            return {
               filterType: 'number',
               type: isUnbounded ? 'greaterThanOrEqual' : 'inRange',
               filter: isUnbounded ? range.min : (range.min - 1), // Adjust for inclusive filter
               filterTo: isUnbounded ? undefined : (range.max + 1) // Adjust for inclusive filter
            };
         })
      };
   }

   private createSetFilter(selectedBars: number[], categories: any[]): any {
      const values = selectedBars
         .map(barIndex => {
            const category = categories[barIndex];
            if (!category) return null;
            // Use displayValue for filters sent to advertListingDetail, otherwise use value
            return category.displayValue || category.value || category.label;
         })
         .filter(v => v);

      if (values.length === 0) return null;

      return {
         filterType: 'set',
         values: values
      };
   }
}
