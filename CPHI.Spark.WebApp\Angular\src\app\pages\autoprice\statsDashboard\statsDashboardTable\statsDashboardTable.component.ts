import { Component, Input, OnInit, ChangeDetectorRef } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { GridApi, GridOptions, GridReadyEvent, ICellRendererParams, ModelUpdatedEvent, RowClassParams, RowClickedEvent } from 'ag-grid-community';
import { BuildTotalAndAverageRowsParams } from 'src/app/model/BuildTotalAndAverageRowsParams';
import { CphPipe } from 'src/app/cph.pipe';
import { SameModelAdvert } from 'src/app/model/SameModelAdvert';
import { SimpleExampleItem } from 'src/app/model/SimpleExampleItem';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { localeEs } from 'src/environments/locale.es.js';
import { StatsVehicle } from 'src/app/model/StatsVehicle';
import { StatsDashboardPageComponentType } from '../statsDashboard.component';
import { StatsDashboardService } from '../statsDashboard.service';
import { AutopriceRendererService } from 'src/app/services/autopriceRenderer.service';
import { AutoPriceInsightsModalService } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.service';
import { AutoPriceInsightsModalComponent } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component';
import { AutotraderImageCellComponent } from 'src/app/components/autotraderImageCell/autotraderImageCell.component';

//This describes the properties that a service must commit to having, when it's used with this component.
//In any service that wants to use this compnent, it must implement this interface using this syntax:
//   export class MyCoolService implements MyComponentParams  { etc.
//now, from this component we can call params. each of these properties and rely that there are present on the service.
// so the component is type safe but is not tightly coupled to any particular service
export interface StatsDashboardTableParams {
  gridRef: StatsDashboardTableComponent;
  vehicles: StatsVehicle[];
  dealWithFilteredItems:  (filteredItems: StatsVehicle[], callingComponent:StatsDashboardPageComponentType ) => void;

}


@Component({
  selector: 'statsDashboardTable',
  templateUrl: './statsDashboardTable.component.html',
  styleUrls: ['./statsDashboardTable.component.scss']
})
export class StatsDashboardTableComponent implements OnInit {

  @Input() tableParams: StatsDashboardTableParams;
  gridOptions: GridOptions;
  indicateNewData: boolean;
  gridApi: GridApi;

  constructor(
    public gridHelpersService: AGGridMethodsService,
    public constantsService: ConstantsService,
    public cphPipe: CphPipe,
    private modalService: NgbModal,
    private columnTypesService: ColumnTypesService,
    public service: StatsDashboardService,
    public autopriceRendererService: AutopriceRendererService,
    public autoPriceInsightsModalService: AutoPriceInsightsModalService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.setGridDefinitions();
  }

  ngOnDestroy() {
    this.service.gridRef = null;
  }

  setGridDefinitions() {
    this.gridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params: any) => this.constantsService.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      defaultColDef: {
        resizable: true,
        sortable: true,
        filterParams: {
          applyButton: false,
          clearButton: true,
          cellHeight: this.gridHelpersService.getFilterListItemHeight()
        },
        autoHeight: true,
        floatingFilter: true,
        // cellStyle: {'white-space': 'initial'}
      },
      // getRowClass: (params) => {
      //   return this.rowClassGetter(params)
      // } ,
      getRowHeight: (params) => {
        return this.gridHelpersService.getHeaderHeight();
      },
      headerHeight: this.gridHelpersService.getRowHeight(40),
      columnTypes: {
        ...this.columnTypesService.provideColTypes([]),
      },
      onRowDoubleClicked: (params) => this.onRowClick(params),
      rowData: this.tableParams.vehicles,
      columnDefs: [
        { headerName: '', colId: 'VsStrategyBanding', valueGetter: (params) => this.formatStrategyBandLabel(params), valueFormatter: (params) => { return ''; }, type: 'labelSetFilter', cellClass: (params) => { return params.data.VsStrategyBanding }, width: 20 },
        { headerName: '',
          colId: 'ImageURL',
          field: 'ImageURL',
          type: 'specialNoFilter',
          cellRendererFramework: AutotraderImageCellComponent,
          cellRendererParams: { width: 70 },
          width: 30
        },
        { headerName: 'VRM', colId: 'VehicleReg', field: 'VehicleReg', type: 'special',
          cellRenderer: (params) => this.autopriceRendererService.regPlateRenderer(params, { fontSize: 1.1, noMargin: true }), width: 50,minWidth:50 },
        { headerName: 'Description', colId: 'Description', field: 'Description', type: 'labelSetFilter', width: 100, cellClass: 'wrapText' },
        { headerName: 'Site', colId: 'RetailerName', field: 'RetailerName', type: 'labelSetFilter', width: 40,cellClass: 'wrapText'  },
        { headerName: 'Days Listed', colId: 'DaysListed', field: 'DaysListed', type: 'number', width: 40 },
        { headerName: 'Retail Rating', colId: 'RetailRating', field: 'RetailRating', type: 'number',
          cellRenderer: (params) => this.autopriceRendererService.autoTraderRetailRatingRenderer(this.getRetailRatingRendererParams(params)), width: 50,minWidth:50 },
        { headerName: 'Perf. Rating', colId: 'PerformanceRating', field: 'PerformanceRating', type: 'number',
          cellRenderer: (params) => this.autopriceRendererService.autoTraderPerformanceRatingSimpleRenderer(params), width: 40 },
        { headerName: 'Adv. Price', colId: 'AdvertisedPrice', field: 'AdvertisedPrice', type: 'currency', width: 40 },
        { headerName: 'PP%', colId: 'PPPercent', field: 'PPPercent', type: 'percent1dp', width: 40 },
        { headerName: 'vs Strategy', colId: 'VsStrategyPrice', field: 'VsStrategyPrice', type: 'currency', width: 40 },

        // Hidden just for filtering
        { hide: true, colId: 'AdId', field: 'AdId', type: 'labelSetFilter' },
        { hide: true, colId: 'NoImages', field: 'NoImages', type: 'labelSetFilter' },
        { hide: true, colId: 'NoVideo', field: 'NoVideo', type: 'labelSetFilter' },
        { hide: true, colId: 'IsLowQuality', field: 'IsLowQuality', type: 'labelSetFilter' },
        { hide: true, colId: 'LessThan9Images', field: 'LessThan9Images', type: 'labelSetFilter' },
        { hide: true, colId: 'NoAttentionGrabber', field: 'NoAttentionGrabber', type: 'labelSetFilter' },
        { hide: true, colId: 'VsStrategyBandingFilter', field: 'VsStrategyBanding', type: 'labelSetFilter' }

      ],
      onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
      onFilterChanged: (event) => this.onFilterChanged(event),
      onModelUpdated: (event: ModelUpdatedEvent) => this.service.gridApi?.sizeColumnsToFit(),
      pinnedBottomRowData: this.providePinnedBottomRowData()
    }
  }
  rowClassGetter(params: RowClassParams<any, any>):string[] {
    const item: SimpleExampleItem = params.data;
    return item.isChosen ?  ['brightHighlight'] : []
  }

  getImage(params: ICellRendererParams) {
    const row: SameModelAdvert = params.data;

    if (!row || !row?.ImageUrl) return '';
    return `<img style="height: 50px; width: 100%;" src=${row.ImageUrl} />`;
  }

  onGridReady(event: GridReadyEvent) {
    this.gridApi = event.api;
    this.service.gridApi = event.api;
    this.service.gridApi.sizeColumnsToFit();
    this.service.gridRef = this;
    if (this.service.filterModel) {
      this.service.gridApi.setFilterModel(this.service.filterModel);
      // Update pinned bottom row after setting filter model
      this.cdr.detectChanges();
      this.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData());
    }
  }

  dealWithNewData(data: StatsVehicle[]) {
    this.service.gridApi.setRowData(data);
    this.service.gridApi.sizeColumnsToFit();
    this.indicateNewData = true;
    setTimeout(() => { this.indicateNewData = false; }, 1000);

    // Update pinned bottom row data when data changes
    if (this.gridApi) {
      this.cdr.detectChanges();
      this.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData());
    }
  }

  onFilterChanged(event: any) {
    // Get the filtered rows
    const filteredNodes = [];
    this.service.gridApi.forEachNodeAfterFilter(node => {
      filteredNodes.push(node);
    })
    const filteredItems = filteredNodes.map(node => node.data); // Get data from each node

    // Call the parent service method and pass the filtered items
    if (this.tableParams.dealWithFilteredItems) {
      this.tableParams.dealWithFilteredItems(filteredItems, StatsDashboardPageComponentType.grid);
    }

    // Update pinned bottom row when filter changes
    if (this.gridApi) {
      this.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData());
    }
  }

  onRowClick(params: RowClickedEvent<any, any>): void {
    // const item: SimpleExampleItem = params.data;
    // item.isChosen = !item.isChosen;
    // params.node.setData(item);
    // this.tableParams.dealWithFilteredItems(this.tableParams.vehicles, StatsDashboardPageComponentType.grid);

    this.autoPriceInsightsModalService.initialise(params.data.AdId, null)

    const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, { keyboard: true, size: 'lg' });
    modalRef.result.then((result) => { });
  }

  getRetailRatingRendererParams(params: any) {
    return {
      params: {
        RetailRating: params.data.RetailRating,
        InTableCell: true
      }
    }
  }

  formatStrategyBandLabel(params) {
    if (!params.data) { return '' }
    if (params.data.VsStrategyBanding === 'OnStrategyPrice') return 'On Strategy';
    if (params.data.VsStrategyBanding === 'UnderPriced') return 'Underpriced';
    if (params.data.VsStrategyBanding === 'VeryUnderPriced') return 'V. Underpriced';
    if (params.data.VsStrategyBanding === 'OverPriced') return 'Overpriced';
    if (params.data.VsStrategyBanding === 'VeryOverPriced') return 'V. Overpriced';
    if (params.data.VsStrategyBanding === 'NoValuation') return 'No Strat Price';
    if (params.data.VsStrategyBanding === 'NoAdvertisedPrice') return 'No Price';
    return params.data.VsStrategyBanding;
  }

  providePinnedBottomRowData(): any[] {
    const params: BuildTotalAndAverageRowsParams = {
      colsToSkipAverageIfZero: [],
      colsToTotalOrAverage: ['DaysListed', 'RetailRating', 'PerformanceRating', 'AdvertisedPrice', 'PPPercent', 'VsStrategyPrice'],
      colsToTotal: ['AdvertisedPrice', 'VsStrategyPrice'],
      selectedCountFieldName: 'VehicleReg',
      labelFieldName: 'Description',
      colsToSetToTrue: [],
      itemName: 'vehicle',
      api: this.gridApi,
      includeTotalRow: false,
      showTotalInAverage: false
    };

    return this.gridHelpersService.buildTotalAndAverageRows(params);
  }

  updatePinnedBottomRow() {
    if (this.gridApi) {
      // Ensure change detection runs before updating pinned row
      this.cdr.detectChanges();
      this.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData());
    }
  }
}
