import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SalesRole, SiteVM } from "../../model/main.model";
import { ConstantsService } from "../../services/constants.service";
import { SelectionsService } from "../../services/selections.service";
//import { ConstantsService } from './services/constants.service'

interface personRow {
   name: string;
   id: number;
   isChanges: boolean;
   months: {
      label: string;
      salesRole: SalesRole;
   }[];
}

@Component({
   selector: "salesmanEfficiencyPeopleEditor",
   template: `
      <ng-template id="peopleEditor" #modal let-modal>
         <div class="modal-header">
            <h4 class="modal-title" id="modal-basic-title">Edit Salesmen Roles and Sites</h4>
            <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
               <span aria-hidden="true">&times;</span>
            </button>
         </div>

         <div [ngClass]="constants.environment.customer" class="modal-body ">
            <!--  Main body content -->
            <table class="cph fullWidth">
               <thead>
                  <tr>
                     <th></th>

                     <th *ngFor="let m of monthNames">{{ m }}</th>

                     <th></th>
                  </tr>
               </thead>
               <tbody>
                  <tr [ngClass]="{ bright: person.isChanges }" *ngFor="let person of personTable">
                     <td>{{ person.name }}</td>

                     <ng-container *ngFor="let m of person.months; let i = index">
                        <td>
                           <div *ngIf="m.salesRole" class="spaceBetween column">
                              <!-- site dropdown -->
                              <div ngbDropdown container="body" class="d-inline-block bringToFont">
                                 <button
                                    [ngClass]="{ faded: m.salesRole.Role == 'None' }"
                                    class=" btn btn-primary"
                                    ngbDropdownToggle
                                 >
                                    {{ provideSiteName(m.salesRole.SiteId) }}
                                 </button>

                                 <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                                    <!-- ngFor buttons -->
                                    <button
                                       *ngFor="let site of constants.Sites"
                                       (click)="chooseNewSites(site, person.months, i)"
                                       [ngClass]="{ active: site.SiteId == m.salesRole.SiteId }"
                                       ngbDropdownItem
                                    >
                                       {{ site.SiteDescShort }}
                                    </button>
                                 </div>
                              </div>

                              <!-- role dropdown -->
                              <div ngbDropdown container="body" class="d-inline-block bringToFont">
                                 <button
                                    [ngClass]="{ faded: m.salesRole.Role == 'None' }"
                                    class=" btn btn-primary"
                                    ngbDropdownToggle
                                 >
                                    {{ m.salesRole.Role }}
                                 </button>

                                 <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                                    <!-- ngFor buttons -->
                                    <!-- <button *ngFor="let salesRole of constants.salesRoleRoles" (click)="chooseNewRoles(salesRole,person.months,i)"
                          [ngClass]="{'active':salesRole == m.salesRole.Role}" ngbDropdownItem>
                          {{salesRole}}
                        </button> -->
                                 </div>
                              </div>
                           </div>
                        </td>
                     </ng-container>
                     <td>
                        <ng-container *ngIf="person.isChanges">
                           <div (click)="cancelChanges(person)"><i class="fas fa-undo"></i></div>
                           <!-- <div (click)="saveChanges(person)"> <i   class="fas fa-save"></i> </div> -->
                        </ng-container>
                     </td>
                  </tr>
               </tbody>
            </table>
         </div>

         <div class="modal-footer">
            <!-- <button [disabled]="!constants.isInitialLoadComplete" type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">
        Close
      </button> -->
         </div>
      </ng-template>
   `,
   styles: [
      `
         table {
            table-layout: fixed;
         }
         table td:first-of-type,
         table th:first-of-type {
            width: 12em;
         }
         table th {
            text-align: center !important;
         }
         table td {
            line-height: 2em;
         }
         tr.bright td {
            background: var(--brightColourLightest);
         }
         table button.dropdown-toggle {
            width: 7em;
            min-width: 7em;
            max-width: 7em;
            padding: 0px;
         }
         table button.dropdown-item {
            text-align: center;
            height: 2.8em;
         }
         button.faded {
            opacity: 0.5;
         }
         svg {
            margin: 0em 0.3em;
            cursor: pointer;
         }
      `,
   ],
})
export class SalesmanEfficiencyPeopleEditorComponent implements OnInit {
   @Input() franchisesFromParent: string[];
   @Output() changed = new EventEmitter<boolean>();

   @ViewChild("modal", { static: true }) modal: ElementRef;

   personTable: personRow[];
   monthNames: string[];

   constructor(
      public constants: ConstantsService,
      public modalService: NgbModal,
      public selections: SelectionsService,

      public dataMethods: GetDataMethodsService
   ) {}

   ngOnInit(): void {
      this.initParams();
   }

   initParams() {
      //build a people object
      // this.personTable = [];
      // let peopleForSite = this.constants.People.filter(x => x.CurrentSite.Id == this.selections.salesmanEfficiency.site.Id && x.IsSalesExec)
      // peopleForSite = peopleForSite.sort((a, b) => (a.Name > b.Name) ? 1 : -1);

      // let year = this.selections.salesmanEfficiency.months.find(x => x.name == 'Year To Date').startDate.getFullYear()

      // peopleForSite.forEach(person => {
      //   let months: { label: string, salesRole: SalesRole }[] = [];
      //   this.monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      //   [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].forEach(month => {
      //     //find salesRole for this month
      //     let salesRole = person.SalesRoles.find(x => x.Year == year && x.Month == month)

      //     //create default salesRole for this month
      //     let defaultSalesRole: SalesRole = {
      //       Id: null,
      //       Year: year,
      //       Month: month,
      //       Role: 'None',
      //       SiteId: this.selections.salesmanEfficiency.site.Id
      //     }

      //     months.push({
      //       label: this.monthNames[month - 1],
      //       salesRole: salesRole || defaultSalesRole
      //     })
      //   })

      //   this.personTable.push({
      //     name: person.Name,
      //     id: person.Id,
      //     isChanges: false,
      //     months: months
      //   })
      // })

      this.selections.triggerSpinner.next({ show: false });
      this.modalService
         .open(this.modal, {
            size: "lg",
            windowClass: "reallyWideModal peopleEditorModal",
            backdropClass: "peopleEditorBackdrop",
            keyboard: false,
            //backdrop: "static",
            ariaLabelledBy: "modal-basic-title",
         })
         .result.then(
            (result) => {
               //'okd'
               // this.passBack()
               this.modalService.dismissAll();
            },
            //closed
            (reason) => {
               //cancelled, so no passback
               this.modalService.dismissAll();
               // this.selections.salesmanEfficiency.showPeopleEditor = false;
            }
         );
   }

   cancelChanges(person: personRow) {
      person.months.forEach((m) => {
         if (m.salesRole.roleHasChanged) {
            m.salesRole.Role = m.salesRole.oldRole;
            m.salesRole.newRole = null;
            m.salesRole.oldRole = null;
            m.salesRole.roleHasChanged = false;
         }
         if (m.salesRole.siteIdHasChanged) {
            m.salesRole.SiteId = m.salesRole.oldSiteId;
            m.salesRole.newSiteId = null;
            m.salesRole.oldSiteId = null;
            m.salesRole.siteIdHasChanged = false;
         }
      });
      person.isChanges = false;
   }

   chooseNewRoles(newRole: string, months: { label: string; salesRole: SalesRole }[], index: number) {
      for (let i = index; i < months.length; i++) {
         this.chooseNewRole(months[i].salesRole, newRole);
      }

      this.checkForChangedPeople();
   }

   chooseNewRole(salesRole: SalesRole, newRole: string) {
      if (salesRole.roleHasChanged) {
         //already changed once, so don't update old role
         salesRole.newRole = newRole; //set new role
         salesRole.Role = newRole; //set existing
      } else {
         salesRole.oldRole = salesRole.Role;
         salesRole.newRole = newRole;
         salesRole.Role = newRole;
      }

      salesRole.roleHasChanged = true;
   }

   chooseNewSites(newSite: SiteVM, months: { label: string; salesRole: SalesRole }[], index: number) {
      for (let i = index; i < months.length; i++) {
         this.chooseNewSite(months[i].salesRole, newSite);
      }

      this.checkForChangedPeople();
   }

   chooseNewSite(salesRole: SalesRole, newSite: SiteVM) {
      if (salesRole.siteIdHasChanged) {
         //already changed once, so don't update old role
         salesRole.newSiteId = newSite.SiteId; //set new role
         salesRole.SiteId = newSite.SiteId; //set existing
      } else {
         salesRole.oldSiteId = salesRole.SiteId;
         salesRole.newSiteId = newSite.SiteId;
         salesRole.SiteId = newSite.SiteId;
      }

      salesRole.siteIdHasChanged = true;
   }

   checkForChangedPeople() {
      this.personTable.forEach((person) => {
         person.months.forEach((month) => {
            if (month.salesRole && (month.salesRole.siteIdHasChanged || month.salesRole.roleHasChanged))
               person.isChanges = true;
         });
      });
   }

   provideSiteName(id: number): string {
      let site = this.constants.Sites.find((x) => x.SiteId == id);
      return site.SiteDescShort;
   }
}
