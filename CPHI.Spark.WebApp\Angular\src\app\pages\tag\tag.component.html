<nav class="navbar">
   <nav class="generic">
      <h4 id="pageTitle">
         <div>
            Tag Settings
         </div>
      </h4>
   </nav>

   <nav class="pageSpecific">
      <button id="addNewTagBtn" class="btn btn-primary" (click)="addTag()">
         <i class="fas fa-plus"></i> Add New Tag
      </button>
   </nav>
</nav>

<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
   <div class="content-new">
      <div class="content-inner-new" #contentInner>

         <!-- the main grid -->
         <div id="gridHolder" id="tagMaintenance">
            <div id="tableContainer" #tableContainer>

               <div id="excelExport" (click)="excelExport()">
                  <img [src]="constants.provideExcelLogo()">
               </div>

               <ng-container *ngIf="showGrid">
                  <ag-grid-angular
                     id="tagsGrid"
                     class="ag-theme-balham"
                     [rowData]="rowData"
                     [gridOptions]="gridOptions"
                     (gridReady)="onGridReady($event)"
                     [components]="components">
                  </ag-grid-angular>
               </ng-container>

            </div>
         </div>

      </div>
   </div>
</div>
