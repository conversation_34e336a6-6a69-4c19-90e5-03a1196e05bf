import {<PERSON>mpo<PERSON>, ElementRef, Host<PERSON><PERSON>ener, On<PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {
   ColDef,
   ColumnApi,
   GetContextMenuItemsParams,
   GridApi,
   GridOptions,
   ICellRendererParams,
   MenuItemDef
} from 'ag-grid-community';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {CustomHeaderNew} from 'src/app/components/customHeader/customHeader.component';
import {CustomHeaderService} from 'src/app/components/customHeader/customHeader.service';
import {CphPipe} from 'src/app/cph.pipe';
import {CreateTagDTO, TagDTO} from 'src/app/model/Tag';
import {AGGridMethodsService} from 'src/app/services/agGridMethods.service';
import {ColumnTypesService} from 'src/app/services/columnTypes.service';
import {ExcelExportService} from 'src/app/services/excelExportService';
import {TagService} from 'src/app/services/tag.service';
import {ConstantsService} from 'src/app/services/constants.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {localeEs} from '../../../environments/locale.es';
import {TagModalComponent} from './tagModal/tagModal.component';
import {CPHAutoPriceColDef, GridCellRendererEnum} from "../../model/CPHColDef";

@Component({
   selector: 'app-tag',
   templateUrl: './tag.component.html',
   styleUrls: ['./tag.component.scss']
})
export class TagComponent implements OnInit, OnDestroy {

   @ViewChild('tableContainer', {static: true}) tableContainer: ElementRef;

   @HostListener('window:resize', [])
   private onResize(event) {
      this.selections.screenWidth = window.innerWidth;
      this.selections.screenHeight = window.innerHeight;
      if (this.gridApi) {
         this.gridApi.resetRowHeights();
         this.resizeGrid();
      }
   }

   gridOptions: GridOptions;
   gridApi: GridApi;
   gridColumnApi: ColumnApi;
   showGrid: boolean = false;
   rowData: TagDTO[] = [];

   public components: {
      [p: string]: any;
   } = {
      agColumnHeader: CustomHeaderNew,
   };

   constructor(
      public constants: ConstantsService,
      public modalService: NgbModal,
      public columnTypeService: ColumnTypesService,
      public selections: SelectionsService,
      public cphPipe: CphPipe,
      public excel: ExcelExportService,
      public gridHelpers: AGGridMethodsService,
      public customHeader: CustomHeaderService,
      public tagService: TagService
   ) {
   }

   ngOnInit() {
      this.selections.triggerSpinner.next({show: true, message: this.constants.translatedText.Common_Loading});
      this.loadData().then();
   }

   ngOnDestroy() {
   }

   async loadData() {
      try {
         this.tagService.search().then((tags) => {
            this.constants.tags = [...tags];
            this.rowData = [...tags];
            this.tagService.clearTagCache();
            this.initiateAndRefresh();
            console.log("ROWDATA ", this.rowData);
            this.selections.triggerSpinner.next({show: false});
         });
      } catch (error) {
         this.constants.toastDanger('Failed to load tags');
         this.selections.triggerSpinner.next({show: false});
      }
   }

   initGridParams() {
      let gridScaleValue = this.tableContainer.nativeElement.clientWidth / 1300;
      this.gridOptions = {
         getContextMenuItems: (params) => this.getContextMenuItems(params),
         getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
         getLocaleText: (params) => this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
         suppressColumnMoveAnimation: true,
         getRowHeight: (params) => 50,
         getRowId: (params) => params.data.Id ? params.data.Id.toString() : null,
         headerHeight: 50,
         columnTypes: {
            ...this.columnTypeService.provideColTypes([]),
         },
         statusBar: {
            statusPanels: [
               {statusPanel: 'agAggregationComponent', align: 'right'}
            ]
         },
         context: {thisComponent: this},
         defaultColDef: {
            resizable: true,
            sortable: true,
            hide: false,
            floatingFilter: true,
            filterParams: {
               applyButton: false,
               clearButton: true,
               applyMiniFilterWhileTyping: true,
               cellHeight: this.gridHelpers.getFilterListItemHeight()
            },
            cellClass: 'agAlignCentreVertically',
            headerComponentParams: {showPinAndRemoveOptions: false},
            autoHeaderHeight: true
         },
         enableRangeSelection: true,
         suppressAggFuncInHeader: true,
         onColumnGroupOpened: () => this.sizeGrid(),
         rowClassRules: {
            'inactive-row': (params) => !params.data.IsActive
         },
         onCellDoubleClicked: (params) => {
            this.editTag(params.data);
         },
         columnDefs: this.getColumnDefs(gridScaleValue),
         suppressClickEdit: true,
      };
   }

   getColumnDefs(gridScaleValue: number): CPHAutoPriceColDef[] {
      return [
         {
            headerName: 'ID',
            field: 'Id',
            colId: 'id',
            width: 30 * gridScaleValue,
            type: 'number',
         },
         {
            headerName: 'Colour',
            field: 'Colour',
            colId: 'colour',
            width: 35 * gridScaleValue,
            cellRenderer: this.colourCellRenderer
         },
         {
            headerName: 'Label',
            field: 'Label',
            colId: 'label',
            width: 200 * gridScaleValue,
            type: 'label',
            cellRenderer: (params) => this.tagCellRenderer(params)
         },
         {
            headerName: 'Strategy Impact %',
            field: 'StrategyImpactPct',
            colId: 'strategyImpactPct',
            width: 150 * gridScaleValue,
            type: 'number',
            valueFormatter: (params) => params.value ? `${params.value}%` : ''
         },
         {
            headerName: 'Strategy Impact Amount',
            field: 'StrategyImpactAmount',
            colId: 'strategyImpactAmount',
            width: 180 * gridScaleValue,
            type: 'currency'
         },
         {
            headerName: 'Active',
            field: 'IsActive',
            colId: 'isActive',
            width: 100 * gridScaleValue,
            cellRenderer: this.activeCellRenderer
         },
         {
            headerName: 'Created',
            field: 'CreatedDate',
            colId: 'CreatedDate',
            width: 100 * gridScaleValue,
            type: 'dateLongYear'
         },
         {
            headerName: 'Created By',
            field: 'CreatedBy.Name',
            colId: 'CreatedBy.Name',
            width: 100 * gridScaleValue,
         },
         {
            headerName: 'Updated',
            field: 'UpdatedDate',
            colId: 'UpdatedDate',
            width: 100 * gridScaleValue,
            type: 'dateLongYear'
         },
         {
            headerName: 'Updated By',
            field: 'UpdatedBy.Name',
            colId: 'UpdatedBy.Name',
            width: 100 * gridScaleValue,
         },
      ];
   }

   tagCellRenderer(params: ICellRendererParams): HTMLElement {
      const tag = params.data as TagDTO;

      // Create a wrapper with custom styling
      const wrapper = document.createElement('div');
      wrapper.className = 'tag-grid-cell';
      wrapper.innerHTML = this.tagService.renderSingleTag(tag.Id);

      // Apply inline styles to override padding specifically for this grid
      const tagElement = wrapper.querySelector('.tag') as HTMLElement;
      if (tagElement) {
         tagElement.style.padding = '3px 6px'; // Reduce padding (adjust as needed)
         tagElement.style.fontSize = '12px';    // Optionally adjust font size
      }

      return wrapper;
   }

   colourCellRenderer(params: ICellRendererParams): HTMLElement {
      const colourDiv = document.createElement('div');
      colourDiv.style.display = 'flex';
      colourDiv.style.alignItems = 'center';
      colourDiv.style.gap = '8px';

      const colourBox = document.createElement('div');
      colourBox.style.width = '20px';
      colourBox.style.height = '20px';
      colourBox.style.backgroundColor = params.value || '#cccccc';
      colourBox.style.border = '1px solid #ccc';
      colourBox.style.borderRadius = '3px';

      const colourText = document.createElement('span');
      colourText.textContent = params.value || '';

      colourDiv.appendChild(colourBox);

      return colourDiv;
   }

   activeCellRenderer(params: ICellRendererParams): HTMLElement {
      const statusDiv = document.createElement('div');
      statusDiv.style.display = 'flex';
      statusDiv.style.alignItems = 'center';
      statusDiv.style.justifyContent = 'center';

      const badge = document.createElement('span');
      badge.className = params.value ? 'badge badge-success' : 'badge badge-secondary';
      badge.textContent = params.value ? 'Active' : 'Inactive';

      statusDiv.appendChild(badge);
      return statusDiv;
   }

   getContextMenuItems(params: GetContextMenuItemsParams<any, any>): (string | MenuItemDef)[] {
      let normal = this.gridHelpers.getContextMenuItems(params);
      let row: TagDTO = params.node.data;

      normal.push(
         'separator',
         {
            name: 'Edit Tag',
            action: () => {
               this.editTag(row);
            }
         }
      );

      if (row.IsActive) {
         normal.push({
            name: 'Deactivate Tag',
            cssClasses: ['redFont'],
            action: () => {
               this.softDeleteTag(row);
            }
         });
      } else {
         normal.push({
            name: 'Restore Tag',
            cssClasses: ['greenFont'],
            action: () => {
               this.restoreTag(row);
            }
         });
      }

      return normal;
   }

   initiateAndRefresh() {
      setTimeout(() => {
         if (!this.gridOptions) {
            this.initGridParams();
         } else {
            this.refreshGrid();
         }
         this.showGrid = true;
      }, 350);
   }

   refreshGrid() {
      if (this.gridApi) {
         this.gridApi.setRowData(this.rowData);
         this.gridApi.refreshCells({ force: true });
      }
   }

   sizeGrid() {
      this.resizeGrid();
   }

   resizeGrid() {
      if (this.gridApi) {
         this.gridApi.sizeColumnsToFit();
      }
   }

   onGridReady(params) {
      this.gridApi = params.api;
      this.gridColumnApi = params.columnApi;
      this.resizeGrid();
      this.selections.triggerSpinner.next({show: false});
   }

   excelExport(): void {
      let tableModel = this.gridApi.getModel();
      this.excel.createSheetObject(tableModel, 'Tags', 1, 1);
   }

   addTag() {
      const newTag: CreateTagDTO = {
         Label: '',
         IsActive: true,
         StrategyImpactPct: 0,
         StrategyImpactAmount: 0,
         Colour: '#007bff'
      };
      this.openTagModal(newTag, true);
   }

   editTag(tag: TagDTO) {
      this.openTagModal(tag, false);
   }

   private openTagModal(tag: TagDTO | CreateTagDTO, isNew: boolean) {

      const modalRef = this.modalService.open(TagModalComponent, {size: 'sm'});
      modalRef.componentInstance.initialiseModal(tag, isNew);

      modalRef.result.then(res => {
         if (res) {
            this.loadData();
         }
      }, () => {
         // Modal dismissed
      });
   }

   async softDeleteTag(tag: TagDTO) {

      this.tagService.softDelete(tag.Id).then(() => {
         this.constants.toastSuccess('Tag deactivated successfully');
         this.loadData();
      })
   }

   async restoreTag(tag: TagDTO) {
      this.tagService.restore(tag.Id).then(() => {
         this.constants.toastSuccess('Tag restored successfully');
         this.loadData();
      })
   }
}
