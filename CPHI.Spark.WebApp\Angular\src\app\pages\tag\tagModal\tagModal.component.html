<div class="modal-header">
   <h4 class="modal-title">
      {{ isNew ? 'Create New Tag' : 'Edit Tag' }}
   </h4>
   <button type="button" class="close" aria-label="Close" (click)="cancel()">
      <span aria-hidden="true">&times;</span>
   </button>
</div>

<div class="modal-body" [ngClass]="constants.environment.customer">
   <form [formGroup]="tagForm" novalidate>

      <!-- Label Field -->
      <div class="form-group">
         <div class="mb-1">Label</div>
         <input
            [readonly]="!canEditTags"
            type="text"
            id="label"
            class="form-control"
            style="max-width: 40em"
            formControlName="label"
            [class.is-invalid]="isFieldInvalid('label')"
            placeholder="Enter tag label">
         <div class="invalid-feedback" *ngIf="isFieldInvalid('label')">
            {{ getFieldError('Label') }}
         </div>
      </div>


      <!-- Strategy Impact Percentage -->
      <div class="form-group">
         <div class="mb-1">Strategy Impact Percentage</div>
         <div class="d-flex align-items-center">
            <div class="me-2">
               <input
                  [readonly]="!canEditTags"
                  type="number"
                  id="strategyImpactPct"
                  class="form-control amount-input d-inline-block"
                  style="text-align: left;"
                  formControlName="strategyImpactPct"
                  [class.is-invalid]="isFieldInvalid('strategyImpactPct')"
                  min=-100
                  max=200
                  step="0.1"
                  placeholder="0.0">
            </div>

            <instructionButton
               [note]="'You can optionally specify a % impact that a label will have on the strategy price calculation for an advert.   Leaving this at 100% will mean it has no impact'">
            </instructionButton>

         </div>
         <div class="invalid-feedback" *ngIf="isFieldInvalid('strategyImpactPct')">
            {{ getFieldError('Strategy Impact Percentage') }}
         </div>
      </div>

      <!-- Strategy Impact Amount -->
      <div class="form-group">
         <div class="mb-1">Strategy Impact Amount</div>
         <div class="d-flex align-items-center">
            <div class="me-2">
               <input
                  [readonly]="!canEditTags"
                  type="number"
                  autoFocus="false"
                  id="strategyImpactAmount"
                  class="form-control amount-input"
                  formControlName="strategyImpactAmount"
                  [class.is-invalid]="isFieldInvalid('strategyImpactAmount')"
                  style="text-align: left;"
                  min="-1000"
                  max="1000"
                  step="1"
                  placeholder="0.00">
            </div>

            <instructionButton [note]="'You can optionally specify a £ impact that a label will have on the strategy price calculation for an advert.   Leaving this at £0 will mean it has no impact'">
            </instructionButton>

         </div>
         <div class="invalid-feedback" *ngIf="isFieldInvalid('strategyImpactAmount')">
            {{ getFieldError('Strategy Impact Amount') }}
         </div>
      </div>

      <!-- Color Selection -->
      <div class="form-group">
         <div class="mb-1">Colour</div>
         <div class="colour-selection">
            <div class="custom-colour">
               <input
                  [readonly]="!canEditTags"
                  type="color"
                  id="colour"
                  class="form-control colour-input"
                  formControlName="colour"
                  (change)="onColourInputChange($event)">
            </div>
            <div class="predefined-colours">
               <div
                  *ngFor="let colour of predefinedColours"
                  class="colour-option"
                  [style.background-color]="colour"
                  [class.selected]="formControls['colour'].value === colour"
                  (click)="selectColour(colour)">
               </div>
            </div>

         </div>
         <div class="invalid-feedback" *ngIf="isFieldInvalid('colour')">
            {{ getFieldError('Colour') }}
         </div>
      </div>

      <!-- Color Selection -->
      <div class="form-group">
         <div class="mb-1">Preview</div>
         <app-tag-display [tag]="tagPreview" [preview]="true" [canEditTags]="false"></app-tag-display>
      </div>


      <div class="form-group">
         <div class="mb-1">Active</div>
         <!-- Active Status -->
         <div class="d-flex mt-1">
            <div>
               <toggleSwitch
                  [disabled]="!canEditTags"
                  size="large"
                  formControlName="isActive"></toggleSwitch>
            </div>
            <div class="d-flex" style="align-items: center;">
               <div>
                  <div class="">
                     Inactive tags will not be available for selection but existing associations will remain.
                  </div>
               </div>
            </div>
         </div>
      </div>

   </form>
</div>

<div class="modal-footer">
   <button
      type="button"
      class="btn btn-success"
      (click)="save()"
      [disabled]="isLoading || tagForm.invalid || !canEditTags">
      <span *ngIf="isLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
      {{ isNew ? 'Create' : 'Update' }}
   </button>
   <button type="button" class="btn btn-secondary" (click)="cancel()" [disabled]="isLoading">
      Cancel
   </button>
</div>
