.modal-body {
   padding: 1.5rem;
}

.form-group {
   margin-bottom: 1.5rem;
}

.colour-selection {
   display: flex;
   gap: 3em;
}

.infoBlue {
   color: var(--infoBlue);
}

.amount-input {
   max-width: 10em;
   width: 10em;
}

.predefined-colours {
   display: grid;
   grid-template-rows: repeat(2, 1fr);
   grid-auto-flow: column;
   grid-auto-columns: 1fr;
   gap: 0.5em;
}

.colour-option {
   width: 20px;
   height: 20px;
   border-radius: 4px;
   border: 2px solid #dee2e6;
   cursor: pointer;
   transition: all 0.2s ease;

   &:hover {
      transform: scale(1.1);
      border-color: var(--secondaryLighter)
   }

   &.selected {
      border-color: var(--secondaryLighter);
      border-width: 3px;
      transform: scale(1.1);
   }
}

.custom-colour {
   display: flex;
   align-items: center;
   gap: 0.5rem;
}

.tag-info {
   color: var(--grey80);
   font-size: 1.3em;
   &:hover {
      color: var(--infoBlue);
   }
}


.colour-input {
   width: 6em;
   height: 4em;
   padding: 0;
   border: 1px solid #dee2e6;
   border-radius: 4px;
   cursor: pointer;

   &::-webkit-color-swatch-wrapper {
      padding: 0;
   }

   &::-webkit-color-swatch {
      border: none;
      border-radius: 3px;
   }
}

.colour-value {
   font-family: monospace;
   font-size: 0.9rem;
   color: #6c757d;
   background-color: #f8f9fa;
   padding: 0.25rem 0.5rem;
   border-radius: 3px;
   border: 1px solid #dee2e6;
}

.form-check {
   padding-left: 1.5rem;
}

.form-check-input {
   margin-left: -1.5rem;
}

.spinner-border-sm {
   width: 1rem;
   height: 1rem;
   margin-right: 0.5rem;
}

.invalid-feedback {
   display: block;
}

.is-invalid {
   border-color: var(--badColourLight);

   &:focus {
      border-color: var(--badColourLight);
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
   }
}

.input-group-text {
   background-color: #f8f9fa;
   border-color: #dee2e6;
   font-size: 1em;
}

.form-text {
   font-size: 0.875rem;
   margin-top: 0.25rem;
}

.modal-footer {
   border-top: 1px solid #dee2e6;
   padding: 1rem 1.5rem;

   .btn {
      margin-left: 0.5rem;

      &:first-child {
         margin-left: 0;
      }
   }
}
