import {Component, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import {CreateTagDTO, TagDTO} from 'src/app/model/Tag';
import {TagService} from 'src/app/services/tag.service';
import {ConstantsService} from 'src/app/services/constants.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {compare} from "fast-json-patch";
import {Subscription} from "rxjs";

@Component({
   selector: 'app-tag-modal',
   templateUrl: './tagModal.component.html',
   styleUrls: ['./tagModal.component.scss']
})
export class TagModalComponent implements OnInit {

   tagForm: FormGroup;
   tag: TagDTO | CreateTagDTO;
   isNew: boolean = false;
   isLoading: boolean = false;

   predefinedColours: string[] = [
      '#007bff', '#28a745', '#dc3545', '#ffc107', '#17a2b8',
      '#6f42c1', '#e83e8c', '#fd7e14', '#20c997', '#6c757d',
      '#343a40', '#f8f9fa', '#e9ecef', '#dee2e6', '#ced4da'
   ];
   private beforeEdit: TagDTO | CreateTagDTO;
   public canEditTags: boolean;
   tagPreview: TagDTO;
   private tagFormSubscription: Subscription;

   constructor(
      public activeModal: NgbActiveModal,
      private formBuilder: FormBuilder,
      private tagService: TagService,
      public constants: ConstantsService,
      public selections: SelectionsService
   ) {
   }

   ngOnInit() {
      this.buildForm();
      this.updateTagPreview();
      this.canEditTags = this.selections.user?.permissions?.canEditPricingStrategy;
   }

   initialiseModal(tag: TagDTO | CreateTagDTO, isNew: boolean) {
      this.tag = {...tag};
      this.isNew = isNew;
      this.buildForm();
      this.beforeEdit = {...this.tagForm.value};
   }

   buildForm() {
      this.tagForm = this.formBuilder.group({
         label: [this.tag?.Label || '', [Validators.required, Validators.maxLength(100)]],
         colour: [this.tag?.Colour || '#007bff', [Validators.required]],
         strategyImpactPct: [this.tag?.StrategyImpactPct || 100, [Validators.min(0), Validators.max(200)]],
         strategyImpactAmount: [this.tag?.StrategyImpactAmount || 0, [Validators.min(-10000), Validators.max(10000)]],
         isActive: [this.tag?.IsActive !== undefined ? this.tag.IsActive : true],
      });

      this.tagFormSubscription = this.tagForm.valueChanges.subscribe(() => {
         this.updateTagPreview();
      });
   }

   get formControls() {
      return this.tagForm.controls;
   }

   selectColour(colour: string) {

      if (!this.canEditTags) return;

      this.tagForm.patchValue({colour: colour});
   }

   onColourInputChange(event: any) {
      this.tagForm.patchValue({colour: event.target.value});
   }

   async save() {

      if (this.tagForm.invalid) {
         this.markFormGroupTouched();
         return;
      }

      this.isLoading = true;
      const formValue = this.tagForm.value;

      try {
         if (this.isNew) {

            const createTag: CreateTagDTO = {
               Id: 0,
               ...formValue
            };

            const result: TagDTO = await this.tagService.create(createTag);
            this.constants.toastSuccess('Tag created successfully');
            this.activeModal.close(result);
         } else {

            const patch = compare(this.beforeEdit, this.tagForm.value);

            if (patch.length == 0) {
               this.activeModal.close(false);
               return;
            }

            const result: TagDTO = await this.tagService.patch(this.tag.Id, patch);
            this.tagService.clearTagCache();
            this.constants.toastSuccess('Tag updated successfully');
            this.activeModal.close(result);
         }
      } catch (error) {
         console.error('Error saving tag:', error);
         this.constants.toastDanger(this.isNew ? 'Failed to create tag' : 'Failed to update tag');
         this.isLoading = false;
      }
   }

   cancel() {
      this.activeModal.dismiss(false);
   }

   private markFormGroupTouched() {
      Object.keys(this.tagForm.controls).forEach(key => {
         const control = this.tagForm.get(key);
         control?.markAsTouched();
      });
   }

   isFieldInvalid(fieldName: string): boolean {
      const field = this.tagForm.get(fieldName);
      return !!(field && field.invalid && (field.dirty || field.touched));
   }

   getFieldError(fieldName: string): string {
      const field = this.tagForm.get(fieldName);
      if (field && field.errors && (field.dirty || field.touched)) {
         if (field.errors['required']) {
            return `${fieldName} is required`;
         }
         if (field.errors['maxlength']) {
            return `${fieldName} must be less than ${field.errors['maxlength'].requiredLength} characters`;
         }
         if (field.errors['min']) {
            return `${fieldName} must be at least ${field.errors['min'].min}`;
         }
         if (field.errors['max']) {
            return `${fieldName} must be no more than ${field.errors['max'].max}`;
         }
      }
      return '';
   }

   private updateTagPreview() {

      this.tagPreview = {
         Label: this.tagForm.value.label,
         Id: this.tag.Id,
         StrategyImpactAmount: this.tagForm.value.strategyImpactAmount,
         StrategyImpactPct: this.tagForm.value.strategyImpactPct,
         Colour: this.tagForm.value.colour
      };
   }
}
