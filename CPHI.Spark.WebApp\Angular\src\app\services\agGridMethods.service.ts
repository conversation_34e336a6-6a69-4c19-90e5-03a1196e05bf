import {EventEmitter, Injectable} from '@angular/core';
import {SelectionsService} from './selections.service'
import {ConstantsService} from './constants.service';
import {CphPipe} from '../cph.pipe';
import {
   CellClassParams,
   ColDef,
   ColGroupDef,
   ColumnState,
   GetContextMenuItemsParams,
   GetMainMenuItemsParams,
   GridApi,
   IRowNode,
   MenuItemDef
} from 'ag-grid-community';
import {ColDefCph} from '../model/ColDefCPH';
import {TopBottomHighlightRule} from "../model/TopBottomHighlightRule";
import {GetDataMethodsService} from './getDataMethods.service';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {StockListRow} from "../pages/stockList/StockListRow";
import {StockItemModalComponent} from '../components/stockItemModal/stockItemModal.component';
import {BuildTotalAndAverageRowsParams} from '../model/BuildTotalAndAverageRowsParams';
import {CPHColGroupDef} from "../model/CPHColGroupDef";
import {CPHAutoPriceColDef, CPHColDef} from "../model/CPHColDef";
import {TagDTO} from "../model/Tag";
import {VehicleAdvertTagService} from "./vehicleAdvertTag.service";

@Injectable({
   providedIn: 'root'
})
export class AGGridMethodsService {
   removeTagsModal: any;
   public showOptOutModalEmitter: EventEmitter<any[]> = new EventEmitter<any[]>();


   constructor(
      public selections: SelectionsService,
      public constants: ConstantsService,
      public cphPipe: CphPipe,
      public getData: GetDataMethodsService,
      public modalService: NgbModal,
      private vehicleAdvertTagService: VehicleAdvertTagService
   ) {


   }


   public aggregateUnfilteredLeafs(node: IRowNode<any>, unfilteredLeafs: IRowNode[]) {
      node.childrenAfterFilter.forEach(child => {
         if (!child.data) {
            this.aggregateUnfilteredLeafs(child, unfilteredLeafs)
         } else {
            unfilteredLeafs.push(child);
         }
      });
   }


   public workoutColWidths(rowData: any[], colDefs: (CPHAutoPriceColDef | ColGroupDef | ColDef)[], colSpacer: number, scaleFactor: number) {
      if (rowData?.length > 0) {
         colDefs.map(def => {
            // if (def.headerName == 'Cust Order No.') {
            // }
            if ((def as ColGroupDef).children) {
               (def as ColGroupDef).children.map(childDef => {
                  this.sizeCol(rowData, childDef, colSpacer, scaleFactor);
               })
            } else {
               this.sizeCol(rowData, def, colSpacer, scaleFactor);
            }
         });

      }
   }

   public mergeStateIntoDefs(savedState:ColumnState[], baseColDefs: CPHAutoPriceColDef[]) {
         const stateMap = new Map(savedState.map((col, index) => [col.colId, { ...col, index }]));

         // Merge state into defs
         let merged = baseColDefs.map((def) => {
            const state = stateMap.get(def.colId);
            return state
               ? {
                  ...def,
                  hide: state.hide,
                  width: state.width,
                  pinned: state.pinned,
                  sort: state.sort,
                  sortIndex: state.sortIndex,
               }
               : def;
         });

         // 🔑 Reorder defs to match savedState array order
         merged.sort((a, b) => {
            const indexA = stateMap.get(a.colId)?.index ?? 9999;
            const indexB = stateMap.get(b.colId)?.index ?? 9999;
            return indexA - indexB;
         });
         return merged;
      }

   public extractAverageAndTotalCols(colDefs: (CPHColGroupDef | CPHColDef)[], params: BuildTotalAndAverageRowsParams) {
      colDefs.forEach(colDef => {
         if ((colDef as CPHColGroupDef).children) {
            (colDef as CPHColGroupDef).children.forEach(child => {
               if (child.shouldAverageIfValue) {
                  params.colsToTotalOrAverage.push(child.field);
                  params.colsToSkipAverageIfZero.push(child.field);
               }
               if (child.shouldAverage) {
                  params.colsToTotalOrAverage.push(child.field);
               }
               if (child.shouldTotal) {
                  params.colsToTotalOrAverage.push(child.field);
                  params.colsToTotal.push(child.field);
               }
            });
         } else {

            if (colDef.shouldAverageIfValue) {
               params.colsToTotalOrAverage.push((colDef as CPHColDef).field);
               params.colsToSkipAverageIfZero.push((colDef as CPHColDef).field);
            }
            if (colDef.shouldAverage) {
               params.colsToTotalOrAverage.push((colDef as CPHColDef).field);
            }
            if (colDef.shouldTotal) {
               params.colsToTotalOrAverage.push((colDef as CPHColDef).field);
               params.colsToTotal.push((colDef as CPHColDef).field);
            }
         }
      });
   }


   public buildTotalAndAverageRows(params: BuildTotalAndAverageRowsParams): any[] {  //is any as it's generic to be used across tables

      if (!params.api) {
         return []
      }


      let result = [];

      //----------------------------------
      //1. do for selected rows
      //----------------------------------

      const selectedRows = params.api.getSelectedRows();

      if (selectedRows?.length > 1) {
         //create total and average row
         this.buildUpRows(params, selectedRows, result, 'selected');
      }


      //----------------------------------
      //2. add a row if we have filters in place
      //----------------------------------
      if (params.api.isAnyFilterPresent()) {

         //do for filters
         {
            const filteredRows = [];
            params.api.forEachNodeAfterFilter(node => {
               if (node.data && !node.isRowPinned()) {
                  filteredRows.push(node.data)
               }
            })
            this.buildUpRows(params, filteredRows, result, 'filtered');
         }
      }

      //----------------------------------
      //3. add a total row for the very total at the end
      //----------------------------------
      const allRows = [];
      params.api.forEachLeafNode(node => {
         if (node.data && !node.isRowPinned()) {
            allRows.push(node.data)
         }
      })
      this.buildUpRows(params, allRows, result, 'total');


      return result;
   }

   private buildUpRows(params: BuildTotalAndAverageRowsParams, selectedRows: any[], result: any[], rowType: string) {
      const {averageRow, totalRow} = this.createTotalAndAverageRow(params, selectedRows, rowType);
      if (params.showTotalInAverage) {
         params.colsToTotal.forEach(col => {
            averageRow[col] = totalRow[col];
         });
      }
      result.push(averageRow);
      if (params.includeTotalRow) {
         result.push(totalRow);
      }
   }

   private createTotalAndAverageRow(params: BuildTotalAndAverageRowsParams, rowsData: any[], rowType: string) {

      let rowsCount = rowsData.length;
      let totalRow = {};
      totalRow[params.selectedCountFieldName] = '';
      totalRow[params.labelFieldName] = 'Total';

      params.colsToTotalOrAverage.forEach(key => {
         totalRow[key] = 0;
      });


      let averageNumeratorCounts = {}

      //create total row
      params.colsToTotalOrAverage.forEach(col => {

         let averageCount = 0;
         let shouldSkipIfZero = params.colsToSkipAverageIfZero.includes(col);

         rowsData.forEach(rowData => {
            let rowVal: number = rowData[col];
            if (!isNaN(rowVal) && (rowVal || !shouldSkipIfZero)) {
               totalRow[col] = Number(totalRow[col]) + Number(rowVal);
               averageCount++;
            }
         })

         averageNumeratorCounts[col] = averageCount
      })


      //create average row
      let averageRow = {};
      averageRow[params.selectedCountFieldName] = `${this.cphPipe.transform(rowsCount, 'number', 0)} ${rowType}`;
      averageRow[params.labelFieldName] = `Average per ${params.itemName}`;

      //set averages
      params.colsToTotalOrAverage.forEach(col => {

         let thisColumnAverageCount = averageNumeratorCounts[col];
         averageRow[col] = thisColumnAverageCount > 0 ? totalRow[col] / thisColumnAverageCount : 0;
         if (!params.colsToTotal.includes(col)) {
            totalRow[col] = null;
         }
      });

      //set cols that must be true
      params.colsToSetToTrue.forEach(col => {
         averageRow[col] = true;
         totalRow[col] = true;
      });


      return {averageRow, totalRow};
   }

   private sizeCol(rowData: any[], def: CPHAutoPriceColDef, colSpacer: number, scaleFactor: number) {
      // Split the header text and return the largest char count word - E.g. "Vs Strategy Band" returns 8.
      // Col width will be minumum largest word of header.
      let characterLength = 6;
      if(def?.headerName){
         characterLength = this.splitHeaderAndReturnLargestCharCount(def.headerName);
      }

      if (characterLength < 6) {
         characterLength = 6;
      }

      // If char length is less than 7, E.g. "Make", then set to 7. Allows for burger, explanation, and filter icon to all fit.
      if (def.hasExplanation && characterLength < 7) {
         characterLength = 7;
      }

      // Get largest char length looping through subset of the data.
      // Override dates as will try to get char count of 2025-01-01T00:00:00Z rather than 1 Jan 2025 as we would display
      rowData.slice(0, 100).forEach(row => {
         var thisRowVal = row[def.field];
         if (thisRowVal) {

            //if a date
            if (this.constants.dateIsValid(thisRowVal) || def.field.includes('Date')) {
               if (12 > characterLength) {
                  characterLength = 12;
               }
            }

            //not a date, measure length
            else if (thisRowVal.length > characterLength) {
               characterLength = thisRowVal.length;
            }
         }
      });

      // Loop rows and get the char lengths for each column value. Only concerned with strings here as they are the ones which
      // Exceed the maxCharLength
      let columnCharLengths: number[] = [];

      rowData.map(x => {
         let value = x[def.field];
         if (value && typeof value === 'string') {
            columnCharLengths.push(value.length);
         }
      })

      const averageCharLength: number = columnCharLengths.length > 0 ? columnCharLengths.reduce((p, c) => p + c, 0) / columnCharLengths.length : null;

      let maxCharLength: number = window.innerWidth >= 1680 ? 80 : 40;

      if (characterLength > maxCharLength) {
         // If the largest character length exceeds the maxCharLength, then check if the average char length falls beneath the maxCharLength
         // If so, set the character length to the average. Otherwise just set to the maxCharLength.
         if (averageCharLength) {
            characterLength = averageCharLength < maxCharLength ? averageCharLength : maxCharLength;
         } else {
            characterLength = maxCharLength;
         }
      } else {
         // Special case for certain columns like "Model" where there are large ranges in char count for a column.
         // E.g. Can have "A1" and "2 Series Gran Coupe".

         if (columnCharLengths.length > 0) {
            // Calculate 10% of the array length (rounding up for removal)
            let removeCount: number = Math.ceil(columnCharLengths.length * 0.1);
            let smallestCharCount: number = Math.min(...columnCharLengths.slice(removeCount));
            let largestCharCount: number = Math.max(...columnCharLengths.slice(0, -removeCount));

            // If the char range is over 200% larger, get the average between the smallest and largest char count lengths.
            // E.g. Difference between "A1" and "2 Series Gran Coupe" is 850% so would take the average char count instead
            // In this instance 10.5
            if ((largestCharCount - smallestCharCount) / smallestCharCount > 2) {
               characterLength = (smallestCharCount + largestCharCount) / 2;
            }
         }
      }

      // String cols seem to require a bit more padding due to capital letters.
      // 4 seems a good value for minimal padding and avoiding slicing off chars.
      let paddingToAdd: number = columnCharLengths.length > 0 ? 4 : 2;

      // Add additional padding to currency cols. 1000 would render as £1,000 so need to factor in additional chars.
      if (def.type?.includes('currency')) {
         paddingToAdd = 3;
      }

      let lengthToAdd = ((characterLength + paddingToAdd) * 6);

      def.width = this.getRelativeColumnWidth(lengthToAdd);

      if (def.minWidth) {
         def.minWidth = this.getRelativeColumnWidth(def.minWidth);
      }
   }

   private splitHeaderAndReturnLargestCharCount(headerText: string): number {
      const split = headerText.split(' ');

      let mostChars = split.reduce((longest, currentWord) => {
         return currentWord.length > longest.length ? currentWord : longest;
      }, "");

      return mostChars.length;
   }

   public booleanRenderer(value: boolean) {
      //console.log(value)
      return value ? '&check;' : '&#x2715;';
   }


   getMainMenuItems(colId?: string): (string | MenuItemDef)[] {

      let embellishedMenu = [];

      embellishedMenu.push({
         name: "Top / bottom 1",
         action: 'highlightTopBottom',
         count: 1,
         isButton: true,

      });

      embellishedMenu.push({
         name: "Top / bottom 3",
         action: 'highlightTopBottom',
         count: 3,
         isButton: true,
      });

      embellishedMenu.push({
         name: "Top / bottom 5",
         action: 'highlightTopBottom',
         count: 5,
         isButton: true,
      });


      embellishedMenu.push({
         name: 'divider',
         isButton: false,
      });

      embellishedMenu.push({
         name: "Clear Highlighting",
         action: 'clearHighlighting',
         isButton: true,
      });

      embellishedMenu.push({
         name: 'divider',
         isButton: false,
      });

      //add drilldown option if relevant
      if (colId && ['New', 'Demo', 'ExDemo', 'ExManagement', 'Tactical', 'CoreUsed', 'Trade'].indexOf(colId) > -1) {
         embellishedMenu.push({
            name: 'Drilldown by month',
            action: 'showAgeingColumns',
            isButton: true,
         })
      }


      return embellishedMenu

   }

   getColMenuWithTopBottomHighlight(params: GetMainMenuItemsParams<any, any>, rules: TopBottomHighlightRule[]): (MenuItemDef | string)[] {

      const colDef = params.column.getColDef();

      let cphMenu: (MenuItemDef | string)[] = [
         'pinSubMenu',
         'separator',
         'autoSizeThis',
         'autoSizeAll',
         {
            name: 'Autosize All Columns To Page',
            action: () => {
               this.resizeGrid(params.api)
            }
         },
         'resetColumns'
      ]

      const typesToEmbellish: string[] = ['currency', 'number', 'number1dp', 'percent', 'numberSimple'];

      if (!typesToEmbellish.includes((colDef.type) as string)) {
         return cphMenu
      }

      const field = colDef.field;
      let existingRulesThisCol = rules.find(x => x.field === field)

      cphMenu.push('separator');
      cphMenu.push({
         name: "Top / bottom 1",
         icon: '&#10102;',
         action: () => {
            const goodBads = this.highlightTopBottomNew(params, 1);
            this.putGoodBadsIntoRules(existingRulesThisCol, goodBads, rules, field);
            params.api.refreshCells({force: true})
         }

      });

      cphMenu.push({
         name: "Top / bottom 3",
         action: () => {
            const goodBads = this.highlightTopBottomNew(params, 3);
            this.putGoodBadsIntoRules(existingRulesThisCol, goodBads, rules, field);
            params.api.refreshCells({force: true})
         },
         icon: '&#10104;',
      });

      cphMenu.push({
         name: "Top / bottom 5",
         icon: '&#10106;',
         action: () => {
            const goodBads = this.highlightTopBottomNew(params, 5);
            this.putGoodBadsIntoRules(existingRulesThisCol, goodBads, rules, field);
            params.api.refreshCells({force: true})
         },
      });

      cphMenu.push({
         name: "Clear Highlighting",
         action: () => {
            const ruleIndex = rules.findIndex(x => x.field === field)
            if (ruleIndex > -1) {
               rules.splice(ruleIndex, 1)
            }
            params.api.refreshCells({force: true})
         },
      });

      return cphMenu
   }

   private putGoodBadsIntoRules(existingRulesThisCol: TopBottomHighlightRule, goodBads: {
      goodIds: string[];
      badIds: string[];
   }, rules: TopBottomHighlightRule[], field: string) {
      if (existingRulesThisCol) {
         existingRulesThisCol.goodColIds.length = 0;
         goodBads.goodIds.forEach(id => existingRulesThisCol.goodColIds.push(id));
         existingRulesThisCol.badColIds.length = 0;
         goodBads.badIds.forEach(id => existingRulesThisCol.badColIds.push(id));
      } else {
         rules.push(
            {field: field, goodColIds: goodBads.goodIds, badColIds: goodBads.badIds}
         );
      }
   }

   highlightTopBottom(colDef: any, topBottomN: number, flipCols: string[], gridApi: any) {
      let values = [];
      let rows = [];
      let flip = 1;
      if (flipCols.indexOf(colDef.colId) > -1) {
         flip = -1;
      }

      gridApi.forEachNodeAfterFilter((node, index) => {
         values.push(node.data);
         rows.push(node);
      })


      values.sort((a, b) => {

         return this.constants.getNestedItem(a, colDef.field) * flip - this.constants.getNestedItem(b, colDef.field) * flip

      })

      let goods = []
      for (let i = values.length - 1; i > values.length - topBottomN - 1; i--) {
         goods.push(values[i].Label)
      }

      let bads = []
      for (let i = 0; i < topBottomN; i++) {
         bads.push(values[i].Label)
      }


      colDef.goods = goods;
      colDef.bads = bads;

      gridApi.redrawRows(rows)

   }


   getMainMenuItemsNew(params: GetMainMenuItemsParams): (string | MenuItemDef)[] {
      // you don't need to switch, we switch below to just demonstrate some different options
      // you have on how to build up the menu to return
      const colType = params.column.getColDef();

      switch (colType.type) {
         // return the defaults, put add some extra items at the end
         case 'number':
            const numberMenuItems: (| MenuItemDef | string)[] = params.defaultItems.slice(0);
            numberMenuItems.push({
               name: 'AG Grid Is Great',
               action: () => {
                  console.log('AG Grid is great was selected');
               },
            });

            return numberMenuItems;

         default:
            // make no changes, just accept the defaults
            return params.defaultItems;
      }
   }


   highlightTopBottomNew(params: GetMainMenuItemsParams<any, any>, numberToHighlight: number): {
      goodIds: string[],
      badIds: string[]
   } {

      const colDef = params.column.getUserProvidedColDef() as ColDefCph;
      const coefficient = colDef.isFlipped ? -1 : 1

      const field = params.column.getColDef().field;

      const allNodes: IRowNode[] = [];
      params.api.forEachLeafNode(node => {
         if (!node.isRowPinned()) {
            allNodes.push(node)
         }
      })

      const allNodesSorted = allNodes.sort((a, b) => (b.data[field] * coefficient) - (a.data[field] * coefficient));

      const topNodeIds = allNodesSorted.map(x => x.id).slice(0, numberToHighlight)
      allNodesSorted.reverse();
      const bottomNodeIds = allNodesSorted.map(x => x.id).slice(0, numberToHighlight)

      return {goodIds: topNodeIds, badIds: bottomNodeIds}
   }


   resizeGrid(gridApi) {
      if (gridApi) {
         gridApi.sizeColumnsToFit();
      }
   }


   cellClassProviderWithColourFontNew(params: CellClassParams, highlights: TopBottomHighlightRule[]): string {

      if (highlights.length > 0) {
         let goodOrBadFormat: string = this.highlightGoodBadPerformers(params, highlights);
         let goodBadClass = goodOrBadFormat || (params?.value < 0 ? 'badFont' : '')
         return goodBadClass += ' ag-right-aligned-cell';
      }

      //still here so no goods / bads so do number colour
      let cellClass: string = 'ag-right-aligned-cell';

      if (params.value < 0) {
         cellClass += ' badFont';
      }
      return cellClass;
   }


   private highlightGoodBadPerformers(params: CellClassParams, highlights: TopBottomHighlightRule[]): string {

      if (!params) {
         return
      }
      const rulesThisCol = highlights.find(x => x.field === params.colDef.field)
      if (!rulesThisCol) {
         return;
      }

      if (rulesThisCol.goodColIds.includes(params.node.id)) {
         return 'good';
      } else if (rulesThisCol.badColIds.includes(params.node.id)) {
         return 'bad';
      }

      return null;
   }


   clearHighlighting(colDef: any, gridApi: any) {
      colDef.goods = [];
      colDef.bads = [];

      let rows = []
      gridApi.forEachNode((node, index) => {
         if (colDef.goods.indexOf(node.data.Label) > -1 || colDef.bads.indexOf(node.data.Label) > -1)
            rows.push(node);
      })


      gridApi.redrawRows(rows)
   }

   refreshCells(gridApi) {
      if (gridApi) gridApi.refreshCells();
   }

   getContextMenuItems(params: GetContextMenuItemsParams, includeOpenStockModal?: boolean, gridApi?: GridApi): (string | MenuItemDef)[] {
      let result: (string | MenuItemDef)[] = [];

      if (includeOpenStockModal && params.node.data.StockItemId > 0) {
         result.push(
            {
               name: 'Open Stock Modal',
               cssClasses: ['bold'],
               action: () => {
                  this.openStockModal(params)
               },
            },
            'separator'
         )
      }

      result.push(
         'copy',
         'copyWithHeaders',
      )

      const selectedAdvertIds = params.api.getSelectedNodes().map(x => x.data.AdId);

      const addTag =
         (this.constants.tags.length == 0) ? [{name: 'No tags available'}] :
            this.constants.tags.map(x => ({
               name: this.tagLabel(x),
               action: () => {
                  this.addTagClick(x, selectedAdvertIds, params)
                  this.constants.toastSuccess("Tag added");
               }
            }));

      const tagIdsSet = new Set();
      params.api.getSelectedNodes().forEach(node => {
         if (node.data?.VehicleAdvertTagList?.length > 0) {
            node.data.VehicleAdvertTagList.split("||").forEach(tagId => {
               tagIdsSet.add(parseInt(tagId, 10));
            });
         }
      });

      const tagsOnAdverts = this.constants.tags.filter(x => tagIdsSet.has(x.Id));

      const removeTag =
         (tagsOnAdverts.length == 0) ? [{name: 'No tags on selected adverts'}] :
            tagsOnAdverts.map(x => ({
               name: this.tagLabel(x),
               action: () => {
                  this.removeTagClick(x, selectedAdvertIds, params)
                  this.constants.toastSuccess("Tag removed");
               }
            }));

      result.push('separator', {
         icon: '🏷️',
         name: 'Add Tags',
         subMenu: addTag
      }, {
         icon: '🏷️',
         name: 'Remove Tags',
         subMenu: removeTag
      });

      if (params.column && params.column.getColDef().type == 'labelSetFilter') {
         result.push('separator',
            {
               name: `Filter column to only show ${params.value}`,
               icon: '▼',
               action: () => {
                  const field = params.column.getColDef().field;
                  const filterInstance = params.api.getFilterInstance(field);

                  let existingModel = params.api.getFilterModel();

                  existingModel[field] = {filterType: 'set', values: [params.value]};
                  params.api.setFilterModel(existingModel);
                  params.api.onFilterChanged();
               }
            },
            {
               name: `Filter column to exclude ${params.value}`,
               icon: '▼',
               action: () => {
                  const field = params.column.getColDef().field;
                  const filterInstance = params.api.getFilterInstance(field);

                  let existingModel = params.api.getFilterModel();
                  let allValsThisCol = []
                  params.api.forEachLeafNode(node => {
                     allValsThisCol.push(node.data[field])
                  })
                  let uniques = [...new Set(allValsThisCol)];

                  existingModel[field] = {filterType: 'set', values: uniques.filter(x => x !== params.value)};
                  params.api.setFilterModel(existingModel);
                  params.api.onFilterChanged();
               }
            })
      }


      return result;
   }

   openStockModal(params) {
      this.getData.getStockListRowItem(params.node.data.StockItemId).subscribe((res: StockListRow[]) => {
         const modalRef = this.modalService.open(StockItemModalComponent, {keyboard: true, size: 'lg'});
         modalRef.componentInstance.givenStockItem = res[0];
         modalRef.result.then((result) => {
         });
      })
   }

   getHeaderHeight() {
      return this.getRowHeight(40);
   }

   getRowPinnedHeight() {
      return this.getRowHeight(35);
   }

   getStandardHeight() {
      return this.getRowHeight(22);
   }

   getGroupHeaderHeight() {
      return this.getRowHeight(30);
   }

   getFloatingFilterHeight() {
      return this.getRowHeight(30);
   }

   getRowHeight(heightInPx: number) {
      let width: number = window.innerWidth;

      if (width >= 1920) {
         //full HD or more
         return heightInPx;
      } else if (width < 1920 && width >= 1527) {
         //mid sized
         // console.log('medium', width)
         return (heightInPx / 140) * 100;
      } else {
         //small
         // console.log('small',width)
         return (heightInPx / 180) * 100;
      }
   }

   getAbsoluteColumnWidth(width: number) {
      let windowWidth: number = window.innerWidth;

      if (windowWidth >= 1920) {
         return width;
      }
      if (windowWidth < 1920 && windowWidth >= 1527) {
         return width * 1.25;
      }
      return width * 1.5;
   }

   getRelativeColumnWidth(width: number) {
      let windowWidth: number = window.innerWidth;

      if (windowWidth >= 1920) {
         return width;
      }
      if (windowWidth < 1920 && windowWidth >= 1527) {
         return (width / 125) * 100;
      }
      return (width / 150) * 100;
   }

   getFilterListItemHeight() {
      let windowWidth: number = window.innerWidth;

      if (windowWidth >= 1527) {
         return 24;
      }
      return 16;
   }

   private tagLabel(x: TagDTO) {
      return `<span style='vertical-align: middle; display: inline-block; width: 1em; height: 1em; background-color: ${x.Colour}; margin-right: 5px; border-radius: 2px'></span>` + x.Label
   }

   private addTagClick(x: TagDTO, selectedAdvertIds: number[], params) {
      this.vehicleAdvertTagService.applyToVehicles(x.Id, selectedAdvertIds).then(res => {
         // Update local data after successful API call
         params.api.getSelectedNodes().forEach(node => {
            if (node.data) {
               const currentTags = node.data.VehicleAdvertTagList || '';
               const tagIds = currentTags ? currentTags.split('||').map(id => parseInt(id, 10)) : [];

               // Add the new tag ID if not already present
               if (!tagIds.includes(x.Id)) {
                  tagIds.push(x.Id);
                  node.data.VehicleAdvertTagList = tagIds.join('||');
               }
            }
         });

         // Refresh the cells to update the display
         params.api.refreshCells({
            force: true,
            columns: ['VehicleAdvertTagList']
         });
      });
   }

   private removeTagClick(x: TagDTO, selectedAdvertIds: number[], params) {
      this.vehicleAdvertTagService.removeFromVehicles(x.Id, selectedAdvertIds).then(res => {
         // Update local data after successful API call
         params.api.getSelectedNodes().forEach(node => {
            if (node.data && node.data.VehicleAdvertTagList) {
               const tagIds = node.data.VehicleAdvertTagList.split('||')
                  .map(id => parseInt(id, 10))
                  .filter(id => id !== x.Id);

               node.data.VehicleAdvertTagList = tagIds.length > 0 ? tagIds.join('||') : '';
            }
         });

         // Refresh the cells to update the display
         params.api.refreshCells({
            force: true,
            columns: ['VehicleAdvertTagList']
         });
      });
   }

   optOutableVehicles(gridApi: GridApi) {

      const optOutableVehicles = gridApi.getSelectedRows().filter((x) => {
         const site = this.constants.RetailerSites.find((y) => y.Id == x.RetailerSiteId);
         return site && site.UpdatePricesAutomatically;
      });

      return optOutableVehicles;
   }
}




