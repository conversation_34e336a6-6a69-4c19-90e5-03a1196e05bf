import {Injectable} from '@angular/core';
import {NavigationEnd, Router} from '@angular/router';
import {forkJoin} from 'rxjs';
import {AccessToken, Languages, UseLogItem} from '../model/main.model';
import {StandingDataSet} from '../model/StandingDataSet';
import {ConstantsService} from './constants.service';
import {GetDataMethodsService} from './getDataMethods.service';
import {SelectionsService} from './selections.service';
import {AppConfigService} from './appConfig.service';
import {EnvironmentService} from './environment.service';
import {AppThemeService} from './appTheme.service';
import {MenuBuilderService} from './menuBuilder.service';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {BroadcastMessageModalComponent} from '../components/broadcastMessageModal/broadcastMessageModal.component';
import {UserPreferenceService} from './userPreference.service';
import {PreferenceKey, UserPreference} from '../model/UserPreference';
import {GlobalParamsService} from './globalParams.service';
import {GlobalParam} from '../model/GlobalParam';

@Injectable({providedIn: 'root'})
@Injectable({providedIn: "root"})
export class AppStartService {


   constructor(
      public constants: ConstantsService,
      public selections: SelectionsService,
      public router: Router,
      public getDataMethodsService: GetDataMethodsService,
      public appConfigService: AppConfigService,
      public environmentService: EnvironmentService,
      public appThemeService: AppThemeService,
      private menuBuilderService: MenuBuilderService,
      private modalService: NgbModal,
      private userPreferenceService: UserPreferenceService,
      private globalParamsService: GlobalParamsService //public dexie: DexieService
   ) {
   }

   updateTranslatedTextInEnvironment(object: any) {
      if (!object) return;
      Object.entries(object).forEach(([key, value]) => {
         if (typeof value == "object") {
            this.updateTranslatedTextInEnvironment(value);
         } else {
            if (key === "translatedTextField") {
               if (value === "Dashboard_TelephoneStats") {
                  // console.log(this.constants.translatedText)
                  // console.log(object)
                  // console.log(this.constants.translatedText[value as string])
               }
               object.translatedTextValue = this.constants.translatedText[value as string];
            }
         }
      });
   }

   public startApp(accessToken: string) {
      this.appThemeService.getAndApplySavedTheme();

      if (!accessToken) {
         this.setInitialStartupToComplete();
         this.redirectToLogin();
      }

      let decodedToken: AccessToken = this.parseJwt(accessToken);
      this.setUser(decodedToken);

      this.createDates();
      this.setScreenWidthReferences();
      this.subscribeToSpinnerEvents();
      this.subscribeToRouteChanges();

      let requests = [];
      requests.push(this.getDataMethodsService.getTranslations());
      requests.push(this.getDataMethodsService.getClientAppStartData());
      requests.push(this.getDataMethodsService.getBroadcastMessages());
      //requests.push(this.getDataMethodsService.getUsageItems())

      forkJoin(requests).subscribe((results: any[]) => {
         this.constants.amLoggedIn = true; //dropping this in here for now.   RP 2 Sep 23

         //--------------------------------------------------
         // 1. Translations results
         //--------------------------------------------------
         let translations = results[0];
         if (translations != null) {
            this.constants.currentLang = Languages[translations.Language];
            this.constants.translatedText = JSON.parse(translations.JsonTranslation);
            this.updateTranslatedTextInEnvironment(this.constants.environment); //one-off to update the text words within the environment file
            this.initialiseConstants(); //can do this now as it requires translations
         }

         //--------------------------------------------------
         // 2. Standing data results
         //--------------------------------------------------
         let standingData: StandingDataSet = results[1];

         standingData.GlobalParams.map((g) => {
            g.DateFrom = new Date(g.DateFrom);
            if (!!g.DateTo) {
               g.DateTo = new Date(g.DateTo);
            }
         });

         const alpineSiteIds: number[] = [1, 2, 3, 4, 5];
         this.constants.userRetailerSites = standingData.RetailerSites.filter((x) => !alpineSiteIds.includes(x.Id));

         this.setLastUpdatedDates(standingData);

         this.constants.tags = standingData.Tags;

         //add translated property to departments
         standingData.Departments.map((x) => {
            if (x.Name == "Fleet") {
               x.translation = this.constants.translatedText.Common_Fleet;
            } else if (x.Name == "New Retail") {
               x.translation = this.constants.translatedText.Common_New;
            } else if (x.Name == "Used") {
               x.translation = this.constants.translatedText.Common_Used;
            } else if (x.Name == "All") {
               x.translation = this.constants.translatedText.Common_All;
            }
         });
         this.putStandingDataIntoConstants(standingData);

         //setup autoprice environment
         if (this.selections.userRetailerSite) {
            this.constants.autopriceEnvironment = {
               defaultShowUnpublishedAds: this.selections.userRetailerSite.IncludeUnPublishedAds,
               defaultShowNewVehicles: this.selections.userRetailerSite.ShowNewVehicles,
               lifecycleStatusDefault: this.selections.userRetailerSite.LifecycleStatusDefaults?.split(","),
               applyPriceScenarios: this.selections.userRetailerSite.AllowPriceScenarios,
               allowTestStrategy: this.selections.userRetailerSite.AllowTestStrategy,
               vehicleTypes: this.selections.userRetailerSite.VehicleTypes?.split(","),
               separateBuyingStrategy: this.selections.userRetailerSite.SeparateBuyingStrategy,
               separateBuyingStrategy2: this.selections.userRetailerSite.SeparateBuyingStrategy2,
               defaultVehicleTypes: this.selections.userRetailerSite.DefaultVehicleTypes?.split(","),
               stockReport: {
                  showDMSSellingPrice_Col: this.selections.userRetailerSite.StockReport_ShowDmsSellingPrice,
                  showVsDMSSellingPrice_Col: this.selections.userRetailerSite.StockReport_ShowVsDmsSellingPrice,
                  showPhysicalLocation_Col: this.selections.userRetailerSite.StockReport_ShowPhysicalLocation,
               },
            };
         } else {
            //we don't have a retailer site.   Probably are a user who has a default retailer siteId correspnding to a site which they don't have access to
            //only know of this being the case for a few fleet orderbook users
            //in every case we are likely to not need access to autoprice, so we just set everything to none
            this.constants.autopriceEnvironment = {
               defaultShowUnpublishedAds: false,
               defaultShowNewVehicles: false,
               lifecycleStatusDefault: [],
               applyPriceScenarios: false,
               allowTestStrategy: false,
               vehicleTypes: [],
               separateBuyingStrategy: false,
               separateBuyingStrategy2: false,
               defaultVehicleTypes: [],
               stockReport: {
                  showDMSSellingPrice_Col: false,
                  showVsDMSSellingPrice_Col: false,
                  showPhysicalLocation_Col: false,
               },
            };
         }

         //setup user prefs
         standingData.UserPreferences.forEach((pref) => {
            this.userPreferenceService.preferenceStore[pref.PreferenceName] = new UserPreference(pref);
         });

         //setup global params
         standingData.GlobalParams.forEach((param) => {
            this.globalParamsService.globalParamStore[param.Description] = new GlobalParam(param);
         });

         let openItems = this.userPreferenceService.getPreference(PreferenceKey.OpenSideMenuItems);
         this.constants.menuSections = this.menuBuilderService.buildMenuItemsNew(openItems ?? []);

         this.setupVehicleTypes();
         this.setupOrderTypes();

         this.constants.sitesActive = this.constants.Sites.filter((x) => x.IsActive);
         this.constants.sitesActiveSales = this.constants.sitesActive.filter((s) => s.IsSales);

         //all these 4 need to go in v. 3.1
         this.selections.userSites = this.constants.Sites.filter(
            (e) => this.selections.user.eligibleSiteIds.indexOf(e.SiteId) > -1
         );
         this.selections.userSite = this.constants.Sites.find((e) => e.SiteId == this.selections.user.SiteId);
         this.selections.userSiteIds = this.selections.userSites.map((s) => s.SiteId);
         this.selections.user.site = this.constants.Sites.find((x) => x.SiteId === this.selections.user.SiteId);

         this.selections.dealerGroupName = standingData.DealerGroup;

         this.selections.selectedSites = [];
         this.constants.Sites.forEach((site) => {
            if (this.selections.user.eligibleSiteIds.includes(site.SiteId)) {
               this.selections.selectedSites.push(site);
            }
         });

         this.selections.selectedSitesIds = this.selections.selectedSites.map((x) => x.SiteId);

         this.setInitialStartupToComplete();

         if (this.constants.urlsToBeSkippedToRefresh.includes(window.location.pathname)) {
            this.navigateToStartPage();
         } else {
            this.navigateToRefreshedPage();
         }

         this.constants.broadcastMessages = results[2];

         // If we have messages to display, open the modal
         if (this.constants.broadcastMessages.length > 0) {
            const modalRef = this.modalService.open(BroadcastMessageModalComponent, {keyboard: true, size: "md"});
            modalRef.result.then((result) => {
            });
         }

         this.constants.highlightActiveMenuBasedOnUrl();
         //this.constants.fixSideMenu = localStorage.getItem('fixSideMenu') === 'true';
      });
   }

   private setupVehicleTypes() {
      this.constants.vehicleTypeTypes = [...new Set(this.constants.VehicleTypes.map((e) => e.Type))];
      this.constants.departments.map((dept) => {
         dept.VehicleTypeTypes = dept.VehicleTypeTypes.filter((x) => this.constants.vehicleTypeTypes.includes(x));
      });
   }

   private setupOrderTypes() {
      this.constants.orderTypeTypes = [...new Set(this.constants.OrderTypes.map((e) => e.Type))];
      this.constants.orderTypeTypesNoTrade = this.constants.orderTypeTypes.filter(
         (x) => x !== "Trade" && x !== "Auction"
      );

      this.constants.allGroups = this.constants.environment.allGroups;
      this.constants.allFamilyCodes = this.constants.environment.allFamilyCodes;

      this.constants.departments.map((dept) => {
         dept.OrderTypeTypes = dept.OrderTypeTypes.filter((x) => this.constants.orderTypeTypes.includes(x));
      });
   }

   private putStandingDataIntoConstants(standingData: StandingDataSet) {
      this.constants.Sites = standingData.Sites;
      this.constants.RetailerSites = standingData.RetailerSites;
      this.constants.FranchiseCodes = standingData.FranchiseCodes;
      this.constants.VehicleTypes = standingData.VehicleTypes;
      this.constants.OrderTypes = standingData.OrderTypes;
      //this.constants.GlobalParams = standingData.GlobalParams;
      this.constants.LastUpdatedDates = standingData.LastUpdatedDates;
      this.constants.departments = standingData.Departments;
      this.constants.Blobname = standingData.Blobname;
      this.constants.LatestSnapshotDate = standingData.LatestSnapshotDate;
      this.constants.UserClaims = standingData.UserClaims;
      this.constants.OptOutReasons = standingData.OptOutReasons;
   }

   private setLastUpdatedDates(standingData: StandingDataSet) {
      if (!standingData) {
         return;
      }
      if (standingData.LastUpdatedDates.Deals) {
         standingData.LastUpdatedDates.Deals = new Date(standingData.LastUpdatedDates.Deals);
      }
      if (standingData.LastUpdatedDates.Stocks) {
         standingData.LastUpdatedDates.Stocks = new Date(standingData.LastUpdatedDates.Stocks);
      }
      if (standingData.LastUpdatedDates.FinancialLines) {
         standingData.LastUpdatedDates.FinancialLines = new Date(standingData.LastUpdatedDates.FinancialLines);
      }
      if (standingData.LastUpdatedDates.Registrations) {
         standingData.LastUpdatedDates.Registrations = new Date(standingData.LastUpdatedDates.Registrations);
      }
      if (standingData.LastUpdatedDates.Debtors) {
         standingData.LastUpdatedDates.Debtors = new Date(standingData.LastUpdatedDates.Debtors);
      }
      if (standingData.LastUpdatedDates.CitNow) {
         standingData.LastUpdatedDates.CitNow = new Date(standingData.LastUpdatedDates.CitNow);
      }
      if (standingData.LastUpdatedDates.Voc) {
         standingData.LastUpdatedDates.Voc = new Date(standingData.LastUpdatedDates.Voc);
      }
      if (standingData.LastUpdatedDates.Bookings) {
         standingData.LastUpdatedDates.Bookings = new Date(standingData.LastUpdatedDates.Bookings);
      }
      if (standingData.LastUpdatedDates.PartsStock) {
         standingData.LastUpdatedDates.PartsStock = new Date(standingData.LastUpdatedDates.PartsStock);
      }
      if (standingData.LastUpdatedDates.EVHC) {
         standingData.LastUpdatedDates.EVHC = new Date(standingData.LastUpdatedDates.EVHC);
      }
      if (standingData.LastUpdatedDates.Wip) {
         standingData.LastUpdatedDates.Wip = new Date(standingData.LastUpdatedDates.Wip);
      }
   }

   navigateToRefreshedPage() {
      const redirectURL = window.location.pathname;
      this.router.navigateByUrl("/", { skipLocationChange: false }).then(() => {
         this.router.navigate([redirectURL]);
      });
      this.selections.fullyLoaded = true;
   }

   navigateToStartPage() {
      this.selections.triggerSpinner.next({show: false});

      this.router.navigateByUrl("/", {skipLocationChange: false}).then(() => {
         this.checkInitialPageURL();
      });
      this.selections.fullyLoaded = true;
   }

   checkInitialPageURL() {
      let initialPageURL = this.constants.environment.initialPageURL;

      //If a user had an initial page, then set it or else use the default
      if (this.selections.user.permissions.initialPageURL.length > 0) {
         initialPageURL = this.selections.user.permissions.initialPageURL;
      }

      this.router.navigate([initialPageURL]);
   }

   public parseJwt(token): AccessToken {
      var base64Url = token.split(".")[1];
      var base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      var jsonPayload = decodeURIComponent(
         atob(base64)
            .split("")
            .map(function (c) {
               return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
            })
            .join("")
      );

      return JSON.parse(jsonPayload);
   }

   createDates() {
      //console.log('the time is exactly', new Date())
      this.constants.appStartTime = new Date();
      this.constants.todayStart = this.constants.startOfToday();

      this.constants.todayEnd = this.constants.endOfDay(this.constants.todayStart);
      this.constants.yesterdayStart = this.constants.addDays(this.constants.todayStart, -1); // this.constants.deductTimezoneOffset(new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth(), this.constants.appStartTime.getDate() - 1));
      this.constants.tomorrowStart = this.constants.addDays(this.constants.todayStart, 1); // this.constants.deductTimezoneOffset(new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth(), this.constants.appStartTime.getDate() + 1));

      this.constants.thisWeekStartDate = this.constants.mostRecentMonday(this.constants.appStartTime);
      this.constants.lastWeekStartDate = this.constants.addDays(this.constants.thisWeekStartDate, -7);
      this.constants.nextWeekStartDate = this.constants.addDays(this.constants.thisWeekStartDate, 7);
      this.constants.thisWeekEndDate = this.constants.endOfDay(
         this.constants.addDays(this.constants.thisWeekStartDate, 6)
      );

      this.constants.thisMonthStart = this.constants.deductTimezoneOffset(
         new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth(), 1)
      );
      this.constants.thisMonthEnd = this.constants.deductTimezoneOffset(
         new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth() + 1, 0, 23, 59, 59)
      );
      this.constants.lastMonthStart = this.constants.deductTimezoneOffset(
         new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth() - 1, 1)
      );
      this.constants.nextMonthStart = this.constants.deductTimezoneOffset(
         new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth() + 1, 1)
      );

      this.constants.thisYearStart = this.constants.deductTimezoneOffset(
         new Date(this.constants.appStartTime.getFullYear(), 0, 1)
      );
      this.constants.earliestOrderDate = this.constants.deductTimezoneOffset(new Date(2015, 0, 1));

      this.constants.stockMonths = [
         {
            name:
               "<" +
               new Date(
                  this.constants.appStartTime.getFullYear(),
                  this.constants.appStartTime.getMonth() - 3,
                  1
               ).toLocaleString("default", {month: "short"}),
         },
         {
            name: new Date(
               this.constants.appStartTime.getFullYear(),
               this.constants.appStartTime.getMonth() - 3,
               1
            ).toLocaleString("default", {month: "short"}),
            startDate: new Date(
               this.constants.appStartTime.getFullYear(),
               this.constants.appStartTime.getMonth() - 3,
               1
            ),
         },
         {
            name: new Date(
               this.constants.appStartTime.getFullYear(),
               this.constants.appStartTime.getMonth() - 2,
               1
            ).toLocaleString("default", {month: "short"}),
            startDate: new Date(
               this.constants.appStartTime.getFullYear(),
               this.constants.appStartTime.getMonth() - 2,
               1
            ),
         },
         {
            name: new Date(
               this.constants.appStartTime.getFullYear(),
               this.constants.appStartTime.getMonth() - 1,
               1
            ).toLocaleString("default", {month: "short"}),
            startDate: new Date(
               this.constants.appStartTime.getFullYear(),
               this.constants.appStartTime.getMonth() - 1,
               1
            ),
         },
         {
            name: this.constants.appStartTime.toLocaleString("default", {month: "short"}),
            startDate: new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth(), 1),
         },
      ];
   }

   public subscribeToSpinnerEvents() {
      this.selections.triggerSpinner.subscribe((update) => {
         this.selections.spinner = update;

         //debounce the backdrop
         let debounce = 300;
         if (!!update && update.show) {
            //now wait for a bit..
            setTimeout(() => {
               //if still true, show
               if (this.selections.spinner && this.selections.spinner.show) this.selections.showSpinnerBackdrop = true;
            }, debounce);
         }

         if (!update || !update.show) {
            //now wait for a bit..
            setTimeout(() => {
               //if still the case, hide
               if (!this.selections.spinner || !this.selections.spinner.show)
                  this.selections.showSpinnerBackdrop = false;
            }, debounce);
         }
      });
   }

   public setScreenWidthReferences() {
      this.selections.screenWidth = window.innerWidth / window.devicePixelRatio;
      this.selections.screenHeight = window.innerHeight / window.devicePixelRatio;
      if (window.innerWidth >= 1910) {
         this.selections.chartFontsize = 14;
      } else {
         this.selections.chartFontsize = 11;
      }
   }

   public subscribeToRouteChanges() {
      if (!!this.constants.routeChangeSub) {
         this.constants.routeChangeSub.unsubscribe();
      }
      this.constants.routeChangeSub = this.router.events.subscribe((ev) => {
         // if (ev instanceof NavigationStart) {
         //   //If refreshed.
         //   if (this.router.navigated == false){
         //     this.router.navigateByUrl('/');
         //   }
         // }
         // else
         if (ev instanceof NavigationEnd) {
            //don't do this if app still starting
            if (!this.selections.fullyLoaded) return;

            //don't do this if user doesn't exist
            if (!this.selections.user) {
               return;
            }
            if (!this.constants.amLoggedIn) {
               return;
            }
            this.getDataMethodsService.getTodayNewUsedOrders();
            this.highlightActiveMenu(ev.url);
            this.refreshLastUpdatedDateForNewPage(ev);

            //logging user activity
            if (ev.url != "/") {
               this.logUserActivity(ev);
            }
         }
      });
   }

   private setUser(decodedToken: AccessToken) {
      if (!decodedToken.EligibleSiteIdsString) {
         //user has not got eligible sites, probably still got a token from v2 spark, logout
         this.logout();
      }
      //check if person doesn't match with environment.  NO LONGER NEEDED ON UNIFIED
      // if (decodedToken.Environment !== this.constants.environment.customer) {
      //   this.logout();
      // }
      //this.appConfigService.client = decodedToken.Environment;
      this.environmentService.loadEnvFile(decodedToken.Environment.toLowerCase());
      this.constants.environment = this.environmentService.get(); //this sets the proper env.

      let eligibleSiteIds = decodedToken.EligibleSiteIdsString.split(",").map((x) => parseInt(x));

      this.selections.user = {
         PersonId: decodedToken.LinkedPersonId,
         Name: decodedToken.UsersName,
         SiteId: parseInt(decodedToken.CurrentSiteId),
         RetailerSiteId: parseInt(decodedToken.CurrentRetailerSiteId),
         AccessAllSites: decodedToken.AccessAllSites == "true",
         JobTitle: decodedToken.JobTitle,
         RoleName: decodedToken.UserRole,
         Email: decodedToken.userName,

         permissions: {
            initialPageURL: decodedToken.initialPageURL,
            seeOrderbook: !!decodedToken.seeOrderbook && decodedToken.seeOrderbook == "true",
            seeDealsDoneThisWeek: !!decodedToken.seeDealsDoneThisWeek && decodedToken.seeDealsDoneThisWeek == "true",
            seeDealsForTheMonth: !!decodedToken.seeDealsForTheMonth && decodedToken.seeDealsForTheMonth == "true",
            seeHandoverDiary: !!decodedToken.seeHandoverDiary && decodedToken.seeHandoverDiary == "true",
            seePerformanceLeague: !!decodedToken.seePerformanceLeague && decodedToken.seePerformanceLeague == "true",
            seePerformanceTrends: !!decodedToken.seePerformanceTrends && decodedToken.seePerformanceTrends == "true",
            seeWhiteboard: !!decodedToken.seeWhiteboard && decodedToken.seeWhiteboard == "true",
            seeScratchCards: !!decodedToken.seeScratchCards && decodedToken.seeScratchCards == "true",
            seeUserMaintenance: !!decodedToken.seeUserMaintenance && decodedToken.seeUserMaintenance == "true",
            seeStockPricing: !!decodedToken.seeStockPricing && decodedToken.seeStockPricing == "true",
            seeStockList: !!decodedToken.seeStockList && decodedToken.seeStockList == "true",
            seeDashboard: !!decodedToken.seeDashboard && decodedToken.seeDashboard == "true",
            canEditPricingStrategy:
               !!decodedToken.canEditPricingStrategy && decodedToken.canEditPricingStrategy == "true",
            reviewCommission: !!decodedToken.CommissionAuthority && decodedToken.CommissionAuthority == "review",
            seeStockLanding: !!decodedToken.seeStockLanding && decodedToken.seeStockLanding == "true",
            seeRegistrations: !!decodedToken.SeeRegistrations && decodedToken.SeeRegistrations == "true",
            accessReportCentre: !!decodedToken.accessReportCentre && decodedToken.accessReportCentre == "true",
            allowReportUpload: !!decodedToken.allowReportUpload && decodedToken.allowReportUpload == "true",
            salesExecReview: decodedToken.SalesExecReview,
            selfOnlyCommission: !!decodedToken.CommissionAuthority && decodedToken.CommissionAuthority == "selfOnly",
            liveForecast: decodedToken.LiveForecast,
            seeFleetOrderbook: !!decodedToken.SeeFleetOrderbook && decodedToken.SeeFleetOrderbook == "true",
            CanUpdateSiteBusinessManagersTable:
               !!decodedToken.CanUpdateSiteBusinessManagersTable &&
               decodedToken.CanUpdateSiteBusinessManagersTable == "true",
            canActionStockPrices: !!decodedToken.canActionStockPrices && decodedToken.canActionStockPrices == "true",
            canReviewStockPrices: !!decodedToken.canReviewStockPrices && decodedToken.canReviewStockPrices == "true",
            canEditStockPriceMatrix:
               !!decodedToken.canEditStockPriceMatrix && decodedToken.canEditStockPriceMatrix == "true",
            canSeeDataSets: !!decodedToken.canSeeDataSets && decodedToken.canSeeDataSets == "true",
            editExecManagerMapping:
               !!decodedToken.editExecManagerMappings && decodedToken.editExecManagerMappings == "true",
            canViewStockInsight: decodedToken.CanViewStockInsight && decodedToken.CanViewStockInsight == "true",
            canSetQualifyingPartEx:
               decodedToken.canSetQualifyingPartEx && decodedToken.canSetQualifyingPartEx == "true",
            canSetStandardReports: decodedToken.canSetStandardReports && decodedToken.canSetStandardReports == "true",
            seeSitesLeague: !!decodedToken.seeSitesLeague && decodedToken.seeSitesLeague == "true",
            seeStatsDashboard: !!decodedToken.seeStatsDashboard && decodedToken.seeStatsDashboard == "true",
            seeStockInsight: !!decodedToken.seeStockInsight && decodedToken.seeStockInsight == "true",
            seeAdvertSimpleListing:
               !!decodedToken.seeAdvertSimpleListing && decodedToken.seeAdvertSimpleListing == "true",
            seeStockReports: !!decodedToken.seeStockReports && decodedToken.seeStockReports == "true",
            seeLeavingVehicleTrends:
               !!decodedToken.seeLeavingVehicleTrends && decodedToken.seeLeavingVehicleTrends == "true",
            seeLeavingVehicleDetail:
               !!decodedToken.seeLeavingVehicleDetail && decodedToken.seeLeavingVehicleDetail == "true",
            seeBulkValuation: !!decodedToken.seeBulkValuation && decodedToken.seeBulkValuation == "true",
            seeLocalBargains: !!decodedToken.seeLocalBargains && decodedToken.seeLocalBargains == "true",
            seeLocationOptimiser: !!decodedToken.seeLocationOptimiser && decodedToken.seeLocationOptimiser == "true",
            seeTodaysPrices: !!decodedToken.seeTodaysPrices && decodedToken.seeTodaysPrices == "true",
            seeOptOuts: !!decodedToken.seeOptOuts && decodedToken.seeOptOuts == "true",
            seeHome: !!decodedToken.seeHome && decodedToken.seeHome == "true",
            seeAdvertListingDetail: !!decodedToken.seeAdvertListingDetail && decodedToken.seeAdvertListingDetail == "true",
            seeLeavingVehicleTrendsOverTime: !!decodedToken.seeLeavingVehicleTrendsOverTime && decodedToken.seeLeavingVehicleTrendsOverTime == "true",
            seeSiteSettings: !!decodedToken.seeSiteSettings && decodedToken.seeSiteSettings == "true",
            seeScheduledReports: !!decodedToken.seeScheduledReports && decodedToken.seeScheduledReports == "true",
         },

         site: null,
         eligibleSiteIds: eligibleSiteIds,
      };

      this.selections.selectedSitesIds = eligibleSiteIds;

      this.constants.amLoggedIn = true;
   }

   highlightActiveMenu(url) {
      // if (!this.constants.menu) return;
      // this.constants.menu.forEach(section => {
      //   section.forEach(menuItem => {
      //     if (menuItem.link == url) {
      //       menuItem.isActive = true;
      //     } else if (['/stockReport', '/debts', '/salesPerformance', '/fAndISummary', '/registrationsPosition', '/serviceSummary', '/partsSummary', '/salesmanEfficiency'].includes(url) && menuItem.link == '/reportingCentre') {
      //       menuItem.isActive = true;
      //     } else {
      //       menuItem.isActive = false;
      //     }
      //   })
      // })
   }

   public refreshLastUpdatedDateForNewPage(ev: NavigationEnd) {
      let dealUrls = [
         "dashboard",
         "dealsDoneThisWeek",
         "dealsForTheMonth",
         "fAndISummary",
         "handoverDiary",
         "orderBook",
         "performanceLeague",
         "salesmanEfficiency",
         "whiteboard",
         "salesPerformance",
         "salesPerformance2",
         "superCup",
         "scratchCard",
         "salesCommission",
      ];
      let stockUrls = ["stockList", "stockReport"];
      let financialLinesUrls = ["partsSummary", "serviceSummary", "stockLanding"];
      let registrationUrls = ["registrationsPosition"];
      let debtorsUrls = ["debts"];
      let citNowUrls = ["citnow"];
      let vocUrls = ["voc"];
      let bookingsUrls = ["serviceBookings"];
      let partsStockUrls = ["partsStock"];
      let evhcUrls = ["evhc"];
      let wipUrls = [];

      let urlNoFrontSlash = ev.url.substring(1);

      if (dealUrls.includes(urlNoFrontSlash)) {
         this.getDataMethodsService.getLatestUpdatedDate("Deals");
      }
      if (stockUrls.includes(urlNoFrontSlash)) {
         this.getDataMethodsService.getLatestUpdatedDate("Stocks");
      }
      if (financialLinesUrls.includes(urlNoFrontSlash)) {
         this.getDataMethodsService.getLatestUpdatedDate("FinancialLines");
      }
      if (registrationUrls.includes(urlNoFrontSlash)) {
         this.getDataMethodsService.getLatestUpdatedDate("Registrations");
      }
      if (debtorsUrls.includes(urlNoFrontSlash)) {
         this.getDataMethodsService.getLatestUpdatedDate("Debtors");
      }
      if (citNowUrls.includes(urlNoFrontSlash)) {
         this.getDataMethodsService.getLatestUpdatedDate("CitNow");
      }
      if (vocUrls.includes(urlNoFrontSlash)) {
         this.getDataMethodsService.getLatestUpdatedDate("Voc");
      }
      if (bookingsUrls.includes(urlNoFrontSlash)) {
         this.getDataMethodsService.getLatestUpdatedDate("Bookings");
      }
      if (partsStockUrls.includes(urlNoFrontSlash)) {
         this.getDataMethodsService.getLatestUpdatedDate("PartsStock");
      }
      if (evhcUrls.includes(urlNoFrontSlash)) {
         this.getDataMethodsService.getLatestUpdatedDate("EVHC");
      }
      if (wipUrls.includes(urlNoFrontSlash)) {
         this.getDataMethodsService.getLatestUpdatedDate("Wip");
      }
   }

   public logUserActivity(ev: NavigationEnd) {
      if (!this.selections.fullyLoaded) {
         return;
      }

      let fromPage = null;
      let sessionDuration = 0;
      let timeSinceLastChange = 0;

      //wipe out any items with a different userId (in dev, frequently log in and out, causes issues)
      this.constants.useLogBuffer = this.constants.useLogBuffer.filter(
         (x) => x.PersonId === this.selections.user.PersonId
      );

      //find last item
      if (this.constants.useLogBuffer.length > 0) {
         let mostRecentItem: UseLogItem = this.constants.useLogBuffer[this.constants.useLogBuffer.length - 1];
         fromPage = mostRecentItem.PageTo;
         timeSinceLastChange = (new Date().getTime() - mostRecentItem.ChangeDate.getTime()) / 1000;

         if (timeSinceLastChange < 5 * 60 * 1000) {
            //within last 5 minutes, assume it's a continuing session
            sessionDuration = Math.round(mostRecentItem.SessionDuration + timeSinceLastChange);
         }
      }

      //create the current event
      let newItem: UseLogItem = {
         PersonId: this.selections.user.PersonId,
         PageFrom: fromPage,
         PageTo: ev.url,
         ChangeDate: new Date(),
         SessionDuration: sessionDuration,
      };

      //add it to the latest buffer of events
      this.constants.useLogBuffer.push(newItem);

      //check if have done more than x changes, if so post the buffer
      if (this.constants.useLogBuffer.length > 0) {
         let itemsToSave = this.constants.useLogBuffer.splice(0, 1);
         this.getDataMethodsService.uploadUseLogItems(itemsToSave).subscribe(
            (result) => {
            },
            (e) => {
               console.error(JSON.stringify(e));
            }
         );
      }
   }

   initialiseConstants() {
      const theme = localStorage.getItem("theme");

      if (!theme || theme === "grey") {
         this.constants.backgroundColours = this.constants.getCSSVariableArray("--backgroundColoursGrey");
         this.constants.borderColoursForTranslucentAreas = this.constants.getCSSVariableArray(
            "--borderColoursTranslucentGrey"
         );
         this.constants.backgroundColoursTranslucent = this.constants.getCSSVariableArray(
            "--backgroundColoursTranslucentGrey"
         );
         this.constants.actualColour = this.constants.getCSSVariable("--actualColourGrey");
      } else if (theme === "green") {
         this.constants.backgroundColours = this.constants.getCSSVariableArray("--backgroundColoursLegacy");
         this.constants.borderColoursForTranslucentAreas = this.constants.getCSSVariableArray(
            "--borderColoursTranslucentLegacy"
         );
         this.constants.backgroundColoursTranslucent = this.constants.getCSSVariableArray(
            "--backgroundColoursTranslucentLegacy"
         );
         this.constants.actualColour = this.constants.getCSSVariable("--actualColourLegacy");
      } else {
         this.constants.backgroundColours = this.constants.getCSSVariableArray("--backgroundColoursGrey");
         this.constants.borderColoursForTranslucentAreas = this.constants.getCSSVariableArray(
            "--borderColoursTranslucentGrey"
         );
         this.constants.backgroundColoursTranslucent = this.constants.getCSSVariableArray(
            "--backgroundColoursTranslucentGrey"
         );
         this.constants.actualColour = this.constants.getCSSVariable("--actualColourGrey");
      }

      this.constants.renaultColour = this.constants.getCSSVariable("--renaultColour");
      this.constants.nissanColour = this.constants.getCSSVariable("--nissanColour");

      this.constants.firstOrderDate = new Date(2008, 0, 1);

      this.constants.comparatives = [
         {description: "Budget", actuality: "budget", yearOffset: 0},
         {description: "Last Year", actuality: "actuals", yearOffset: -1},
      ];

      this.constants.lateCostOptions = [
         {
            name: this.constants.translatedText.Common_IncludeLateCosts,
            includeLateCosts: true,
            includeNormalCosts: true,
         },
         {
            name: this.constants.translatedText.Common_ExcludeLateCosts,
            includeLateCosts: false,
            includeNormalCosts: true,
         },
         {
            name: this.constants.translatedText.Common_OnlyLateCosts,
            includeLateCosts: true,
            includeNormalCosts: false,
         },
      ];

      this.constants.orderOptions = [
         {name: this.constants.translatedText.Common_IncludeOrders, includeOrders: true, includeInvoiced: true},
         {name: this.constants.translatedText.Common_ExcludeOrders, includeOrders: false, includeInvoiced: true},
         {name: this.constants.translatedText.Common_OnlyOrders, includeOrders: true, includeInvoiced: false},
      ];
   }

   public redirectToLogin() {
      if (!window.location.href.includes("noWebAccess")) {
         this.router.navigateByUrl("/login");
      }
   }

   setInitialStartupToComplete() {
      this.constants.initialStartupComplete = true;
   }

   public logout() {
      //Clear the token and redirect to Login Page
      localStorage.clear();
      window.location.reload();
      // this.dexie.deleteDatabase().subscribe(res => {
      //   //this.router.navigateByUrl('login');
      // }, e => {
      //   console.error(e)
      // });
   }
}
