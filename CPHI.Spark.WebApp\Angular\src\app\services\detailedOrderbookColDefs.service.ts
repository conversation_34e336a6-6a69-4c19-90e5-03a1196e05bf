import { Injectable } from "@angular/core";
import { CPHAutoPriceColDef } from "src/app/model/CPHColDef";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { AutopriceRendererService } from "src/app/services/autopriceRenderer.service";
import { ConstantsService } from "src/app/services/constants.service";
import { AutotraderService } from "src/app/services/autotrader.service";

@Injectable({
   providedIn: "root",
})

export class DetailedOrderbookColDefsService {
   constructor(
      private autopriceRendererService: AutopriceRendererService,
      private gridHelpersService: AGGridMethodsService,
      private constants: ConstantsService
   ) {}

   public provideDetailedOrderbookColDefs(): CPHAutoPriceColDef[] {
      const colDefs: CPHAutoPriceColDef[] = [
         // Identification
         { headerName: "Deal Id", colId: "DealId", field: "DealId", type: "number", columnSection: "Orderbook" },
         {
            headerName: "Stock No",
            colId: "StockNumber",
            field: "StockNumber",
            type: "label",
            columnSection: "Orderbook",
         },
         { headerName: "Reg", colId: "Reg", field: "Reg", type: "label", columnSection: "Orderbook" },
         {
            headerName: "OEM Ref",
            colId: "OemReference",
            field: "OemReference",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Last Location",
            colId: "LastPhysicalLocation",
            field: "LastPhysicalLocation",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Description",
            colId: "Description",
            field: "Description",
            type: "label",
            columnSection: "Orderbook",
            width: 150,
         },
         {
            headerName: "Model Year",
            colId: "ModelYear",
            field: "ModelYear",
            type: "number",
            columnSection: "Orderbook",
         },
         {
            headerName: "Vehicle Age",
            colId: "VehicleAge",
            field: "VehicleAge",
            type: "number",
            columnSection: "Orderbook",
         },

         // Dates
         { headerName: "Stock Date", colId: "StockDate", field: "StockDate", type: "date", columnSection: "Orderbook" },
         {
            headerName: "Registered Date",
            colId: "RegisteredDate",
            field: "RegisteredDate",
            type: "date",
            columnSection: "Orderbook",
         },
         { headerName: "Order Date", colId: "OrderDate", field: "OrderDate", type: "date", columnSection: "Orderbook" },
         {
            headerName: "Invoice Date",
            colId: "InvoiceDate",
            field: "InvoiceDate",
            type: "date",
            columnSection: "Orderbook",
         },
         {
            headerName: "Handover Date",
            colId: "HandoverDate",
            field: "HandoverDate",
            type: "date",
            columnSection: "Orderbook",
         },
         {
            headerName: "Delivery Date",
            colId: "ActualDeliveryDate",
            field: "ActualDeliveryDate",
            type: "date",
            columnSection: "Orderbook",
         },
         {
            headerName: "Accounting Date",
            colId: "AccountingDate",
            field: "AccountingDate",
            type: "date",
            columnSection: "Orderbook",
         },
         {
            headerName: "Dealfile Sent",
            colId: "DealfileSentDate",
            field: "DealfileSentDate",
            type: "date",
            columnSection: "Orderbook",
         },

         // Flags
         {
            headerName: "Invoiced",
            colId: "IsInvoiced",
            field: "IsInvoiced",
            type: "boolean",
            columnSection: "Orderbook",
         },
         {
            headerName: "Delivered",
            colId: "IsDelivered",
            field: "IsDelivered",
            type: "boolean",
            columnSection: "Orderbook",
         },
         {
            headerName: "Late Cost",
            colId: "IsLateCost",
            field: "IsLateCost",
            type: "boolean",
            columnSection: "Orderbook",
         },
         {
            headerName: "Financed",
            colId: "IsFinanced",
            field: "IsFinanced",
            type: "boolean",
            columnSection: "Orderbook",
         },
         { headerName: "Closed", colId: "IsClosed", field: "IsClosed", type: "boolean", columnSection: "Orderbook" },
         { headerName: "Car", colId: "IsCar", field: "IsCar", type: "boolean", columnSection: "Orderbook" },

         // Parties
         { headerName: "Customer", colId: "Customer", field: "Customer", type: "label", columnSection: "Orderbook" },
         {
            headerName: "Finance Co",
            colId: "FinanceCo",
            field: "FinanceCo",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Salesman",
            colId: "SalesmanName",
            field: "SalesmanName",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Salesman Id",
            colId: "SalesmanId",
            field: "SalesmanId",
            type: "number",
            columnSection: "Orderbook",
         },
         {
            headerName: "Salesman DMS Id",
            colId: "SalesmanDmsId",
            field: "SalesmanDmsId",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Site",
            colId: "SiteDescription",
            field: "SiteDescription",
            type: "label",
            columnSection: "Orderbook",
         },
         { headerName: "Site Id", colId: "SiteId", field: "SiteId", type: "number", columnSection: "Orderbook" },
         { headerName: "Site Code", colId: "SiteCode", field: "SiteCode", type: "number", columnSection: "Orderbook" },
         {
            headerName: "Region",
            colId: "RegionDescription",
            field: "RegionDescription",
            type: "label",
            columnSection: "Orderbook",
         },
         { headerName: "Franchise", colId: "Franchise", field: "Franchise", type: "label", columnSection: "Orderbook" },

         // Order info
         {
            headerName: "Order Type",
            colId: "OrderType",
            field: "OrderType",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Order Type Desc",
            colId: "OrderTypeDescription",
            field: "OrderTypeDescription",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Vehicle Type",
            colId: "VehicleType",
            field: "VehicleType",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Vehicle Type Desc",
            colId: "VehicleTypeDescription",
            field: "VehicleTypeDescription",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Vehicle Type Code",
            colId: "VehicleTypeCode",
            field: "VehicleTypeCode",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Delivery Site",
            colId: "DeliverySiteDescription",
            field: "DeliverySiteDescription",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Vehicle Source",
            colId: "VehicleSource",
            field: "VehicleSource",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Vehicle Class",
            colId: "VehicleClassDescription",
            field: "VehicleClassDescription",
            type: "label",
            columnSection: "Orderbook",
         },

         // Units
         { headerName: "Units", colId: "Units", field: "Units", type: "number", columnSection: "Orderbook" },

         // Financials - headline
         {
            headerName: "Sale",
            colId: "Sale",
            field: "Sale",
            type: "currency",
            columnSection: "Orderbook",
            shouldAverageIfValue: true,
         },
         {
            headerName: "Cost",
            colId: "CoS",
            field: "CoS",
            type: "currency",
            columnSection: "Orderbook",
            shouldAverageIfValue: true,
         },
         { headerName: "Discount", colId: "Discount", field: "Discount", type: "currency", columnSection: "Orderbook" },
         {
            headerName: "Fuel Sale",
            colId: "FuelSale",
            field: "FuelSale",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Fuel Cost",
            colId: "FuelCost",
            field: "FuelCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Oem Delivery Sale",
            colId: "OemDeliverySale",
            field: "OemDeliverySale",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Oem Delivery Cost",
            colId: "OemDeliveryCost",
            field: "OemDeliveryCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Accessories Sale",
            colId: "AccessoriesSale",
            field: "AccessoriesSale",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Accessories Cost",
            colId: "AccessoriesCost",
            field: "AccessoriesCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         { headerName: "PDI Cost", colId: "PDICost", field: "PDICost", type: "currency", columnSection: "Orderbook" },
         {
            headerName: "Mech Prep",
            colId: "MechPrep",
            field: "MechPrep",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Body Prep",
            colId: "BodyPrep",
            field: "BodyPrep",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "New Bonus 1",
            colId: "NewBonus1",
            field: "NewBonus1",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "New Bonus 2",
            colId: "NewBonus2",
            field: "NewBonus2",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Paint Prot Acc Sale",
            colId: "PaintProtectionAccessorySale",
            field: "PaintProtectionAccessorySale",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Paint Prot Acc Cost",
            colId: "PaintProtectionAccessoryCost",
            field: "PaintProtectionAccessoryCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Broker Cost",
            colId: "BrokerCost",
            field: "BrokerCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Intro Commission",
            colId: "IntroCommission",
            field: "IntroCommission",
            type: "currency",
            columnSection: "Orderbook",
         },
         { headerName: "Error", colId: "Error", field: "Error", type: "currency", columnSection: "Orderbook" },
         { headerName: "Other", colId: "Other", field: "Other", type: "currency", columnSection: "Orderbook" },
         { headerName: "VAT Cost", colId: "VatCost", field: "VatCost", type: "currency", columnSection: "Orderbook" },
         {
            headerName: "PartEx Over Allowance",
            colId: "PartExOverAllowance1",
            field: "PartExOverAllowance1",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Factory Bonus",
            colId: "FactoryBonus",
            field: "FactoryBonus",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Reg Bonus",
            colId: "RegBonus",
            field: "RegBonus",
            type: "currency",
            columnSection: "Orderbook",
         },

         // Flags for extras
         {
            headerName: "Has Service Plan",
            colId: "HasServicePlan",
            field: "HasServicePlan",
            type: "boolean",
            columnSection: "Orderbook",
         },
         { headerName: "Has Tyre", colId: "HasTyre", field: "HasTyre", type: "boolean", columnSection: "Orderbook" },
         { headerName: "Has Alloy", colId: "HasAlloy", field: "HasAlloy", type: "boolean", columnSection: "Orderbook" },
         {
            headerName: "Has Tyre+Alloy",
            colId: "HasTyreAlloy",
            field: "HasTyreAlloy",
            type: "boolean",
            columnSection: "Orderbook",
         },
         {
            headerName: "Has WheelGuard",
            colId: "HasWheelGuard",
            field: "HasWheelGuard",
            type: "boolean",
            columnSection: "Orderbook",
         },
         {
            headerName: "Has Cosmetic",
            colId: "HasCosmetic",
            field: "HasCosmetic",
            type: "boolean",
            columnSection: "Orderbook",
         },
         {
            headerName: "Has Paint Prot",
            colId: "HasPaintProtection",
            field: "HasPaintProtection",
            type: "boolean",
            columnSection: "Orderbook",
         },
         { headerName: "Has GAP", colId: "HasGap", field: "HasGap", type: "boolean", columnSection: "Orderbook" },
         {
            headerName: "Has Warranty",
            colId: "HasWarranty",
            field: "HasWarranty",
            type: "boolean",
            columnSection: "Orderbook",
         },

         // Totals
         {
            headerName: "Total Product Count",
            colId: "TotalProductCount",
            field: "TotalProductCount",
            type: "number",
            columnSection: "Orderbook",
         },

         // Commissions
         {
            headerName: "Finance Commission",
            colId: "FinanceCommission",
            field: "FinanceCommission",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Finance Subsidy",
            colId: "FinanceSubsidy",
            field: "FinanceSubsidy",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Select Commission",
            colId: "SelectCommission",
            field: "SelectCommission",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "ProPlus Commission",
            colId: "ProPlusCommission",
            field: "ProPlusCommission",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Standards Commission",
            colId: "StandardsCommission",
            field: "StandardsCommission",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "RCI Finance Commission",
            colId: "RCIFinanceCommission",
            field: "RCIFinanceCommission",
            type: "currency",
            columnSection: "Orderbook",
         },

         // Product Sales/Costs
         {
            headerName: "Service Plan Sale",
            colId: "ServicePlanSale",
            field: "ServicePlanSale",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Service Plan Cost",
            colId: "ServicePlanCost",
            field: "ServicePlanCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Tyre Sale",
            colId: "TyreSale",
            field: "TyreSale",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Tyre Cost",
            colId: "TyreCost",
            field: "TyreCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Alloy Sale",
            colId: "AlloySale",
            field: "AlloySale",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Alloy Cost",
            colId: "AlloyCost",
            field: "AlloyCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Tyre+Alloy Sale",
            colId: "TyreAlloySale",
            field: "TyreAlloySale",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Tyre+Alloy Cost",
            colId: "TyreAlloyCost",
            field: "TyreAlloyCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "WheelGuard Sale",
            colId: "WheelGuardSale",
            field: "WheelGuardSale",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "WheelGuard Cost",
            colId: "WheelGuardCost",
            field: "WheelGuardCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Cosmetic Sale",
            colId: "CosmeticSale",
            field: "CosmeticSale",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Cosmetic Cost",
            colId: "CosmeticCost",
            field: "CosmeticCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Paint Prot Sale",
            colId: "PaintProtectionSale",
            field: "PaintProtectionSale",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Paint Prot Cost",
            colId: "PaintProtectionCost",
            field: "PaintProtectionCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         { headerName: "Gap Sale", colId: "GapSale", field: "GapSale", type: "currency", columnSection: "Orderbook" },
         { headerName: "Gap Cost", colId: "GapCost", field: "GapCost", type: "currency", columnSection: "Orderbook" },
         {
            headerName: "Warranty Sale",
            colId: "WarrantySale",
            field: "WarrantySale",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Warranty Cost",
            colId: "WarrantyCost",
            field: "WarrantyCost",
            type: "currency",
            columnSection: "Orderbook",
         },
         {
            headerName: "Standard Warranty Cost",
            colId: "StandardWarrantyCost",
            field: "StandardWarrantyCost",
            type: "currency",
            columnSection: "Orderbook",
         },

         // Profits
         {
            headerName: "Metal Profit",
            colId: "MetalProfit",
            field: "MetalProfit",
            type: "currency",
            columnSection: "Orderbook",
            shouldAverageIfValue: true,
         },
         {
            headerName: "Other Profit",
            colId: "OtherProfit",
            field: "OtherProfit",
            type: "currency",
            columnSection: "Orderbook",
            shouldAverageIfValue: true,
         },
         {
            headerName: "AddOn Profit",
            colId: "AddOnProfit",
            field: "AddOnProfit",
            type: "currency",
            columnSection: "Orderbook",
            shouldAverageIfValue: true,
         },
         {
            headerName: "Finance Profit",
            colId: "FinanceProfit",
            field: "FinanceProfit",
            type: "currency",
            columnSection: "Orderbook",
            shouldAverageIfValue: true,
         },
         {
            headerName: "Total Profit",
            colId: "TotalProfit",
            field: "TotalProfit",
            type: "currency",
            columnSection: "Orderbook",
            shouldAverageIfValue: true,
         },

         // Misc
         {
            headerName: "Finance Type",
            colId: "FinanceType",
            field: "FinanceType",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Last Adv Price",
            colId: "LastAdvertisedPrice",
            field: "LastAdvertisedPrice",
            type: "currency",
            columnSection: "Orderbook",
         },
         { headerName: "Variant", colId: "Variant", field: "Variant", type: "label", columnSection: "Orderbook" },
         {
            headerName: "Variant Txt",
            colId: "VariantTxt",
            field: "VariantTxt",
            type: "label",
            columnSection: "Orderbook",
         },
         {
            headerName: "Delivered",
            colId: "Delivered",
            field: "Delivered",
            type: "boolean",
            columnSection: "Orderbook",
         },
         {
            headerName: "Enquiry No",
            colId: "EnquiryNumber",
            field: "EnquiryNumber",
            type: "number",
            columnSection: "Orderbook",
         },
         {
            headerName: "Comment",
            colId: "Comment",
            field: "Comment",
            type: "label",
            columnSection: "Orderbook",
            width: 200,
         },
      ];

      return colDefs;
   }
}
