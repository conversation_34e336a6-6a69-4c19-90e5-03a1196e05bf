import {Injectable} from '@angular/core';
import {environmentRRG} from 'src/environments/environmentRRG';
import {environmentSpain} from 'src/environments/environmentSpain';
import {environmentVindis} from 'src/environments/environmentVindis';
import {environmentAutoprice} from 'src/environments/environmentAutoprice';
import {Channel} from '../model/sales.model';
import {DashboardSection} from '../pages/dashboard/dashboard.component';
import {AppConfigService} from './appConfig.service';
import {environmentV12} from 'src/environments/environmentV12';
import {environmentMJMotorCo} from 'src/environments/environmentMJMotorCo';
import {environmentBrindley} from 'src/environments/environmentBrindley';
import {environmentKCSOfSurrey} from 'src/environments/environmentKCSOfSurrey';
import {environmentWaylands} from 'src/environments/environmentWaylandsGroup';
import {environmentSturgessGroup} from 'src/environments/environmentSturgessGroup';
import {environmentJJPremiumCars} from 'src/environments/environmentJJPremiumCars';
import {environmentPentagonGroup} from 'src/environments/environmentPentagonGroup';
import {environmentHippoApproved} from 'src/environments/environmentHippoApproved';
import {environmentLMCOfFarnham} from 'src/environments/environmentLMCOfFarnham';
import {environmentStartin} from 'src/environments/environmentStartin';
import {environmentOakwoodMotorCo} from 'src/environments/environmentOakwoodMotorCo';
import {environmentSparshattsGroup} from 'src/environments/environmentSparshattsGroup';
import {environmentThrifty} from 'src/environments/environmentThrifty';
import {environmentBellsCrossgar} from 'src/environments/environmentBellsCrossgar';
import {environmentCroxdale} from 'src/environments/environmentCroxdale';
import {environmentEMG} from 'src/environments/environmentEMGGroup';
import {environmentAcorn} from 'src/environments/environmentAcornGroup';
import {environmentLithia} from 'src/environments/environmentLithia';
import {environmentFordsOfWinsford} from 'src/environments/environmentFordsOfWinsford';
import {environmentLSH} from 'src/environments/environmentLSH';
import {environmentSytner} from 'src/environments/environmentSytner';
import {environmentEnterprise} from 'src/environments/environmentEnterprise';
import {environmentSandicliffe} from 'src/environments/environmentSandicliffe';
import {environmentNuneaton} from 'src/environments/environmentNuneaton';
import { environmentCarco } from 'src/environments/environmentCarco';
import { environmentSRMotorGroup } from 'src/environments/environmentSRMotorGroup';
import { environmentCarWorld } from 'src/environments/environmentCarWorld';
import { environmentWJKing } from 'src/environments/environmentWJKing';
import { environmentArthursCars } from 'src/environments/environmentArthursCars';
import { environmentGravelHillCars } from 'src/environments/environmentGravelHillCars';
import { SparkEnvironment } from '../model/SparkEnvironment';
import { environmentBigMotoringWorld } from 'src/environments/environmentBigMotoringWorld';
import { environmentJCT600 } from 'src/environments/environmentJCT600';
import { environmentCitygate } from 'src/environments/environmentCitygate';
import { environmentBridgendFord } from 'src/environments/environmentBridgendFord';




export interface AgeingOptionsEntityOrAgeingOption {
  description: string;
  ageCutoff: number;
}


@Injectable({
   providedIn: 'root'
})
export class EnvironmentService {

   private env: SparkEnvironment;

   constructor(
      // Unused ?
      public appConfigService: AppConfigService) {
   }

   get(): SparkEnvironment {
      if (!this.env) {
         this.loadEnvFile('dev');
      }
      return this.env;
   }


   loadEnvFile(env: string): SparkEnvironment {
      switch (env) {  // note env has been toLower() 'd
         case 'rrguk':
         case 'rrglocal':
         case 'rrgdev':
         case 'rrgtest':
         case 'rrgstage':
         case 'rrgprod': {
            this.env = environmentRRG;
            return;
         }
         case 'rrgspain':
         case 'spainlocal':
         case 'spaindev':
         case 'spaintest':
         case 'spainstage':
         case 'spainprod': {
            this.env = environmentSpain;
            return;
         }
         case 'vindis':
         case 'vindislocal':
         case 'vindisdev':
         case 'vindistest':
         case 'vindisstage':
         case 'vindisprod': {
            this.env = environmentVindis;
            return;
         }
         case 'dev': {
            this.env = environmentSpain;
            return;
         }
         case 'pinkstones':
         case 'jardine':
         case 'lmc':
         case 'autoprice':
         case 'forduk':
         case 'berry': {
            this.env = environmentAutoprice;
            return;
         }
         case 'mjmotorco': {
            this.env = environmentMJMotorCo;
            return;
         }
         case 'v12': {
            this.env = environmentV12;
            return;
         }
         case 'brindleygroup': {
            this.env = environmentBrindley;
            return;
         }
         case 'kcsofsurrey': {
            this.env = environmentKCSOfSurrey;
            return;
         }
         case 'waylandsgroup': {
            this.env = environmentWaylands;
            return;
         }
         case 'sturgessgroup': {
            this.env = environmentSturgessGroup;
            return;
         }
         case 'jjpremiumcars': {
            this.env = environmentJJPremiumCars;
            return;
         }
         case 'pentagongroup': {
            this.env = environmentPentagonGroup;
            return;
         }
         case 'hippoapproved': {
            this.env = environmentHippoApproved;
            return;
         }
         case 'lmcoffarnham': {
            this.env = environmentLMCOfFarnham;
            return;
         }
         case 'startin': {
            this.env = environmentStartin;
            return;
         }
         case 'oakwoodmotorco': {
            this.env = environmentOakwoodMotorCo;
            return;
         }
         case 'sparshattsgroup': {
            this.env = environmentSparshattsGroup;
            return;
         }

         case 'thrifty': {
            this.env = environmentThrifty;
            return;
         }

         case 'bellscrossgar': {
            this.env = environmentBellsCrossgar;
            return;
         }
         case 'croxdalegroup': {
            this.env = environmentCroxdale;
            return;
         }
         case 'emggroup': {
            this.env = environmentEMG;
            return;
         }
         case 'acorngroup': {
            this.env = environmentAcorn;
            return;
         }
         case 'lithia': {
            this.env = environmentLithia;
            return;
         }
         case 'fordsofwinsford': {
            this.env = environmentFordsOfWinsford;
            return;
         }
         case 'lsh': {
            this.env = environmentLSH;
            return;
         }
         case 'sytner': {
            this.env = environmentSytner;
            return;
         }
         case 'enterprise': {
            this.env = environmentEnterprise;
            return;
         }
         case 'sandicliffe': {
            this.env = environmentSandicliffe;
            return;
         }
         case 'nuneaton': {
            this.env = environmentNuneaton;
            return;
         }
         case 'carco': {
            this.env = environmentCarco;
            return;
         }
          case 'srmotorgroup': {
            this.env = environmentSRMotorGroup;
            return;
         }
         case 'carworld':{
            this.env = environmentCarWorld;
            return;
         }
         case 'wjking':{
            this.env = environmentWJKing;
            return;
         }
         case 'arthurscars':{
            this.env = environmentArthursCars;
            return;
         }
         case 'gravelhillcars':{
            this.env = environmentGravelHillCars;
            return;
         }
         case 'bigmotoringworld':{
            this.env = environmentBigMotoringWorld;
            return;
         }
         case 'jct600':{
            this.env = environmentJCT600;
            return;
         }
         case 'citygate': {
            this.env = environmentCitygate;
            return;
         }
         case 'bridgendford':{
            this.env = environmentBridgendFord;
            return;
         }

         default: {
            alert(`environment ${env} not found`);
         }
      }

   }
}
