import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ActiveFutureSiteStrategyVersionVM } from "../model/ActiveFutureSiteStrategyVersionVM";
import { PricingRuleSetSaveParams } from "../model/PricingRuleSetSaveParams";
import { VehicleOptOutParams } from "../model/VehicleOptOutParams";
import { UpdatePriceParams } from "../model/UpdatePriceParams";
import { DashboardDataVNParams } from '../model/DashboardDataVNParams';
import { GetLeavingVehicleItemsParams } from '../model/GetLeavingVehicleItemsParams';
import { GetLocationOptimiserAdvertsParams } from '../model/GetLocationOptimiserAdvertsParams';
import { GetOptOutsParams } from "../model/GetOptOutsParams";
import { GetPriceChangesParams } from '../model/GetPriceChangesParams';
import { GetPriceChangesParamsOld } from '../model/GetPriceChangesParamsOld';
import { GetPricingDashboardParams } from '../model/GetPricingDashboardParams';
import { GetSiteStatsParams } from '../model/GetSiteStatsParams';
import { GetValuationPriceSetParams } from '../model/GetValuationPriceSetParams.model';
import { GetVehicleAdvertsWithRatingsParams } from '../model/GetVehicleAdvertWithRatingsParams';
import { GetVehicleSpecOptionsParams } from '../model/GetVehicleSpecOptionsParams.model';
import { ImportMaskToSave } from '../model/ImportMask';
import { LeavingVehicleItem } from '../model/LeavingVehicleItem';
import { PartsStockAgeBySiteDetailed } from '../model/PartsStockAgeBySiteDetailed';
import { RetailerSiteStrategyVersion } from '../model/RetailerSiteStrategyVersion';
import { SaveSiteSettingsParams } from '../model/SaveSiteSettingsParams';
import { SaveValuationsResultsForNewVehiclesParams } from '../model/SaveValuationsResultsForNewVehiclesParams.model';
import { SellingOutlookParams } from '../model/SellingOutlookParams';
import { SetPriceChangesParams } from "../model/SetPriceChangesParams";
import { StrategyVersionVM } from "../model/StrategyVersionVM";
import { ValuationResultForAdvertToSave } from '../model/ValuationResultForAdvertToSave.model';
import { VehicleAdvertNewCommentParams } from '../model/VehicleAdvertNewCommentParams.model';
import { VehicleSpecOption } from '../model/VehicleSpecOption.model';
import { OptionChange } from "../model/OptionChange";
import { AftersalesDailyFigures, AftersalesDatasetsParams, CitNowDetail, CitNowWipsAndVidsSummaryItem, DailyWipCountPerPerson } from '../model/afterSales.model';
import { ApprovalStateUpdateParams, ClearForecastVersionsParams, CreateNewVersionParams, ForecastInput, ForecastRefresh, SaveLiveForecast } from '../model/liveForecast.model';
import { CitNowRegionRollingDataSet, CitNowSiteSummary, Comment, CommissionAdjustmentVM, DebtsParams, EvhcPersonRow, EvhcPersonRowRRG, EvhcSiteRow, EvhcSiteRowRRG, ExcelChoices, ExcelReportNames, ExecManagerMappingRow, GetVehiclesAwaitingPrepModalParams, ImageRatioRow, ImageRatiosRegionRollingDataSet, Manager, StockCheckScansParams, UseLogItem, WipDetailParams } from '../model/main.model';
import { NewCommentParams, OemOrdersSiteRow, RegistrationsRunRate, RegistrationsSiteRow, ScratchcardLine, StockModalRowParms, TodayNewUsedOrder } from '../model/sales.model';
import { AlcopaParams } from '../pages/alcopaSummary/alcopaSummary.model';
import { DailyNetOrder, DailyNetOrdersCancellationsCount, DashboardDataPack, DashboardDataPackSpainAftersales, DashboardDataPackSpainAftersalesDetail, DashboardDataPackSpainOverview, DashboardDataParams, DashboardDataSpainAftersalesParams, DashboardDataSpainOverviewParams } from '../pages/dashboard/dashboard.model';
import { AftersalesKPIRow } from '../pages/dashboard/dashboards/dashboardAftersalesSpainKPI/dashboardAftersalesSpainKPI.model';
import { DashboardDataFrameParams } from '../pages/dashboard/dashboards/dashboardInvoicedDealsSpain/dashboardInvoicedDealsSpain.model';
import { SpainUsedStockParams, StockSnapshotChoice } from '../pages/dashboard/dashboards/dashboardUsedSpain/dashboardUsedSpain.model';
import { DailyOrdersVsLastYearParams } from '../pages/dashboard/tiles/dailyOrdersVsLastYear/dailyOrdersVsLastYear.component';
import { DailyOrderDetailItem, DailyOrderDetailItemParams, DailyOrdersSiteDetail, DailyOrdersSiteDetailParams } from '../pages/dashboard/tiles/thisWeekOrders/thisWeekOrders.model';
import { DistrinetParams } from '../pages/distrinet/distrinet.model';
import { GDPRParams } from '../pages/gdpr/GDPRParams';
import { OrderbookParams } from "../model/OrderbookParams";
import { OrdersSpainSummaryBySiteParams, OrdersSummarySpain } from '../pages/ordersBySite/ordersBySite.model';
import { PartsStockDetailedParams } from '../pages/partsStock/partsStock.component';
import CompetitorSearchOurVehicle from '../pages/performanceTrends/CompetitorSearchOurVehicle';
import { CompetitorSearchUserChoices } from '../pages/performanceTrends/CompetitorSearchUserChoices';
import { GetCompetitorAnalysisParams } from '../pages/performanceTrends/GetCompetitorAnalysisParams';
import { GetEstimatedDayToSellAndPriceIndicatorParams } from "../pages/performanceTrends/GetEstimatedDayToSellAndPriceIndicatorParams";
import { GetValuationModalCompetitorAnalysisParams } from '../pages/performanceTrends/GetValuationModalCompetitorAnalysisParams';
import { PerformanceTrendsParams } from '../pages/performanceTrends/PerformanceTrendsParams';
import { SalesActivityParams } from '../pages/salesActivity/SalesActivityParams';
import { CommissionPersonItemsParams, CommissionPersonParams } from '../pages/salesCommission/salesCommission.component';
import { BonusForPeopleRowsParams, CommissionQualificationParams } from '../pages/salesCommissionVindis/salesCommissionVindis.model';
import { UpdateManagerMappingsParams } from '../pages/salesExecReview/execManagerMapping/execManagerMapping.model';
import { GetSEReviewFormParams, SaveSEReviewFormParams, UpdateSEReviewFormApprovalStateParams } from '../pages/salesExecReview/salesExecReview.model';
import { SalesmanEfficiencyParams } from '../pages/salesmanEfficiency/SalesmanEfficiencyParams';
import { ServiceBookingsSiteRow } from "../model/ServiceBookingsSiteRow";
import { ServiceBookingDetailItem } from "../model/ServiceBookingDetailItem";
import { StockListParams } from '../pages/stockList/stockList.model';
import { SpainUsedStockModalParams } from '../pages/stockReport/stockReport.model';
import { StockSiteRowsParams } from '../pages/stockReport/stockSiteRowsParams';
import { UpsellsParams } from '../pages/upsells/UpsellsParams';
import { ConstantsService } from './constants.service';
import { SelectionsService } from './selections.service';
import { GetValuationPriceSetChangeParams } from '../model/GetValuationPriceSetChangeParams';
import { VehicleAdvertWithRating } from '../model/VehicleAdvertWithRating';
import { VehicleAdvertWithRatingDTO } from "../model/VehicleAdvertWithRatingDTO";
import { GetValuationModalNewParams } from '../model/GetValuationModalNewParams';
import { UserPreference } from '../model/UserPreference';
import { ValuationResultForNewVehicleToSave } from '../model/ValuationResultForNewVehicleToSave.model';
import { StrategyFull } from '../model/StrategyFull';
import { AtMakeItem } from '../model/AtMakeItem';
import { TaxonomyFacetAndChoices } from '../model/TaxonomyFacetAndChoices';
import { AtModelItem } from '../model/AtModelItem';
import { AtGenerationItem } from '../model/AtGenerationItem';
import { GetDerivativesParams } from '../model/GetDerivativesParams';
import { AtDerivativeItem } from '../model/AtDerivativeItem';
import { GetNewVehicleModalParams } from '../model/GetNewVehicleModalParams';
import { NewVehicleValuationModal } from '../model/NewVehicleValuationModal';
import { CompetitorVehicle } from '../model/CompetitorVehicle';
import { StatsDashboard, StatsDashboardDTO } from '../model/StatsDashboard';
import { ExampleItem } from '../model/ExampleItem';
import { UpdateCostPriceTotalParams } from "../model/UpdateCostPriceTotalParams";
import { SimpleExampleItem } from '../model/SimpleExampleItem';
import { GetExampleItemsParams } from '../model/GetExampleItemsParams';
import { SimpleExampleItemForClient } from '../model/SimpleExampleItemForClient';
import { GetStatsDashboardParams } from '../model/GetStatsDashboardParams';
import { UsageItem } from '../model/UsageItem';
import { StatsSiteDashboardDTO } from '../model/StatsSiteDashboard';
import { StockProfileItemDTO } from '../model/StockProfileItem';
import { GetStatsStockProfileItemsParams } from '../model/GetStatsStockProfileItemsParams';
import { GetVehicleHistoryParams } from '../model/GetVehicleHistoryParams';
import { GetLeavingVehicleItemsForModalParams } from '../model/GetLeavingVehicleItemsForModalParams';
import { GetSameModelAdvertsParams } from '../model/GetSameModelAdvertsParams';
import { GetUsageItemsParams } from '../model/GetUsageItemsParams';
import { SiteSettings } from '../model/SiteSettings';
import { GetLeavingVehicleExplorerItemsParams } from '../model/GetLeavingVehicleExplorerItemsParams';
import { LVExplorerItem } from '../model/LVExplorerItem';
import { ExampleItemForServer } from '../model/ExampleItemForServer';
import { StrategyPriceBuildUpDetailItem } from '../model/StrategyPriceBuildUpDetailItem';





@Injectable({
  providedIn: 'root'
})
export class GetDataMethodsService {




  constructor(
    public http: HttpClient,
    public constants: ConstantsService,
    public selections: SelectionsService,
    //public dexie: DexieService,


  ) {
  }


  public getDashboardDataSpainOverview(parms: DashboardDataSpainOverviewParams) {
    const observable = new Observable(observer => {

      let url: string = `${this.constants.backEndBaseURL}/api/Dashboard/GetDashboardDataSpainOverview`
      this.http.post(url, parms).subscribe((res: DashboardDataPackSpainOverview) => {

        res.SpainDailyNetOrdersUsed.map(x => x.DayDate = this.constants.deductTimezoneOffset(new Date(x.DayDate)))
        res.SpainDailyNetOrdersNew.map(x => x.DayDate = this.constants.deductTimezoneOffset(new Date(x.DayDate)))
        observer.next(res);
        observer.complete();
      })

    })
    return observable
  }

  saveUserPreference(userPreference: UserPreference) {
    let url = `${this.constants.backEndBaseURL}/api/User/SaveUserPreference`
    return this.http.post(url, userPreference)
  }

  public getDashboardDataSpainAftersales(parms: DashboardDataSpainAftersalesParams) {
    const observable = new Observable(observer => {

      let url: string = `${this.constants.backEndBaseURL}/api/Dashboard/GetDashboardDataSpainAftersales`
      this.http.post(url, parms).subscribe((res: DashboardDataPackSpainAftersales) => {

        // res.SpainDailyNetOrdersUsed.map(x=>x.DayDate=this.constants.deductTimezoneOffset(new Date(x.DayDate)))
        // res.SpainDailyNetOrdersNew.map(x=>x.DayDate=this.constants.deductTimezoneOffset(new Date(x.DayDate)))
        observer.next(res);
        observer.complete();
      })

    })
    return observable
  }


  public getDashboardDataSpainAftersalesKPIs(parms: DashboardDataSpainAftersalesParams) {

    const observable = new Observable(observer => {

      let url: string = `${this.constants.backEndBaseURL}/api/Dashboard/GetDashboardDataSpainAftersalesKPIs`
      this.http.post(url, parms).subscribe((res: AftersalesKPIRow[]) => {

        // res.SpainDailyNetOrdersUsed.map(x=>x.DayDate=this.constants.deductTimezoneOffset(new Date(x.DayDate)))
        // res.SpainDailyNetOrdersNew.map(x=>x.DayDate=this.constants.deductTimezoneOffset(new Date(x.DayDate)))
        observer.next(res);
        observer.complete();
      })

    })
    return observable
  }

  public getDashboardDataSpainAftersalesDetail(parms: DashboardDataSpainAftersalesParams) {
    const observable = new Observable(observer => {

      let url: string = `${this.constants.backEndBaseURL}/api/Dashboard/GetDashboardDataSpainAftersalesDetail`
      this.http.post(url, parms).subscribe((res: DashboardDataPackSpainAftersalesDetail) => {

        // res.SpainDailyNetOrdersUsed.map(x=>x.DayDate=this.constants.deductTimezoneOffset(new Date(x.DayDate)))
        // res.SpainDailyNetOrdersNew.map(x=>x.DayDate=this.constants.deductTimezoneOffset(new Date(x.DayDate)))
        //console.
        observer.next(res);
        observer.complete();
      })

    })
    return observable
  }


  public getDashboardData(parms: DashboardDataParams) {


    const observable = new Observable(observer => {

      let url: string = `${this.constants.backEndBaseURL}/api/Dashboard/GetDashboardData`
      this.http.post(url, parms).subscribe((res: DashboardDataPack) => {

        if (!!res.DailyNetOrdersNew) { res.DailyNetOrdersNew.map(x => Object.assign(x, new DailyNetOrder(x))) }
        if (!!res.DailyNetOrdersFleet) { res.DailyNetOrdersFleet.map(x => Object.assign(x, new DailyNetOrder(x))) }
        if (!!res.DailyNetOrdersUsed) { res.DailyNetOrdersUsed.map(x => Object.assign(x, new DailyNetOrder(x))) }
        if (!!res.DailyNetOrdersCancellationsNew) { res.DailyNetOrdersCancellationsNew.map(x => Object.assign(x, new DailyNetOrdersCancellationsCount(x))) }
        if (!!res.DailyNetOrdersCancellationsUsed) { res.DailyNetOrdersCancellationsUsed.map(x => Object.assign(x, new DailyNetOrdersCancellationsCount(x))) }
        if (!!res.DailyNetOrdersCancellationsFleet) { res.DailyNetOrdersCancellationsFleet.map(x => Object.assign(x, new DailyNetOrdersCancellationsCount(x))) }


        if (!!res.ActivityLevelsAndOverdues) {
          res.ActivityLevelsAndOverdues.ActivityLevels.map(x => {
            x.Day = new Date(x.Day);
          })
        }

        if (!!res.VocSummary) {
          res.VocSummary.map(x => {
            x.YearMonth = new Date(x.YearMonth)
          })
        }


        observer.next(res);
        observer.complete();
      })

    })
    return observable
  }


  public GetAllSites() {
    let url: string = `${this.constants.backEndBaseURL}/api/Sites/GetAllSites`
    return this.http.get(url);
  }

  public GetUserRetailerSites() {
    let url: string = `${this.constants.backEndBaseURL}/api/User/GetUserRetailerSites`
    return this.http.get(url);
  }





  public getDetailedExcel(reportName: ExcelReportNames, params: ExcelChoices) {

    this.selections.triggerSpinner.emit({ show: true, message: 'Downloading Excel..' })

    let headers = new HttpHeaders({
      responseType: 'blob' as 'json',
      'Access-Control-Expose-Headers': 'Content-Disposition'

    });
    let options = { headers: headers };

    let url = `${this.constants.backEndBaseURL}/api/ExcelDownload/Download?reportName=${ExcelReportNames[reportName]}`

    //console.log(reportName, "reportName!");

    this.http.post(url, params, { responseType: 'blob' as 'json', reportProgress: false }).subscribe(res => {

      let blobContents: any = res;
      const downloadedFile = new Blob([blobContents], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
      const a = document.createElement('a');
      a.setAttribute('style', 'display:none;');
      document.body.appendChild(a);
      a.download = reportName;
      a.href = URL.createObjectURL(downloadedFile);
      a.target = '_blank';
      a.click();
      document.body.removeChild(a);

      this.selections.triggerSpinner.emit({ show: false })

    }, e => {
      this.constants.toastDanger('Failed to get excel report')
      this.selections.triggerSpinner.emit({ show: false })
    });


  }


  public getFleetOrderbookTemplate(reportName: ExcelReportNames, params: ExcelChoices) {

    this.selections.triggerSpinner.emit({ show: true, message: 'Downloading Excel..' })

    let headers = new HttpHeaders({
      responseType: 'blob' as 'json',
      'Access-Control-Expose-Headers': 'Content-Disposition'

    });

    let options = { headers: headers };

    let fileName = reportName == ExcelReportNames.NissanUploadTemplate ? 'Upload template - Nissan' : 'Upload template - Renault';

    let url = `${this.constants.backEndBaseURL}/api/ExcelDownload/Download?reportName=${ExcelReportNames[reportName]}`
    this.http.post(url, params, { responseType: 'blob' as 'json', reportProgress: false }).subscribe(res => {

      let blobContents: any = res;
      const downloadedFile = new Blob([blobContents], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
      const a = document.createElement('a');
      a.setAttribute('style', 'display:none;');
      document.body.appendChild(a);
      a.download = fileName;
      a.href = URL.createObjectURL(downloadedFile);
      a.target = '_blank';
      a.click();
      document.body.removeChild(a);

      this.selections.triggerSpinner.emit({ show: false })

    }, e => {
      this.constants.toastDanger('Failed to get excel report')
      this.selections.triggerSpinner.emit({ show: false })
    });


  }



  public getServiceDailyFigures(start: Date, end: Date): any {
    let myObservable = new Observable(observer => {

      let endPlusOne: Date = new Date(end.getFullYear(), end.getMonth(), end.getDate(), end.getHours() + 1, end.getMinutes(), end.getSeconds())

      this.http.get(`${this.constants.backEndBaseURL}/api/ServicePerformance?start=${start.toISOString()}&end=${endPlusOne.toISOString()}`).subscribe((result: { serviceFigures: AftersalesDailyFigures[] }) => {
        result.serviceFigures.forEach(x => {
          x.Day = new Date(x.Day);
          x.Actuality = x['actuality']
          delete x['actuality']
        })
        observer.next(result);
        observer.complete();
      }, () => {
        console.error('error getting serviceDailyFigs');
        observer.complete();
      })
    })
    return myObservable
  }



  public getPartsDailyFigures(start: Date, end: Date): any {
    let myObservable = new Observable(observer => {

      let endPlusOne: Date = new Date(end.getFullYear(), end.getMonth(), end.getDate(), end.getHours() + 1, end.getMinutes(), end.getSeconds())

      this.http.get(`${this.constants.backEndBaseURL}/api/PartsPerformance?start=${start.toISOString()}&end=${endPlusOne.toISOString()}`).subscribe((result: { partsFigures: AftersalesDailyFigures[] }) => {
        result.partsFigures.forEach(x => {
          x.Day = new Date(x.Day);
          x.Actuality = x['actuality']
          delete x['actuality']
        })
        observer.next(result);
        observer.complete();
      }, () => {
        console.error('error getting partsDailyFigs');
        observer.complete();
      })
    })
    return myObservable
  }


  public getRatioOrders(parms: OrdersSpainSummaryBySiteParams): any {
    let myObservable = new Observable(observer => {
      let url: string = `${this.constants.backEndBaseURL}/api/OrdersSpain/GetSiteRows`;
      this.http.post(url, parms).subscribe((result: OrdersSummarySpain[]) => {
        observer.next(result);
        observer.complete();
      }, () => {
        console.error('error getting getRatioOrders');
        observer.complete();
      })
    })
    return myObservable
  }

  public getRatioOrdersForSite(parms: OrdersSpainSummaryBySiteParams): any {
    let myObservable = new Observable(observer => {
      let url: string = `${this.constants.backEndBaseURL}/api/OrdersSpain/GetSiteDetail`;
      this.http.post(url, parms).subscribe((result: any[]) => {
        observer.next(result);
        observer.complete();
      }, () => {
        console.error('error getting getRatioOrders');
        observer.complete();
      })
    })
    return myObservable
  }

  public getStockSnapshotChoices(): Observable<StockSnapshotChoice[]> {
    let myObservable = new Observable<StockSnapshotChoice[]>(observer => {
      let url: string = `${this.constants.backEndBaseURL}/api/Stock/GetStockSnapshotChoices`;
      this.http.get(url).subscribe((result: StockSnapshotChoice[]) => {
        result.map(x => x.SnapshotDate = new Date(x.SnapshotDate))
        observer.next(result);
        observer.complete();
      }, () => {
        console.error('error getting getRatioOrders');
        observer.complete();
      })
    })
    return myObservable
  }


  getDashboardProfitSummary(sitesCsv: string): Observable<any> {
    let myObservable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/DashboardProfitSummary?sitesCSV=${sitesCsv}`).subscribe((result: { partsFigures: AftersalesDailyFigures[] }) => {

        observer.next(result);
        observer.complete();
      }, () => {
        console.error('error getting partsDailyFigs');
        observer.complete();
      })
    })
    return myObservable
  }



  public getRegistrationsSiteRows(timePeriodStart: Date, salesChannel: string, isMonth: boolean) {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/Registrations/GetRegistrationsSiteRows?timePeriodStart=${timePeriodStart.toISOString()}&salesChannel=${salesChannel}&isMonth=${isMonth}`).subscribe((results: RegistrationsSiteRow[]) => {
        //casts


        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting regs summary');
        observer.complete();
      })

    })
    return myObserverable
  }

  public getOemOrdersSiteRows(timePeriodStart: Date, salesChannel: string, isMonth: boolean) {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/Registrations/GetOemOrdersSiteRows?timePeriodStart=${timePeriodStart.toISOString()}&salesChannel=${salesChannel}&isMonth=${isMonth}`).subscribe((results: OemOrdersSiteRow[]) => {
        //casts

        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting regs summary');
        observer.complete();
      })

    })
    return myObserverable
  }


  public getRegistrationsRunRate(timePeriodStart: Date, salesChannel: string, isMonth: boolean, siteIdsList: number[]) {
    const myObserverable = new Observable(observer => {

      let siteIds: string = siteIdsList.join(',');
      this.http.get(`${this.constants.backEndBaseURL}/api/Registrations/GetRegistrationsRunRate?timePeriodStart=${timePeriodStart.toISOString()}&salesChannel=${salesChannel}&isMonth=${isMonth}&siteIds=${siteIds}`)
        .subscribe((results: RegistrationsRunRate) => {
          //casts
          results.DailyRegsItems.map(x => {
            x.DayDate = new Date(x.DayDate)
          })

          observer.next(results);
          observer.complete();
        }, () => {
          console.error('error getting regs summary');
          observer.complete();
        })

    })
    return myObserverable
  }




  public getDailyOrdersSiteDetails(parms: DailyOrdersSiteDetailParams) {
    const myObserverable = new Observable(observer => {
      const url = `${this.constants.backEndBaseURL}/api/Deals/GetDailyOrdersSiteDetails`;


      this.http.post(url, parms).subscribe((results: DailyOrdersSiteDetail[]) => {

        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error ');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }

  public getDailyOrdersItemDetails(parms: DailyOrderDetailItemParams) {
    const myObserverable = new Observable(observer => {
      const url = `${this.constants.backEndBaseURL}/api/Deals/GetDailyOrdersOrderDetails`;


      this.http.post(url, parms).subscribe((results: DailyOrderDetailItem[]) => {

        results.map(x => {
          x.OrigOrderDate = new Date(x.OrigOrderDate);
          x.SparkOrderOrCancelDate = new Date(x.SparkOrderOrCancelDate);
        })

        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error ');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }

  public getCitNowServiceSiteRows(start: Date): any {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/CitNow/GetCitNowServiceSiteRows?monthStart=${start.toDateString()}`).subscribe((results: CitNowSiteSummary[]) => {

        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting GetCitNowServiceSiteRows');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }

  public getImageRatioChartDataSets(): any {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/ImageRatios/GetImageRatioChartDataSets`).subscribe((results: ImageRatiosRegionRollingDataSet[]) => {

        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting GetImageRatioChartDataSets');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }

  public getImageRatiosRows(): any {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/ImageRatios/GetImageRatioRows`).subscribe((results: ImageRatioRow[]) => {

        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting GetImageRatioRows');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }

  public getCitNowSalesSiteRows(start: Date): any {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/CitNow/GetCitNowSalesSiteRows?monthStart=${start.toISOString()}`).subscribe((results: CitNowSiteSummary[]) => {

        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting getCitNowSalesSiteRows');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }

  public getCitNowEDynamixData(start: Date, siteIds: string): any {
    const myObserverable = new Observable(observer => {
      let url: string = `${this.constants.backEndBaseURL}/api/CitNow/GetCitNowServicePersonRows`;

      this.http.get(url, {
        params: {
          monthStart: `${start.toISOString()}`,
          siteIds: siteIds
        }
      }).subscribe((results: CitNowSiteSummary[]) => {
        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting CitNOW EDynamix data');
        observer.complete();
      }, () => { })
    })
    return myObserverable
  }

  public getCitNowChartDataSets(type: string): any {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/CitNow/GetCitNowChartDataSets?type=${type}`).subscribe((results: CitNowRegionRollingDataSet[]) => {

        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting GetCitNowChartDataSets');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }

  public getCitNowDetail(monthStart: Date, siteIds: string): any {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/CitNow/GetCitNowDetail?monthStart=${monthStart.toISOString()}&siteIds=${siteIds}`).subscribe((results: CitNowDetail[]) => {

        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting getCitNowDetail');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }



  public GetCitNowSimpleDetail(monthStart: Date, siteIds: string, isSales: boolean): any {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/CitNow/GetCitNowSimpleDetail?monthStart=${monthStart.toISOString()}&siteIds=${siteIds}&isSales=${isSales}`).subscribe((results: CitNowDetail[]) => {

        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting GetCitNowSimpleDetail');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }

  public GetExecManagerMappingRows(year: number): any {

    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/ExecManagerMapping/GetExecManagerMappingRows?year=${year.toString()}`).subscribe((results: ExecManagerMappingRow[]) => {

        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting GetExecManagerMappingRows');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }


  public GetManagers(): any {

    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/ExecManagerMapping/GetManagers`).subscribe((results: Manager[]) => {

        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting GetManagers');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }

  public UpdateManagers(year: number, month: number, execName: string, managerId: number): any {

    let parms: UpdateManagerMappingsParams = {
      Year: year,
      Month: month,
      ExecName: execName,
      ManagerId: managerId
    }

    const observable = new Observable(observer => {

      let url = `${this.constants.backEndBaseURL}/api/ExecManagerMapping/UpdateManagers`;

      this.http.post(url, parms).subscribe((result: any) => {

        observer.next(result);
        observer.complete();

      }, e => {
        console.error('error updating manager mappings ' + JSON.stringify(e))
      })

    })

    return observable;


  }

  // public getEVHCJobsByDay(start: Date, end: Date): any {
  //   const myObserverable = new Observable(observer => {

  //     this.http.get(`${this.constants.backEndBaseURL}/api/EVHCJobsByDay?start=${start.toISOString()}&end=${end.toISOString()}`).subscribe((results: EVHCDailyJobCount[]) => {
  //       results.forEach(item => item.JobDate = new Date(item.JobDate))
  //       observer.next(results);
  //       observer.complete();
  //     }, () => {
  //       console.error('error getting evhcJobsByDay');
  //       observer.complete();
  //     }, () => {
  //       //when it has completed
  //     })

  //   })
  //   return myObserverable
  // }

  public getDailyWipCountsPerPerson(start: Date, end: Date): any {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/DailyWipCountPerPerson?start=${start.toISOString()}&end=${end.toISOString()}`).subscribe((results: DailyWipCountPerPerson[]) => {
        results.forEach((item, i, a) => {
          a[i].TransDate = new Date(item.TransDate)
          a[i].WipCount = parseInt(item.WipCount as any)
          a[i].ServiceAdvisor_Id = parseInt(item.ServiceAdvisor_Id as any)
          a[i].Site_Id = parseInt(item.Site_Id as any)
          a[i].Technician_Id = parseInt(item.Technician_Id as any)
        })


        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting dailyWipCountsPerPerson');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }

  public getGetEvhcSiteRows(startofMonth: string, includeRed: boolean, includeAmber: boolean): any {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/Evhc/GetEvhcSiteRows?startofMonth=${startofMonth}&includeRed=${includeRed}&includeAmber=${includeAmber}`).subscribe((results: EvhcSiteRow[]) => {


        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting getGetEvhcSiteRows');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }


  public getGetEvhcSiteRowsRRG(startofMonth: string,): any {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/Evhc/GetEvhcSiteRowsRRG?startofMonth=${startofMonth}`).subscribe((results: EvhcSiteRowRRG[]) => {


        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting getGetEvhcSiteRowsRRG');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }

  public GetEvhcPeopleRows(startofMonth: string, includeRed: boolean, includeAmber: boolean, siteIds: number[]): any {
    const myObserverable = new Observable(observer => {
      this.http.get(`${this.constants.backEndBaseURL}/api/Evhc/GetEvhcPeopleRows?startofMonth=${startofMonth}&includeRed=${includeRed}&includeAmber=${includeAmber}&choseSiteIds=${siteIds.join(',')}`).subscribe((results: EvhcPersonRow[]) => {


        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting GetEvhcPeopleRows');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }

  public GetEvhcPeopleRowsRRG(startofMonth: string, siteIds: number[]): any {
    const myObserverable = new Observable(observer => {
      this.http.get(`${this.constants.backEndBaseURL}/api/Evhc/GetEvhcPeopleRowsRRG?startofMonth=${startofMonth}&choseSiteIds=${siteIds.join(',')}`).subscribe((results: EvhcPersonRowRRG[]) => {


        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting GetEvhcPeopleRowsRRG');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }




  public getCitNowWipsAndVidsSummaryAftersales(start: Date, end: Date): any {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/CitNowWipsAndVidsSummaryAftersales?start=${start.toISOString()}&end=${end.toISOString()}`).subscribe((results: CitNowWipsAndVidsSummaryItem[]) => {
        results.map(item => {
          item.TransDate = new Date(item.TransDate)
          item.Site_Id = parseInt(item.Site_Id as any)
          item.ServiceAdvisor_Id = parseInt(item.ServiceAdvisor_Id as any)
          item.Technician_Id = parseInt(item.Technician_Id as any)
          item.TotalWips = parseInt(item.TotalWips as any)
          item.TotalVideos = parseInt(item.TotalVideos as any)
          item.TotalSent = parseInt(item.TotalSent as any)
        })


        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting CitNowWipsAndVidsSummaryAftersales');
        observer.complete();
      }, () => {
        //when it has completed
      })

    })
    return myObserverable
  }





  public getAftersalesLeagueStats(start: Date, end: Date): any {
    const myObserverable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/AftersalesLeague?start=${start.toISOString()}&end=${end.toISOString()}`).subscribe((results: any) => {

        results.techDailyHoursItems = results.techDailyHoursItems;
        results.techDailyHoursItems.forEach(x => x.Day = new Date(x.Day))

        results.serviceSalesByPersonItems = results.serviceSalesByPersonItems;
        results.serviceSalesByPersonItems.forEach(x => x.Day = new Date(x.Day))

        results.evhcPersonStats = results.evhcPersonStats;
        results.evhcPersonStats.forEach(x => x.Day = new Date(x.Day))

        results.evhcPersonMissedStats = results.evhcPersonMissedStats;
        results.evhcPersonMissedStats.forEach(x => x.Day = new Date(x.Day))



        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting service bookings');
        observer.complete();
      })

    })
    return myObserverable
  }


  public getDailySiteBookingStats(start: Date, end: Date, siteId?: number): any {
    const myObserverable = new Observable(observer => {

      let url = `${this.constants.backEndBaseURL}/api/DailySiteBookingStats?start=${start.toISOString()}&end=${end.toISOString()}`
      if (siteId) url += `&siteId=${siteId}`

      this.http.get(url).subscribe((results: any) => {
        results.DailySiteBookingStats.forEach(x => x.Day = new Date(x.Day))
        observer.next(results.DailySiteBookingStats);
        observer.complete();
      }, () => {
        console.error('error getting service bookings');
        observer.complete();
      })

    })
    return myObserverable
  }

  public getDailySiteBookingStatsDetail(start: Date, end: Date, siteId?: number): any {
    const myObserverable = new Observable(observer => {

      let url = `${this.constants.backEndBaseURL}/api/DailySiteBookingStatsDetail?start=${start.toISOString()}&end=${end.toISOString()}`
      if (siteId) url += `&siteId=${siteId}`

      this.http.get(url).subscribe((results: any) => {
        results.BookingsByType.forEach(x => x.Day = new Date(x.Day))
        results.ServiceTurnoverByDay.forEach(x => x.Day = new Date(x.Day))
        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting service bookings');
        observer.complete();
      })

    })
    return myObserverable
  }


  public getServiceBookingSiteRows(start: Date): any {
    const myObserverable = new Observable<ServiceBookingsSiteRow[]>(observer => {

      let url = `${this.constants.backEndBaseURL}/api/Service/GetServiceBookingSiteRows?startDate=${start.toISOString()}`

      this.http.get(url).subscribe((results: ServiceBookingsSiteRow[]) => {
        results.map(item => {
          item.DailyBookingSummaries.map(daily => {
            if (!!daily.DayDate) { daily.DayDate = new Date(daily.DayDate) }
          })
        })
        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting service bookings');
        observer.complete();
      })

    })
    return myObserverable
  }


  public getServiceBookingsDetail(siteIds: number[]): any {
    const myObserverable = new Observable<ServiceBookingDetailItem[]>(observer => {

      let siteIdsString = siteIds.join(',');
      let url = `${this.constants.backEndBaseURL}/api/Service/GetServiceBookingsDetail?siteIdsString=${siteIdsString}`

      this.http.get(url).subscribe((results: ServiceBookingDetailItem[]) => {
        results.map(item => {
          item.DayDate = new Date(item.DayDate)
        })
        observer.next(results);
        observer.complete();
      }, () => {
        console.error('error getting service bookings');
        observer.complete();
      })

    })
    return myObserverable
  }


  getLatestUpdatedDate(dateName: string) {
    let dealerGroup = this.constants.environment.customer;
    let url = `${this.constants.backEndBaseURL}/api/GlobalParams/GetSpecificLastUpdateDate?dateName=${dateName}&dealerGroup=${dealerGroup}`

    this.http.get(url).subscribe((result: string) => {
      this.constants.LastUpdatedDates[dateName] = new Date(result);
    })
  }

  getTodayNewUsedOrders() {
    let url = `${this.constants.backEndBaseURL}/api/Deals/GetTodayOrders`
    this.http.get(url).subscribe((result: TodayNewUsedOrder) => {
      this.constants.TodayNewUsedOrders = result;
    })
  }



  getTranslations() {
    let url = `${this.constants.backEndBaseURL}/api/Translation`
    return this.http.get(url)
  }


  getClientAppStartData() {
    let url = `${this.constants.backEndBaseURL}/api/ClientAppStart`
    return this.http.get(url)
  }


  getBroadcastMessages() {
    let url = `${this.constants.backEndBaseURL}/api/BroadcastMessage/GetBroadcastMessages`
    return this.http.get(url)
  }

   getStatsDashboard(parms: GetStatsDashboardParams):Promise<StatsDashboardDTO> {
    let url = `${this.constants.backEndBaseURL}/api/AutoPrice/GetStatsDashboard`
    return this.http.post<StatsDashboardDTO>(url, parms).toPromise();
  }
   getStatsSitesDashboard(parms: GetStatsDashboardParams):Promise<StatsSiteDashboardDTO[]> {
    let url = `${this.constants.backEndBaseURL}/api/AutoPrice/GetStatsSitesDashboard`
    return this.http.post<StatsSiteDashboardDTO[]>(url, parms).toPromise();
  }
  getStatsStockProfileItems(parms: GetStatsStockProfileItemsParams):Promise<StockProfileItemDTO[]> {
    let url = `${this.constants.backEndBaseURL}/api/AutoPrice/GetStatsStockProfileItems`
    return this.http.post<StockProfileItemDTO[]>(url, parms).toPromise();
  }
  
  getLeavingVehicleExplorerItems(parms: GetLeavingVehicleExplorerItemsParams):Promise<LVExplorerItem[]> {
    let url = `${this.constants.backEndBaseURL}/api/LeavingVehicles/GetLeavingVehicleExplorerItems`
    return this.http.post<LVExplorerItem[]>(url, parms).toPromise();
  }


  getFleetOrderBookLastUpdated() {
    let url = `${this.constants.backEndBaseURL}/api/FleetOrderbook/GetLastUpdatedDates`
    return this.http.get(url)
  }



  public loadPartsStock(): any {
    const observable = new Observable(observer => {
      let url = `${this.constants.backEndBaseURL}/api/PartsStock`;
      url = url += `?ageBasedOnDateSold=true`

      this.http.get(url).subscribe((result: any) => {

        observer.next(result);
        observer.complete();

      }, e => {
        console.error('error getting all PartsStock ' + JSON.stringify(e))
      })

    })

    return observable
  }

  public loadPartsStockDetailed(ageBand: string, siteIds: number[], familyCodes: string[], stockGroups: string[]): any {

    let parms: PartsStockDetailedParams = {
      AgeBand: ageBand,
      SiteIds: siteIds,
      StockFamilies: familyCodes.toString(),
      StockGroups: stockGroups.toString(),
    }

    const observable = new Observable(observer => {

      let url = `${this.constants.backEndBaseURL}/api/PartsStock/GetPartsStockDetailed`;

      this.http.post(url, parms).subscribe((result: PartsStockAgeBySiteDetailed[]) => {

        // result.filteredByAgeBand = result.filteredByAgeBand.filter(x => familyCodes.includes(x.FamilyCodeGroup));

        observer.next(result);
        observer.complete();

      }, e => {
        console.error('error getting all PartsStock ' + JSON.stringify(e))
      })

    })

    return observable;


  }




  public getCommissionSiteRows(chosenMonth: Date, ignoreAuditPass?: boolean): Observable<any> {

    let parms: { MonthStart: Date, IgnoreAuditPass?: boolean } = { MonthStart: chosenMonth, IgnoreAuditPass: ignoreAuditPass };

    const commissionSchemeURLs = {
      RRGUK: `${this.constants.backEndBaseURL}/api/Commissions/GetCommissionSiteRows`,
      Vindis: `${this.constants.backEndBaseURL}/api/Commissions/GetCommissionSiteRowsVindis`,
      // Add more entries as needed
    };

    // Use the commission scheme name to determine the URL
    const commissionSchemeName = this.constants.environment.commissionSchemeName;
    const url = commissionSchemeURLs[commissionSchemeName] || `${this.constants.backEndBaseURL}/api/Commissions/DefaultCommissionSiteRows`;
    return this.http.post(url, parms)
  }


  public getCommissionPeopleRows(chosenMonth: Date, chosenSiteIds: number[], isLBDM: boolean, ignoreAuditPass: boolean): Observable<any> {

    let parms: CommissionPersonParams = {
      MonthStart: chosenMonth,
      SiteIds: chosenSiteIds,
      IgnoreAuditPass: ignoreAuditPass
    }

    let routeName = '';
    if (isLBDM) {
      routeName = 'GetCommissionLBDMPersonRows'
    } else {
      if (this.constants.environment.commissionSchemeName == 'RRGUK') {
        routeName = 'GetCommissionPersonRows'
      } else {
        routeName = 'GetCommissionPersonRowsVindis'
      }
    }

    const url = `${this.constants.backEndBaseURL}/api/Commissions/${routeName}`;

    return this.http.post(url, parms)
  }


  public getCommissionLBDMPeopleRows(chosenMonth: Date, chosenSiteIds: number[]): Observable<any> {
    let parms: CommissionPersonParams = {
      MonthStart: chosenMonth,
      SiteIds: chosenSiteIds,
      IgnoreAuditPass: null
    }

    let url: string;
    url = `${this.constants.backEndBaseURL}/api/Commissions/GetCommissionLBDMPersonRows`;

    return this.http.post(url, parms)
  }

  public lockCommissionMonth(chosenMonth: Date) {
    const url = `${this.constants.backEndBaseURL}/api/Commissions/LockCommissionMonth`;
    return this.http.get(url, {
      params: {
        chosenMonth: chosenMonth.toISOString()
      }
    });
  }


  public getCommissionItemsForPerson(chosenMonth: Date, chosenPerson: number, isLBDM: boolean): Observable<any> {

    let routeName: string = isLBDM ? 'GetCommissionLBDMPersonItems' : 'GetCommissionPersonItems';

    let parms: CommissionPersonItemsParams = {
      MonthStart: chosenMonth,
      SalesmanId: chosenPerson
    }
    let url = `${this.constants.backEndBaseURL}/api/Commissions/${routeName}`;

    return this.http.post(url, parms)
  }





  public getSuperCup(): Observable<any> {
    const observable = new Observable<any>((observer) => {

      this.http.get(`${this.constants.backEndBaseURL}/api/Deals/GetSuperCupMatchRows`).subscribe((res: { Site_Id: number, Salesman_Id: number, Value: number }[]) => {

        observer.next(res)
        observer.complete();
      }), e => {
        console.error('error getting all superCupStats ' + JSON.stringify(e))
        observer.complete();
      }
    })
    return observable;
  }

  public getSuperCupTwo(): Observable<any> {
    const observable = new Observable<any>((observer) => {

      this.http.get(`${this.constants.backEndBaseURL}/api/Deals/GetSuperCupTwoMatchRows`).subscribe((res: { Site_Id: number, Salesman_Id: number, Value: number }[]) => {

        observer.next(res)
        observer.complete();
      }), e => {
        console.error('error getting all superCupStats ' + JSON.stringify(e))
        observer.complete();
      }
    })
    return observable;
  }

  public getScratchcard(): Observable<ScratchcardLine[]> {
    const observable = new Observable<ScratchcardLine[]>((observer) => {

      this.http.get(`${this.constants.backEndBaseURL}/api/Scratchcard`).subscribe((res: ScratchcardLine[]) => {

        observer.next(res)
        observer.complete();
      }), e => {
        console.error('error getting all scratchcards ' + JSON.stringify(e))
        observer.complete();
      }
    })
    return observable;
  }








  registerUser(person) {
    let headers = new HttpHeaders({
      'Accept': 'application/json', 'Content-Type': 'application/json'
    });
    let options = { headers: headers };

    return this.http.post(this.constants.backEndBaseURL + '/registerNewUser', person, options)
  }


  getUsers() {
    let headers = new HttpHeaders({
      'Accept': 'application/json', 'Content-Type': 'application/json'
    });
    let options = { headers: headers };
    return this.http.get(this.constants.backEndBaseURL + '/api/AspNetUsers', options)
  }

  uploadUseLogItems(useLogItems: UseLogItem[]) {
    let headers = new HttpHeaders({
      'Accept': 'application/json', 'Content-Type': 'application/json'
    });
    let options = { headers: headers };

    return this.http.post(this.constants.backEndBaseURL + '/api/UseLogBulkUpload?', useLogItems, options)
  }

  // uploadErrorLogItems() {
  //   let headers = new HttpHeaders({
  //     'Accept': 'application/json', 'Content-Type': 'application/json'
  //   });
  //   let options = { headers: headers };


  //   this.dexie.getAllErrorsFromIndexedDb().then(result => {
  //     let errorsToUpload: ErrorLogItem[] = [];
  //     result.forEach(item => {
  //       let e: ErrorLogItem = {
  //         StackTrace: item.value.stack,
  //         Message: item.value.message,
  //         ErrorDate: item.storeTime,
  //         PersonId: this.selections.user.PersonId,
  //       }
  //       errorsToUpload.push(e);
  //     })


  //     //send to db
  //     this.http.post(this.constants.backEndBaseURL + '/api/ErrorLogItemsBulkUpload?', errorsToUpload, options).subscribe(() => {
  //       //success.  wipe out the table
  //       this.dexie.clearAllItems('errorLog').then(() => {
  //         //ok
  //       }, e => {
  //         console.error('failed to clear table ' + JSON.stringify(e))
  //       })
  //     }, e => {
  //       console.error('did not upload comments' + JSON.stringify(e))
  //     })


  //   }, e => {
  //     console.error(JSON.stringify(e));
  //   })

  // }




  saveComment(comment: Comment, showAsRemoved?: boolean) {
    let url = this.constants.backEndBaseURL + '/odata/Comments';
    let headers = new HttpHeaders({
      'Accept': 'application/json', 'Content-Type': 'application/json'
    });
    let options = { headers: headers };

    //strip out unnecessary parts of comment
    let commentToSave: Comment = {
      Id: comment.Id,
      Date: comment.Date,
      Text: comment.Text,
      PersonId: comment.PersonId,
      StockNumber: comment.StockNumber
    }
    if (showAsRemoved) {
      commentToSave.IsRemoved = true;
      commentToSave.Date = new Date();
    }

    if (comment.Id) {
      //must be existing, so replace
      return this.http.put(url + '(' + commentToSave.Id + ')', commentToSave, options);
    } else {
      //no id so new
      delete commentToSave.Id
      var data = JSON.stringify(commentToSave)
      return this.http.post(url, data, options);
    }
  }

  getPriceChangesForStockItem(stockNumber: string) {
    let url = this.constants.backEndBaseURL + `/api/Stock/GetDiffStockPrices?modelIdent=${stockNumber}`
    return this.http.get(url);
  }


  getOldestAllowableDealCache() {
    let url = this.constants.backEndBaseURL + `/api/Deals/GetOldestAllowableDealCache`
    return this.http.get(url);
  }




  // public getStockListReports() {
  //   let url = `${this.constants.backEndBaseURL}/api/StockListReport/GetReports`
  //   return this.http.get(url);
  // }



  // public saveStockListReport(report: StockReportForStocklist): any {
  //   let url = `${this.constants.backEndBaseURL}/api/StockListReport/SaveReport`;
  //   return this.http.post(url, report);
  // }

  // public updateStockListReport(report: StockReportForStocklist): any {
  //   let url = `${this.constants.backEndBaseURL}/api/StockListReport/SaveReport`;
  //   return this.http.post(url, report);
  // }

  // public deleteStockListReport(report: StockReportForStocklist): any {
  //   let url = `${this.constants.backEndBaseURL}/api/StockListReport/DeleteReport`;
  //   return this.http.post(url, report);
  // }




  logoff() { return this.http.post(this.constants.backEndBaseURL + '/account/logoff', null) }

  saveProfilePic(profilePicString: string): Observable<any> {
    let headers = new HttpHeaders({
      'Accept': 'application/json', 'Content-Type': 'application/json'
    });
    let options = { headers: headers };

    let url = `${this.constants.backEndBaseURL}/api/User/UploadProfileImage`
    return this.http.post(url, JSON.stringify(profilePicString), options);
  }

  getProfilePic(userids: number[]): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/User/GetProfilesImage`
    return this.http.post(url, userids);
  }

  switchDealerGroup(newDgId: number): Promise<{ accessToken: string, refreshToken: string }> {

    let url = `${this.constants.backEndBaseURL}/api/Account/SwitchDealerGroup?newDealerGroupId=${newDgId}`
    return this.http.get<{ accessToken: string, refreshToken: string }>(url).toPromise();
  }



  public getAllReports(): any {
    let url = `${this.constants.backEndBaseURL}/api/Report/`
    return this.http.get(url);
  }


  public downloadReport(reportId: number): any {
    let url = `${this.constants.backEndBaseURL}/api/Report/${reportId}`
    return this.http.get(url);
  }

  public uploadReport(reportId: number, reportData: string): any {

    let headers = new HttpHeaders({
      'Accept': 'application/json', 'Content-Type': 'application/json'
    });
    let options = { headers: headers };

    let uploadReport = { reportId: reportId, reportData: reportData }

    return this.http.post(this.constants.backEndBaseURL + `/api/Report/UploadReport`, uploadReport, options);
  }

  public updateUserPrefLanguage(newLang: string): Observable<any> {

    return this.http.post(this.constants.backEndBaseURL + `/api/User/Language/${newLang}`, null);

  }

  public getUserPrefLanguage(): Observable<any> {
    return this.http.get(this.constants.backEndBaseURL + `/api/User/Language`);
  }

  public getSarsLines(startDate: Date, siteIds: number[]): any {
    let url = `${this.constants.backEndBaseURL}/api/SarsLines/StartDate/${startDate}/siteIds/${siteIds}/`
    return this.http.get(url);
  }

  public getDebtsSummary(): any {
    let url = `${this.constants.backEndBaseURL}/api/Debtors/DebtsSummary`
    return this.http.get(url);
  }

  public getStockLandingsNew() {
    let url = `${this.constants.backEndBaseURL}/api/StockLandings/GetStockLandings`
    return this.http.get(url);
  }

  public getStockReport(franchises: string, agedOver: number, ageFor: string, useGroupDays: number, chosenMonth: Date, includeReservedCarsOption: boolean): Observable<any> {

    let parms: StockSiteRowsParams =
    {
      Franchises: franchises,
      AgedOver: agedOver,
      AgeFor: ageFor,
      UseGroupDays: useGroupDays,
      ChosenMonth: chosenMonth,
      IncludeReservedCarsOption: includeReservedCarsOption
    }

    let url = `${this.constants.backEndBaseURL}/api/Stock/GetStockSiteRows`
    return this.http.post(url, parms);
  }

  public getStockMerchandisingRows(franchises: string): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Stock/GetStockMerchandisingRows?franchises=${franchises}`
    return this.http.get(url);
  }

  public getStockModalRows(chosenReport: string, ageFor: string, siteIds: string, agedOver: number, useGroupDays: boolean, franchises: string): Observable<any> {
    let parms: StockModalRowParms = {
      ChosenReport: chosenReport,
      SiteIds: siteIds,
      AgedOver: agedOver,
      UseGroupDays: useGroupDays,
      Franchises: franchises,
      AgeFor: ageFor,
    }
    let url = `${this.constants.backEndBaseURL}/api/Stock/GetStockModalRows?agedOver=${agedOver}&siteIds=${siteIds}&useGroupDays=${useGroupDays}&chosenReport=${chosenReport}&franchises=${franchises}`
    return this.http.post(url, parms);
  }

  public getUsedMerchStockModalRows(chosenReport: string, siteIds): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Stock/GetStockModalMerchRows?siteIds=${siteIds}&chosenReport=${chosenReport}`
    return this.http.get(url);
  }

  public getVehiclesAwaitingPrepModal(parms: GetVehiclesAwaitingPrepModalParams): Observable<any> {

    // let url = `${this.constants.backEndBaseURL}/api/Stock/GetVehiclesAwaitingPrepModal?siteIds=${siteIds}`
    // return this.http.get(url);

    let url: string = `${this.constants.backEndBaseURL}/api/Stock/GetVehiclesAwaitingPrepModal`;
    return this.http.post(url, parms);
  }

  public getComments(stockNumbers: string[], dealIds: number[]) {

    let parms = { StockNumbers: stockNumbers.join(','), DealIds: dealIds.join(',') }

    let url = `${this.constants.backEndBaseURL}/api/Comments/GetComments`
    return this.http.post(url, parms);
  }

  public getSalesActivity(params: SalesActivityParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/Deals/GetSalesActivities`;
    return this.http.get(url, {
      params: {
        vehicleType: params.vehicleType,
        monthYear: params.monthYear
      }
    });
  }

  public getAlcopaSiteRows(params: AlcopaParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/Alcopa/GetSiteRows`;
    return this.http.post(url, params);
  }

  public getAlcopaPersonRows(params: AlcopaParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/Alcopa/GetPersonRows`;
    return this.http.post(url, params);
  }

  public getSalesActivityForSite(params: SalesActivityParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/Deals/GetSalesActivitiesForSite`;
    return this.http.get(url, {
      params: {
        siteIds: params.siteIds,
        vehicleType: params.vehicleType,
        monthYear: params.monthYear,
        salesManagerId: params.salesManagerId
      }
    });
  }

  public getUpsells(params: UpsellsParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/Upsell/GetUpsells`;
    return this.http.get(url, {
      params: {
        siteIds: params.siteIds,
        monthYear: params.monthYear
      }
    });
  }

  public getGDPR(params: GDPRParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/Deals/GetGDPRs`;
    return this.http.get(url, {
      params: {
        orderTypeIds: params.orderTypeIds,
        monthYear: params.monthYear
      }
    });
  }

  public getGDPRForSite(params: GDPRParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/Deals/GetGDPRsForSite`;
    return this.http.get(url, {
      params: {
        siteIds: params.siteIds,
        orderTypeIds: params.orderTypeIds,
        monthYear: params.monthYear,
        salesManagerId: params.salesManagerId
      }
    });
  }


  public getSalesmanEfficiencyRows(params: SalesmanEfficiencyParams) {
    let url = `${this.constants.backEndBaseURL}/api/Deals/GetSalesmanEfficiencyRows`
    return this.http.post(url, params);
  }

  public getSiteCompare(department: string, showMargin: boolean, timePeriod: string): any {
    const observable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/Dashboard/GetSiteCompare?department=${department}&showMargin=${showMargin}&timePeriod=${timePeriod}`).subscribe((result: any) => {

        observer.next(result);
        observer.complete();

      }, e => {
        observer.error('failed');
        console.error('error getting site compare ' + JSON.stringify(e))
      })

    })

    return observable
  }

  public getSiteCompareTodayMap(department: string): any {
    const observable = new Observable(observer => {

      this.http.get(`${this.constants.backEndBaseURL}/api/Dashboard/GetSiteCompareTodayMap?department=${department}`).subscribe((result: any) => {

        observer.next(result);
        observer.complete();

      }, e => {
        observer.error('failed');
        console.error('error getting site compare today map' + JSON.stringify(e))
      })

    })

    return observable
  }

  public getEvhcSummary(siteIds: string): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Dashboard/GetEvhcSummary?siteIds=${siteIds}`
    return this.http.get(url);
  }


  public getCitNowSummary(siteIds: string): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Dashboard/GetCitNowSummary?siteIds=${siteIds}`
    return this.http.get(url);
  }


  // New VoC endpoints
  public getVocSiteRows(month: Date, isSales: boolean) {
    let url = `${this.constants.backEndBaseURL}/api/Voc/GetVocSiteRows`;
    return this.http.get(url, {
      params: {
        month: month.toISOString(),
        isSales: isSales.toString()
      }
    });
  }

  public getVocStatCards(siteId: number, month: Date, isSales: boolean) {
    let url = `${this.constants.backEndBaseURL}/api/Voc/GetVocStatCards`;
    return this.http.get(url, {
      params: {
        siteId: siteId.toString(),
        month: month.toISOString(),
        isSales: isSales.toString()
      }
    });
  }

  public getOrderbookRows(params: OrderbookParams) {
    let url = `${this.constants.backEndBaseURL}/api/Orderbook/GetOrderbookRows`;
    return this.http.post(url, params);
  }

  // public getFleetOrderbookRows(brand: string, includeHidden:boolean) {

  //   let url = `${this.constants.backEndBaseURL}/api/FleetOrderbook/GetFleetOrderbookRows?brand=${brand}&includeHidden=${includeHidden}`;
  //   return this.http.get(url);
  // }


  public getStockListRows(params: StockListParams) {
    let url = `${this.constants.backEndBaseURL}/api/Stock/GetStockListRows`;
    return this.http.post(url, params);
  }

  public getDistrinetRows(params: DistrinetParams) {
    let url = `${this.constants.backEndBaseURL}/api/Distrinet/GetDistrinetRows`;
    return this.http.post(url, params);
  }

  public getDistrinetModalItem(distrinetItemId: number, originType: string) {
    let url = `${this.constants.backEndBaseURL}/api/Distrinet/GetDistrinetModalData?distrinetItemId=${distrinetItemId}&originType=${originType}`;
    return this.http.get(url);
  }

  public getStockListRowItem(stockItemId: number) {
    let url = `${this.constants.backEndBaseURL}/api/Stock/GetStockListRowItem?stockItemId=${stockItemId}`;
    return this.http.get(url);
  }

  public getLastThreeStockCheckScans(params: StockCheckScansParams) {
    let url = `${this.constants.backEndBaseURL}/api/Stock/GetLastThreeStockCheckScans`;
    return this.http.post(url, params);
  }

  public addNewComment(params: NewCommentParams) {
    let url = `${this.constants.backEndBaseURL}/api/Comments/AddComment?commentText=${params.Text}&commentStockNumber=${params.StockNumber}&dealId=${params.DealId}`;
    return this.http.get(url);
  }

  public deleteComment(commentId: number) {
    let url = `${this.constants.backEndBaseURL}/api/Comments/DeleteComment?commentId=${commentId}`;
    return this.http.get(url);
  }
  public updateComment(commentId: number, text: string) {
    let url = `${this.constants.backEndBaseURL}/api/Comments/UpdateComment?commentId=${commentId}&text=${text}`;
    return this.http.get(url);
  }



  // Service sales
  getServiceSiteRows(timePeriod: string, channelsString: string[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Service/GetServiceSiteRows`;
    return this.http.get(url, {
      params: {
        timePeriod: timePeriod,
        channelsString: channelsString.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  getServiceDailySales(channelsString: string[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Service/GetServiceDailySales`;
    return this.http.get(url, {
      params: {
        channelsString: channelsString.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  getPartsDailySales(channelsString: string[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Parts/GetPartsDailySales`;
    return this.http.get(url, {
      params: {
        channelsString: channelsString.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  // Dashboard tiles - Service

  // Sales position
  getServiceVsTarget(siteIds: number[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Dashboard/GetServiceVsTarget`;
    return this.http.get(url, {
      params: {
        siteIds: siteIds.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  // Sales by day
  getServiceDailyDoneVsTarget(siteIds: number[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Dashboard/GetServiceDailyDoneVsTarget`;
    return this.http.get(url, {
      params: {
        siteIds: siteIds.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  // Sales chase chart
  getServiceDailyChaseChartData(siteIds: number[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Dashboard/GetServiceDailyChaseChartData`;
    return this.http.get(url, {
      params: {
        siteIds: siteIds.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  // Sales by type
  getServiceChannelSplits(siteIds: number[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Dashboard/GetServiceChannelSplits`;
    return this.http.get(url, {
      params: {
        siteIds: siteIds.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  // WIP summary
  getWipSummary(siteIds: number[]) {
    let url = `${this.constants.backEndBaseURL}/api/Dashboard/GetWipSummary`;
    return this.http.get(url, {
      params: {
        siteIds: siteIds.toString(),
      }
    });
  }

  // Run rate and requirement
  getServiceRunRate(siteIds: number[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Dashboard/GetServiceRunRate`;
    return this.http.get(url, {
      params: {
        siteIds: siteIds.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  // Debtors page
  public getDebtsSitesSummary(params: DebtsParams) {
    let url = `${this.constants.backEndBaseURL}/api/Debtors/GetSiteRows`;
    return this.http.get(url, {
      params: {
        asAtMonthEnd: params.asAtMonthEnd,
        ageOnDueDate: params.ageOnDueDate
      }
    });
  }

  // Run rate and requirement
  getPartsRunRate(siteIds: number[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Parts/GetPartsRunRate`;
    return this.http.get(url, {
      params: {
        siteIds: siteIds.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  public getDebtsDebtsDetailSummary(params: DebtsParams) {
    let url = `${this.constants.backEndBaseURL}/api/Debtors/GetDebtsDetailSummary`;
    return this.http.get(url, {
      params: {
        asAtMonthEnd: params.asAtMonthEnd,
        ageOnDueDate: params.ageOnDueDate,
        siteIds: params.siteIds
      }
    });
  }

  public getDebtsBonusDetailSummary(params: DebtsParams) {
    let url = `${this.constants.backEndBaseURL}/api/Debtors/GetBonusDetailSummary`;
    return this.http.get(url, {
      params: {
        asAtMonthEnd: params.asAtMonthEnd,
        ageOnDueDate: params.ageOnDueDate,
        siteIds: params.siteIds
      }
    })
  }

  // WIP
  public getWipSiteRows(asAtMonthEnd: boolean) {
    let url = `${this.constants.backEndBaseURL}/api/Wips/GetSiteRows`;
    return this.http.get(url, {
      params: {
        asAtMonthEnd: asAtMonthEnd.toString()
      }
    });
  }

  public getWipsDetails(params: WipDetailParams) {
    let url = `${this.constants.backEndBaseURL}/api/Wips/GetWipsDetails`;
    return this.http.get(url, {
      params: {
        asAtMonthEnd: params.asAtMonthEnd.toString(),
        includeZeroWips: params.includeZeroWips.toString(),
        siteIds: params.siteIds.toString()
      }
    });
  }



  // Parts sales
  getPartsSiteRows(timePeriod: string, channels: string[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Parts/GetPartsSiteRows`;
    return this.http.get(url, {
      params: {
        timePeriod: timePeriod,
        channels: channels.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  // Dashboard tiles - Parts

  // Sales position
  getPartsVsTarget(siteIds: number[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Parts/GetPartsVsTarget`;
    return this.http.get(url, {
      params: {
        siteIds: siteIds.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  // Sales by day
  getPartsDailyDoneVsTarget(siteIds: number[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Parts/GetPartsDailyDoneVsTarget`;
    return this.http.get(url, {
      params: {
        siteIds: siteIds.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  // Sales chase chart
  getPartsDailyChaseChartData(siteIds: number[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Parts/GetPartsDailyChaseChartData`;
    return this.http.get(url, {
      params: {
        siteIds: siteIds.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  // Sales by type
  getPartsChannelSplits(siteIds: number[], monthCommencing: Date) {
    let url = `${this.constants.backEndBaseURL}/api/Parts/GetPartsChannelSplits`;
    return this.http.get(url, {
      params: {
        siteIds: siteIds.toString(),
        monthCommencing: monthCommencing.toISOString()
      }
    });
  }

  getPartsStockByPartByCover(stockFamilies: string, stockGroups: string): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/PartsStock/GetPartsStockByPartByCover`;
    return this.http.get(url, {
      params: {
        stockFamilies: stockFamilies,
        stockGroups: stockGroups
      }
    });
  }

  getPartsStockAgeBySite(stockFamilies: string, stockGroups: string): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/PartsStock/GetPartsStockAgeBySite`;
    return this.http.get(url, {
      params: {
        stockFamilies: stockFamilies,
        stockGroups: stockGroups
      }
    });
  }

  // Used stock summary (Spain)
  getSpainUsedStockSummary(params: SpainUsedStockParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Stock/GetSpainUsedStockSummary`;
    return this.http.post(url, params);
  }

  getDashboardInvoicedDealsSpain(params: DashboardDataFrameParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/DealLatest/GetDealLatests`;
    return this.http.post(url, params);
  }

  getDashboardVNOrders(params: DashboardDataVNParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Distrinet/GetDashboardVNOrders`;
    return this.http.post(url, params);
  }
  getDashboardVNStocksAndBook(params: DashboardDataVNParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Distrinet/GetDashboardVNStocksAndBook`;
    return this.http.post(url, params);
  }


  // Used stock ageing summary (Spain)
  getSpainUsedStockAgeingSummary(params: SpainUsedStockParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Stock/GetSpainUsedStockAgeingSummary`;
    return this.http.post(url, params);
  }

  getStockModalRowsSpain(params: SpainUsedStockModalParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Stock/GetStockModalRowsSpain`;
    return this.http.post(url, params);
  }

  getCommissionAdjustments(salesExecId: string, datetime: string): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/CommissionAdjustment/GetAll?salesExecId=${salesExecId}&datetime=${datetime}`;
    return this.http.get(url);
  }

  createCommissionAdjustment(params: CommissionAdjustmentVM): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/CommissionAdjustment/Create`;
    return this.http.post(url, params);
  }

  updateCommissionAdjustment(params: CommissionAdjustmentVM[]): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/CommissionAdjustment/Update`;
    return this.http.post(url, params);
  }
  deleteCommissionAdjustment(Id: number): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/CommissionAdjustment/Delete?Id=${Id}`;
    return this.http.get(url);
  }

  // Daily orders vs last year (Spain)
  getDailyOrdersVsLastYear(parms: DailyOrdersVsLastYearParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Dashboard/GetDailyOrdersVsLastYear`;
    return this.http.post(url, parms);
  }

  // Sales Exec Performance Review
  // Site summary by measure
  getSEReviewSiteSummaryByMeasure(month: Date): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/SalesExecReview/GetSiteSummaryByMeasure`;
    return this.http.post(url, {
      Month: month
    });
  }

  // Site summary by month
  getSEReviewSiteSummaryByMonth(measureName: string): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/SalesExecReview/GetSiteSummaryByMonth`;
    return this.http.post(url, {
      MeasureName: measureName
    });
  }

  // People summary by measure
  getSEReviewPeopleSummaryByMeasure(month: Date, siteIds: number[]): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/SalesExecReview/GetPersonSummaryByMeasure`;
    return this.http.post(url, {
      Month: month,
      SiteIds: siteIds
    });
  }

  // Site summary by month
  getSEReviewPeopleSummaryByMonth(measureName: string, siteIds: number[]): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/SalesExecReview/GetPersonSummaryByMonth`;
    return this.http.post(url, {
      MeasureName: measureName,
      SiteIds: siteIds
    });
  }

  // Get form
  getSEReviewForm(params: GetSEReviewFormParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/SalesExecReview/GetForm`;
    return this.http.post(url, params);
  }

  // Refresh form
  refreshSEReviewForm(params: GetSEReviewFormParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/SalesExecReview/RefreshForm`;
    return this.http.post(url, params);
  }

  // Save form
  saveSEReviewForm(params: SaveSEReviewFormParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/SalesExecReview/SaveForm`;
    return this.http.post(url, params);
  }

  // Update form approval state
  updateSEReviewFormApprovalState(params: UpdateSEReviewFormApprovalStateParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/SalesExecReview/UpdateApprovalState`;
    return this.http.post(url, params);
  }


  //fcst
  getAllAvailableForecasts(): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/AllForecasts`;
    return this.http.get(url);
  }
  getAllDepartments(siteId: number): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/AllDepartments?siteId=${siteId}`;
    return this.http.get(url);
  }
  getLiveForecast(params: CreateNewVersionParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/All`;
    return this.http.get(url, {
      params: {
        siteId: params.SiteId.toString(),
        departmentId: params.DepartmentId.toString(),
        forecastId: params.ForecastId.toString(),
        forecastVersionId: params.ForecastVersionId.toString()
      }
    });
  }

  saveLiveForecast(parms: SaveLiveForecast): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/Save`;
    return this.http.post(url, parms);
  }

  getForecastVersions(siteId: number, forecastId: number): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/ForecastVersions?siteId=${siteId}&forecastId=${forecastId}`;
    return this.http.get(url);
  }

  UpdateForecastVersionApprovalState(params: ApprovalStateUpdateParams) {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/UpdateForecastVersionApprovalState`;
    return this.http.get(url, {
      params: {
        forecastId: params.ForecastId.toString(),
        forecastVersionId: params.ForecastVersionId.toString(),
        forecastApprovalStates: params.ForecastApprovalStates.toString()
      }
    });
  }

  recalculateForecast(forecastInput: ForecastInput) {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/Recalculate`;
    return this.http.post(url, forecastInput);
  }
  refreshForecast(forecastRefresh: ForecastRefresh) {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/Refresh`;
    return this.http.post(url, forecastRefresh);
  }

  getExcelDownloadDetail(month: string): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/ForecastExcelDownload`;
    return this.http.get(url, {
      params: {
        forecastDate: month
      }
    });
  }

  getForecastStatus(month: string, forecastId: number): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/ForecastStatus`;
    return this.http.get(url, {
      params: {
        month: month,
        forecastId: forecastId.toString()
      }
    });
  }

  getForecastReview(month: string): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/ForecastReview`;
    return this.http.get(url, {
      params: {
        month: month
      }
    });
  }

  renameVersion(forecastVersionId: string, newVersionName: string) {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/RenameVersion`;
    return this.http.get(url, {
      params: {
        forecastVersionId: forecastVersionId,
        newVersionName: newVersionName
      }
    });
  }

  clearAllForecastVersions(params: ClearForecastVersionsParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/LiveForecast/ClearAllForecastVersions`;
    return this.http.post(url, params);
  }

  getMostRecentDateInDailyOrders(): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Dashboard/GetMostRecentDateInDailyOrders`;
    return this.http.get(url);
  }

  getBonusSummaryForPerson(params: CommissionQualificationParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Commissions/GetBonusSummary`;
    return this.http.post(url, params);
  }

  getBonusSummaryForSites(params: CommissionQualificationParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Commissions/GetBonusSummariesForSite`;
    return this.http.post(url, params);
  }

  getBonusSummaryForPeopleRows(params: BonusForPeopleRowsParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Commissions/GetBonusSummariesForPeople`;
    return this.http.post(url, params);
  }

  setCommissionQualificationForPerson(params: CommissionQualificationParams): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/Commissions/SetCommissionQualificationForPerson`;
    return this.http.post(url, params);
  }

  getCommissionBusinessManagerRows(chosenMonth: Date): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/Commissions/GetCommissionBusinessManagerRows`;
    return this.http.get(url, {
      params: {
        monthStart: chosenMonth.toISOString()
      }
    });
  }
  getLBDMRows(chosenMonth: Date): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/Commissions/GetCommissionLBDMRows`;
    return this.http.get(url, {
      params: {
        monthStart: chosenMonth.toISOString()
      }
    });
  }




  getPriceChanges(params: GetPriceChangesParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetPriceChanges`;
    return this.http.post(url, params);
  }
  setPriceChanges(params: SetPriceChangesParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/SetPriceChanges`;
    return this.http.post(url, params);
  }
  // getPriceChangesOld(params: GetPriceChangesParamsOld): Observable<any> {
  //   let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetPriceChangesOld`;
  //   return this.http.post(url, params);
  // }


  getOptOuts(params: GetOptOutsParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetOptOuts`;
    return this.http.post(url, params);
  }



  saveNewPricingRuleSet(params: PricingRuleSetSaveParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/PricingRuleSet/SaveNewPricingRuleSet`;
    return this.http.post(url, params);
  }

  getWebsiteListingPerformance(params: any): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetWebsiteListingPerformance`;
    return this.http.get(url, {
      params: {
        // ToDo
      }
    });
  }

  getPricing(params: any): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetPricing`;
    return this.http.get(url, {
      params: {
        // ToDo
      }
    });
  }

  getVehicleOptOutStatus(vehicleAdvertId: number): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetVehicleOptOutStatus?vehicleAdvertId=${vehicleAdvertId}`;
    return this.http.get(url)
  }

  optOut(params: VehicleOptOutParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/CreateOrUpdateVehicleOptOut`;
    return this.http.post(url, params);
  }

  getVehicleAdvertWithRatings(params: GetVehicleAdvertsWithRatingsParams): Promise<VehicleAdvertWithRatingDTO[]> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetVehicleAdvertsWithRatings`;
    return this.http.post<VehicleAdvertWithRatingDTO[]>(url, params).toPromise();
  }

  updateStockPrice(params: UpdatePriceParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/UpdateStockPrice?vehicleAdvertId=${params.vehicleAdvertId}&newPrice=${params.newPrice}`;
    return this.http.get(url);
  }

  bulkUpdateStockPrice(params: UpdatePriceParams[]): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/BulkUpdateStockPrices`;
    return this.http.post(url, params);
  }

  getAftersalesDatasets(params: AftersalesDatasetsParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/AftersalesDatasets/GetAftersalesDatasets`;
    return this.http.get(url, {
      params: {
        dataset: params.Dataset,
        month: params.Month.toISOString()
      }
    });
  }

  public getPerformingTrends(params: PerformanceTrendsParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/Deals/GetPerformanceTrends`;
    return this.http.post(url, params);
  }
  public getPerformingTrendsForSite(params: PerformanceTrendsParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/Deals/GetPerformanceTrendsForSite`;
    return this.http.post(url, params);
  }

  getAutoPriceCompetitorAnalysis(
    advertid: number,
     websiteSearchIdentifier: string,
     retailerSiteRetailerId: number,
     userChoiceParams: CompetitorSearchUserChoices,
     ourVehicleParams: CompetitorSearchOurVehicle,
    postcode:string): Observable<any> {

    const url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetAdvertModalCompetitorAnalysis`;
    const parms: GetCompetitorAnalysisParams = {
      AdvertId: advertid,
      WebsiteSearchIdentifier: websiteSearchIdentifier,
      RetailerSiteRetailerId: retailerSiteRetailerId,
      UserChoiceParams: userChoiceParams,
      OurVehicleParams: ourVehicleParams
    };
    return this.http.post(url, parms);
  }


  getAutoPriceCompetitorAnalysisForNewValuation(parms: GetValuationModalCompetitorAnalysisParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetValuationModalCompetitorAnalysis`;

    return this.http.post(url, parms);
  }



  getAutoPriceInsightModalForAdId(adId: number): Observable<any> {
    const url = `${this.constants.backEndBaseURL}/api/AutoPrice/GetAdvertModalForAdId?advertId=${adId}`;
    return this.http.get(url);
  }

  getAutoPriceEstimatedDayToSellAndPriceIndicator(parms: GetEstimatedDayToSellAndPriceIndicatorParams): Observable<any> {
    parms.FirstRegisteredDate = new Date(parms.FirstRegisteredDate);
    const url = `${this.constants.backEndBaseURL}/api/AutoPrice/GetEstimatedDayToSellAndPriceIndicator`;
    return this.http.post(url, parms);
  }

  public getDashboardDataSet(parms: GetPricingDashboardParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetDashboardDataSet`;
    return this.http.post(url, parms);
  }

  public getVehicleAdvertsWithRatings(params: GetVehicleAdvertsWithRatingsParams) {

    const observable = new Observable(observer => {

      let url: string = `${this.constants.backEndBaseURL}/api/Dashboard/GetDashboardDataSpainOverview`
      this.http.post(url, params).subscribe((res: VehicleAdvertWithRating[]) => {

        res.map(x => {
          x.ThisVehicleValnVsAverage = x.ValuationAdjRetail - x.ValuationMktAvRetail
          x.todayPriceChange = x.NewPrice === 0 ? 0 : x.NewPrice - x.AdvertisedPrice
          x.siteModelClean = `${x.RetailerSiteName}|${x.ModelCleanedUp}`
          x.count = 1
        })

        observer.next(res);
        observer.complete();
      })

    })
    return observable
  }


  getAnalysisDimensions(pageName: string): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetAnalysisDimensions?pageName=${pageName}`;
    return this.http.get(url);
  }



  createStrategyVersion(sv: StrategyVersionVM): Observable<any> {
    const url = `${this.constants.backEndBaseURL}/api/AutoPriceStrategy/CreateStrategyVersion`;
    return this.http.post(url, sv);
  }

  saveStrategyVersion(sv: StrategyVersionVM): Observable<any> {
    const url = `${this.constants.backEndBaseURL}/api/AutoPriceStrategy/SaveStrategyVersion`;
    return this.http.post(url, sv);
  }

  deleteStrategyVersion(sv: StrategyVersionVM): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceStrategy/DeleteStrategyVersion?strategyVersionId=${sv.Id}`;
    return this.http.get(url);
  }

  saveStrategy(strategy: StrategyFull): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceStrategy/SaveStrategy`;
    return this.http.post<number|null>(url, strategy);
  }

  deleteStrategy(strategyId: number): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceStrategy/DeleteStrategy?strategyId=${strategyId}`;
    return this.http.get(url);
  }

  getAllStrategies(): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceStrategy/GetAllStrategies`;
    return this.http.get(url);
  }
  getAllPricingPolicies(): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceStrategy/GetAllPricingPolicies`;
    return this.http.get(url);
  }
  getAllStrategyFieldNames(): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceStrategy/GetAllStrategyFieldNames`;
    return this.http.get(url);
  }
  getStrategy(strategyId: number): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceStrategy/GetStrategy?strategyId=${strategyId}`;
    return this.http.get(url);
  }

  recalculateTestStrategyPrices():Promise<void>{
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceStrategy/RecalculateTestStrategyImpacts`;
    return this.http.get<void>(url).toPromise();
  }

  recalculateTestStrategyDaysToSell():Promise<void>{
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceStrategy/RecalculateTestStrategyDaysToSell`;
    return this.http.get<void>(url).toPromise();
  }

  getAllFacets(generation: AtGenerationItem):Promise<TaxonomyFacetAndChoices[]>{
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceTaxonomy/GetAllFacets?generationId=${generation.generationId}`;
    return this.http.get<TaxonomyFacetAndChoices[]>(url).toPromise();
  }
  getNewValuationModal(parms: GetNewVehicleModalParams):Promise<NewVehicleValuationModal>{
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetNewValuationModal`;
    return this.http.post<NewVehicleValuationModal>(url,parms).toPromise();
  }



  getDerivatives(generation: AtGenerationItem, choices:TaxonomyFacetAndChoices[] ):Promise<AtDerivativeItem[]>{
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceTaxonomy/GetDerivatives?generationId=${generation.generationId}`;
    const parms:GetDerivativesParams = {generationId:generation.generationId,choices:choices};
    return this.http.post<AtDerivativeItem[]>(url,parms).toPromise();
  }
  getVehicleTypes():Promise<string[]>{
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceTaxonomy/GetVehicleTypes`;
    return this.http.get<string[]>(url).toPromise();
  }
  getMakes(vehicleType:string):Promise<AtMakeItem[]>{
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceTaxonomy/GetMakes?vehicleType=${vehicleType}`;
    return this.http.get<AtMakeItem[]>(url).toPromise();
  }
  getModels(make:AtMakeItem):Promise<AtModelItem[]>{
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceTaxonomy/GetModels?makeId=${make.makeId}`;
    return this.http.get<AtModelItem[]>(url).toPromise();
  }
  getGenerations(model:AtModelItem):Promise<AtGenerationItem[]>{
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceTaxonomy/GetGenerations?modelId=${model.modelId}`;
    return this.http.get<AtGenerationItem[]>(url).toPromise();
  }


  getDashboardDataSetBySite(): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetDashboardDataSetBySite/`;
    return this.http.get(url);
  }
  saveNewVehicleAdvertComment(params: VehicleAdvertNewCommentParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleAdvert/AddComment`;
    return this.http.post(url, params);
  }

  deleteVehicleAdvertComment(commentId: number) {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleAdvert/DeleteComment?commentId=${commentId}`;
    return this.http.get(url);
  }

  getLocationOptimiserAdverts(params: GetLocationOptimiserAdvertsParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetLocationOptimiserAdverts`;
    // if (params.advertId) {
    //   url += `?advertId=${params.advertId}`;
    // }
    return this.http.post(url, params);
  }

  getVehicleHistory(params: GetVehicleHistoryParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetVehicleHistory`;
    return this.http.post(url, params);
  }

  getLeavingItemsLastSixMonths(params: GetLeavingVehicleItemsForModalParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetLeavingVehicleItemsForModel`;
    return this.http.post(url, params);
  }

  getStockCover(params: GetLeavingVehicleItemsForModalParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetStockCover`;
    return this.http.post(url, params);
  }

  getSameModelAdverts(params: GetSameModelAdvertsParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetSameModelAdverts`;
    return this.http.post(url, params);
  }

  getLatestVehicleValuationSummaryForAdvert(advertId: number): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetLatestVehicleValuationSummaryForAdvert?advertId=${advertId}`;
    return this.http.get(url);
  }

  getExistingValuationForAdvert(batchId: number) {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetExistingValuationForAdvert?batchId=${batchId}`;
    return this.http.get(url).toPromise();
  }

  getOptionChangeImpact(parms: GetValuationPriceSetChangeParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetOptionChangeImpact`;
    return this.http.post(url, parms).toPromise();
  }


  async getNewValuationForAdvert(params: GetValuationPriceSetParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetNewValuationForAdvert`;
    return this.http.post(url, params).toPromise();
  }
  async getRecallStatus(reg: string) {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetVehicleRecallStatus?reg=${reg}`;
    return this.http.get(url).toPromise();
  }

  getVehicleInformation(vehicleReg: string, mileage: number, batchId: number): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetVehicleInformation?vehicleReg=${vehicleReg}&mileage=${mileage}&batchId=${batchId ?? 0}`;
    return this.http.get(url);
  }


  getValuationModalNew(parms: GetValuationModalNewParams): Observable<any> {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetValuationModalNew`;
    return this.http.post(url, parms);
  }

  async getVehicleSpecOptions(params: GetVehicleSpecOptionsParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetVehicleSpecOptions`;
    return this.http.post<VehicleSpecOption[]>(url, params).toPromise();
  }

  async getLeavingVehicleItems(params: GetLeavingVehicleItemsParams): Promise<LeavingVehicleItem[]> {
    let url: string = `${this.constants.backEndBaseURL}/api/LeavingVehicles/GetLeavingVehicleItems`;
    return this.http.post<LeavingVehicleItem[]>(url, params).toPromise();
  }
 
  async getStrategyPriceBuildUpDetailItems(chosenDate: Date): Promise<StrategyPriceBuildUpDetailItem[]> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPriceStrategy/GetStrategyPriceBuildUpDetailItems`;
    const params = new HttpParams().set('chosenDate', chosenDate.toISOString());

  return this.http.get<StrategyPriceBuildUpDetailItem[]>(url, { params }).toPromise();
  }

  saveVehicleSpecBuildForAdvert(params: ValuationResultForAdvertToSave) {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/SaveVehicleSpecBuildForAdvert`;
    return this.http.post(url, params);
  }

  saveVehicleValuation(params: ValuationResultForNewVehicleToSave) {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/SaveVehicleValuation`;
    return this.http.post(url, params);
  }

  saveImportMask(importMask: ImportMaskToSave, id?: number): Observable<any> {
    let url = `${this.constants.backEndBaseURL}/api/ImportMasks/SaveImportMask`;
    return this.http.post(url, importMask);
  }

  getVehicleSpecBuildForAdvert(advertId: number) {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetPreviousValuationResultForAdvert?advertId=${advertId}`;
    return this.http.get(url);
  }

  deleteVehicleSpecBuildForAdvert(advertId: number) {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/DeleteVehicleSpecBuildForAdvert?advertId=${advertId}`;
    return this.http.get(url);
  }

  getLocalBargains(chosenDate: string) {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetLocalBargains?chosenDate=${chosenDate}`;
    return this.http.get(url);
  }

  async getExampleItems(parms:GetExampleItemsParams):Promise<ExampleItem[]> {
    let url: string = `${this.constants.backEndBaseURL}/api/ExampleItems/GetItems`;
    return  this.http.post<ExampleItem[]>(url,parms).toPromise();
  }
  async getSimpleExampleItems(parms:GetExampleItemsParams):Promise<SimpleExampleItemForClient[]> {
    let url: string = `${this.constants.backEndBaseURL}/api/ExampleItems/GetSimpleItems`;
    return  this.http.post<SimpleExampleItemForClient[]>(url,parms).toPromise();
  }

  async saveSimpleExampleItems(itemsToSave: ExampleItemForServer[]):Promise<ExampleItemForServer[]> {
    let url: string = `${this.constants.backEndBaseURL}/api/ExampleItems/SaveItems`;
    return  this.http.post<ExampleItemForServer[]>(url,itemsToSave).toPromise();
  }

  async getExampleItemCostPriceTotal(parms:UpdateCostPriceTotalParams):Promise<number> {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetExampleItemCostPriceTotal`;
    return  this.http.post<number>(url,parms).toPromise();
  }

  async getExampleItemCostPriceHalved(costPrice:number):Promise<number> {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetExampleItemCostPriceHalved?costPrice=${costPrice}`;
    return  this.http.get<number>(url).toPromise();
  }

  async getStockLevelAdjustment(model: string, retailerSiteId: number) {
    let url: string = `${this.constants.backEndBaseURL}/api/VehicleValuation/GetStockLevelAdjustment?model=${model}&retailerSiteId=${retailerSiteId}`;
    return this.http.get(url).toPromise();
  }
  getSitesSettings(): Observable<SiteSettings[]> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/GetSitesSettings`;
    return this.http.get<SiteSettings[]>(url);
  }

  saveSiteSettings(params: SaveSiteSettingsParams): Promise<void> {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/SaveSiteSettings`;
    return this.http.post<void>(url, params).toPromise();
  }

  saveSiteStrategy(params: RetailerSiteStrategyVersion) {
    let url: string = `${this.constants.backEndBaseURL}/api/AutoPrice/SaveSiteStrategy`;
    return this.http.post(url, params);
  }

  getOrdersForSalesIncentive() {
    let url: string = `${this.constants.backEndBaseURL}/api/Deals/GetOrdersForSalesIncentive`;
    return this.http.get(url);
  }

  getTotalAdvertsForAdvertiser(hoveredItem: CompetitorVehicle) {
    let url: string = `${this.constants.backEndBaseURL}/api/Autoprice/GetTotalAdvertsForAdvertiser?advertiserName=${hoveredItem.CompetitorName}&advertiserId=${hoveredItem.CompetitorId}`;
    return this.http.get(url);
  }

  getUsageItems(parms: GetUsageItemsParams) {
    let url: string = `${this.constants.backEndBaseURL}/api/User/GetUsageItems`;
    return this.http.post(url, parms);
  }

   getCompetitorAnalysisFacets(retailerSiteRetailerId: number, derivativeId: string): Promise<any> {
      const url = `${this.constants.backEndBaseURL}/api/AutoPrice/RetailerSiteRetailer/${retailerSiteRetailerId}/Derivative/${derivativeId}/DerivativeFacets`;
      return this.http.get(url).toPromise();
   }

}
