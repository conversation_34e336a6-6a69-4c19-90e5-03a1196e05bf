import { Injectable } from "@angular/core";
import { CPHAutoPriceColDef } from "src/app/model/CPHColDef";
import { GlobalParamKey } from "src/app/model/GlobalParam";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { AutopriceRendererService } from "src/app/services/autopriceRenderer.service";
import { AutotraderService } from "src/app/services/autotrader.service";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { GlobalParamsService } from "src/app/services/globalParams.service";
import { SelectionsService } from "src/app/services/selections.service";

@Injectable({
  providedIn: 'root'
})

export class LeavingVehicleColDefsService {
 

  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public selectionsService: SelectionsService,
    public globalParmsService: GlobalParamsService,
    public getDataService: GetDataMethodsService,
    private autopriceRendererService: AutopriceRendererService,
    public gridHelpersService: AGGridMethodsService,
  ) {

  }

 

 public provideColDefsLeavingVehicleDetail(): CPHAutoPriceColDef[] {
      // Check if the default retailer site has TrackLeavingVehicles enabled
      const showTrackLeavingVehiclesColumns = this.selectionsService.userRetailerSite?.TrackLeavingVehicles === true;
      const showDidBeatWholesaleTargetColumn = this.globalParmsService.getGlobalParam(GlobalParamKey.ShowDidBeatWholesaleTargetColumn);

      const colDefs = [
         {
            headerName: "Division",
            colId: "Region",
            field: "Region",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Site",
            colId: "RetailerSiteName",
            field: "RetailerSiteName",
            type: "labelSetFilter",
            width: 30,
            columnSection: "Leaving Vehicles",
         },

         {
            headerName: "Reg",
            colId: "VehicleReg",
            field: "VehicleReg",
            minWidth:90,
            type: "label",
            // cellRenderer: (params) =>
            //    this.autopriceRendererService.regPlateRenderer(params, {
            //       extraParams: { noMargin: true },
            //    }),
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Reg Year",
            colId: "RegYear",
            field: "RegYear",
            type: "labelSetFilter",
            minWidth: 70,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Type",
            colId: "Type",
            field: "VehicleType",
            type: "labelSetFilter",
            minWidth: 100,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Make",
            colId: "Make",
            field: "Make",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Model",
            colId: "Model",
            field: "Model",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Derivative",
            colId: "Derivative",
            field: "Derivative",
            type: "label",
            width: 100,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Mileage",
            colId: "Mileage",
            field: "Mileage",
            type: "number",
            minWidth: 70,
            columnSection: "Leaving Vehicles",
            shouldAverageIfValue: true,
         },
         {
            headerName: "Mileage Band",
            colId: "MileageBand",
            field: "MileageBand",
            type: "labelSetFilter",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForMileageBand()
               ),
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            minWidth: 80,
         },
         {
            headerName: "Body Type",
            colId: "BodyType",
            field: "BodyType",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Fuel Type",
            colId: "FuelType",
            field: "FuelType",
            type: "labelSetFilter",
            // cellRenderer: (params) =>
            //    this.autopriceRendererService.addEnergyTypeIcon(params),
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Vehicle Type",
            field: "VehicleType",
            colId: "VehicleType",
            width: 10,
            type: "labelSetFilter",
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Age band",
            colId: "AgeBand",
            field: "AgeBand",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "When Listed",
            colId: "ListedDate",
            field: "ListedDate",
            valueGetter: (params) =>
               !params.data ? null : this.getMonthName(params.data?.ListedDate),
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
            comparator: (valueA, valueB, nodeA, nodeB) => AutotraderService.getSortOrderWhenListed(nodeA, nodeB),
         },
         {
            headerName: "When Listed Date",
            colId: "ListedDate",
            field: "ListedDate",
            type: "date",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "When Removed",
            colId: "RemovedDate",
            field: "RemovedDate",
            valueGetter: (params) =>
               !params.data
                  ? null
                  : this.getMonthName(params.data?.RemovedDate),
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
            comparator: (valueA, valueB, nodeA, nodeB) => AutotraderService.getSortOrderRemovedDate(nodeA, nodeB),
         },
         {
            headerName: "When Removed Date",
            colId: "RemovedDate",
            field: "RemovedDate",
            type: "date",
            width: 10,
            columnSection: "Leaving Vehicles",
         },

         {
            headerName: "Days Listed",
            colId: "DaysListed",
            field: "DaysListed",
            type: "number",
            shouldAverage:true,
            width: 10,
            columnSection: "Leaving Vehicles",
            shouldAverageIfValue: true,
         },
         {
            headerName: "Days Listed Band",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForDaysBand()
               ),
            colId: "DaysListedBand",
            field: "DaysListedBand",
            type: "labelSetFilter",
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            width: 10,
         },
         {
            headerName: "Days On Strategy",
            colId: "DaysOnStrategy",
            field: "DaysOnStrategy",
            type: "number",
            width: 10,
            columnSection: "Leaving Vehicles",
            shouldAverageIfValue: true,
         },
         {
            headerName: "Days Opted Out",
            colId: "DaysOptedOut",
            field: "DaysOptedOut",
            type: "number",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Pct Days Opted Out",
            colId: "PercentDaysOptedOut",
            field: "PercentDaysOptedOut",
            type: "number",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Opted Out Pct Band",
            colId: "OptedOutPctBand",
            field: "OptedOutPctBand",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Was On Strategy",
            colId: "IsOnStrategy",
            field: "IsOnStrategy",
            type: "boolean",
            width: 10,
            columnSection: "Leaving Vehicles",
         },

         {
            headerName: "First Price",
            colId: "FirstPrice",
            field: "FirstPrice",
            type: "currency",
            minWidth: 70,
            columnSection: "Leaving Vehicles",
            shouldAverageIfValue: true,
         },
         {
            headerName: "First Price Position",
            colId: "FirstPP",
            field: "FirstPP",
            type: "percent1dp",
            minWidth: 70,
            columnSection: "Leaving Vehicles",
            shouldAverageIfValue: true,
         },
         {
            headerName: "First Price Position Band",
            colId: "FirstPPBand",
            field: "FirstPPBand",
            type: "labelSetFilter",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForPPBand()
               ),
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            minWidth: 100,
         },
         {
            headerName: "Last Price",
            colId: "LastPrice",
            field: "LastPrice",
            type: "currency",
            minWidth: 70,
            columnSection: "Leaving Vehicles",
            shouldAverageIfValue: true,
         },
         {
            headerName: "Last Valuation",
            colId: "LastValuation",
            field: "LastValuation",
            type: "currency",
            minWidth: 70,
            columnSection: "Leaving Vehicles",
            shouldAverageIfValue: true,
         },
         {
            headerName: "Last Price Band",
            colId: "LastPriceBand",
            field: "LastPriceBand",
            type: "labelSetFilter",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForValueBand()
               ),
               cellRenderer: (params) =>
                  this.autopriceRendererService.autoTraderLozengeRenderer(params),
               minWidth: 100,
            },
            {
               headerName: "Last Price Position",
               colId: "LastPP",
               field: "LastPP",
               type: "percent1dp",
               minWidth: 70,
               columnSection: "Leaving Vehicles",
               shouldAverageIfValue: true,
            },
            {
               headerName: "Last Price Position Band",
               colId: "LastPPBand",
               field: "LastPPBand",
               type: "label",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForPPBand()
               ),
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            minWidth: 100,
         },
         {
            headerName: "Did beat target",
            colId: "DidBeatWholesaleTarget",
            field: "DidBeatWholesaleTarget",
            type: "boolean",
            width: 10,
            columnSection: "Leaving Vehicles",
            hide: !showDidBeatWholesaleTargetColumn,
         },
         {
            headerName: "Price Indicator",
            colId: "LastPriceIndicator",
            field: "LastPriceIndicator",
            type: "labelSetFilter",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForPriceIndicator()
               ),
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderPriceIndicatorRenderer(
                  params
               ),
            minWidth: 100,
         },

         {
            headerName: "Retail Rating",
            colId: "LastRetailRating",
            field: "LastRetailRating",
            type: "number",
            minWidth: 50,
            // cellRenderer: (params) =>
            //    this.autopriceRendererService.autoTraderRetailRatingRenderer({
            //       params: { RetailRating: params.value, InTableCell: true },
            //    }),
            columnSection: "Leaving Vehicles",
            shouldAverageIfValue: true,
         },
         {
            headerName: "Retail Rating Band",
            colId: "RetailRatingBand",
            field: "RetailRatingBand",
            type: "labelSetFilter",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForRetailRatingBand()
               ),
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            width: 10,
         },

         // Just for filters
         //{ headerName: 'Achieved Sale Type', colId: 'AchievedSaleType', field: 'AchievedSaleType', type: 'labelSetFilter', width: 10 ,columnSection: 'Leaving Vehicles'},
         {
            headerName: "Transmission Type",
            colId: "TransmissionType",
            field: "TransmissionType",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },

         {
            headerName: "Initial days to sell estimate",
            colId: "FirstRetailDaysToSell",
            field: "FirstRetailDaysToSell",
            type: "number",
            width: 10,
            columnSection: "Leaving Vehicles",
            shouldAverageIfValue: true,
         },

         // Track Sold Vehicles columns - only visible if TrackLeavingVehicles is enabled
         {
            headerName: "Is Sold",
            colId: "RC_IsSold",
            field: "RC_IsSold",
            type: "boolean",
            width: 10,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer Seller Name",
            colId: "RC_SellerName",
            field: "RC_SellerName",
            type: "label",
            width: 20,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer Seller Segment",
            colId: "RC_Segment",
            field: "RC_Segment",
            type: "label",
            width: 20,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer Days Listed",
            colId: "RC_DaysListed",
            field: "RC_DaysListed",
            type: "number",
            width: 10,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer Valuation",
            colId: "RC_Valuation",
            field: "RC_Valuation",
            type: "currency",
            width: 10,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer Price",
            colId: "RC_Price",
            field: "RC_Price",
            type: "currency",
            width: 10,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer PP%",
            colId: "RC_PPPct",
            field: "RC_PPPct",
            type: "percent1dp",
            width: 10,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer Sold Date",
            colId: "RC_SoldDate",
            field: "RC_SoldDate",
            type: "date",
            width: 15,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Days to sell vs initial estimate",
            colId: "DaysListedVsFirstRetailDaysToSell",
            field: "DaysListedVsFirstRetailDaysToSell",
            type: "number",
            width: 10,
            columnSection: "Leaving Vehicles",
            shouldAverageIfValue: true,
         },
      ];

      return colDefs;
   }



   customSort(valueA: string, valueB: string, rangesOrder: string[]): number {
      return AutotraderService.customSort(valueA, valueB, rangesOrder);
   }

   getMonthName(date: Date): string {
      date = new Date(date);
      return date.toLocaleDateString(this.constants.translatedText.LocaleCode, {
         month: "short",
         year: "2-digit",
      });
   }
}
