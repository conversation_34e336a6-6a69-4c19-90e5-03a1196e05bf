import { Injectable } from "@angular/core";
import { MenuItem, MenuItemNew, MenuSection } from "../model/main.model";
import { TranslatedText } from "../model/translations.model";
import { ConstantsService } from "./constants.service";
import { SelectionsService } from "./selections.service";
import { GlobalParamsService } from "./globalParams.service";
import { GlobalParamKey } from "../model/GlobalParam";
import { SparkEnvironment } from "../model/SparkEnvironment";
import { PageNameRoutes } from "../app-routing.module";

@Injectable({providedIn: 'root'})
export class MenuBuilderService {


   constructor(
      private constants: ConstantsService,
      private selections: SelectionsService,
      private globalParamsService: GlobalParamsService
   ) {
   }

   public buildMenuItemsNew(openItems: string[]): MenuSection[] {
      //---------------- how this works ---------------------

      //each section has subItems
      //each subItem has link
      //each subItem has pageName.  this must be unique

      //-------------------------------------

      const translated: TranslatedText = this.constants.translatedText;
      const env = this.constants.environment;
      //const perms = this.selections.user.permissions;

      let menuItems: MenuSection[] = [];

      const dashboards = this.provideDashboards(env, translated);
      if (dashboards[0].subItems.some((x) => x.visible)) {
         menuItems.push(...dashboards);
      }

      const operationalReports = this.provideOperationalReports(env, translated);
      if (operationalReports[0].subItems.some((x) => x.visible)) {
         menuItems.push(...operationalReports);
      }

      const salesReports = this.provideSalesReport(env, translated);
      if (salesReports[0].subItems.some((x) => x.visible)) {
         menuItems.push(...salesReports);
      }

      if (this.selections.isPageVisible(PageNameRoutes.reportingCentre)) {
         menuItems.push(
            // REPORT PORTAL
            {
               group: "reportPortal",
               name: translated.ReportPortal_Title,
               subItems: [
                  {
                     isSales: null,
                     nameAbbreviated: translated.ReportPortal_Title,
                     name: translated.ReportPortal_Title,
                     icon: "fas fa-file-chart-pie fa-fw",
                     link: "/reportingCentre",
                     pageName: "ReportingCentre",
                     isActive: false,
                     visible: true,
                     parent: "reportPortal",
                  },
               ],
               expanded: null,
            }
         );
      }

      const afterSalesReports = this.provideAfterSalesReport(env, translated);
      if (afterSalesReports[0].subItems.some((x) => x.visible)) {
         menuItems.push(...afterSalesReports);
      }

      const peopleReports = this.providePeopleReports(env, translated);
      if (peopleReports[0].subItems.some((x) => x.visible)) {
         menuItems.push(...peopleReports);
      }

      if (env.sideMenu_hasVehiclePricing && !env.sideMenu_ShowDetailedMenu) {
         menuItems.push(...this.providePricingPages(env));
      }

      //The various separate ones
      if (env.sideMenu_hasVehiclePricing && env.sideMenu_ShowDetailedMenu) {
         menuItems.push(...this.providePricingSeparatePages(env));
      }


      //The various separate ones
      menuItems.push(...this.provideMaintenanceReports(env, translated))

      if (openItems) {
         menuItems.map((item) => {
            item.expanded = openItems.includes(item.name);
            if (item.subItems) {
            }
         });
      }

      //remove group if no subItems are visible
      menuItems = menuItems.filter((item) => item.subItems.some((subItem) => subItem.visible));

      return menuItems;
   }

   // private testUniquenessOfPageNames(env, translated: TranslatedText) {
   //    const names = [];
   //    this.testDupes(this.provideDashboards(env, translated), names);
   //    this.testDupes(this.provideOperationalReports(env, translated), names);
   //    this.testDupes(this.provideSalesReport(env, translated), names);
   //    this.testDupes(this.provideAfterSalesReport(env, translated), names);
   //    this.testDupes(this.providePeopleReports(env, translated), names);
   //    this.testDupes(this.providePricingPages(env), names);
   //    this.testDupes(this.providePricingSeparatePages(env), names);
   //    this.testDupes(this.provideMaintenanceReports(env, translated), names);
   // }

   // private testDupes(sections: MenuSection[], names: any[]) {
   //    sections.forEach((section) => {
   //       section.subItems.forEach((menuItem) => {
   //          let name = menuItem.pageName;
   //          if (names.includes(name)) {
   //             console.error(`Duplicate name ${name}`);
   //          }
   //          names.push(name);
   //       });
   //    });
   // }

   private provideDashboards(env, translated: TranslatedText) {
      return [
         {
            group: 'dashboards',
            name: 'Dashboards',
            icon: 'fas fa-tachometer-fast fa-fw',
            isActive: false,
            visible: true,
            expanded: true,
            nameAbbreviated: 'Dash boards',
            subItems: [
               //Overview Spain
               {
                  subItems: null,
                  isSales: null,
                  expanded: false,
                  nameAbbreviated: "Home",
                  name: "Home",
                  icon: "fa-solid fa-gauge-simple-high",
                  link: "/dashboardOverviewSpain",
                  pageName: "dashboardOverviewSpain",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.dashboardOverviewSpain),
                  isDashboard: true,
                  parent: 'dashboards'
               },
               //OVERVIEW
               {
                  subItems: null,
                  isSales: null,
                  expanded: false,
                  nameAbbreviated: translated.Common_Overview,
                  name: translated.Common_Overview,
                  icon: "fa-solid fa-gauge-simple-high",
                  link: "/dashboardOverviewVindis",
                  pageName: "dashboardOverview",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.dashboardOverviewVindis),
                  isDashboard: true,
                  parent: 'dashboards'
               },
               //Sales RRG
               {
                  subItems: null,
                  isSales: null,
                  expanded: false,
                  nameAbbreviated: translated.Common_Sales,
                  name: translated.Common_Sales,
                  icon: "fa-solid fa-car-side",
                  link: "/dashboardSalesRRG",
                  pageName: "dashboardSalesRRG",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.dashboardSalesRRG),
                  isDashboard: true,
                  parent: 'dashboards'
               },
               //Sales Vindis
               {
                  subItems: null,
                  isSales: null,
                  expanded: false,
                  nameAbbreviated: translated.Common_Sales,
                  name: translated.Common_Sales,
                  icon: "fa-solid fa-car-side",
                  link: "/dashboardSalesVindis",
                  pageName: "dashboardSalesVindis",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.dashboardSalesVindis),
                  isDashboard: true,
                  parent: 'dashboards'
               },
               //New VN Spain
               {
                  subItems: null,
                  isSales: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_NewKPIsTitle,
                  name: translated.Dashboard_NewKPIsTitle,
                  icon: "fa-solid fa-car-side",
                  link: "/dashboardNewKPIs",
                  pageName: "dashboardNewVNSpain",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.dashboardNewKPIs),
                  isDashboard: true,
                  parent: 'dashboards'
               },
               //Used Spain
               {
                  subItems: null,
                  isSales: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_UsedKPIsTitle,
                  name: translated.Dashboard_UsedKPIsTitle,
                  icon: "fa-solid fa-car-side",
                  link: "/dashboardUsedKPIs",
                  pageName: "dashboardUsedSpain",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.dashboardUsedKPIs),
                  isDashboard: true,
                  parent: 'dashboards'
               },
               //Aftersales RRG
               {
                  subItems: null,
                  isSales: null,
                  expanded: false,
                  nameAbbreviated: translated.Common_Aftersales,
                  name: translated.Common_Aftersales,
                  icon: "fa-solid fa-wrench",
                  link: "/dashboardAfterSalesRRG",
                  pageName: "dashboardAfterSalesRRG",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.dashboardAfterSalesRRG),
                  isDashboard: true,
                  parent: 'dashboards'
               },
               //Aftersales Vindis
               {
                  subItems: null,
                  isSales: null,
                  expanded: false,
                  nameAbbreviated: translated.Common_Aftersales,
                  name: translated.Common_Aftersales,
                  icon: "fa-solid fa-wrench",
                  link: "/dashboardAfterSalesVindis",
                  pageName: "dashboardAfterSalesVindis",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.dashboardAfterSalesVindis),
                  isDashboard: true,
                  parent: 'dashboards'
               },
               //Aftersales
               {
                  subItems: null,
                  isSales: null,
                  expanded: false,
                  nameAbbreviated: translated.Common_Aftersales,
                  name: translated.Common_Aftersales,
                  icon: "fa-solid fa-wrench",
                  link: "/dashboardAfterSalesSpain",
                  pageName: "dashboardAftersales",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.dashboardAfterSalesSpain),
                  isDashboard: true,
                  parent: 'dashboards'
               },
               //Site Compare
               {
                  subItems: null,
                  isSales: null,
                  expanded: false,
                  nameAbbreviated: translated.SiteCompare,
                  name: translated.SiteCompare,
                  icon: "fa-solid fa-building-flag",
                  link: "/siteCompare",
                  pageName: "dashboardSiteCompare",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.siteCompare),
                  isDashboard: true,
                  parent: 'dashboards'
               }
            ],
            link: null,
            pageName: null,
            isSales: null
         },
      ]
   }

   private provideOperationalReports(env: SparkEnvironment, translated: TranslatedText) {
      let orderbookTitle: string = this.constants.environment.languageSelection
         ? this.constants.translatedText.Orderbook_TitleSpain
         : this.constants.environment.orderBook_orderbookTitle;

      const perms = this.selections.user.permissions;

      return [
         {
            group: 'operationreports',
            name: 'Operational Reports',
            icon: 'fas fa-tachometer-fast fa-fw',
            isActive: false,
            visible: env.sideMenu_hasOperationReports,
            nameAbbreviated: "Op. Reports",
            subItems: [
               //Retail Orderbook
               {
                  pageName: 'retailOrderbook',
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: orderbookTitle,
                  name: orderbookTitle,
                  icon: 'fas fa-list-ol fa-fw',
                  link: this.constants.environment.orderBookURL,
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.orderBookNew) && perms.seeOrderbook,
                  isSales: false,
                  parent: 'operationreports'
               },
               //Distrinet
               {
                  pageName: 'distrinet',
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: 'Retail Order book',
                  name: 'Distrinet',
                  icon: 'fas fa-cars fa-fw',
                  link: '/distrinet',
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.distrinet) && perms.seeOrderbook,
                  isSales: false,
                  parent: 'operationreports'
               },
               //Fleet Orderbook
               {
                  pageName: 'fleetOrderbook',
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: 'Fleet Order book',
                  name: 'Fleet Order book',
                  icon: 'fas fa-th-list',
                  link: this.constants.environment.fleetOrderbookURL,
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.fleetOrderbook) && perms.seeFleetOrderbook,
                  isSales: false,
                  parent: 'operationreports'
               },
               //Deals  for the week
               {
                  pageName: 'dealsDoneThisWeek',
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: 'Deals For Week',
                  name: translated.DealsDoneThisWeek_Title,
                  icon: 'far fa-heart-rate fa-fw',
                  link: '/dealsDoneThisWeek',
                  isActive: false,
                  visible:
                     this.selections.isPageVisible(PageNameRoutes.dealsDoneThisWeek) && perms.seeDealsDoneThisWeek,
                  isSales: false,
                  parent: 'operationreports'
               },
               //Deals for the month
               {
                  pageName: 'dealsForTheMonth',
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: 'Deals For Month',
                  name: translated.DealsDoneThisMonth_TitleShort,
                  icon: 'far fa-chart-line fa-fw',
                  link: '/dealsForTheMonth',
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.dealsForTheMonth) && perms.seeDealsForTheMonth,
                  isSales: false,
                  parent: 'operationreports'
               },
               //Whiteboard
               {
                  pageName: 'whiteboard',
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Whiteboard_Title,
                  name: translated.Whiteboard_Title,
                  icon: 'fas fa-th fa-fw',
                  link: '/whiteboard',
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.whiteboard) && perms.seeDealsDoneThisWeek,
                  isSales: false,
                  parent: 'operationreports'
               },
               //Handover diary
               {
                  pageName: 'handoverDiary',
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.HandoverDiary_Title,
                  name: translated.HandoverDiary_Title,
                  icon: 'far fa-calendar-alt fa-fw',
                  link: '/handoverDiary',
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.handoverDiary) && perms.seeHandoverDiary,
                  isSales: false,
                  parent: 'operationreports'
               },
               //Telephone stats
               {
                  subItems: '',
                  pageName: 'telephoneStats',
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_TelephoneStats,
                  name: translated.Dashboard_TelephoneStats,
                  icon: 'fa-solid fa-phone',
                  link: '/dashboard',
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.teleStats),
                  isSales: false,
                  parent: 'operationreports'
               },
               //Stock Landing
               {
                  pageName: 'stockLanding',
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.StockLanding_Title,
                  name: translated.StockLanding_Title,
                  icon: 'fas fa-plane-arrival fa-fw',
                  link: '/stockLanding',
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.stockLanding) && perms.seeStockLanding,
                  isSales: false,
                  parent: 'operationreports'
               },
               //Performance Trends
               {
                  pageName: 'performanceTrends',
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: 'Perf. Trends',
                  name: 'Performance Trends',
                  icon: 'far fa-analytics fa-fw',
                  link: '/performanceTrends',
                  isActive: false,
                  visible:
                     this.selections.isPageVisible(PageNameRoutes.performanceTrends) && perms.seePerformanceTrends,
                  isSales: false,
                  parent: 'operationreports'
               }
            ],
            link: null,
            pageName: null,
            expanded: null,
            isSales: null
         },
      ]
   }

   private provideAfterSalesReport(env: SparkEnvironment, translated: TranslatedText) {
      return [
         {
            group: 'aftersalesreports',
            name: 'Aftersales Reports',
            icon: 'fas fa-tachometer-fast fa-fw',
            isActive: false,
            visible: true,
            nameAbbreviated: "Aftersales Reports",
            subItems: [
               //Service sales
               {
                  isSales: null,
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_ServiceSales_Title,
                  name: translated.Dashboard_ServiceSales_Title,
                  icon: "fa-solid fa-wrench",
                  link: "/serviceSummary",
                  pageName: "ServiceSales",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.serviceSummary),
                  parent: "aftersalesreports",
               },
               //Service bookings
               {
                  isSales: null,
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_ServiceBookings_Title,
                  name: translated.Dashboard_ServiceBookings_Title,
                  icon: "fa-regular fa-calendar",
                  link: "/serviceBookings",
                  pageName: "ServiceBookings",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.serviceBookings),
                  parent: "aftersalesreports",
               },
               //EVHC
               {
                  isSales: null,
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_Evhc_Title,
                  name: translated.Dashboard_Evhc_Title,
                  icon: "fa-solid fa-notes-medical",
                  link: "/evhc",
                  pageName: "EVHC",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.evhc),
                  parent: "aftersalesreports",
               },
               //Upsells
               {
                  isSales: null,
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_Upsells,
                  name: translated.Dashboard_Upsells,
                  icon: "fa-solid fa-wrench",
                  link: "/upsells",
                  pageName: "Upsells",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.upsells),
                  parent: "aftersalesreports",
               },
               //WIP
               {
                  isSales: null,
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_WipReport_Title,
                  name: translated.Dashboard_WipReport_Title,
                  icon: "fa-solid fa-timer",
                  link: "/wipReport",
                  pageName: "Wip",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.wipReport),
                  parent: "aftersalesreports",
               },
               //Debtors
               {
                  isSales: null,
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_Debtors_Title,
                  name: translated.Dashboard_Debtors_Title,
                  icon: "fa-solid fa-coins",
                  link: "/debtsAfterSales",
                  pageName: "DebtorsAftersales",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.debtsAfterSales),
                  parent: "aftersalesreports",
               },
               //CitNow
               {
                  isSales: null,
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_CitNow_Title,
                  name: translated.Dashboard_CitNow_Title,
                  icon: "fa-solid fa-camera",
                  link: "/citnow",
                  pageName: "CitNowAftersales",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.citnow),
                  parent: "aftersalesreports",
               },
               //Parts
               {
                  isSales: null,
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_PartsSales_Title,
                  name: translated.Dashboard_PartsSales_Title,
                  icon: "fa-solid fa-wrench",
                  link: "/partsSummary",
                  pageName: "PartsSales",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.partsSummary),
                  parent: "aftersalesreports",
               },
               //Parts Stock
               {
                  isSales: null,
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Common_PartsStock,
                  name: translated.Common_PartsStock,
                  icon: "fa-solid fa-gears",
                  link: "/partsStock",
                  pageName: "PartsStock",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.partsStock),
                  parent: "aftersalesreports",
               },
            ],
            link: null,
            pageName: null,
            expanded: null,
            isSales: null
         },
      ]
   }

   private provideSalesReport(env: SparkEnvironment, translated: TranslatedText) {
      return [
         {
            group: 'salesreports',
            name: 'Sales Reports',
            icon: 'fas fa-tachometer-fast fa-fw',
            isActive: false,
            visible: true,
            nameAbbreviated: "Sales Reports",
            subItems: [

               //Sales Performance
               {
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: 'Sales Perf.',
                  name: translated.Dashboard_SalesPerformance_Title,
                  icon: "fa-solid fa-car-side",
                  link: "/salesPerformance",
                  pageName: "SalesPerformance",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.salesPerformance),
                  isSales: false,
                  parent: 'salesreports'
               },
               //Alcopas
               {
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: "Alcopas",
                  name: "Alcopas",
                  icon: "fa-solid fa-car-side",
                  link: "/alcopa",
                  pageName: "Alcopa",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.alcopa),
                  isSales: false,
                  parent: 'salesreports'
               },
               //Order Rate
               {
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Common_OrderRate,
                  name: translated.Common_OrderRate,
                  icon: "fa-solid fa-car-side",
                  link: "/orderRate",
                  pageName: "OrderRate",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.orderRate),
                  isSales: false,
                  parent: 'salesreports'
               },
               //Registrations
               {
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_Registrations_Title,
                  name: translated.Dashboard_Registrations_Title,
                  icon: "fa-solid fa-car",
                  link: "/registrationsPosition",
                  pageName: "Registrations",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.registrationsPosition),
                  isSales: false,
                  parent: 'salesreports'
               },
               //F&I
               {
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.FinanceAddons_Title,
                  name: translated.FinanceAddons_Title,
                  icon: "fa-solid fa-list",
                  link: "/fAndISummary",
                  pageName: "FinanceAddOns",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.fAndISummary),
                  isSales: false,
                  parent: 'salesreports'
               },
               //Stock Reports
               {
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_StockReport_Title,
                  name: translated.Dashboard_StockReport_Title,
                  icon: "fa-solid fa-car-side",
                  link: "/stockReport",
                  pageName: "StockReport",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.stockReport),
                  isSales: false,
                  parent: 'salesreports'
               },
               //Stock List
               {
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.StockList_Title,
                  name: translated.StockList_Title,
                  icon: 'fa-solid fa-cars',
                  link: '/stockList',
                  pageName: 'stockList',
                  isActive: false,
                  visible:
                     this.selections.isPageVisible(PageNameRoutes.stockList) &&
                     this.selections.user.permissions.seeStockList,
                  isSales: false,
                  parent: 'salesreports'
               },
               //Debtors
               {
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_Debtors_Title,
                  name: translated.Dashboard_Debtors_Title,
                  icon: "fa-solid fa-coins",
                  link: "/debtsSales",
                  pageName: "Debtors",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.debtsSales),
                  isSales: false,
                  parent: 'salesreports'
               },
               //CitNow
               {
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_CitNow_Title,
                  name: translated.Dashboard_CitNow_Title,
                  icon: "fa-solid fa-camera",
                  link: "/citnow",
                  pageName: "CitNow",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.citnow),
                  isSales: true,
                  parent: 'salesreports'
               },
               //Image Ratios
               {
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_ImageRatios_Title,
                  name: translated.Dashboard_ImageRatios_Title,
                  icon: "fa-solid fa-camera",
                  link: "/imageRatios",
                  pageName: "ImageRatios",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.imageRatios),
                  isSales: true,
                  parent: 'salesreports'
               },
               //Activities
               {
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_Activities,
                  name: translated.Dashboard_Activities,
                  icon: "fa-solid fa-chart-network",
                  link: "/salesActivity",
                  pageName: "Activities",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.salesActivity),
                  isSales: true,
                  parent: 'salesreports'
               },
               //GDPR
               {
                  subItems: null,
                  expanded: false,
                  nameAbbreviated: translated.Dashboard_GDPRCapture,
                  name: translated.Dashboard_GDPRCapture,
                  icon: "fa-solid fa-lock",
                  link: "/gdpr",
                  pageName: "GDPR",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.gdpr),
                  isSales: true,
                  parent: 'salesreports'
               }
            ],
            link: null,
            pageName: null,
            expanded: null,
            isSales: null
         },
      ]
   }

   private providePeopleReports(env: SparkEnvironment, translated: TranslatedText): MenuSection[] {
      const perms = this.selections.user.permissions;

      return [
         {
            group: 'peoplereports',
            name: 'People Reports',
            subItems: [

               //Performance League
               {
                  pageName: 'performanceLeague',
                  isSales: null,
                  nameAbbreviated: 'Perf. League',
                  name: translated.PerformanceLeague_Title,
                  icon: 'fas fa-star fa-fw',
                  link: '/performanceLeague',
                  isActive: false,
                  visible:
                     this.selections.isPageVisible(PageNameRoutes.performanceLeague) && perms.seePerformanceLeague,
                  parent: "peoplereports",
               },
               //Salesman efficiency
               {
                  isSales: null,
                  nameAbbreviated: 'Sales Efficiency',
                  name: translated.Dashboard_SalesmanEfficiency_Title,
                  icon: "fa-solid fa-user",
                  link: "/salesmanEfficiency",
                  pageName: "SalesmanEfficiency",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.salesmanEfficiency),
                  parent: "peoplereports",
               },

               //Commission
               {
                  pageName: 'salesCommission',
                  isSales: null,
                  nameAbbreviated: `${translated.Common_Sales} ${translated.Common_Commission}`,
                  name: `${translated.Common_Sales} ${translated.Common_Commission}`,
                  icon: 'fas fa-money-bill-wave fa-fw',
                  link: '/salesCommission',
                  isActive: false,
                  visible:
                     this.selections.isPageVisible(PageNameRoutes.salesCommission) &&
                     (perms.reviewCommission || perms.selfOnlyCommission) &&
                     this.constants.environment.dashboard_showCommissions,
                  parent: "peoplereports",
               },

               //Scratch Card
               {
                  pageName: 'scratchCard',
                  isSales: null,
                  nameAbbreviated: 'Scratch Card',
                  name: 'Scratchcard',
                  icon: 'fas fa-coin fa-fw',
                  link: '/scratchCard',
                  isActive: false,
                  visible:
                     this.selections.isPageVisible(PageNameRoutes.scratchCard) &&
                     this.globalParamsService.getGlobalParam(GlobalParamKey.webAppShowScratchcard) &&
                     perms.seeScratchCards,
                  parent: "peoplereports",
               },

               //Sales Exec Review
               {
                  pageName: 'salesExecReview',
                  isSales: null,
                  nameAbbreviated: 'Sales Exec Review',
                  name: 'Sales Exec Review',
                  icon: 'fas fa-handshake',
                  link: '/salesExecReview',
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.salesExecReview),
                  parent: "peoplereports",
               },


            ],
            expanded: null,
         },
      ]
   }

   private providePricingPages(env): MenuSection[] {
      const perms = this.selections.user.permissions;

      const subItems = [
         {
            pageName: "home",
            isSales: null,
            nameAbbreviated: "Home",
            name: "Home",
            icon: "fa fa-house",
            link: "/home",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.home),
            parent: "vehiclepricing",
         },
      ];

      if (         this.selections.isPageVisible(PageNameRoutes.stockProfiler)       ) {
         subItems.push(
            //Stock Profiler
            {
               pageName: "stockProfiler",
               isSales: null,
               nameAbbreviated: "Stock Profiler",
               name: "Stock Profiler",
               icon: "fa-solid fa-chart-scatter",
               link: "/stockProfiler",
               isActive: false,
               visible: true,
               parent: "vehiclepricing",
            }
         );
      }

      subItems.push(
         //Sites League
         {
            pageName: "sitesLeague",
            isSales: null,
            nameAbbreviated: "Sites League",
            name: "Sites League",
            icon: "fa-solid fa-chart-bar",
            link: "/sitesLeague",
            isActive: false,
            visible:
               this.selections.isPageVisible(PageNameRoutes.sitesLeague) && this.constants.RetailerSites.length > 1,
            parent: "vehiclepricing",
         },
         //Site Dashboard
         {
            pageName: "statsDashboard",
            isSales: null,
            nameAbbreviated: "Site Dashboard",
            name: "Site Dashboard",
            icon: "fa-solid fa-chart-column",
            link: "/statsDashboard",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.statsDashboard),
            parent: "vehiclepricing",
         },
         //Stock Dashboard
         {
            pageName: "stockInsight",
            isSales: null,
            nameAbbreviated: "Stock Dashboard",
            name: "Stock Dashboard",
            icon: "fa-solid fa-chart-scatter",
            link: "/stockInsight",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.stockInsight),
            parent: "vehiclepricing",
         },
         //Stock Quick Search
         {
            pageName: "advertSimpleListing",
            isSales: null,
            nameAbbreviated: "Stock Quick Search",
            name: "Stock Quick Search",
            icon: "fa fa-magnifying-glass",
            link: "/advertSimpleListing",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.advertSimpleListing),
            parent: "vehiclepricing",
         },
         //Stock Reports
         {
            pageName: "advertListingDetail",
            isSales: null,
            nameAbbreviated: "Stock Report",
            name: "Stock Report",
            icon: "fas fa-car fa-fw",
            link: "/advertListingDetail",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.advertListingDetail),
            parent: "vehiclepricing",
         },
         //Leaving Vehicle Trends
         {
            pageName: "leavingVehicleTrends",
            isSales: null,
            nameAbbreviated: "Leaving V. Trends",
            name: "Leaving Vehicle Trends",
            icon: "fas fa-arrow-trend-up",
            link: "/leavingVehicleTrends",
            isActive: false,
            visible:
               this.selections.isPageVisible(PageNameRoutes.leavingVehicleTrends),
            parent: "vehiclepricing",
         },
         //Leaving Vehicle Trends Over Time
         {
            pageName: "leavingVehicleTrendsOverTime",
            isSales: null,
            nameAbbreviated: "Leaving Trends Change",
            name: "Trends Over Time",
            icon: "fas fa-arrow-trend-up",
            link: "/leavingVehicleTrendsOverTime",
            isActive: false,
            visible:
               this.selections.isPageVisible(PageNameRoutes.leavingVehicleTrendsOverTime), 
            parent: "vehiclepricing",
         },
         //Leaving Vehicle Detail
         {
            pageName: "leavingVehicleDetail",
            isSales: null,
            nameAbbreviated: "Leaving V. Detail",
            name: "Leaving Vehicle Detail",
            icon: "fas fa-magnifying-glass-chart",
            link: "/leavingVehicleDetail",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.leavingVehicleDetail),
            parent: "vehiclepricing",
         },
         //Vehicle Valuation
         {
            pageName: "bulkValuation",
            isSales: null,
            nameAbbreviated: "Vehicle Valuation",
            name: "Vehicle Valuation",
            icon: "fa fa-pound-sign",
            link: "/bulkValuation",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.bulkValuation),
            parent: "vehiclepricing",
         },
         //Buying Opportunities
         {
            pageName: "localBargains",
            isSales: null,
            nameAbbreviated: "Buying Ops.",
            name: "Buying Opportunities",
            icon: "fas fa-gift",
            link: "/localBargains",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.localBargains),
            parent: "vehiclepricing",
         },
         //Location Optimiser
         {
            pageName: "locationOptimiser",
            isSales: null,
            nameAbbreviated: "Location Optimiser",
            name: "Location Optimiser",
            icon: "fa fa-map-marker-alt",
            link: "/locationOptimiser",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.locationOptimiser),
            parent: "vehiclepricing",
         },

         //Price Changes for today
         {
            pageName: "todaysPrices",
            isSales: null,
            nameAbbreviated: "Price Changes",
            name: "Today's Price Changes",
            icon: "fas fa-barcode-read fa-fw",
            link: "/todaysPrices",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.todaysPrices),
            parent: "vehiclepricing",
         },
         //Opted Out Vehicles
         {
            pageName: "optOuts",
            isSales: null,
            nameAbbreviated: "Opted Out Vehicles",
            name: "Opted Out Vehicles",
            icon: "fas fa-bell-slash",
            link: "/optOuts",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.optOuts),
            parent: "vehiclepricing",
         }
      );

      return [
         {
            name: "Vehicle Pricing",
            group: "vehiclepricing",
            subItems: subItems,
            expanded: null,
         },
      ];
   }

   private providePricingSeparatePages(env: SparkEnvironment): MenuSection[] {
      const perms = this.selections.user.permissions;

      const dashboardSubItems = [
         //Sites League
         {
            pageName: "sitesLeague",
            isSales: null,
            nameAbbreviated: "Sites League",
            name: "Sites League",
            icon: "fa-solid fa-chart-bar",
            link: "/sitesLeague",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.sitesLeague),
            parent: "vehiclepricing",
         },
         //Site Dashboard
         {
            pageName: "statsDashboardSeparate",
            isSales: null,
            nameAbbreviated: "Site Dashboard",
            name: "Site Dashboard",
            icon: "fa-solid fa-chart-column",
            link: "/statsDashboard",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.statsDashboard),
            parent: "vehiclepricing",
         },
         //Stock Dashboard
         {
            pageName: "stockInsight",
            isSales: null,
            nameAbbreviated: "Stock Dashboard",
            name: "Stock Dashboard",
            icon: "fa-solid fa-chart-scatter",
            link: "/stockInsight",
            isActive: false,
            visible: this.selections.isPageVisible(PageNameRoutes.stockInsight),
            parent: "vehiclepricing",
         },
      ];

      if (this.selections.isPageVisible(PageNameRoutes.stockProfiler)     
      ) {
         dashboardSubItems.push(
            //Stock Profiler
            {
               pageName: "stockProfiler",
               isSales: null,
               nameAbbreviated: "Stock Profiler",
               name: "Stock Profiler",
               icon: "fa-solid fa-chart-scatter",
               link: "/stockProfiler",
               isActive: false,
               visible: true,
               parent: "vehiclepricing",
            }
         );
      }

      let menuItems: MenuSection[] = [];

      menuItems.push(
         {
            group: "home",
            name: "Home",
            subItems: [
               {
                  pageName: "home",
                  isSales: null,
                  nameAbbreviated: "Home",
                  name: "Home",
                  icon: "fa fa-house",
                  link: "/home",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.home),
                  parent: "vehiclepricing",
               },
            ],
            expanded: null,
         },
         {
            group: "vehiclepricingDashboards",
            name: "Dashboards",
            subItems: dashboardSubItems,
            expanded: null,
         },

         {
            group: "vehiclepricingReports",
            name: "Reports",
            subItems: [
               //Home
               // {
               //   pageName: null, isSales: null,   nameAbbreviated: 'Vehicle Valuation',
               //   name: 'Home', icon: 'fa fa-house', link: '/home', isActive: false, visible: env.sideMenu_vehicleValuation, parent: 'vehiclepricing'
               // },

               //Stock Quick Search
               {
                  pageName: "advertSimpleListingSeparate",
                  isSales: null,
                  nameAbbreviated: "Stock Quick Search",
                  name: "Stock Quick Search",
                  icon: "fa fa-magnifying-glass",
                  link: "/advertSimpleListing",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.advertSimpleListing),
                  parent: "vehiclepricing",
               },
               //Stock Reports
               {
                  pageName: "stockReportsSeparate",
                  isSales: null,
                  nameAbbreviated: "Stock Report",
                  name: "Stock Report",
                  icon: "fas fa-car fa-fw",
                  link: "/advertListingDetail",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.advertListingDetail),
                  parent: "vehiclepricing",
               },
               //Leaving Vehicle Trends
               {
                  pageName: "leavingVehicleTrendsSeparate",
                  isSales: null,
                  nameAbbreviated: "Leaving V. Trends",
                  name: "Leaving Vehicle Trends",
                  icon: "fas fa-arrow-trend-up",
                  link: "/leavingVehicleTrends",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.leavingVehicleTrends),
                  parent: "vehiclepricing",
               },
               //Leaving Vehicle Trends Over Time
               {
                  pageName: "leavingVehicleTrendsOverTime",
                  isSales: null,
                  nameAbbreviated: "Leaving Trends Change",
                  name: "Leaving Trends Change",
                  icon: "fas fa-arrow-trend-up",
                  link: "/leavingVehicleTrendsOverTime",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.leavingVehicleTrendsOverTime),
                  parent: "vehiclepricing",
               },
               //Leaving Vehicle Detail
               {
                  pageName: "leavingVehicleDetailSeparate",
                  isSales: null,
                  nameAbbreviated: "Leaving V. Detail",
                  name: "Leaving Vehicle Detail",
                  icon: "fas fa-magnifying-glass-chart",
                  link: "/leavingVehicleDetail",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.leavingVehicleDetail),
                  parent: "vehiclepricing",
               },
               {
                  pageName: "leavingVehiclesExplorer",
                  isSales: null,
                  nameAbbreviated: "Leaving Explorer",
                  name: "Leaving Explorer",
                  icon: "fas fa-magnifying-glass-chart",
                  link: "/leavingVehicleExplorer",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.leavingVehicleExplorer),
                  parent: "vehiclepricing",
               },
              
            ],
            expanded: null,
         }
      );

      if (
         this.selections.isPageVisible(PageNameRoutes.vehicleValuation) ||
         this.selections.isPageVisible(PageNameRoutes.localBargains) ||
         this.selections.isPageVisible(PageNameRoutes.locationOptimiser)
      ) {
         menuItems.push({
            group: "vehiclepricingSourcing",
            name: "Sourcing Tools",
            subItems: [
               //Vehicle Valuation
               {
                  pageName: "bulkValuationSeparate",
                  isSales: null,
                  nameAbbreviated: "Vehicle Valuation",
                  name: "Vehicle Valuation",
                  icon: "fa fa-pound-sign",
                  link: "/bulkValuation",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.bulkValuation),
                  parent: "vehiclepricing",
               },
               //Buying Opportunities
               {
                  pageName: "localBargainsSeparate",
                  isSales: null,
                  nameAbbreviated: "Buying Ops.",
                  name: "Buying Opportunities",
                  icon: "fas fa-gift",
                  link: "/localBargains",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.localBargains),
                  parent: "vehiclepricing",
               },
               //Location Optimiser
               {
                  pageName: "locationOptimiserSeparate",
                  isSales: null,
                  nameAbbreviated: "Location Optimiser",
                  name: "Location Optimiser",
                  icon: "fa fa-map-marker-alt",
                  link: "/locationOptimiser",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.locationOptimiser),
                  parent: "vehiclepricing",
               },
            ],
            expanded: null,
         });
      }
      const pricingPageSubItems =  [
            //Price Changes for today
            {
               pageName: "todaysPricesSeparate",
               isSales: null,
               nameAbbreviated: "Price Changes",
               name: "Today's Price Changes",
               icon: "fas fa-barcode-read fa-fw",
               link: "/todaysPrices",
               isActive: false,
               visible: this.selections.isPageVisible(PageNameRoutes.todaysPrices),
               parent: "vehiclepricing",
            },
            //Opted Out Vehicles
            {
               pageName: "optOutsSeparate",
               isSales: null,
               nameAbbreviated: "Opted Out Vehicles",
               name: "Opted Out Vehicles",
               icon: "fas fa-bell-slash",
               link: "/optOuts",
               isActive: false,
               visible: this.selections.isPageVisible(PageNameRoutes.optOuts),
               parent: "vehiclepricing",
            },
            {
                  pageName: "strategyPriceBuildUp",
                  isSales: null,
                  nameAbbreviated: "Strategy Build Up",
                  name: "Strategy Price Build Up",
                  icon: "fas fa-sitemap",
                  link: "/strategyPriceBuildUp",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.strategyPriceBuildUp),
                  parent: "vehiclepricing",
               }
         ]

         // if(this.selections.isPageVisible(PageNameRoutes.strategyPriceBuildUp)){{
         //    pricingPageSubItems.push(
         //       {
         //          pageName: "strategyPriceBuildUp",
         //          isSales: null,
         //          nameAbbreviated: "Strategy Build Up",
         //          name: "Strategy Price Build Up",
         //          icon: "fas fa-sitemap",
         //          link: "/strategyPriceBuildUp",
         //          isActive: false,
         //          visible: this.selections.isPageVisible(PageNameRoutes.strategyPriceBuildUp),
         //          parent: "vehiclepricing",
         //       }
         //    );
         // }}


      menuItems.push({
         group: "vehiclepricingPricing",
         name: "Pricing Pages",
         subItems: pricingPageSubItems,
         expanded: null,
      });

      return menuItems;
   }

   private provideMaintenanceReports(env: SparkEnvironment, translated: TranslatedText): MenuSection[] {
      const perms = this.selections.user.permissions;

      return [
         {
            group: "settings",
            name: "Settings",
            subItems: [
               //Site Settings
               {
                  pageName: "siteSettings",
                  isSales: null,
                  nameAbbreviated: "Site Settings",
                  name: "Site Settings",
                  icon: "fas fa-cog fa-fw",
                  link: "/siteSettings",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.siteSettings),
                  parent: "settings",
               },
               //Tags Settings
               {
                  pageName: 'tagSettings',
                  isSales: null,
                  nameAbbreviated: 'Tag Settings',
                  name: 'Tag Settings',
                  icon: 'fas fa-tag fa-fw',
                  link: '/tagSettings',
                  isActive: false,
                  visible: perms.canEditPricingStrategy,
                  parent: 'settings'
               },
               //User Maintenance
               {
                  pageName: "userMaintenance",
                  isSales: null,
                  nameAbbreviated: translated.UserMaintenance_Title,
                  name: translated.UserMaintenance_Title,
                  icon: "fas fa-user fa-fw",
                  link: "/userMaintenance",
                  isActive: false,
                  visible:
                     this.selections.isPageVisible(PageNameRoutes.userMaintenance) &&
                     this.selections.user.RoleName == "System Administrator" &&
                     perms.seeUserMaintenance,
                  parent: "settings",
               },

               // Usage Report
               {
                  isSales: null,
                  nameAbbreviated: translated.UsageReport_Title,
                  name: translated.UsageReport_Title,
                  icon: "fa-solid fa-user",
                  link: "/usageReport",
                  pageName: "UsageReport",
                  isActive: false,
                  visible: true,
                  parent: "settings",
               },
                {
                  pageName: "scheduledReports",
                  isSales: null,
                  nameAbbreviated: "Scheduled Reports",
                  name: "Scheduled Reports",
                  icon: "fas fa-clock",
                  link: "/scheduledReports",
                  isActive: false,
                  visible: this.selections.isPageVisible(PageNameRoutes.scheduledReports),
                  parent: "settings",
               },
            ],
            expanded: null,
         },
      ];
   }
}
