import {Injectable} from '@angular/core';
import html2canvas from 'html2canvas';
import jspdf from 'jspdf';
import {SelectionsService} from './selections.service';

@Injectable({
    providedIn: 'root'
})
export class PrintService {

    constructor(private selectionsService: SelectionsService) {
    }

    async printElementAsPDF(
        elementSelector: string,
        fileName: string,
        elementsToExpand?: string[]
    ): Promise<void> {

        this.selectionsService.triggerSpinner.emit({show: true, message: 'Generating PDF...'});

        try {
            const element = document.querySelector(elementSelector) as HTMLElement;

            if (!element) {
                throw new Error(`Element with selector '${elementSelector}' not found`);
            }

            // Store original styles and expand modal elements
            const elementsToModify = this.getElementsToExpand(element, elementsToExpand);
            const originalStyles = new Map();

            this.storeAndModifyStyles(elementsToModify, originalStyles);

            // Wait for DOM to adjust and any remaining animations
            await this.delay(2000);

            // Take screenshot and generate PDF
            const canvas = await html2canvas(element, {
                useCORS: true,
                allowTaint: false,
                scale: 1,
                backgroundColor: '#ffffff',
                height: element.scrollHeight,
                width: element.scrollWidth,
                scrollX: 0,
                scrollY: 0
            });

            // Restore styles
            this.restoreStyles(originalStyles);

            // Note: Accordion restoration would need to be handled by the calling component

            // Generate and save PDF
            await this.generatePDF(canvas, fileName);

            // Hide loading spinner
            this.selectionsService.triggerSpinner.emit({show: false});

        } catch (error) {
            // Hide loading spinner even on error
            this.selectionsService.triggerSpinner.emit({show: false});

            console.error('Print failed:', error);
            throw error;
        }
    }


    private getElementsToExpand(element: HTMLElement, customElements?: string[]): HTMLElement[] {
        let elementsToFind: string[] = [];

        if (customElements && customElements.length > 0) {
            elementsToFind = customElements;
        }

        // Get elements from selectors
        const selectedElements = elementsToFind
            .map(selector => selector.startsWith('#')
                ? document.getElementById(selector.substring(1))
                : document.querySelector(selector)
            )
            .filter(el => el) as HTMLElement[];

        // Add any elements with scroll overflow
        const scrollableElements = Array.from(element.querySelectorAll('*')).filter((el: HTMLElement) => {
            const computedStyle = window.getComputedStyle(el);
            return computedStyle.overflow === 'scroll' || computedStyle.overflow === 'auto' ||
                computedStyle.overflowY === 'scroll' || computedStyle.overflowY === 'auto' ||
                computedStyle.overflowX === 'scroll' || computedStyle.overflowX === 'auto'
        }) as HTMLElement[];

        // Deduplicate elements - use a Set which compares object references
        const uniqueElements = new Set<HTMLElement>();
        [...selectedElements, ...scrollableElements].forEach(el => {
            if (el && !uniqueElements.has(el)) {
                uniqueElements.add(el);
            }
        });

        return Array.from(uniqueElements);
    }

    private storeAndModifyStyles(elements: HTMLElement[], originalStyles: Map<HTMLElement, any>): void {
        elements.forEach((el: HTMLElement) => {
            if (el) {
                // Skip if already processed
                if (originalStyles.has(el)) {
                    return;
                }

                // Store both inline styles and a flag for whether styles were originally empty
                const hasInlineStyles = {
                    height: !!el.style.height,
                    maxHeight: !!el.style.maxHeight,
                    minHeight: !!el.style.minHeight,
                    overflow: !!el.style.overflow,
                    overflowY: !!el.style.overflowY,
                    overflowX: !!el.style.overflowX,
                    position: !!el.style.position,
                    top: !!el.style.top,
                    bottom: !!el.style.bottom
                };

                originalStyles.set(el, {
                    height: el.style.height,
                    maxHeight: el.style.maxHeight,
                    minHeight: el.style.minHeight,
                    overflow: el.style.overflow,
                    overflowY: el.style.overflowY,
                    overflowX: el.style.overflowX,
                    position: el.style.position,
                    top: el.style.top,
                    bottom: el.style.bottom,
                    hasInlineStyles: hasInlineStyles
                });

                // Expand all elements to show full content with !important to override CSS
                el.style.setProperty('height', 'auto', 'important');
                el.style.setProperty('max-height', 'none', 'important');
                el.style.setProperty('min-height', 'auto', 'important');
                el.style.setProperty('overflow', 'visible', 'important');
                el.style.setProperty('overflow-y', 'visible', 'important');
                el.style.setProperty('overflow-x', 'visible', 'important');

                // Handle fixed positioning that might limit content
                const computedStyle = window.getComputedStyle(el);
                if (computedStyle.position === 'fixed' || computedStyle.position === 'absolute') {
                    el.style.setProperty('position', 'relative', 'important');
                }
            }
        });
    }

    private restoreStyles(originalStyles: Map<HTMLElement, any>): void {
        originalStyles.forEach((styles, el) => {
            // Check if element had any original inline styles
            const hadAnyInlineStyles = Object.values(styles.hasInlineStyles).some(v => v);

            if (!hadAnyInlineStyles) {
                // If there were no original inline styles, remove the entire style attribute
                el.removeAttribute('style');
            } else {
                // Clear properties we modified - use setProperty with empty value and important flag
                const propertiesToClear = [
                    'height', 'max-height', 'min-height',
                    'overflow', 'overflow-y', 'overflow-x',
                    'position', 'top', 'bottom'
                ];

                propertiesToClear.forEach(prop => {
                    // First try to remove with important flag
                    el.style.setProperty(prop, '', 'important');
                    // Then remove the property entirely
                    el.style.removeProperty(prop);
                });

                // Now restore ONLY the properties that originally had inline values
                Object.keys(styles.hasInlineStyles).forEach(prop => {
                    if (styles.hasInlineStyles[prop] && styles[prop] !== undefined && styles[prop] !== '') {
                        // Convert camelCase to kebab-case for setProperty
                        const kebabProp = prop.replace(/[A-Z]/g, m => '-' + m.toLowerCase());
                        el.style.setProperty(kebabProp, styles[prop]);
                    }
                });
            }
        });
    }

    private async generatePDF(canvas: HTMLCanvasElement, fileName: string): Promise<void> {
        const imgData = canvas.toDataURL('image/jpeg', 0.9);
        const imgWidth = canvas.width;
        const imgHeight = canvas.height;

        // Determine orientation based on content width
        const isLandscape = imgWidth > imgHeight;

        // Standard A4 dimensions in mm
        const a4Width = isLandscape ? 297 : 210;
        const a4Height = isLandscape ? 210 : 297;

        // Calculate scaling to fit width
        const scale = a4Width / imgWidth;
        const scaledImgHeight = imgHeight * scale;

        // Calculate how many pages we need
        const pagesNeeded = Math.ceil(scaledImgHeight / a4Height);

        // Create PDF with A4 dimensions
        const pdf = new jspdf(isLandscape ? 'l' : 'p', 'mm', 'a4');

        // Add content across multiple pages
        for (let i = 0; i < pagesNeeded; i++) {
            if (i > 0) {
                pdf.addPage('a4', isLandscape ? 'l' : 'p');
            }

            // Calculate the Y offset for this page
            const yOffset = -i * a4Height;

            // Add the image with the appropriate offset
            pdf.addImage(imgData, 'JPEG', 0, yOffset, a4Width, scaledImgHeight);
        }

        pdf.save(`${fileName}.pdf`);
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
