import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {ConstantsService} from './constants.service';
import {CreateTagDTO, TagDTO, TagSearchDTO} from '../model/Tag';
import {CphPipe} from '../cph.pipe';

@Injectable({providedIn: 'root'})

export class TagService {
   // Cache for rendered tag HTML
   private tagHtmlCache = new Map<number, string>();

   constructor(
      private http: HttpClient,
      private constants: ConstantsService,
      private cphPipe: CphPipe
   ) {
   }

   search(searchParams?: TagSearchDTO): Promise<TagDTO[]> {
      const url = `${this.constants.backEndBaseURL}/api/Tags`;

      return new Promise((resolve, reject) => {
         this.http.get<TagDTO[]>(url, {params: searchParams as any}).subscribe({
            next: (data) => resolve(data),
            error: (error) => reject(error)
         });
      });
   }

   get(id: number): Promise<TagDTO> {
      const url = `${this.constants.backEndBaseURL}/api/Tags/${id}`;
      return new Promise((resolve, reject) => {
         this.http.get<TagDTO>(url).subscribe({
            next: (data) => resolve(data),
            error: (error) => reject(error)
         });
      });
   }

   create(tagData: CreateTagDTO): Promise<TagDTO> {
      const url = `${this.constants.backEndBaseURL}/api/Tags`;
      return new Promise((resolve, reject) => {
         this.http.post<TagDTO>(url, tagData).subscribe({
            next: (data) => resolve(data),
            error: (error) => reject(error)
         });
      });
   }

   patch(id: number, patchDocument: any): Promise<TagDTO> {
      const url = `${this.constants.backEndBaseURL}/api/Tags/${id}`;
      return new Promise((resolve, reject) => {
         this.http.patch<TagDTO>(url, patchDocument)
            .subscribe({
               next: (data) => resolve(data),
               error: (error) => reject(error),
            });
      });
   }

   softDelete(id: number): Promise<TagDTO> {
      const patchDocument = [
         {op: 'replace', path: '/IsActive', value: false}
      ];
      return this.patch(id, patchDocument);
   }

   restore(id: number): Promise<TagDTO> {
      const patchDocument = [
         {op: 'replace', path: '/IsActive', value: true}
      ];
      return this.patch(id, patchDocument);
   }

   update(id: number, tag: Partial<TagDTO>): Promise<TagDTO> {
      const patchDocument = Object.keys(tag).map(key => ({
         op: 'replace',
         path: `/${key}`,
         value: tag[key]
      }));
      return this.patch(id, patchDocument);
   }

   renderSingleTag(tagId: number, useCache: boolean = true): string {

      // Check cache first if enabled
      if (useCache) {
         const cached = this.tagHtmlCache.get(tagId);
         if (cached) {
            return cached;
         }
      }

      const tagList = this.constants.tags;

      const tag = tagList.find(t => t.Id === tagId);
      if (!tag) {
         return '';
      }

      // Generate the tag HTML
      const impactPct = tag.StrategyImpactPct;
      const impactAmount = tag.StrategyImpactAmount;

      let tagContent = tag.Label;

      if (impactPct && impactPct !== 100) {
         tagContent += ' ' + this.cphPipe.transform(impactPct / 100, 'percent1sdp', 1, false);
      }

      if (impactAmount) {
         tagContent += ' ' + this.cphPipe.transform(impactAmount, 'currency', 0, true);
      }

      const tagHtml = `<span class='tag' style='background-color:${tag.Colour}'>${tagContent}</span>`;

      // Cache the result if caching is enabled
      if (useCache) {
         this.tagHtmlCache.set(tag.Id, tagHtml);

         // Limit cache size to prevent memory leaks
         if (this.tagHtmlCache.size > 1000) {
            // Remove oldest entry
            const firstKey = this.tagHtmlCache.keys().next().value;
            this.tagHtmlCache.delete(firstKey);
         }
      }

      return tagHtml;
   }

   clearTagCache(): void {
      this.tagHtmlCache.clear();
   }
}
