import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {ConstantsService} from './constants.service';
import {CreateVehicleAdvertTagDTO, VehicleAdvertTagDTO, VehicleAdvertTagSearchDTO} from '../model/VehicleAdvertTag';

@Injectable({providedIn: 'root'})
export class VehicleAdvertTagService {

   constructor(
      private http: HttpClient,
      private constants: ConstantsService
   ) {
   }

   search(searchParams?: VehicleAdvertTagSearchDTO): Promise<VehicleAdvertTagDTO[]> {
      const url = `${this.constants.backEndBaseURL}/api/VehicleAdvertTags`;

      return new Promise((resolve, reject) => {
         const request = searchParams
            ? this.http.get<VehicleAdvertTagDTO[]>(url, {params: searchParams as any})
            : this.http.get<VehicleAdvertTagDTO[]>(url);

         request.subscribe({
            next: (result) => resolve(result),
            error: (error) => reject(error)
         });
      });
   }

   get(id: number): Promise<VehicleAdvertTagDTO> {
      const url = `${this.constants.backEndBaseURL}/api/VehicleAdvertTags/${id}`;
      return new Promise((resolve, reject) => {
         this.http.get<VehicleAdvertTagDTO>(url).subscribe({
            next: (result) => resolve(result),
            error: (error) => reject(error)
         });
      });
   }

   applyToVehicles(tagId: number, vehicleAdvertIds: number[], updateCache: boolean = true): Promise<VehicleAdvertTagDTO> {
      const url = `${this.constants.backEndBaseURL}/api/VehicleAdvertTags/${tagId}/ApplyToVehicles`;
      return new Promise((resolve, reject) => {
         this.http.put<VehicleAdvertTagDTO>(url, {vehicleAdvertIds: vehicleAdvertIds}).subscribe({
            next: (result) => resolve(result),
            error: (error) => reject(error)
         });
      });
   }

   removeFromVehicles(tagId: number, vehicleAdvertIds: number[], updateCache: boolean = true): Promise<VehicleAdvertTagDTO> {
      const url = `${this.constants.backEndBaseURL}/api/VehicleAdvertTags/${tagId}/RemoveFromVehicles`;
      return new Promise((resolve, reject) => {
         this.http.put<VehicleAdvertTagDTO>(url, {vehicleAdvertIds: vehicleAdvertIds}).subscribe({
            next: (result) => resolve(result),
            error: (error) => reject(error)
         });
      });
   }

   create(vehicleAdvertTagData: CreateVehicleAdvertTagDTO): Promise<VehicleAdvertTagDTO> {
      const url = `${this.constants.backEndBaseURL}/api/VehicleAdvertTags`;
      return new Promise((resolve, reject) => {
         this.http.post<VehicleAdvertTagDTO>(url, vehicleAdvertTagData).subscribe({
            next: (result) => resolve(result),
            error: (error) => reject(error)
         });
      });
   }

   delete(adTagId: number): Promise<VehicleAdvertTagDTO> {
      const url = `${this.constants.backEndBaseURL}/api/VehicleAdvertTags/${adTagId}`;
      return new Promise((resolve, reject) => {
         this.http.delete<VehicleAdvertTagDTO>(url).subscribe({
            next: (result) => resolve(result),
            error: (error) => reject(error)
         });
      });
   }

   patch(id: number, patchDocument: any): Promise<VehicleAdvertTagDTO> {
      const url = `${this.constants.backEndBaseURL}/api/VehicleAdvertTags/${id}`;
      return new Promise((resolve, reject) => {
         this.http.patch<VehicleAdvertTagDTO>(url, patchDocument).subscribe({
            next: (result) => resolve(result),
            error: (error) => reject(error)
         });
      });
   }
}

