import { SparkEnvironment } from "src/app/model/SparkEnvironment";
import packagejson from '../../package.json';

export const environmentFordsOfWinsford: SparkEnvironment = {
  customer: "FordsOfWinsford",
  production: true,
  version: packagejson.version,
  
  franchisePicker: true,
  stockGroupPicker: true,
  lateCostPicker: true,
  orderTypePicker: true,
  
  displayCurrency: "GBP",
  displayCurrencySymbol: '£',
  
  fAndISummary_includeTargets: false,
  sideMenu_stockReport_showBcaColumns: false,

  bookingsBar_barStyle1: true,
  bookingsBar_barStyle2: false,

  dealDetailModal_currencyDP: 0,
  dealDetailModal_componentName: "DealDetailsRRGComponent",
  dealDetailModal_costColumnTranslation: 'Common_CoS',
  dealDetailModal_dealDetailsSection_showVariant: false,
  dealDetailModal_dealDetailsSection_showWebsiteDiscount: true,
  dealDetailModal_dealDetailsSection_showFinanceType: false,
  dealDetailModal_dealDetailsSection_showOEMReference: false,
  dealDetailModal_dealDetailsSection_showQualifyingPartEx: false,
  dealDetailModal_dealDetailsSection_showPhysicalLocation: false,
  dealDetailModal_dealDetailsSection_showIsClosed: false,
  dealDetailModal_dealDetailsSection_showFinanceCo: true,
  dealDetailModal_dealDetailsSection_showDescription: true,
  dealDetailModal_dealDetailsSection_showUnits: true,
  dealDetailModal_dealDetailsSection_showVehicleAge: true,
  dealDetailModal_dealDetailsSection_showIsLateCost: true,
  dealDetailModal_dealDetailsSection_showAuditPass: false,
  dealDetailModal_dealDetailsSection_showInvoiceNo: false,
  dealDetailModal_metalProfitSection_headerTranslation: 'DealDetails_TinProfit',
  dealDetailModal_metalProfitSection_showVATCost: false,
  dealDetailModal_otherProfitSection_showRegBonus: true,
  dealDetailModal_otherProfitSection_showIntroComm: true,
  dealDetailModal_otherProfitSection_showBrokerCost: true,
  dealDetailModal_otherProfitSection_showAccessories: true,
  dealDetailModal_otherProfitSection_showPaintProtectionAccessory: false,
  dealDetailModal_otherProfitSection_showFuel: true,
  dealDetailModal_otherProfitSection_showDelivery: true,
  dealDetailModal_otherProfitSection_showStandardWarranty: true,
  dealDetailModal_otherProfitSection_showPdi: true,
  dealDetailModal_otherProfitSection_showMechPrep: true,
  dealDetailModal_otherProfitSection_showBodyPrep: true,
  dealDetailModal_otherProfitSection_showOther: false,
  dealDetailModal_otherProfitSection_showError: true,
  dealDetailModal_otherProfitSection_showTotal: true,
  dealDetailModal_addonsSection_showPaintProtection: true,
  dealDetailModal_addonsSection_showWarrantyForNewCar: true,
  dealDetailModal_datesSection_showCustomerDestinationDeliveryDate: true,
  dealDetailModal_datesSection_showEnterImportCentreDate: true,
  dealDetailModal_datesSection_showShipDate: true,
  dealDetailModal_datesSection_showExitImportCentreDate: true,
  dealDetailModal_datesSection_showAllocationDate: true,
  dealDetailModal_datesSection_showDateVehicleRecondition: false,
  dealDetailModal_datesSection_showDateFactoryTransportation: false,
  dealDetailModal_datesSection_showDateSiteArrival: false,
  dealDetailModal_datesSection_showDateSiteTransportation: false,
  dealDetailModal_financeProfitSection_show: true,
  dealDetailModal_financeProfitSection_rciFinanceCommissionText: 'DealDetails_RciFinanceCommission',
  dealDetailModal_financeProfitSection_financeCommissionText: 'DealDetails_FinanceCommission',
  dealDetailModal_financeProfitSection_showSelectCommission: true,
  dealDetailModal_financeProfitSection_showProPlusCommission: true,
  dealDetailModal_financeProfitSection_showStandardsCommission: true,
  dealDetailModal_showSmallModal: false,
  dealDetailModal_showTotalProfitExludingFactoryBonusSection: false,
  dealDetailModal_showTotalProfitSection: true,
  dealDetailModal_showOtherProfit: true,
  dealDetailModal_showAddOnProfit: true,
  dealDetailModal_showDealFileSentDate: false,
  dealDetailModal_showStockDetailButton: false,
  dealDetailModal_enableStockItemModalAutotrader: false,
  dealDetailModal_enableSalesExecPicker: false,

  usedStockTable_vindisFormatting: false,
  usedStockTable_tactical: true,
  usedStockTable_exManagementCount: true,
  usedStockTable_exDemo: true,
  usedStockTable_includeDemoWithUsed: false,

  
  sideMenu_pricingHome: true,
  sideMenu_dashboard: false,
  sideMenu_orderBook: false,
  sideMenu_fleetOrderbook: false,
  sideMenu_dealsDoneThisWeek: false,
  sideMenu_dealsForTheMonth: false,
  sideMenu_whiteboard: false,
  sideMenu_performanceLeague: false,
  sideMenu_performanceTrends: false,
  sideMenu_scratchCard: false,
  sideMenu_salesIncentive: false,
  sideMenu_superCup: false,
  sideMenu_superCupTwo: false,
  sideMenu_handoverDiary: false,
  sideMenu_distrinet: false,
  sideMenu_reportingCentre: false,
  sideMenu_stockList: false,
  
  sideMenu_stockInsight: true,
  sideMenu_leavingVehicles: false,
  sideMenu_pricingDashboard: true,
  
  sideMenu_applyStrategy: false,
  sideMenu_locationOptimiser: false,
    sideMenu_vehicleValuation: true,
  sideMenu_salesCommission: false,
  sideMenu_localBargains: true,
  sideMenu_salesExecReview: false,
  sideMenu_stockLanding: false,
  sideMenu_liveForecastInput: false,sideMenu_liveForecastStatus: false,sideMenu_liveForecastReview: false,
  sideMenu_userMaintenance: true,
  sideMenu_siteSettings: true,sideMenu_scheduledReports:true,
  
  sideMenu_advertListingDetail: true,
  sideMenu_bulkValuation: true,
  sideMenu_optOuts: true,
  sideMenu_todaysPrices: true,
  sideMenu_leavingVehicleDetail: true,
  sideMenu_leavingVehicleTrends: true,sideMenu_leavingVehicleTrendsOverTime:true,
  sideMenu_groupTitle: " Fords of Winsford",

  citNoww_excludeAudi: false,
  citNoww_pcOfSalesEnquiries: true,
  citNoww_pcOfInvoicedExtWips: true,
  citNoww_pcOfSalesEnquiriesText: "CitNOW Summary - Videos as a % of Sales Enquiries - ",
  citNoww_pcOfInvoicedExtWipsText: "CitNOW Summary - Videos as a % of Invoiced External WIPs - ",
  citNoww_showSimpleCitNowPersonDetail: false,
  citNoww_showVideosViewed: false,
  citNoww_eDynamixView: true,
  citNoww_hideAftersalesCharts: false,

  dashboard_sections: null,

  dashboard_canChooseMonth: false,
  dashboard_showStockCover: true,
  dashboard_includeExtraSpainMenuButtons: false,
  dashboard_showZoeSales: true,
  dashboard_showHandoverDiarySummary: true,
  dashboard_showCashDebts: true,
  dashboard_showBonusDebts: true,
  dashboard_showDataOriginsButton: false,
  dashboard_showRenaultRegistrations: true,
  dashboard_showDaciaRegistrations: true,
  dashboard_showFleetRegistrations: true,
  dashboard_showUsedStockMerchandising: true,
dashboard_showUsedStockOverage: true,
dashboard_showNewStockOverage: true,
   dashboard_showTradeStockOverage: true,
  dashboard_showCommissions: true,
  dashboard_showFinanceAddonPerformance: true,
  dashboard_showfAndIPerformanceRRG: true,
  dashboard_showVocNPSTile: true,
  dashboard_showActivityLevels: true,
  dashboard_showActivityOverdues: true,
  dashboard_showFleetPerformance: true,
  dashboard_showVoC: true,
  dashboard_showVindisAftersalesDashboard: false,
  dashboard_showRRGAftersalesDashboard: false,
  dashboard_showSpainAftersalesDashboard: false,
  dashboard_showServiceBookings: true,
  dashboard_showCitNow: true,
  dashboard_showImageRatios: false,
  dashboard_showSalesmanEfficiency: true,
  dashboard_showEvhc: true,
  dashboard_showVindisSalesDashboard: false,
  dashboard_showRRGSalesDashboard: false,
  dashboard_showFinanceAddons: true,
  dashboard_showRegistrations: true,
  dashboard_showSimpleDealsByDay: false,
  dashboard_excludeTypesFromBreakDown: ["Used"],
  dashboard_showFinanceAddonsAllSites: false,
  dashboard_showAftersalesDatasets: false,
  dashboard_includeDemoStockInStockHealth: false,

  // Debts
  debts_includeBonuses: true,
  debts_simpleSitesTable: true,
  debts_showAgedOnPicker: true,
  debts_showBonusDebtType: true,

  
  allGroups: ['R', 'O', 'N', 'Z', 'T', 'D'],
  allFamilyCodes: ['AxsBonus', 'BodyGrowth', 'BodyHandling', 'Captive', 'Motrio', 'OeCompMech', 'OeCompOther', 'Oil', 'Other'],
  horizontalBar_forRenault: true,
  horizontalBar_forVindis: false,
  stockItemModal_onlineMerchandising: true,
  
  wipsTable_hideBookingColumn: false,
  wipsTable_hideDepartmentColumn: false,
  wipsTable_hideAccountColumn: false,
  wipsTable_hideDueDateOutColumn: false,
  wipsTable_hideWDateInColumn: false,
  wipsTable_hideWDateOutColumn: false,
  wipsTable_hidePartsColumn: false,
  wipsTable_hideOilColumn: false,
  wipsTable_hideLabourColumn: false,
  wipsTable_hideSubletColumn: false,
  wipsTable_hideProvisionColumn: false,
  wipsTable_hideCreatedColumn: false,
  wipsTable_hideNotesColumn: false,


  stockTable_hideTacticalColumn: false,
  stockTable_hideExManagementColumn: false,
  stockTable_hideExDemoColumn: false,
  
  stockList_hideStockDetailsModal: false,
  stockList_tableColumns: [
      "Id",
      "SiteDescription",
      "StockNumberFull",
      "Reg",
      "VehicleType",
      "ProgressCode",
      "DaysInStock",
      "DaysAtBranch",
      "Make",
      "Model",
      "ModelYear",
      "Description",
      "DisposalRoute",
      "PreviousUseCode",
      "Siv",
      "CarryingValue",
      "IsVatQ",
      "CapProvision",
      "StockcheckLocation",
      "SeenAtLatestStkchk",
      "PriceChanges",
      "AttentionGrabber", "WebSiteCreatedDate", "WebsitePrice", "PriceExtraLine", "DaysOnLine", "ImagesCount", "VideosCount",
      "RRGSiteItemStockId",
      "CapValue", "IsOnWebsite", "PricedProfit",
      'VariantClass',
      'VehicleTypeCode',
      'VehicleSuperType',
      'AccountStatus',
      'Colour',
      'Mileage',
      'Fuel',
      'Doors',
      'Transmission',
      'Options',
      'ShouldBePrepped',
      'IsPrepped',
      'Purchased',
      'Selling',
      'NonRecoverableCosts',
      'DealerFitAccessories',
      'OptionCosts',
      'CapID',
      'CapNotes',
      'CapCode'
    ],

  stockList_franchises: ["R", "N", "D", "A", "Z"],
  siteCompareTile_includeToday: true,
  siteCompareTile_hideReportButtons: true,

  performanceLeague_hideBadges: false,
  performanceLeague_showDeliveredButton: false,
  performanceLeague_showExecAndManagerSelector: false,

  overAgeStockTable_hideDemoColumn: false,
  overAgeStockTable_hideTacticalColumn: false,
  overAgeStockTable_hideExManagementColumn: false,
  overAgeStockTable_hideExDemoColumn: false,
  overAgeStockTable_hideTradeColumn: false,
  overAgeStockTable_usedColumnName: "CoreUsed",

  dealPopover_showMetalProfit: true,
  dealPopover_showOtherProfit: true,
  dealPopover_showFinanceProfit: true,
  dealPopover_showAddons: true,
  dealPopover_showAddonProfit: true,

  orderBook_orderbookTitle: 'Order Book',
  orderBook_showNewOrderbook: false,
  orderBook_showNewDealButton: false,
  orderBook_ordersDescription: 'Orders approved between',
  orderBook_hideDeliverySiteColumn: true,
  orderBook_hideVehicleTypeColumn: false,
  orderBook_hideOemReferenceColumn: true,
  orderBook_hideFinanceProfitColumn: false,
  orderBook_hideVehClassColumn: false,
  orderBook_hideModelColumn: false,
  orderBook_hideModelYearColumn: false,
  orderBook_hideVehicleSourceColumn: false,
  orderBook_hideDaysToDeliverColumn: true,
  orderBook_hideDaysToSaleColumn: true,
  orderBook_hideLocationColumn: true,
  orderBook_hideIsConfirmedColumn: true,
  orderBook_hideVehTypeColumn: false,
  orderBook_hideIsClosedColumn: true,
  orderBook_hideUnitsColumn: false,
  orderBook_hideFinanceTypeColumn: true,
  orderBook_hideIsLateCostColumn: false,
  orderBook_hideAddonsColumn: false,
  orderBook_hideDiscountColumn: true,
  orderBook_hideOtherProfitColumn: false,
  orderBook_hideMetalColumn: false,
  orderBook_hideSalesChannel: false,
  orderBook_hideComments: false,
  orderBook_hideOrderAllocationDate: false,
  orderBook_hideChannelColumn: true,
  orderBook_hideTypeColumn: true,
  orderBook_includeAccgDate: true,
  orderBook_customDateTypes: ["Delivery", "Invoice", "Accounting"],
  orderBook_defaultDateType: "Accounting",
  orderBook_showLateCost: true,
  orderBook_showOrderOptions: true,
  orderBook_showAccountingDateButton: true,
  orderBook_showDeliveryOptionButtons: false,
  orderBook_showMetalSummary: true,
  orderBook_showOtherSummary: true,
  orderBook_showFinanceSummary: true,
  orderBook_showInsuranceSummary: true,
  orderBook_siteColumnWidth: 80,
  orderBook_customerColumnWidth: 130,
  orderBook_vehicleClassColumnWidth: 30,
  orderBook_salesExecColumnWidth: 100,
  orderBook_descriptionColumnWidth: 200,
  orderBook_hideOrderDateSelection: false,
  orderBook_hideAuditColumn: true,
  orderBook_showManagerSelector: false,
  orderBook_hideDateFactoryTransportationColumn: true,
  orderBook_hideDateVehicleReconditionColumn: true,
  orderBook_hideDateSiteTransportationColumn: true,
  orderBook_hideDateSiteArrivalColumn: true,
  orderBook_hideReservedDateColumn: true,

  partsSales_showMarginColPerc: true,
  partsSales_showMarginCol: true,
  partsSales_includeMarginCols: true,

  handoverDiary_includeCustomerName: true,
  handoverDiary_includeLastPhysicalLocation: false,
  handoverDiary_includeHandoverDate: true,
  handoverDiary_isInvoiced: true,
  handoverDiary_isConfirmed: false,
  handoverDiary_showManagerSelector: false,
  
  partsStockDetailedTable_hideCreatedColumn: false,
  partsStockDetailedTable_partStockBarCharts1_headerName: "% >1 year",
  partsStockDetailedTable_partStockBarCharts1_field: "PartsStockRRG.PercentOver1yr",
  partsStockDetailedTable_partStockBarCharts1_colId: "PartsStockRRG.PercentOver1yr",
  partsStockDetailedTable_partStockBarCharts2_headerName: null,
  partsStockDetailedTable_partStockBarCharts2_field: null,
  partsStockDetailedTable_partStockBarCharts2_colId: null,
  partsStockDetailedTable_showPartStockAgeingColumnsForRRG: true,
  partsStockDetailedTable_showPartStockAgeingColumnsForVindis: false,
  partsStockDetailedTable_hideOfWhichColumn: false,
  partsStockDetailedTable_hideDeadValueColumn: false,
  partsStockDetailedTable_hideDormantValueColumn: false,
  partsStockDetailedTable_hideDeadProvColumn: false,
  partsStockDetailedTable_hideDormantProvColumn: false,
  partsStockDetailedTable_setClassesForVindis: false,
  partsStockDetailedTable_setClassesForRRG: true,

  salesPerformance_description: "Orders approved between",
  salesPerformance_showFranchisePicker: true,
  salesPerformance_showLateCostButtons: true,
  salesPerformance_showIncludeExcludeOrders: true,
  salesPerformance_showTradeUnitButtons: true,
  salesPerformance_showMotabilityButtons: false,
  salesPerformance_showOrderRateReportType: true,
  salesPerformance_showCustomReportType: true,
  salesPerformance_showAllSites: false,
salesPerformance_showRetailSalesTranslation: false,

  selectionsServiceEnvironment_ageingOptions: [
    { description: "30 days", ageCutoff: 30 },
    { description: "45 days", ageCutoff: 45 },
    { description: "60 days", ageCutoff: 60 },
    { description: "90 days", ageCutoff: 90 },
    { description: "120 days", ageCutoff: 120 },
    { description: "150 days", ageCutoff: 150 },
    { description: "180 days", ageCutoff: 180 },
  ],
  selectionsServiceEnvironment_ageingOption: { description: "60 days", ageCutoff: 60 },
  selectionsServiceEnvironment_deliveryDateDateType: "Accounting",
  selectionsServiceEnvironment_eligibleForCurrentUserCheck: true,

  serviceBookingsTable_clickSiteEnable: true,
  
  stockReport_showAgePicker: true,
  stockReport_hideOnRRGSiteCol: false,
  stockReport_initialStockReport: "Dashboard_PartsStock_UsedStock",
  stockReport_seeUsedStockReport: true,
  stockReport_seeAllStockReport: true,
  stockReport_seeUsedMerchandisingReport: true,
  stockReport_seeOverageStockReport: true,
  stockReport_seeStockGraphsReport: false,
  stockReport_seeStockByAgeReport: false,
  stockReport_includeReservedCarsOption: false,

  whiteboard_showConfirmed: false,
  whiteboard_showNotConfirmed: false,
  whiteboard_showFinance: true,
  whiteboard_showAddons: true,
  whiteboard_showLateCostPicker: true,
  whiteboard_showManagerSelector: false,
  whiteboard_rrgUKSettings: false,

  serviceChannels: [
    { displayName: "Retail", name: "Retail", channelTags: ["retail"], icon: "fas fa-wrench", hasHours: true, divideByChannelName: "Retail", isLabour: false },
    { displayName: "Internal", name: "Internal", channelTags: ["internal"], icon: "fas fa-car-wash", hasHours: true, divideByChannelName: "Internal", isLabour: false },
    { displayName: "Warranty", name: "Warranty", channelTags: ["warranty"], icon: "fas fa-engine-warning", hasHours: true, divideByChannelName: "Warranty", isLabour: false },
    { displayName: "Labour", name: "Labour", channelTags: ["retail", "internal", "warranty"], isTotal: true, icon: "", hasHours: true, divideByChannelName: "Labour", isLabour: false },
    { displayName: "Tyre", name: "Tyre", channelTags: ["tyre", "sublet"], icon: "fas fa-tire", hasHours: true, divideByChannelName: "Retail", isLabour: false },
    { displayName: "Oil", name: "Oil", channelTags: ["oilWarr", "oilExt", "oilInt", "oil"], icon: "fas fa-oil-can", hasHours: true, divideByChannelName: "Retail", isLabour: false },
    { displayName: "Total", name: "Total", channelTags: ["retail", "internal", "warranty", "tyre", "oilWarr", "oilExt", "oilInt", "oil", "sublet"], isTotal: true, icon: "", hasHours: true, divideByChannelName: "Labour", isLabour: false },
  ],

  partsChannels: [
    { displayName: "Retail", name: "Retail", channelTags: ["retail", "nonfran", "network", "trade"], icon: "fas fa-wrench", channelTag: "retail", hasHours: false, divideByChannelName: "Retail", isLabour: false }, //added network in on 28Aug20
    { displayName: "Internal", name: "Internal", channelTags: ["internal"], icon: "fas fa-car-wash", channelTag: "internal", hasHours: false, divideByChannelName: "Internal", isLabour: false },
    { displayName: "Workshop Internal", name: "Workshop Internal", channelTags: ["wshopInternal"], icon: "fas fa-car-wash", channelTag: "wshopInternal", hasHours: false, divideByChannelName: "Workshop Internal", isLabour: false },
    { displayName: "Workshop Retail", name: "Workshop Retail", channelTags: ["wshopRetail"], icon: "fas fas fa-tire ", channelTag: "wshopRetail", hasHours: false, divideByChannelName: "Workshop Retail", isLabour: false },
    { displayName: "Workshop Warranty", name: "Workshop Warranty", channelTags: ["wshopWarranty"], icon: "fas fa-engine-warning", channelTag: "wshopWarranty", hasHours: false, divideByChannelName: "Workshop Warranty", isLabour: false },
    { displayName: "Total", name: "Total", isTotal: true, channelTags: ["retail", "nonfran", "internal", "wshopInternal", "wshopRetail", "wshopWarranty"], icon: "", channelTag: "total", hasHours: false, divideByChannelName: "Total", isLabour: false },
  ],
  initialPageURL: "/home",

  orderBookURL: "/bulkValuation",
  fleetOrderbookURL: "/bulkValuation",
  product_tyreInsurance: "IsTyre",
  product_tyreAlloyInsurance: "IsTyreAlloy",
  dealDone_showVindisSitePicker: false,
  dealDone_showRRGSitePicker: true,
  dealDone_showRRGPopoverContent: true,
  dealDone_showVindisPopoverContent: false,

  evhc_showTechTable: true,
  evhc_vehiclesCheckedPercent: 100,
  evhc_workQuoted: 205,
  evhc_workSoldPercent: 65,
  evhc_eDynamixView: true,
  evhc_redWorkSoldPercent: 65,
  evhc_amberWorkSoldPercent: 25,

  fAndISummary_showManagerTable: false,
  fAndISummary_removeFleetFromDefaultOrderTypes: false,
  fAndISummary_hideAlloyColumn: true,

  partsStock_includeOfWhichColumns: true,
  partsStockSitesCoverTable_partStockName: "PartsStockRRG",
  dealsForTheMonth_showMetal: true,
  dealsForTheMonth_showOther: true,
  dealsForTheMonth_showFinance: true,
  dealsForTheMonth_showAddons: true,
  dealsForTheMonth_showGpu: true,
  dealsForTheMonth_showSpainSpacing: false,
  dealsForTheMonth_showBroughtInColumn: true,
  dealsForTheMonth_showLateCostPicker: true,
  dealsForTheMonth_showIncludeExcludeOrders: true,

  dealsDoneThisWeek_showPlotOptions: true,

  orderTypePicker_showRetail: true,
  orderTypePicker_showFleet: true,
  orderTypePicker_vindisSettings: false,
  orderTypePicker_rrgSpainSettings: false,

  todayMap_defaultPositionLat: 52.698926,
  todayMap_defaultPositionLong: -1.046534,
  todayMap_defaultZoom: 7,

  vehicleTypePicker_showUsed: true,
  vehicleTypePicker_showNew: true,
  vehicleTypePicker_showAll: true,
  vehicleTypePicker_hiddenVehicleTypes: [],
  
  userSetup_hideUploadReports: true,
  userSetup_hideViewReports: true,
  userSetup_hideCommReview: false,
  userSetup_hideCommSelf: false,
  userSetup_hideSerReviewer: true,
  userSetup_hideSerSubmitter: true,
  userSetup_hideStockLanding: false,
  userSetup_hideSuperCup: false,
  userSetup_hideIsSalesExec: false,
  userSetup_hideAllowReportUpload: true,
  userSetup_hideAllowReportCentre: true,
  userSetup_hideLiveforecast: true,
  userSetup_hideCanEditExecManagerMappings: true,
  userSetup_hideSalesRoles: true,
  userSetup_hideTMgr: true,
  userSetup_allSalesRoles: ['New', 'Used', 'None', 'NewUsed', 'Fleet'],
  userSetup_canReviewStockPrices: true,
  userSetup_canActionStockPrices: true,
  userSetup_canEditStockPriceMatrix: true,
  userSetup_showSpanishJobTitles: false,
  languageSelection: false,
gdpr_showManagerView: false, 



  serviceSummary_showTableTypeSelector: true,
  serviceSummary_defaultTableType: 'Cumulative',
  serviceSummary_tableTypes: ['Cumulative', 'Daily'],
  serviceSummary_defaultTimeOption: "MTD",
  serviceSummary_timeOptions: ["MTD", "WTD", "Yesterday"],
  serviceSummary_removeNonLabour: false,
  serviceSummary_showTechGroupColumns: false,

  partsSummary_showTableTypeSelector: true,
  partsSummary_tableTypes: ['Cumulative', 'Daily'],

  serviceSalesDashboard_onlyLabour: false,
  
  donutTile_ShowLastYearUnits: false,


  
  showNewUsedSummaryBadges: false,
  showPrepCostsWhenValuing: false,
  isSingleSiteGroup: true,

  showRotationButton:false,
  showChangePriceNowInputAlways: false,

  
  
  
  
  
  
  
  
  
  
  sideMenu_orderBookNew:false,sideMenu_simpleExample:false,
  
  
  
  
  
  
  
  sideMenu_teleStats: false,
  
  
  
  sideMenu_salesPerformance: false,
  sideMenu_alcopa: false,
  sideMenu_showOrderRate: false,
  sideMenu_registrationsPosition: false,
  sideMenu_fAndISummary: false,
  sideMenu_stockReportOld: false,
  
  sideMenu_debtsSales: false,
  sideMenu_debtsAfterSales: false,
  sideMenu_citnow: false,
  sideMenu_imageRatios: false,
  sideMenu_gdpr: false,
  sideMenu_salesActivity: false,
  
  
  sideMenu_serviceSummary: false,
  sideMenu_serviceBookings: false,
  sideMenu_evhc: false,
  sideMenu_upsells: false,sideMenu_wipReport: false,
  
  
  
  sideMenu_partsSummary: false,
  sideMenu_partsStock: false,
  
  
  sideMenu_salesmanEfficiency: false,
          sideMenu_hasVehiclePricing: true,
  sideMenu_ShowDetailedMenu:true,
  sideMenu_statsDashboard: true,sideMenu_stockProfiler:false,sideMenu_sitesLeague:true,
  sideMenu_stockReport: false,
          
      sideMenu_advertSimpleListing: true,
  sideMenu_home: true,
  
  sideMenu_hasOperationReports: false,
  sideMenu_dashboardSales: false,
  sideMenu_dashboardAfterSales: false,
  sideMenu_siteCompare: false,
  sideMenu_dashboardNewKPIs: false,
  sideMenu_dashboardUsedKPIs: false,
  sideMenu_dashboardOverviewSpain: false,
  sideMenu_orderRate: false,
  sideMenu_dashboardOverviewVindis: false, sideMenu_strategyPriceBuildUp: false, sideMenu_leavingVehicleExplorer: false,

  showNestedSideMenu: false,
  showLatestSnapshotDate: true,
  showApproveAutoPrices:false,
  dealershipBackgroundImageName: 'FordsOfWinsford',
  homeIsLandingPage: true,
  showRegionFilterOnSiteDashboard: false,
  showWholesaleAdjustmentOption: false,
  usageReport_CommissionPageName: null,
runRateTile_hideBudget: false,

  departmentDealBreakDown_RRG: false,
  userModal_userSitePicker_includeInactive: false,
  userModal_doNotRequireCurrentRetailerSite: false,
  userModal_showRetailSiteErrorMessage: true,
  departmentDealBreakDown_Vindis: false,
  vehicleTypePicker_showVehicleTypePickerSpain: false,
  noDateSuffix: false,
  salesmenCanViewWebAppCommission: false,
  donutTile_ShowInvoicingTitle: false,
  showRunChaseTileModal: false,
  stockOverageTile_SpainThresholds: false,
  spainFranchisePickerClass: false,
  donutTile_SpainAdditionalMeasures: false,
  ordersDonutTile_SpainSettings: false,
  salesActivity_showManagerTable: false,
  showTodayMap: true,
  stockInsight_showPrepCost: true,
  stockInsight_showAverageDIS: false,
  stockOverageTile_excludeEOM: false,
  commissionSchemeName: null,


};