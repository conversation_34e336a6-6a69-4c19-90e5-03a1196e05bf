import { SparkEnvironment } from "src/app/model/SparkEnvironment";
import packagejson from "../../package.json";

export const environmentSpain: SparkEnvironment = {
   customer: "RRGSpain",
   production: true,
   version: packagejson.version,
   //webURL: 'https://sparkrrges.cphi.co.uk',
   //backEndBaseURL: 'https://sparkrrgesapi.cphi.co.uk',

   showTodayMap: false,
   franchisePicker: true,
   spainFranchisePickerClass: true,
   stockGroupPicker: true,
   lateCostPicker: false,
   orderTypePicker: true,

   displayCurrency: "EUR",
   displayCurrencySymbol: "€",

   bookingsBar_barStyle1: true,
   bookingsBar_barStyle2: false,

   fAndISummary_includeTargets: false,
   sideMenu_stockReport_showBcaColumns: false,

   // CitNoww
   citNoww_excludeAudi: false,
   citNoww_pcOfSalesEnquiries: true,
   citNoww_pcOfInvoicedExtWips: true,
   citNoww_pcOfSalesEnquiriesText: "CitNOW Resumen: videos como% de consultas de ventas - ",
   citNoww_pcOfInvoicedExtWipsText: "CitNOW Resumen: vídeos como porcentaje de WIP externos facturados - ",
   citNoww_showSimpleCitNowPersonDetail: false,
   citNoww_showVideosViewed: false,
   citNoww_eDynamixView: false,
   citNoww_hideAftersalesCharts: false,

   // Debts
   debts_includeBonuses: false,
   debts_simpleSitesTable: true,
   debts_showAgedOnPicker: true,
   debts_showBonusDebtType: false,

   salesActivity_showManagerTable: false,

   dealPopover_showMetalProfit: false,
   dealPopover_showOtherProfit: false,
   dealPopover_showFinanceProfit: false,
   dealPopover_showAddons: false,
   dealPopover_showAddonProfit: false,

   stockInsight_showPrepCost: true,
   stockInsight_showAverageDIS: false,

   usedStockTable_vindisFormatting: false,
   usedStockTable_tactical: true,
   usedStockTable_exManagementCount: true,
   usedStockTable_exDemo: true,
   usedStockTable_includeDemoWithUsed: false,

   
   sideMenu_pricingHome: false,
   sideMenu_dashboard: true,
   sideMenu_orderBook: true,
   sideMenu_fleetOrderbook: false,
   sideMenu_dealsDoneThisWeek: true,
   sideMenu_dealsForTheMonth: true,
   sideMenu_whiteboard: true,
   sideMenu_performanceLeague: true,
   sideMenu_performanceTrends: false,
   sideMenu_scratchCard: false,
   sideMenu_salesIncentive: false,
   sideMenu_superCup: false,
   sideMenu_superCupTwo: false,
   sideMenu_handoverDiary: true,
   sideMenu_distrinet: true,
   sideMenu_reportingCentre: false,
   sideMenu_stockList: true,
   sideMenu_leavingVehicles: false,
   sideMenu_stockInsight: false,
   
   sideMenu_pricingDashboard: false,
   
   sideMenu_applyStrategy: false,
   sideMenu_locationOptimiser: false,
     sideMenu_vehicleValuation: false,
   sideMenu_salesCommission: false,
   sideMenu_localBargains: false,
   sideMenu_salesExecReview: false,
   sideMenu_stockLanding: false,
   sideMenu_liveForecastInput: false,sideMenu_liveForecastStatus: false,sideMenu_liveForecastReview: false,
   sideMenu_userMaintenance: true,
   sideMenu_siteSettings: false,
   sideMenu_scheduledReports:false,
   
   sideMenu_advertListingDetail: false,
   sideMenu_bulkValuation: false,
   sideMenu_optOuts: false,
   sideMenu_todaysPrices: false,
   sideMenu_leavingVehicleDetail: false,
   sideMenu_leavingVehicleTrends: false,sideMenu_leavingVehicleTrendsOverTime:false,
   sideMenu_groupTitle: " RRG Espana ",

   dashboard_sections: [
      {
         sectionName: "DashboardOverviewSpain",
         translatedTextField: "Dashboard_KPIsTitle",
         translatedTextValue: "",
         pageName: "dashboardOverviewSpain",
         enableSitesSelector: true,
         pages: [
            {
               pageName: "SalesPerformance",
               translatedTextField: "Dashboard_SalesPerformance_Title",
               translatedTextValue: "",
            },
            { pageName: "Alcopa", translatedTextField: "Dashboard_Alcopa_Title", translatedTextValue: "" },
            { pageName: "OrderRate", translatedTextField: "Common_OrderRate", translatedTextValue: "" },
            { pageName: "Debtors", translatedTextField: "Dashboard_Debtors_Title", translatedTextValue: "" },
         ],
      },

      {
         sectionName: "DashboardNewSpain",
         translatedTextField: "Dashboard_NewKPIsTitle",
         translatedTextValue: "",
         pageName: "dashboardNewVNSpain",
         enableSitesSelector: true,
         pages: [
            {
               pageName: "SalesPerformance",
               translatedTextField: "Dashboard_SalesPerformance_Title",
               translatedTextValue: "",
            },
            { pageName: "Alcopa", translatedTextField: "Dashboard_Alcopa_Title", translatedTextValue: "" },
            { pageName: "OrderRate", translatedTextField: "Common_OrderRate", translatedTextValue: "" },
            { pageName: "Debtors", translatedTextField: "Dashboard_Debtors_Title", translatedTextValue: "" },
         ],
      },

      {
         sectionName: "DashboardUsedSpain",
         translatedTextField: "Dashboard_UsedKPIsTitle",
         translatedTextValue: "",
         pageName: "dashboardUsedSpain",
         enableSitesSelector: true,
         pages: [
            {
               pageName: "SalesPerformance",
               translatedTextField: "Dashboard_SalesPerformance_Title",
               translatedTextValue: "",
            },
            { pageName: "Alcopa", translatedTextField: "Dashboard_Alcopa_Title", translatedTextValue: "" },
            { pageName: "OrderRate", translatedTextField: "Common_OrderRate", translatedTextValue: "" },
            { pageName: "Debtors", translatedTextField: "Dashboard_Debtors_Title", translatedTextValue: "" },
         ],
      },

      {
         sectionName: "DashboardAftersalesSpain",
         translatedTextField: "Common_Aftersales",
         translatedTextValue: "",
         pageName: "dashboardAftersales",
         enableSitesSelector: true,
         pages: [
            { pageName: "dashboardAftersales", translatedTextField: "Dashboard_Title", translatedTextValue: "" },
            { pageName: "AftersalesKPIs", translatedTextField: "Dashboard_KPIs", translatedTextValue: "" },
            { pageName: "AftersalesDetail", translatedTextField: "Common_Detail", translatedTextValue: "" },
            { pageName: "AftersalesDatasets", translatedTextField: "Dashboard_Datasets", translatedTextValue: "" },
         ],
      },

      //Site compare dashboard
      {
         sectionName: "SiteCompare",
         translatedTextField: "SiteCompare",
         translatedTextValue: "",
         pageName: "dashboardSiteCompare",
         enableSitesSelector: true,
         pages: [],
      },
   ],

   dashboard_canChooseMonth: true,
   dashboard_showStockCover: false,
   dashboard_includeExtraSpainMenuButtons: true,
   dashboard_showZoeSales: true,
   dashboard_showHandoverDiarySummary: true,
   dashboard_showCashDebts: true,
   dashboard_showBonusDebts: true,
   dashboard_showRenaultRegistrations: false,
   dashboard_showDaciaRegistrations: false,
   dashboard_showFleetRegistrations: true,
   dashboard_showUsedStockMerchandising: false,
   dashboard_showUsedStockOverage: true,
   dashboard_showNewStockOverage: true,
   dashboard_showTradeStockOverage: true,
   dashboard_showCommissions: false,
   dashboard_showVocNPSTile: true,
   dashboard_showVindisAftersalesDashboard: false,
   dashboard_showRRGAftersalesDashboard: false,
   dashboard_showSpainAftersalesDashboard: true,
   dashboard_showfAndIPerformanceRRG: false,
   dashboard_showActivityLevels: false,
   dashboard_showActivityOverdues: false,
   dashboard_showFinanceAddonPerformance: false,
   dashboard_showFleetPerformance: true,
   dashboard_showVoC: false,
   dashboard_showServiceBookings: false,
   dashboard_showCitNow: false,
   dashboard_showImageRatios: false,
   dashboard_showSalesmanEfficiency: false,
   dashboard_showEvhc: false,
   dashboard_showFinanceAddons: false,
   dashboard_showRegistrations: false,
   dashboard_showSimpleDealsByDay: true,
   dashboard_showVindisSalesDashboard: false,
   dashboard_showRRGSalesDashboard: false,
   dashboard_excludeTypesFromBreakDown: null,
   dashboard_showFinanceAddonsAllSites: false,
   dashboard_showDataOriginsButton: true,
   dashboard_showAftersalesDatasets: true,
   dashboard_includeDemoStockInStockHealth: false,

   horizontalBar_forRenault: true,
   horizontalBar_forVindis: false,

   stockItemModal_onlineMerchandising: true,

   wipsTable_hideBookingColumn: false,
   wipsTable_hideDepartmentColumn: false,
   wipsTable_hideAccountColumn: false,
   wipsTable_hideDueDateOutColumn: true,
   wipsTable_hideWDateInColumn: true,
   wipsTable_hideWDateOutColumn: true,
   wipsTable_hidePartsColumn: false,
   wipsTable_hideOilColumn: true,
   wipsTable_hideLabourColumn: false,
   wipsTable_hideSubletColumn: true,
   wipsTable_hideProvisionColumn: true,
   wipsTable_hideCreatedColumn: false,
   wipsTable_hideNotesColumn: false,

   stockTable_hideTacticalColumn: false,
   stockTable_hideExManagementColumn: false,
   stockTable_hideExDemoColumn: false,

   stockList_hideStockDetailsModal: true,
   stockList_tableColumns: [
      "Id",
      "SiteCode",
      "SiteDescription",
      "StockNumber",
      "Reg",
      "VehicleType",
      "Chassis",
      "RegDate",
      "StockDate",
      "DaysInStock",
      "ReservedDate",
      "Make",
      "Model",
      "ModelYear",
      "Description",
      "VehicleType",
      "VehicleSuperType",
      "DisposalRoute",
      "Colour",
      "Mileage",
      "Fuel",
      "Doors",
      "Transmission",
      "C02",
      "Purchased",
      "Siv",
      "Selling",
      "VehicleTypeCode",
      "PhysicalLocation",
      "Zona",
      "DisposalRouteEs",
      "MakeEs",
      "ModelEs",
      "VehicleSource",
      "Antiguedad_Stock",
      "Edad_Vehiculo",
      "Concesionario",
      "Ubicacion",
      "DateFactoryTransportation",
      "DateVehicleRecondition",
      "DateSiteTransportation",
      "DateSiteArrival",
      "IdFamily",
      "IdBrand",
      "IdLocation",
      "IdDataSource",
   ],
   stockList_franchises: ["R", "D", "A", "Z"],

   performanceLeague_hideBadges: true,
   performanceLeague_showDeliveredButton: false,
   performanceLeague_showExecAndManagerSelector: false,

   siteCompareTile_includeToday: false,
   siteCompareTile_hideReportButtons: true,

   overAgeStockTable_hideDemoColumn: true,
   overAgeStockTable_hideTacticalColumn: true,
   overAgeStockTable_hideExManagementColumn: true,
   overAgeStockTable_hideExDemoColumn: true,
   overAgeStockTable_hideTradeColumn: true,
   overAgeStockTable_usedColumnName: "Used",

   partsSales_showMarginColPerc: false,
   partsSales_showMarginCol: false,
   partsSales_includeMarginCols: false,

   orderBook_orderbookTitle: null, // We will use translation here; not required
   orderBook_showNewOrderbook: false,
   orderBook_showNewDealButton: false,
   orderBook_ordersDescription: "Pedidos aprobados entre",
   orderBook_hideDeliverySiteColumn: true,
   orderBook_hideVehicleTypeColumn: true,
   orderBook_hideVehClassColumn: true,
   orderBook_hideFinanceProfitColumn: true,
   orderBook_hideVehTypeColumn: true,
   orderBook_hideModelColumn: false,
   orderBook_hideModelYearColumn: false,
   orderBook_hideVehicleSourceColumn: true,
   orderBook_hideIsConfirmedColumn: true,
   orderBook_hideIsClosedColumn: true,
   orderBook_hideUnitsColumn: false,
   orderBook_hideOtherProfitColumn: true,
   orderBook_hideFinanceTypeColumn: true,
   orderBook_hideIsLateCostColumn: true,
   orderBook_hideMetalColumn: true,
   orderBook_hideSalesChannel: true,
   orderBook_hideComments: true,
   orderBook_hideOrderAllocationDate: true,
   orderBook_customDateTypes: ["Fecha de entrega", "Fecha de la factura", "Fecha de contabilidad"],
   orderBook_defaultDateType: "Invoice",
   orderBook_showLateCost: true,
   orderBook_showAccountingDateButton: true,
   orderBook_showDeliveryOptionButtons: true,
   orderBook_showOrderOptions: false,
   orderBook_hideDiscountColumn: true,
   orderBook_includeAccgDate: false,
   orderBook_hideAddonsColumn: true,
   orderBook_hideChannelColumn: false,
   orderBook_hideTypeColumn: false,
   orderBook_showMetalSummary: false,
   orderBook_showOtherSummary: false,
   orderBook_showFinanceSummary: false,
   orderBook_showInsuranceSummary: false,
   orderBook_siteColumnWidth: 130,
   orderBook_customerColumnWidth: 220,
   orderBook_vehicleClassColumnWidth: 90,
   orderBook_salesExecColumnWidth: 200,
   orderBook_descriptionColumnWidth: 300,
   orderBook_hideOrderDateSelection: true,
   orderBook_hideDaysToDeliverColumn: true,
   orderBook_hideDaysToSaleColumn: true,
   orderBook_hideLocationColumn: true,
   orderBook_hideOemReferenceColumn: true,
   orderBook_hideAuditColumn: true,
   orderBook_showManagerSelector: false,
   orderBook_hideDateFactoryTransportationColumn: false,
   orderBook_hideDateVehicleReconditionColumn: false,
   orderBook_hideDateSiteTransportationColumn: false,
   orderBook_hideDateSiteArrivalColumn: false,
   orderBook_hideReservedDateColumn: false,

   handoverDiary_includeCustomerName: true,
   handoverDiary_includeLastPhysicalLocation: false,
   handoverDiary_includeHandoverDate: true,
   handoverDiary_isInvoiced: true,
   handoverDiary_isConfirmed: false,
   handoverDiary_showManagerSelector: false,

   partsStockDetailedTable_hideCreatedColumn: false,
   partsStockDetailedTable_partStockBarCharts1_headerName: "% >1 year",
   partsStockDetailedTable_partStockBarCharts1_field: "PartsStockRRG.PercentOver1yr",
   partsStockDetailedTable_partStockBarCharts1_colId: "PartsStockRRG.PercentOver1yr",

   partsStockDetailedTable_partStockBarCharts2_headerName: null,
   partsStockDetailedTable_partStockBarCharts2_field: null,
   partsStockDetailedTable_partStockBarCharts2_colId: null,
   partsStockDetailedTable_showPartStockAgeingColumnsForRRG: true,
   partsStockDetailedTable_showPartStockAgeingColumnsForVindis: false,
   partsStockDetailedTable_hideOfWhichColumn: true,
   partsStockDetailedTable_hideDeadValueColumn: true,
   partsStockDetailedTable_hideDormantValueColumn: false,
   partsStockDetailedTable_hideDeadProvColumn: false,
   partsStockDetailedTable_hideDormantProvColumn: false,
   partsStockDetailedTable_setClassesForVindis: false,
   partsStockDetailedTable_setClassesForRRG: true,

   salesPerformance_description: "Pedidos aprobados entre",
   salesPerformance_showFranchisePicker: false,
   salesPerformance_showLateCostButtons: false,
   salesPerformance_showIncludeExcludeOrders: false,
   salesPerformance_showTradeUnitButtons: false,
   salesPerformance_showMotabilityButtons: false,
   salesPerformance_showOrderRateReportType: false,
   salesPerformance_showCustomReportType: true,
   salesPerformance_showAllSites: false,
   salesPerformance_showRetailSalesTranslation: true,

   selectionsServiceEnvironment_ageingOptions: [
      { description: "30 dias", ageCutoff: 30 },
      { description: "60 dias", ageCutoff: 60 },
      { description: "90 dias", ageCutoff: 90 },
      { description: "120 dias", ageCutoff: 120 },
      { description: "150 dias", ageCutoff: 150 },
      { description: "180 dias", ageCutoff: 180 },
      { description: "1 año", ageCutoff: 365 },
   ],
   selectionsServiceEnvironment_ageingOption: { description: "60 dias", ageCutoff: 60 },
   selectionsServiceEnvironment_deliveryDateDateType: "Fecha de contabilidad",
   selectionsServiceEnvironment_eligibleForCurrentUserCheck: true,

   serviceBookingsTable_clickSiteEnable: true,

   stockReport_showAgePicker: false,
   stockReport_hideOnRRGSiteCol: false,
   stockReport_initialStockReport: "Dashboard_PartsStock_UsedStock",
   stockReport_seeUsedStockReport: false,
   stockReport_seeAllStockReport: false,
   stockReport_seeUsedMerchandisingReport: false,
   stockReport_seeOverageStockReport: false,
   stockReport_seeStockGraphsReport: true,
   stockReport_seeStockByAgeReport: true,
   stockReport_includeReservedCarsOption: false,

   whiteboard_showConfirmed: false,
   whiteboard_showNotConfirmed: false,
   whiteboard_showFinance: false,
   whiteboard_showAddons: false,
   whiteboard_showLateCostPicker: true,
   whiteboard_showManagerSelector: false,
   whiteboard_rrgUKSettings: false,

   serviceChannels: [
      {
         displayName: "Mecánica",
         name: "Mechanical",
         channelTags: ["mechanical"],
         icon: "fas fa-wrench",
         hasHours: true,
         divideByChannelName: "Mechanical",
         isLabour: true,
      },
      {
         displayName: "RMS",
         name: "RMS",
         channelTags: ["rms"],
         icon: "fas fa-car-wash",
         hasHours: true,
         divideByChannelName: "RMS",
         isLabour: true,
      },
      {
         displayName: "Carrocería",
         name: "Bodyshop",
         channelTags: ["bodyshop"],
         icon: "fas fa-engine-warning",
         hasHours: true,
         divideByChannelName: "Bodyshop",
         isLabour: true,
      },
      {
         displayName: "Total",
         name: "Total",
         channelTags: ["mechanical", "rms", "bodyshop"],
         isTotal: true,
         icon: "",
         hasHours: true,
         divideByChannelName: "Total",
         isLabour: true,
      },
   ],

   partsChannels: [
      {
         displayName: "Recnmbios",
         name: "Retail",
         channelTags: ["retail"],
         icon: "fas fa-wrench",
         channelTag: "retail",
         hasHours: false,
         divideByChannelName: "Retail",
         isLabour: false,
      },
      {
         displayName: "Cesiones",
         name: "Transfers",
         channelTags: ["transfers"],
         icon: "fas fa-car-wash",
         channelTag: "transfers",
         hasHours: false,
         divideByChannelName: "Transfers",
         isLabour: false,
      },
      {
         displayName: "Taller",
         name: "Workshop",
         channelTags: ["workshop"],
         icon: "fas fas fa-tire ",
         channelTag: "workshop",
         hasHours: false,
         divideByChannelName: "Workshop",
         isLabour: false,
      },
      {
         displayName: "Garantia",
         name: "Warranty",
         channelTags: ["warranty"],
         icon: "fas fa-engine-warning",
         channelTag: "warranty",
         hasHours: false,
         divideByChannelName: "Warranty",
         isLabour: false,
      },
      {
         displayName: "Total",
         name: "Total",
         isTotal: true,
         channelTags: ["retail", "transfers", "workshop", "warranty"],
         icon: "",
         channelTag: "total",
         hasHours: false,
         divideByChannelName: "Total",
         isLabour: false,
      },
   ],
   initialPageURL: "/dashboardOverviewSpain",

   orderBookURL: "/orderBook",
   fleetOrderbookURL: "/fleetOrderbook",

   product_tyreInsurance: "deal.HasTyre",
   product_tyreAlloyInsurance: "deal.HasTyreAlloy",

   dealDone_showVindisSitePicker: false,
   dealDone_showRRGSitePicker: false,
   dealDone_showRRGPopoverContent: true,
   dealDone_showVindisPopoverContent: false,

   evhc_showTechTable: true,
   evhc_vehiclesCheckedPercent: 100,
   evhc_workQuoted: 205,
   evhc_workSoldPercent: 65,
   evhc_eDynamixView: false,
   evhc_redWorkSoldPercent: 65,
   evhc_amberWorkSoldPercent: 25,

   fAndISummary_removeFleetFromDefaultOrderTypes: false,
   fAndISummary_showManagerTable: false,
   fAndISummary_hideAlloyColumn: false,

   partsStock_includeOfWhichColumns: false,

   dealsForTheMonth_showMetal: false,
   dealsForTheMonth_showOther: false,
   dealsForTheMonth_showFinance: false,
   dealsForTheMonth_showAddons: false,
   dealsForTheMonth_showGpu: false,
   dealsForTheMonth_showSpainSpacing: true,
   dealsForTheMonth_showBroughtInColumn: false,
   dealsForTheMonth_showIncludeExcludeOrders: false,
   dealsForTheMonth_showLateCostPicker: true,

   allGroups: ["O", "RE"],
   allFamilyCodes: [
      "P.PROFESIONAL",
      "MOTRIO",
      "VARIOS",
      "NULL",
      "NEUMATICOS",
      "OTROS",
      "ACC.Y BOUTIQUE",
      "LUBRICANTES",
      "IXELL",
      "MANT.Y DESGASTE",
      "CARROC Y PINTUR",
      "MECANICA INCIDE",
   ],

   partsStockSitesCoverTable_partStockName: "PartsStockRRG",
   dealsDoneThisWeek_showPlotOptions: false,

   orderTypePicker_showRetail: false,
   orderTypePicker_showFleet: false,
   orderTypePicker_vindisSettings: false,
   orderTypePicker_rrgSpainSettings: true,

   todayMap_defaultPositionLat: 39.955071,
   todayMap_defaultPositionLong: -1,
   todayMap_defaultZoom: 7,

   vehicleTypePicker_showUsed: false,
   vehicleTypePicker_showNew: false,
   vehicleTypePicker_showAll: false,
   vehicleTypePicker_hiddenVehicleTypes: ["Demo"],

   userSetup_hideUploadReports: true,
   userSetup_hideViewReports: true,
   userSetup_hideCommReview: true,
   userSetup_hideCommSelf: true,
   userSetup_hideSerReviewer: true,
   userSetup_hideSerSubmitter: true,
   userSetup_hideStockLanding: true,
   userSetup_hideSuperCup: true,
   userSetup_hideIsSalesExec: true,
   userSetup_hideAllowReportUpload: true,
   userSetup_hideAllowReportCentre: true,
   userSetup_hideCanEditExecManagerMappings: true,
   userSetup_hideLiveforecast: true,
   userSetup_hideSalesRoles: true,
   userSetup_hideTMgr: true,
   userSetup_allSalesRoles: ["None"],
   userSetup_canReviewStockPrices: false,
   userSetup_canActionStockPrices: false,
   userSetup_canEditStockPriceMatrix: false,
   userSetup_showSpanishJobTitles: true,

   languageSelection: true,

   serviceSummary_showTableTypeSelector: false,
   serviceSummary_defaultTableType: null,
   serviceSummary_tableTypes: null,
   serviceSummary_defaultTimeOption: "MTD",
   serviceSummary_timeOptions: ["MTD", "WTD", "Yesterday"],
   serviceSummary_removeNonLabour: false,
   serviceSummary_showTechGroupColumns: false,

   partsSummary_showTableTypeSelector: false,
   partsSummary_tableTypes: null,

   serviceSalesDashboard_onlyLabour: true,

   dealDetailModal_currencyDP: 0,
   dealDetailModal_componentName: "DealDetailsRRGComponent",
   dealDetailModal_costColumnTranslation: "Common_CoS",

   dealDetailModal_dealDetailsSection_showVariant: true,
   dealDetailModal_dealDetailsSection_showWebsiteDiscount: false,
   dealDetailModal_dealDetailsSection_showFinanceType: false,
   dealDetailModal_dealDetailsSection_showOEMReference: false,
   dealDetailModal_dealDetailsSection_showQualifyingPartEx: false,
   dealDetailModal_dealDetailsSection_showPhysicalLocation: false,
   dealDetailModal_dealDetailsSection_showIsClosed: false,
   dealDetailModal_dealDetailsSection_showFinanceCo: true,
   dealDetailModal_dealDetailsSection_showDescription: true,
   dealDetailModal_dealDetailsSection_showUnits: true,
   dealDetailModal_dealDetailsSection_showVehicleAge: true,
   dealDetailModal_dealDetailsSection_showIsLateCost: true,
   dealDetailModal_dealDetailsSection_showAuditPass: false,
   dealDetailModal_dealDetailsSection_showInvoiceNo: true,

   dealDetailModal_metalProfitSection_headerTranslation: "DealDetails_TinProfit",
   dealDetailModal_metalProfitSection_showVATCost: false,

   dealDetailModal_otherProfitSection_showRegBonus: true,
   dealDetailModal_otherProfitSection_showIntroComm: true,
   dealDetailModal_otherProfitSection_showBrokerCost: false,
   dealDetailModal_otherProfitSection_showAccessories: false,
   dealDetailModal_otherProfitSection_showPaintProtectionAccessory: false,
   dealDetailModal_otherProfitSection_showFuel: false,
   dealDetailModal_otherProfitSection_showDelivery: false,
   dealDetailModal_otherProfitSection_showStandardWarranty: false,
   dealDetailModal_otherProfitSection_showPdi: false,
   dealDetailModal_otherProfitSection_showMechPrep: false,
   dealDetailModal_otherProfitSection_showBodyPrep: false,
   dealDetailModal_otherProfitSection_showOther: false,
   dealDetailModal_otherProfitSection_showError: false,
   dealDetailModal_otherProfitSection_showTotal: false,

   dealDetailModal_addonsSection_showPaintProtection: true,
   dealDetailModal_addonsSection_showWarrantyForNewCar: false,

   dealDetailModal_datesSection_showCustomerDestinationDeliveryDate: true,
   dealDetailModal_datesSection_showEnterImportCentreDate: true,
   dealDetailModal_datesSection_showShipDate: true,
   dealDetailModal_datesSection_showExitImportCentreDate: true,
   dealDetailModal_datesSection_showAllocationDate: true,
   dealDetailModal_datesSection_showDateVehicleRecondition: true,
   dealDetailModal_datesSection_showDateFactoryTransportation: true,
   dealDetailModal_datesSection_showDateSiteArrival: true,
   dealDetailModal_datesSection_showDateSiteTransportation: true,

   dealDetailModal_financeProfitSection_show: false,
   dealDetailModal_financeProfitSection_rciFinanceCommissionText: "DealDetails_RciFinanceCommission",
   dealDetailModal_financeProfitSection_financeCommissionText: "DealDetails_FinanceCommission",
   dealDetailModal_financeProfitSection_showSelectCommission: true,
   dealDetailModal_financeProfitSection_showProPlusCommission: true,
   dealDetailModal_financeProfitSection_showStandardsCommission: true,

   dealDetailModal_showSmallModal: true,
   dealDetailModal_showOtherProfit: false,
   dealDetailModal_showAddOnProfit: false,
   dealDetailModal_showDealFileSentDate: false,
   dealDetailModal_showStockDetailButton: false,
   dealDetailModal_enableStockItemModalAutotrader: false,
   dealDetailModal_enableSalesExecPicker: false,
   dealDetailModal_showTotalProfitExludingFactoryBonusSection: false,
   dealDetailModal_showTotalProfitSection: true,

   donutTile_ShowLastYearUnits: true,
   showNewUsedSummaryBadges: false,
   showPrepCostsWhenValuing: false,
   showChangePriceNowInputAlways: false,

   isSingleSiteGroup: false,

   
   
   
   
   
   
   
   
   
   
   sideMenu_orderBookNew:true,sideMenu_simpleExample:false,
   
   
   
   
   
   
   
   sideMenu_teleStats: false,
   
   
   
   sideMenu_salesPerformance: true,
   sideMenu_alcopa: true,
   sideMenu_showOrderRate: true,
   sideMenu_registrationsPosition: false,
   sideMenu_fAndISummary: false,
   sideMenu_stockReportOld: false,
   
   sideMenu_debtsSales: true,
   sideMenu_debtsAfterSales: false,
   sideMenu_citnow: false,
   sideMenu_imageRatios: false,
   sideMenu_gdpr: false,
   sideMenu_salesActivity: false,
   
   
   sideMenu_serviceSummary: false,
   sideMenu_serviceBookings: false,
   sideMenu_evhc: false,
   sideMenu_upsells: false,sideMenu_wipReport: false,
   
   
   
   sideMenu_partsSummary: false,
   sideMenu_partsStock: false,
   
   sideMenu_salesmanEfficiency: false,
   
   sideMenu_hasVehiclePricing: false,
   sideMenu_ShowDetailedMenu: false,
   sideMenu_statsDashboard: false,sideMenu_stockProfiler:false,sideMenu_sitesLeague:false,
   sideMenu_stockReport: false,
       
   sideMenu_home: false,
   sideMenu_advertSimpleListing: false,
   
  sideMenu_hasOperationReports: false,
  sideMenu_dashboardSales: true,
  sideMenu_dashboardAfterSales: true,
  sideMenu_siteCompare: true,
  sideMenu_dashboardNewKPIs: true,
  sideMenu_dashboardUsedKPIs: true,
  sideMenu_dashboardOverviewSpain: true,
  sideMenu_orderRate: true,
  sideMenu_dashboardOverviewVindis: false, sideMenu_strategyPriceBuildUp: false, sideMenu_leavingVehicleExplorer: false,

   showRotationButton: false,
   showLatestSnapshotDate: false,
   showApproveAutoPrices: false,
   showNestedSideMenu: true,
   showRegionFilterOnSiteDashboard: false,
   showWholesaleAdjustmentOption: false,
   showRunChaseTileModal: true,

   departmentDealBreakDown_RRG: false,
   userModal_userSitePicker_includeInactive: true,
   userModal_doNotRequireCurrentRetailerSite: true,
   userModal_showRetailSiteErrorMessage: false,
   departmentDealBreakDown_Vindis: false,
   noDateSuffix: true,
   gdpr_showManagerView: false,
   salesmenCanViewWebAppCommission: false,
   stockOverageTile_SpainThresholds: true,
   commissionSchemeName: null,
   donutTile_ShowInvoicingTitle: true,
   donutTile_SpainAdditionalMeasures: true,
   ordersDonutTile_SpainSettings: true,
   vehicleTypePicker_showVehicleTypePickerSpain: true,
   stockOverageTile_excludeEOM: true,
   dealershipBackgroundImageName: "RRGSpain",
   homeIsLandingPage: false,
   usageReport_CommissionPageName: null,
   runRateTile_hideBudget: false,
};
