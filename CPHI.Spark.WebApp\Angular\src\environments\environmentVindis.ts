import { SparkEnvironment } from "src/app/model/SparkEnvironment";
import packagejson from "../../package.json";

export const environmentVindis: SparkEnvironment = {
   customer: "Vindis",
   production: true,
   //webURL: 'https://vindisspark.cphi.co.uk',
   //backEndBaseURL: 'https://vindissparkapi.cphi.co.uk',
   version: packagejson.version,

   franchisePicker: false,
   spainFranchisePickerClass: false,
   stockGroupPicker: false,
   lateCostPicker: false,
   orderTypePicker: true,

   displayCurrency: "GBP",
   displayCurrencySymbol: "£",

   fAndISummary_includeTargets: true,

   // CitNoww
   citNoww_excludeAudi: true,
   citNoww_pcOfSalesEnquiries: true,
   citNoww_pcOfInvoicedExtWips: true,
   citNoww_pcOfSalesEnquiriesText: "CitNOW Summary - Videos Viewed as a % of Sales Enquiries - ",
   citNoww_pcOfInvoicedExtWipsText: "CitNOW Summary - Videos Viewed as a % of Invoiced External WIPs - ",
   citNoww_showSimpleCitNowPersonDetail: true,
   citNoww_showVideosViewed: true,
   citNoww_eDynamixView: false,
   citNoww_hideAftersalesCharts: false,

   // Debts
   debts_includeBonuses: true,
   debts_simpleSitesTable: false,
   debts_showAgedOnPicker: false,
   debts_showBonusDebtType: false,

   showTodayMap: true,
   salesActivity_showManagerTable: true,

   todayMap_defaultPositionLat: 52.698926,
   todayMap_defaultPositionLong: -1.046534,
   todayMap_defaultZoom: 7,

   bookingsBar_barStyle1: false,
   bookingsBar_barStyle2: true,

   stockInsight_showPrepCost: false,
   stockInsight_showAverageDIS: true,

   usedStockTable_vindisFormatting: true,
   usedStockTable_tactical: false,
   usedStockTable_exManagementCount: false,
   usedStockTable_exDemo: false,
   usedStockTable_includeDemoWithUsed: true,

   sideMenu_distrinet: false,
   sideMenu_bulkValuation: true,
   sideMenu_locationOptimiser:true,
   sideMenu_pricingHome: false,
   sideMenu_dashboard: true,
   sideMenu_orderBook: true,
   sideMenu_fleetOrderbook: false,
   sideMenu_dealsDoneThisWeek: true,
   sideMenu_dealsForTheMonth: true,
   sideMenu_whiteboard: true,
   sideMenu_performanceLeague: true,
   sideMenu_performanceTrends: true,
   sideMenu_scratchCard: false,
   sideMenu_salesIncentive: false,
   sideMenu_superCup: false,
   sideMenu_superCupTwo: false,
   sideMenu_handoverDiary: true,
   sideMenu_reportingCentre: true,
   sideMenu_stockList: true,
   
   sideMenu_stockInsight: true, //this is the blobs page
   sideMenu_leavingVehicles: false,
   sideMenu_pricingDashboard: false,
   
   sideMenu_applyStrategy: false,
   
     sideMenu_vehicleValuation: true,
   sideMenu_salesCommission: true,
   sideMenu_salesExecReview: true,
   sideMenu_localBargains: true,
   sideMenu_stockLanding: false,
   sideMenu_liveForecastInput: true,sideMenu_liveForecastStatus: true,sideMenu_liveForecastReview: true,
   sideMenu_userMaintenance: true,
   sideMenu_siteSettings: true,sideMenu_scheduledReports:true,

   
   sideMenu_advertListingDetail: true, //this is the main report
   sideMenu_optOuts: true,
   sideMenu_todaysPrices: true,
   sideMenu_leavingVehicleDetail: true,
   sideMenu_leavingVehicleTrends: true,sideMenu_leavingVehicleTrendsOverTime:true,
   sideMenu_groupTitle: " Vindis Group Ltd",

   allGroups: ["R", "O", "N", "Z", "T", "D"],
   allFamilyCodes: ["Franchise", "Non-Franchise", "Tyres", "Oil", "Exchange"],

   dashboard_sections: [
      //Overview dashboard
      {
         sectionName: "Overview",
         translatedTextField: "Common_Overview",
         translatedTextValue: "",
         pageName: "dashboardOverview",
         enableSitesSelector: true,
         pages: [
            {
               pageName: "SalesPerformance",
               translatedTextField: "Dashboard_SalesPerformance_Title",
               translatedTextValue: "",
            },
            {
               pageName: "FinanceAddOns",
               translatedTextField: "Dashboard_FinanceAddons_Title",
               translatedTextValue: "",
            },
            { pageName: "StockReport", translatedTextField: "Dashboard_StockReport_Title", translatedTextValue: "" },
            { pageName: "Debtors", translatedTextField: "Dashboard_Debtors_Title", translatedTextValue: "" },
            { pageName: "GDPR", translatedTextField: "Dashboard_GDPRCapture", translatedTextValue: "" },
            { pageName: "Activities", translatedTextField: "Dashboard_Activities", translatedTextValue: "" },
            { pageName: "CitNow", translatedTextField: "Dashboard_CitNow_Title", translatedTextValue: "" },
            { pageName: "ImageRatios", translatedTextField: "Dashboard_ImageRatios_Title", translatedTextValue: "" },
         ],
      },

      //Sales dashboard
      {
         sectionName: "SalesVindis",
         translatedTextField: "Common_Sales",
         translatedTextValue: "",
         pageName: "dashboardSalesVindis",
         enableSitesSelector: false,
         pages: [
            {
               pageName: "SalesPerformance",
               translatedTextField: "Dashboard_SalesPerformance_Title",
               translatedTextValue: "",
            },
            {
               pageName: "FinanceAddOns",
               translatedTextField: "Dashboard_FinanceAddons_Title",
               translatedTextValue: "",
            },
            { pageName: "StockReport", translatedTextField: "Dashboard_StockReport_Title", translatedTextValue: "" },
            { pageName: "Debtors", translatedTextField: "Dashboard_Debtors_Title", translatedTextValue: "" },
            { pageName: "GDPR", translatedTextField: "Dashboard_GDPRCapture", translatedTextValue: "" },
            { pageName: "Activities", translatedTextField: "Dashboard_Activities", translatedTextValue: "" },
            { pageName: "CitNow", translatedTextField: "Dashboard_CitNow_Title", translatedTextValue: "" },
            { pageName: "ImageRatios", translatedTextField: "Dashboard_ImageRatios_Title", translatedTextValue: "" },
         ],
      },

      //Aftersales dashboard
      {
         sectionName: "Aftersales",
         translatedTextField: "Common_Aftersales",
         translatedTextValue: "",
         pageName: "dashboardAfterSalesVindis",
         enableSitesSelector: false,
         pages: [
            { pageName: "ServiceSales", translatedTextField: "Dashboard_ServiceSales_Title", translatedTextValue: "" },
            { pageName: "PartsSales", translatedTextField: "Dashboard_PartsSales_Title", translatedTextValue: "" },
            {
               pageName: "ServiceBookings",
               translatedTextField: "Dashboard_ServiceBookings_Title",
               translatedTextValue: "",
            },
            { pageName: "PartsStock", translatedTextField: "Dashboard_PartsStock_Title", translatedTextValue: "" },
            { pageName: "EVHC", translatedTextField: "Dashboard_Evhc_Title", translatedTextValue: "" },
            { pageName: "Debtors", translatedTextField: "Dashboard_Debtors_Title", translatedTextValue: "" },
            { pageName: "CitNow", translatedTextField: "Dashboard_CitNow_Title", translatedTextValue: "" },
            { pageName: "Wip", translatedTextField: "Dashboard_WipReport_Title", translatedTextValue: "" },
         ],
      },

      //Site compare dashboard
      {
         sectionName: "SiteCompare",
         translatedTextField: "SiteCompare",
         translatedTextValue: "",
         pageName: "dashboardSiteCompare",
         enableSitesSelector: false,
         pages: [],
      },
   ],
   dashboard_canChooseMonth: false,
   dashboard_showStockCover: true,
   dashboard_includeExtraSpainMenuButtons: false,
   dashboard_showZoeSales: false,
   dashboard_showHandoverDiarySummary: false,
   dashboard_showCashDebts: false,
   dashboard_showBonusDebts: false,
   dashboard_showRenaultRegistrations: false,
   dashboard_showDaciaRegistrations: false,
   dashboard_showFleetRegistrations: false,
   dashboard_showUsedStockMerchandising: false,
dashboard_showUsedStockOverage: true,
dashboard_showNewStockOverage: true,
   dashboard_showTradeStockOverage: true,
   dashboard_showCommissions: true,
   dashboard_showFinanceAddonPerformance: true,
   dashboard_showfAndIPerformanceRRG: false,
   dashboard_showVocNPSTile: false,
   dashboard_showActivityLevels: true,
   dashboard_showActivityOverdues: false,
   dashboard_showFleetPerformance: true,
   dashboard_showVoC: true,
   dashboard_showServiceBookings: true,
   dashboard_showSalesmanEfficiency: true,
   dashboard_showCitNow: true,
   dashboard_showImageRatios: true,
   dashboard_showEvhc: true,
   dashboard_showFinanceAddons: true,
   dashboard_showRegistrations: true,
   dashboard_showVindisAftersalesDashboard: true,
   dashboard_showRRGAftersalesDashboard: false,
   dashboard_showSpainAftersalesDashboard: false,
   dashboard_showVindisSalesDashboard: true,
   dashboard_showAftersalesDatasets: false,
   dashboard_showRRGSalesDashboard: false,
   dashboard_showSimpleDealsByDay: true,
   dashboard_showDataOriginsButton: false,
   dashboard_excludeTypesFromBreakDown: ["Fleet", "Commercial", "Corporate"],
   dashboard_showFinanceAddonsAllSites: true,
   dashboard_includeDemoStockInStockHealth: true,

   horizontalBar_forRenault: false,
   horizontalBar_forVindis: true,

   stockItemModal_onlineMerchandising: false,

   wipsTable_hideBookingColumn: true,
   wipsTable_hideDepartmentColumn: true,
   wipsTable_hideAccountColumn: true,
   wipsTable_hideDueDateOutColumn: true,
   wipsTable_hideWDateInColumn: true,
   wipsTable_hideWDateOutColumn: true,
   wipsTable_hidePartsColumn: true,
   wipsTable_hideOilColumn: true,
   wipsTable_hideLabourColumn: true,
   wipsTable_hideSubletColumn: true,
   wipsTable_hideProvisionColumn: true,
   wipsTable_hideCreatedColumn: true,
   wipsTable_hideNotesColumn: false,

   stockTable_hideTacticalColumn: true,
   stockTable_hideExManagementColumn: true,
   stockTable_hideExDemoColumn: true,

   stockList_hideStockDetailsModal: false,
   stockList_tableColumns: [
      "Id",
      "SiteDescShort",
      "StockNumberFull",
      "Reg",
      "VehicleType",
      "ProgressCode",
      "DaysInStock",
      "Make",
      "Model",
      "ModelYear",
      "Description",
      "DisposalRoute",
      "PreviousUseCode",
      "Siv",
      "CarryingValue",
      "IsVatQ",
      "CapProvision",
      "StockcheckLocation",
      "SeenAtLatestStkchk",
      "PricedProfit",
      "VariantClass",
      "VehicleTypeCode",
      "VehicleSuperType",
      "AccountStatus",
      "Mileage",
      "Fuel",
      "ShouldBePrepped",
      "IsPrepped",
      "Selling",
      "CapCode",
   ],
   stockList_franchises: [],

   performanceLeague_hideBadges: false,
   performanceLeague_showDeliveredButton: true,
   performanceLeague_showExecAndManagerSelector: true,
   siteCompareTile_includeToday: true,
   siteCompareTile_hideReportButtons: true,

   overAgeStockTable_hideDemoColumn: false,
   overAgeStockTable_hideTacticalColumn: true,
   overAgeStockTable_hideExManagementColumn: true,
   overAgeStockTable_hideExDemoColumn: true,
   overAgeStockTable_hideTradeColumn: false,
   overAgeStockTable_usedColumnName: "CoreUsed",

   dealPopover_showMetalProfit: true,
   dealPopover_showOtherProfit: true,
   dealPopover_showFinanceProfit: true,
   dealPopover_showAddons: true,
   dealPopover_showAddonProfit: true,

   orderBook_orderbookTitle: "Order Book",
   orderBook_showNewOrderbook: false,
   orderBook_showNewDealButton: false,
   orderBook_ordersDescription: "Orders created between",
   orderBook_hideDeliverySiteColumn: false,
   orderBook_hideOemReferenceColumn: false,
   orderBook_hideFinanceProfitColumn: false,
   orderBook_hideVehicleTypeColumn: false,
   orderBook_hideVehClassColumn: true,
   orderBook_hideModelColumn: true,
   orderBook_hideModelYearColumn: true,
   orderBook_hideVehicleSourceColumn: true,
   orderBook_hideDaysToDeliverColumn: false,
   orderBook_hideDaysToSaleColumn: false,
   orderBook_hideLocationColumn: false,
   orderBook_hideIsConfirmedColumn: false,
   orderBook_hideIsClosedColumn: false,
   orderBook_hideUnitsColumn: true,
   orderBook_hideFinanceTypeColumn: false,
   orderBook_hideSalesChannel: true,
   orderBook_hideComments: false,
   orderBook_hideOrderAllocationDate: true,
   orderBook_hideVehTypeColumn: false,
   orderBook_hideIsLateCostColumn: true,
   orderBook_hideOtherProfitColumn: false,
   orderBook_hideMetalColumn: false,
   orderBook_hideAddonsColumn: false,
   orderBook_hideDiscountColumn: false,
   orderBook_hideChannelColumn: true,
   orderBook_hideTypeColumn: true,
   orderBook_includeAccgDate: true,
   orderBook_customDateTypes: [],
   orderBook_showMetalSummary: true,
   orderBook_showOtherSummary: true,
   orderBook_showFinanceSummary: true,
   orderBook_showInsuranceSummary: true,
   orderBook_showAccountingDateButton: false,
   orderBook_showDeliveryOptionButtons: true,
   orderBook_defaultDateType: "Delivery",
   orderBook_siteColumnWidth: 80,
   orderBook_customerColumnWidth: 130,
   orderBook_vehicleClassColumnWidth: 30,
   orderBook_salesExecColumnWidth: 100,
   orderBook_descriptionColumnWidth: 200,
   orderBook_showLateCost: true,
   orderBook_showOrderOptions: false,
   orderBook_hideOrderDateSelection: false,
   orderBook_hideAuditColumn: false,
   orderBook_showManagerSelector: true,
   orderBook_hideDateFactoryTransportationColumn: true,
   orderBook_hideDateVehicleReconditionColumn: true,
   orderBook_hideDateSiteTransportationColumn: true,
   orderBook_hideDateSiteArrivalColumn: true,
   orderBook_hideReservedDateColumn: true,

   partsSales_showMarginColPerc: true,
   partsSales_showMarginCol: true,
   partsSales_includeMarginCols: true,

   handoverDiary_includeCustomerName: true,
   handoverDiary_includeLastPhysicalLocation: true,
   handoverDiary_includeHandoverDate: false,
   handoverDiary_isInvoiced: false,
   handoverDiary_isConfirmed: true,
   handoverDiary_showManagerSelector: true,

   partsStockDetailedTable_hideCreatedColumn: true,

   partsStockDetailedTable_partStockBarCharts1_headerName: "% > 6 months",
   partsStockDetailedTable_partStockBarCharts1_field: "PercentOver6months",
   partsStockDetailedTable_partStockBarCharts1_colId: "PercentOver6months",

   partsStockDetailedTable_partStockBarCharts2_headerName: "% 6 - 12 months",
   partsStockDetailedTable_partStockBarCharts2_field: "Percent6to12Months",
   partsStockDetailedTable_partStockBarCharts2_colId: "Percent6to12Months",

   partsStockDetailedTable_showPartStockAgeingColumnsForRRG: false,
   partsStockDetailedTable_showPartStockAgeingColumnsForVindis: true,
   partsStockDetailedTable_hideOfWhichColumn: true,
   partsStockDetailedTable_hideDeadValueColumn: true,
   partsStockDetailedTable_hideDormantValueColumn: true,
   partsStockDetailedTable_hideDeadProvColumn: true,
   partsStockDetailedTable_hideDormantProvColumn: true,
   partsStockDetailedTable_setClassesForVindis: true,
   partsStockDetailedTable_setClassesForRRG: false,

   salesPerformance_description: "Orders created between",
   salesPerformance_showFranchisePicker: true,
   salesPerformance_showLateCostButtons: true,
   salesPerformance_showIncludeExcludeOrders: false,
   salesPerformance_showOrderRateReportType: true,
   salesPerformance_showTradeUnitButtons: true,
   salesPerformance_showMotabilityButtons: false,
   salesPerformance_showCustomReportType: true,
   salesPerformance_showAllSites: true,
   salesPerformance_showRetailSalesTranslation: false,

   selectionsServiceEnvironment_ageingOptions: [
      { description: "30 days", ageCutoff: 30 },
      { description: "45 days", ageCutoff: 45 },
      { description: "60 days", ageCutoff: 60 },
      { description: "90 days", ageCutoff: 90 },
      { description: "120 days", ageCutoff: 120 },
      { description: "180 days", ageCutoff: 180 },
      { description: "270 days", ageCutoff: 270 },
      { description: "1 Year", ageCutoff: 365 },
      { description: "2 Years", ageCutoff: 730 },
      { description: "2+ Years", ageCutoff: 10000 }, // Big number which should never really be reached
   ],
   selectionsServiceEnvironment_ageingOption: { description: "90 days", ageCutoff: 90 },
   selectionsServiceEnvironment_deliveryDateDateType: "Delivery",
   selectionsServiceEnvironment_eligibleForCurrentUserCheck: false,

   stockReport_showAgePicker: false,
   stockReport_hideOnRRGSiteCol: true,
   stockReport_initialStockReport: "Dashboard_PartsStock_UsedStock",
   stockReport_seeUsedStockReport: true,
   stockReport_seeAllStockReport: true,
   stockReport_seeUsedMerchandisingReport: false,
   stockReport_seeOverageStockReport: true,
   stockReport_seeStockGraphsReport: false,
   stockReport_seeStockByAgeReport: false,
   stockReport_includeReservedCarsOption: false,

   serviceBookingsTable_clickSiteEnable: false,

   whiteboard_showConfirmed: true,
   whiteboard_showNotConfirmed: true,
   whiteboard_showFinance: true,
   whiteboard_showAddons: true,
   whiteboard_showLateCostPicker: false,
   whiteboard_showManagerSelector: true,
   whiteboard_rrgUKSettings: false,

   serviceChannels: [
      {
         displayName: "Retail",
         name: "Retail",
         channelTags: ["retail"],
         icon: "fas fa-wrench",
         hasHours: true,
         divideByChannelName: "Retail",
         isLabour: true,
      },
      {
         displayName: "MOT",
         name: "MOT",
         channelTags: ["mot"],
         icon: "fas fa-wrench",
         hasHours: true,
         divideByChannelName: "MOT",
         isLabour: true,
      },
      {
         displayName: "Internal",
         name: "Internal",
         channelTags: ["internal"],
         icon: "fas fa-car-wash",
         hasHours: true,
         divideByChannelName: "Internal",
         isLabour: true,
      },
      {
         displayName: "Warranty",
         name: "Warranty",
         channelTags: ["warranty"],
         icon: "fas fa-engine-warning",
         hasHours: true,
         divideByChannelName: "Warranty",
         isLabour: true,
      },
      {
         displayName: "Labour",
         name: "Labour",
         channelTags: ["retail", "internal", "warranty", "mot"],
         isTotal: true,
         icon: "",
         hasHours: true,
         divideByChannelName: "Labour",
         isLabour: false,
      },
      {
         displayName: "Tyre/Sublet",
         name: "Tyre/Sublet",
         channelTags: ["tyre", "sublet"],
         icon: "fas fa-tire",
         hasHours: true,
         divideByChannelName: "Retail",
         isLabour: false,
      },
      {
         displayName: "Oil",
         name: "Oil",
         channelTags: ["oilWarr", "oilExt", "oilInt", "oil"],
         icon: "fas fa-oil-can",
         hasHours: true,
         divideByChannelName: "Retail",
         isLabour: false,
      },
      {
         displayName: "Other",
         name: "Other",
         channelTags: ["other", "sundry"],
         icon: "fal fa-circle",
         hasHours: true,
         divideByChannelName: "Retail",
         isLabour: false,
      },
      {
         displayName: "Total",
         name: "Total",
         channelTags: [
            "retail",
            "mot",
            "internal",
            "warranty",
            "tyre",
            "oilWarr",
            "oilExt",
            "oilInt",
            "oil",
            "sublet",
            "other",
            "sundry",
            "mot",
         ],
         isTotal: true,
         icon: "",
         hasHours: true,
         divideByChannelName: "Labour",
         isLabour: false,
      },
   ],
   partsChannels: [
      {
         displayName: "Retail",
         name: "Retail",
         channelTags: ["retail", "nonfran", "network", "trade"],
         icon: "fas fa-wrench",
         channelTag: "retail",
         hasHours: false,
         divideByChannelName: "Retail",
         isLabour: false,
      }, //added network in on 28Aug20
      {
         displayName: "Internal",
         name: "Internal",
         channelTags: ["internal"],
         icon: "fas fa-car-wash",
         channelTag: "internal",
         hasHours: false,
         divideByChannelName: "Internal",
         isLabour: false,
      },
      {
         displayName: "Workshop Internal",
         name: "Workshop Internal",
         channelTags: ["wshopInternal"],
         icon: "fas fa-car-wash",
         channelTag: "wshopInternal",
         hasHours: false,
         divideByChannelName: "Workshop Internal",
         isLabour: false,
      },
      {
         displayName: "Workshop Retail",
         name: "Workshop Retail",
         channelTags: ["wshopRetail"],
         icon: "fas fas fa-tire ",
         channelTag: "wshopRetail",
         hasHours: false,
         divideByChannelName: "Workshop Retail",
         isLabour: false,
      },
      {
         displayName: "Workshop Warranty",
         name: "Workshop Warranty",
         channelTags: ["wshopWarranty"],
         icon: "fas fa-engine-warning",
         channelTag: "wshopWarranty",
         hasHours: false,
         divideByChannelName: "Workshop Warranty",
         isLabour: false,
      },
      {
         displayName: "Total",
         name: "Total",
         isTotal: true,
         channelTags: ["retail", "nonfran", "internal", "wshopInternal", "wshopRetail", "wshopWarranty"],
         icon: "",
         channelTag: "total",
         hasHours: false,
         divideByChannelName: "Total",
         isLabour: false,
      },
   ],
   initialPageURL: "/dashboardOverviewVindis",

   orderBookURL: "/orderBook",
   fleetOrderbookURL: "/fleetOrderbook",

   product_tyreInsurance: "HasTyreInsurance",
   product_tyreAlloyInsurance: "HasTyreAndAlloyInsurance",

   dealDone_showVindisSitePicker: true,
   dealDone_showRRGSitePicker: false,
   dealDone_showRRGPopoverContent: false,
   dealDone_showVindisPopoverContent: true,

   evhc_showTechTable: false,
   evhc_vehiclesCheckedPercent: 100,
   evhc_workQuoted: 0,
   evhc_workSoldPercent: 65,
   evhc_eDynamixView: false,
   evhc_redWorkSoldPercent: 65,
   evhc_amberWorkSoldPercent: 25,

   fAndISummary_removeFleetFromDefaultOrderTypes: true,
   fAndISummary_showManagerTable: true,
   fAndISummary_hideAlloyColumn: false,

   partsStock_includeOfWhichColumns: true,

   dealsForTheMonth_showMetal: true,
   dealsForTheMonth_showOther: true,
   dealsForTheMonth_showFinance: true,
   dealsForTheMonth_showAddons: true,
   dealsForTheMonth_showGpu: true,
   dealsForTheMonth_showSpainSpacing: false,
   dealsForTheMonth_showBroughtInColumn: true,
   dealsForTheMonth_showLateCostPicker: false,
   dealsForTheMonth_showIncludeExcludeOrders: false,

   partsStockSitesCoverTable_partStockName: "PartsStock",

   dealsDoneThisWeek_showPlotOptions: true,

   orderTypePicker_showRetail: true,
   orderTypePicker_showFleet: true,
   orderTypePicker_vindisSettings: true,
   orderTypePicker_rrgSpainSettings: false,

   vehicleTypePicker_showUsed: true,
   vehicleTypePicker_showNew: true,
   vehicleTypePicker_showAll: true,
   vehicleTypePicker_hiddenVehicleTypes: [],

   userSetup_hideUploadReports: false,
   userSetup_hideViewReports: false,
   userSetup_hideCommReview: false,
   userSetup_hideCommSelf: false,
   userSetup_hideSerReviewer: false,
   userSetup_hideSerSubmitter: false,
   userSetup_hideStockLanding: true,
   userSetup_hideSuperCup: true,
   userSetup_hideIsSalesExec: false,
   userSetup_hideAllowReportUpload: false,
   userSetup_hideAllowReportCentre: false,
   userSetup_hideLiveforecast: false,
   userSetup_hideSalesRoles: false,
   userSetup_hideCanEditExecManagerMappings: false,
   userSetup_hideTMgr: false,
   userSetup_allSalesRoles: [null],
   userSetup_canReviewStockPrices: true,
   userSetup_canActionStockPrices: true,
   userSetup_canEditStockPriceMatrix: true,
   userSetup_showSpanishJobTitles: false,

   languageSelection: false,
   
   gdpr_showManagerView: true, 

   serviceSummary_showTableTypeSelector: true,
   serviceSummary_defaultTableType: "Cumulative",
   serviceSummary_tableTypes: ["Cumulative", "Daily"],
   serviceSummary_defaultTimeOption: "MTD",
   serviceSummary_timeOptions: ["MTD", "WTD", "Yesterday"],
   serviceSummary_removeNonLabour: true,
   serviceSummary_showTechGroupColumns: true,

   partsSummary_showTableTypeSelector: true,
   partsSummary_tableTypes: ["Cumulative", "Daily"],

   serviceSalesDashboard_onlyLabour: false,

   dealDetailModal_componentName: "DealDetailsComponent",
   dealDetailModal_currencyDP: 2,
   dealDetailModal_costColumnTranslation: "Common_Cost",

   dealDetailModal_dealDetailsSection_showVariant: false,
   dealDetailModal_dealDetailsSection_showWebsiteDiscount: false,
   dealDetailModal_dealDetailsSection_showFinanceType: true,
   dealDetailModal_dealDetailsSection_showOEMReference: true,
   dealDetailModal_dealDetailsSection_showQualifyingPartEx: false,
   dealDetailModal_dealDetailsSection_showPhysicalLocation: true,
   dealDetailModal_dealDetailsSection_showIsClosed: true,
   dealDetailModal_dealDetailsSection_showFinanceCo: false,
   dealDetailModal_dealDetailsSection_showDescription: false,
   dealDetailModal_dealDetailsSection_showUnits: false,
   dealDetailModal_dealDetailsSection_showVehicleAge: false,
   dealDetailModal_dealDetailsSection_showIsLateCost: false,
   dealDetailModal_dealDetailsSection_showAuditPass: true,
   dealDetailModal_dealDetailsSection_showInvoiceNo: false,

   dealDetailModal_metalProfitSection_headerTranslation: "DealDetails_MetalProfit",
   dealDetailModal_metalProfitSection_showVATCost: true,

   dealDetailModal_otherProfitSection_showRegBonus: true,
   dealDetailModal_otherProfitSection_showIntroComm: true,
   dealDetailModal_otherProfitSection_showBrokerCost: true,
   dealDetailModal_otherProfitSection_showAccessories: true,
   dealDetailModal_otherProfitSection_showPaintProtectionAccessory: true,
   dealDetailModal_otherProfitSection_showFuel: true,
   dealDetailModal_otherProfitSection_showDelivery: true,
   dealDetailModal_otherProfitSection_showStandardWarranty: true,
   dealDetailModal_otherProfitSection_showPdi: true,
   dealDetailModal_otherProfitSection_showMechPrep: true,
   dealDetailModal_otherProfitSection_showBodyPrep: true,
   dealDetailModal_otherProfitSection_showOther: true,
   dealDetailModal_otherProfitSection_showError: true,
   dealDetailModal_otherProfitSection_showTotal: true,
   dealDetailModal_addonsSection_showPaintProtection: false,
   dealDetailModal_addonsSection_showWarrantyForNewCar: true,

   dealDetailModal_datesSection_showCustomerDestinationDeliveryDate: false,
   dealDetailModal_datesSection_showEnterImportCentreDate: false,
   dealDetailModal_datesSection_showShipDate: false,
   dealDetailModal_datesSection_showExitImportCentreDate: false,
   dealDetailModal_datesSection_showAllocationDate: false,
   dealDetailModal_datesSection_showDateVehicleRecondition: false,
   dealDetailModal_datesSection_showDateFactoryTransportation: false,
   dealDetailModal_datesSection_showDateSiteArrival: false,
   dealDetailModal_datesSection_showDateSiteTransportation: false,

   dealDetailModal_financeProfitSection_show: true,
   dealDetailModal_financeProfitSection_rciFinanceCommissionText: "DealDetails_PCPFinanceCommission",
   dealDetailModal_financeProfitSection_financeCommissionText: "DealDetails_HPAndOtherFinanceCommission",
   dealDetailModal_financeProfitSection_showSelectCommission: false,
   dealDetailModal_financeProfitSection_showProPlusCommission: false,
   dealDetailModal_financeProfitSection_showStandardsCommission: false,

   dealDetailModal_showSmallModal: false,
   dealDetailModal_showOtherProfit: true,
   dealDetailModal_showAddOnProfit: true,
   dealDetailModal_showDealFileSentDate: false,
   dealDetailModal_showStockDetailButton: true,
   dealDetailModal_enableStockItemModalAutotrader: false,
   dealDetailModal_enableSalesExecPicker: true,
   dealDetailModal_showTotalProfitExludingFactoryBonusSection: true,
   dealDetailModal_showTotalProfitSection: true,

   donutTile_ShowLastYearUnits: false,
   showNewUsedSummaryBadges: true,
   showPrepCostsWhenValuing: false,
   isSingleSiteGroup: false,

   showChangePriceNowInputAlways: false,

   
   
   
   
   
   
   
   
   
   
   sideMenu_orderBookNew:true,sideMenu_simpleExample:false,
   
   
   
   
   
   
   
   sideMenu_teleStats: false,
   
   
   
   sideMenu_salesPerformance: true,
   sideMenu_alcopa: false,
   sideMenu_showOrderRate: false,
   sideMenu_registrationsPosition: false,
   sideMenu_fAndISummary: true,
   sideMenu_stockReportOld: true,
   
   sideMenu_debtsSales: true,
  sideMenu_debtsAfterSales: true,
   sideMenu_citnow: true,
   sideMenu_imageRatios: true,
   sideMenu_gdpr: true,
   sideMenu_salesActivity: true,
   
   
   sideMenu_serviceSummary: true,
   sideMenu_serviceBookings: true,
   sideMenu_evhc: true,
   sideMenu_upsells: false,sideMenu_wipReport: true,
   
   
   
   sideMenu_partsSummary: true,
   sideMenu_partsStock: true,
   
   
   sideMenu_salesmanEfficiency: false,
   sideMenu_hasVehiclePricing: true,
   sideMenu_ShowDetailedMenu: true,
   sideMenu_statsDashboard: true,sideMenu_stockProfiler:false,sideMenu_sitesLeague:true,
   sideMenu_stockReport: true,
   sideMenu_stockReport_showBcaColumns: false,
           
   sideMenu_advertSimpleListing: true,
   sideMenu_home: true,
   
  sideMenu_hasOperationReports: false,
  sideMenu_dashboardSales: true,
  sideMenu_dashboardAfterSales: true,
  sideMenu_siteCompare: true,
  sideMenu_dashboardNewKPIs: false,
  sideMenu_dashboardUsedKPIs: false,
  sideMenu_dashboardOverviewSpain: false,
  sideMenu_orderRate: false,
  sideMenu_dashboardOverviewVindis: true,
  sideMenu_strategyPriceBuildUp: false, sideMenu_leavingVehicleExplorer: true,

   showRotationButton: true,
   showLatestSnapshotDate: false,
   showApproveAutoPrices: true,
   showNestedSideMenu: true,

   departmentDealBreakDown_RRG: false,
   departmentDealBreakDown_Vindis: true,

   userModal_userSitePicker_includeInactive: false,
   userModal_doNotRequireCurrentRetailerSite: false,
   userModal_showRetailSiteErrorMessage: true,
   
   usageReport_CommissionPageName: "commissionVindis",
   runRateTile_hideBudget: false,

   noDateSuffix: false,
   showRunChaseTileModal: false,
   stockOverageTile_SpainThresholds: false,
   commissionSchemeName: "Vindis",
   vehicleTypePicker_showVehicleTypePickerSpain: false,
   donutTile_ShowInvoicingTitle: false,
   donutTile_SpainAdditionalMeasures: false,
   ordersDonutTile_SpainSettings: false,
   stockOverageTile_excludeEOM: false,
   salesmenCanViewWebAppCommission: true,
   dealershipBackgroundImageName: "Vindis",
   homeIsLandingPage: false,
   showRegionFilterOnSiteDashboard: false,
   showWholesaleAdjustmentOption: false,
};
