.custom-checkbox {
   display: flex;
   width: 1.5em;
   height: 1.5em;
   align-items: center;
   justify-content: center;
   background-color: var(--mainAppColour);
   border: none;
   color: #FFFFFF;

   &.checked {
      background-color: var(--secondary);
      color: #000000;
   }
}

.ag-checkbox-input-wrapper.ag-checked::after {
   color: var(--brightColourDark);
}

.ag-checkbox-input-wrapper::after {
   border-color: var(--grey80);
}
