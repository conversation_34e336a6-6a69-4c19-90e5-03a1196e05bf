input, .likeAnInput {
   line-height: 1.8em;
}

.likeAnInput {
   padding-right: 0.5em;
}

input {
   border: 1px solid var(--grey80);
   border-radius: 0.4em;
   padding: 0.2em 0.5em;

   &.lightYellow {
      background: var(--light<PERSON>ellow);
   }
}

input.fullWidth {
   width: 100%;
}

input[type=number] {
   text-align: right;
}


//forms and inputs
input.ng-invalid.ng-touched {
   background-color: var(--bad);
}


input:focus {
   position: relative;
   z-index: 2;
   box-shadow: 0 0 5px 2px var(--brightColour) !important;
   outline: none;
}

//position relative to get zindex to work to pull the box shadow to front
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
   -webkit-appearance: none;
   margin: 0;
}


.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle), .input-group > .input-group-append:last-child > .input-group-text:not(:last-child), .input-group > .input-group-append:not(:last-child) > .btn, .input-group > .input-group-append:not(:last-child) > .input-group-text, .input-group > .input-group-prepend > .btn, .input-group > .input-group-prepend > .input-group-text {
   border-top-right-radius: 0;
   border-bottom-right-radius: 0;
}

input[type=checkbox] {
    accent-color: var(--brightColour);
}