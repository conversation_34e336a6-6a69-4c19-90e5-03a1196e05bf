.priceIndicatorLozenge {
    //
    border-radius: 0.4em;
    //font-weight: 500;
    width: 6em;
    text-align: center;
    line-height: min(var(--ag-internal-calculated-line-height), var(--ag-internal-padded-row-height));
    padding: 0 0.3em;
    display: flex;
    align-items: center;
    justify-content: center;

    &.NoAnalysis,
    &.NOANALYSIS {
        background-color: var(--grey90);
    }

    &.Low,
    &.LOW {
        background-color: var(--piLower);
    }

    &.Fair,
    &.FAIR {
        background-color: var(--piFair);
        color: black;
    }

    &.Good,
    &.GOOD {
        background-color: var(--piGood);
        color: #ffffff;
    }

    &.Great,
    &.GREAT {
        background-color: var(--piGreat);
        color: black;
    }

    &.Higher,
    &.High,
    &.HIGHER,
    &.HIGH {
        background-color: var(--piHigher);
        color: black;
    }

    // &.stockReportsGrid {
    //     width: 80px;
    // }

    &.inTile {
        width: 85px;
    }
}

.ag-cell span:has(> div.auto-trader-cell-lozenge) {
    width: 100%;
}

.auto-trader-cell-lozenge {
    border-radius: 0.4em;
    text-align: center;
    background-color: #D2D2D2;
    //
    padding: 0em 0.3em;
    min-width: 3em;
    max-width: 7.5em;
    width: 100%;

    &.Low {
        background-color: var(--prLower);
    }

    &.BelowAvg {
        background-color: var(--prFair);
    }

    &.AboveAvg {
        background-color: var(--prGood);
    }

    &.Excellent {
        background-color: var(--prGreat);
    }

    &.VeryOverPriced,
    &.VeryUnderPriced {
        background-color: var(--rrSub20Lozenge);
    }

    &.OnStrategyPrice {
        background-color: var(--rrSub80);
    }

    &.UnderPriced,
    &.OverPriced {
        background-color: var(--rrSub60);
    }

    &.inCard {
        padding: 0.2em 0.3em;
        max-width: 9em;
    }
}


.tag {
   border-radius: 7px;
   padding: 0.25em 0.75em;
   color: #fff;
   margin-right: 2px;
}

@media (max-width: 1920px) {
    .priceIndicatorLozenge {
        width: 5em;
    }
}
