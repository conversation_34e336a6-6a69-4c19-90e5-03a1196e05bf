﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <Antlr4 Remove="Angular\**" />
    <Antlr4 Remove="App_Data\**" />
    <Antlr4 Remove="Infrastructure\**" />
    <Compile Remove="Angular\**" />
    <Compile Remove="App_Data\**" />
    <Compile Remove="Infrastructure\**" />
    <Content Remove="Angular\**" />
    <Content Remove="App_Data\**" />
    <Content Remove="Infrastructure\**" />
    <EmbeddedResource Remove="Angular\**" />
    <EmbeddedResource Remove="App_Data\**" />
    <EmbeddedResource Remove="Infrastructure\**" />
    <None Remove="Angular\**" />
    <None Remove="App_Data\**" />
    <None Remove="Infrastructure\**" />
  </ItemGroup>
  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="__Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Update="__Web.Debug.config">
      <DependentUpon>__Web.config</DependentUpon>
      <SubType>Designer</SubType>
    </Content>
    <Content Update="__Web.Release.config">
      <DependentUpon>__Web.config</DependentUpon>
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Properties\PublishProfiles\" />
    <Folder Include="Properties\ServiceDependencies\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.20.0" />
    <PackageReference Include="EPPlus" Version="4.5.3.3" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Microsoft.Exchange.WebServices.NETStandard" Version="1.1.3" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="6.0.0" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="8.0.0" />
    <PackageReference Include="System.ServiceModel.Federation" Version="8.0.0" />
    <PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.421302">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.Runtime.Caching" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CPHI.Spark.BusinessLogic\CPHI.Spark.BusinessLogic.csproj" />
    <ProjectReference Include="..\CPHI.Spark.DataAccess\CPHI.Spark.DataAccess.csproj" />
    <ProjectReference Include="..\CPHI.Spark.Model\CPHI.Spark.Model.csproj" />
    <ProjectReference Include="..\CPHI.Spark.Repository\CPHI.Spark.Repository.csproj" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="Microsoft.jQuery.Unobtrusive.Validation" Version="4.0.0" />
  </ItemGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>false</AutoGenerateBindingRedirects>
  </PropertyGroup>
</Project>