﻿using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.WebApp.Service;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Caching;
using System.Threading.Tasks;

namespace CPHI.Spark.WebApp
{
   public interface IAutoPriceCache
   {
      Task<IEnumerable<VehicleAdvertWithRating>> GetAutoPriceAdverts(DateTime chosenDate, DealerGroupName dealerGroup, List<string> lifecycleStatuses, bool UseTestStrategy = false);
      Task RefreshAdvertsCache(DealerGroupName dealerGroup);
      void RefreshCachesFireAndForget(DealerGroupName dealerGroup);
      void UpdateCacheOptOut(DateTime chosenDate, DealerGroupName dealerGroup, int adId, bool newOptedOutVal, string userName);
      Task<IEnumerable<VehicleAdvertWithRating>> AdvertCachePatch(List<VehicleAdvertWithRatingCachePatch> operations, DateTime? chosenDate, Model.DealerGroupName dealerGroup, bool useTestStrategy = false);

      Task<IEnumerable<VehicleAdvertWithRating>> UpdateTagsVehicleAdvertCache(ApplyTagToVehiclesDTO dto, Model.DealerGroupName dealerGroupName);
   }


   public class AutoPriceCache : IAutoPriceCache
   {
      private readonly IUserService userService;
      private readonly IConfiguration configuration;
      private readonly string _connectionString;
      private readonly DealerGroupName dealerGroup;

      public AutoPriceCache(IUserService userService, IConfiguration configuration)
      {
         this.userService = userService;
         this.configuration = configuration;
         try
         {

            dealerGroup = userService.GetUserDealerGroupName();
            string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
            _connectionString = configuration.GetConnectionString(dgName);
         }
         catch (Exception ex)
         {
            _connectionString = "unknown";
         }
      }


      public void UpdateCacheOptOut(DateTime chosenDate, DealerGroupName dealerGroup, int adId, bool newOptedOutVal, string userName)
      {
         string cacheName = BuildCacheName(chosenDate, dealerGroup);
         if (!MemoryCache.Default.Contains(cacheName))
         {
            return;
         }

         var cacheItems = (IEnumerable<VehicleAdvertWithRating>)MemoryCache.Default.GetCacheItem(cacheName).Value;

         var cacheItemToUpdate = cacheItems.FirstOrDefault(x => x.AdId == adId);

         if (cacheItemToUpdate != null)
         {
            cacheItemToUpdate.OptedOutBy = newOptedOutVal == true ? userName : null;
         }
      }


      public async Task<IEnumerable<VehicleAdvertWithRating>> GetAutoPriceAdverts(DateTime chosenDate, Model.DealerGroupName dealerGroup, List<string> lifecycleStatuses, bool UseTestStrategy = false)
      {
         string cacheName = BuildCacheName(chosenDate, dealerGroup);

         if (!MemoryCache.Default.Contains(cacheName))
         {
            await GenerateAndSaveCache(chosenDate, dealerGroup);
         }

         var cacheItems = (IEnumerable<VehicleAdvertWithRating>)MemoryCache.Default.GetCacheItem(cacheName).Value;
         var toReturn = cacheItems.ToList().ConvertAll(x => new VehicleAdvertWithRating(x, UseTestStrategy));

         if (lifecycleStatuses != null)
         {
            return toReturn.Where(x => lifecycleStatuses.Contains(x.LifecycleStatus)).ToList();
         }
         else
         {
            return toReturn;
         }
      }



      public async Task<IEnumerable<VehicleAdvertWithRating>> AdvertCachePatch(List<VehicleAdvertWithRatingCachePatch> operations, DateTime? chosenDate, Model.DealerGroupName dealerGroup, bool useTestStrategy = false)
      {
         if (chosenDate == null)
         {
            chosenDate = DateTime.Now;
         }

         string cacheName = BuildCacheName(chosenDate.Value, dealerGroup);

         var cacheItems = (IEnumerable<VehicleAdvertWithRating>)MemoryCache.Default.GetCacheItem(cacheName).Value;

         foreach (var operation in operations)
         {
            var item = cacheItems.Where(x => x.AdId == operation.VehicleAdvertId).FirstOrDefault();

            if (item != null)
            {
               operation.Patch.ApplyTo(item);
            }
         }

         SaveAdvertCache(cacheName, cacheItems);

         var toReturn = cacheItems.ToList().ConvertAll(x => new VehicleAdvertWithRating(x, useTestStrategy));

         return toReturn;
      }

      public void RefreshCachesFireAndForget(DealerGroupName dealerGroup)
      {
         RefreshAdvertsCache(dealerGroup);  //deliberately not awaiting as 'FireAndForget'
      }

      public async Task RefreshAdvertsCache(DealerGroupName dealerGroup)
      {
         string cacheName = BuildCacheName(DateTime.Now, dealerGroup);
         MemoryCache.Default.Remove(cacheName);

         await GenerateAndSaveCache(DateTime.Now.Date, dealerGroup);
      }




      //--------- Private Methods


      private async Task GenerateAndSaveCache(DateTime chosenDate, DealerGroupName dealerGroup)
      {

         var vehicleAdvertsService = new VehicleAdvertsService(_connectionString);
         //List<string> lifecycles = new List<string>() { "DUE_IN", "FORECOURT", "IN STOCK NOT ON PORTAL", "SALE_IN_PROGRESS", "SOLD", "WASTEBIN" };
         List<string> lifecycles = AutoPriceHelperService.GetAllLifecycleStatuses();
         var adverts = await vehicleAdvertsService.FetchVehicleAdvertsWithRatingsFromMainDbTables(chosenDate, dealerGroup, lifecycles, null);
         string cacheName = BuildCacheName(chosenDate, dealerGroup);

         var orderedAdverts = adverts.OrderBy(x => x.DaysListed);

         SaveAdvertCache(cacheName, adverts);
      }

      private static bool SaveAdvertCache(string cacheName, object adverts, DateTime? expiry = null)
      {
         if (expiry == null)
         {
            expiry = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);
         }

         lock (MemoryCache.Default)
         {
            MemoryCache.Default.Add(new CacheItem(cacheName, adverts), new CacheItemPolicy() { AbsoluteExpiration = expiry.Value });
         }

         return true;
      }

      private static string BuildCacheName(DateTime chosenDate, DealerGroupName dealerGroup)
      {
         string dateName = chosenDate.ToString("yyyyMMdd");
         string cacheName = $"{dealerGroup}|{dateName}|AutoPriceRows";
         return cacheName;
      }

      public async Task<IEnumerable<VehicleAdvertWithRating>> UpdateTagsVehicleAdvertCache(ApplyTagToVehiclesDTO dto, DealerGroupName dealerGroupName)
      {
         var vehicleAdvertsService = new VehicleAdvertsService(_connectionString);

         var adverts = await vehicleAdvertsService.GetAdvertWithTags(dto.VehicleAdvertIds, dealerGroupName);

         var operations = new List<VehicleAdvertWithRatingCachePatch>();

         foreach (var advert in adverts)
         {
            var newTagList = String.Join("||", advert.TagIds);

            var operation = new VehicleAdvertWithRatingCachePatch()
            {
               VehicleAdvertId = advert.VehicleAdvertId,
               Patch = new JsonPatchDocument<VehicleAdvertWithRating>()
            };

            operation.Patch.Add(x => x.VehicleAdvertTagList, newTagList);
            operations.Add(operation);
         }

         var updatedVehicleAdvertsWithRating = await AdvertCachePatch(operations, null, dealerGroupName);

         return updatedVehicleAdvertsWithRating;

      }
   }
}

