﻿using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using CPHI.Spark.Model;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CPHI.Spark.WebApp.Service;
using CPHI.Spark.Model.ViewModels;
using System;
using CPHI.Spark.DataAccess;
using StockPulse.WebApi.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;


namespace CPHI.Spark.WebApp.Controllers

{

    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ClientAppStartController : ControllerBase
    {
        private readonly IClientAppStartService clientAppStartService;
        private readonly ISitesService sitesService;
        private readonly IGlobalParamService globalParamService;
        private readonly IUserService userService;
        private readonly IOptOutReasonService optOutReasonService;


        public ClientAppStartController(
            IClientAppStartService clientAppStartService,
            ISitesService sitesService,
            IGlobalParamService globalParamService,
            IUserService userService,
            IOptOutReasonService optOutReasonService)
        {
            this.clientAppStartService = clientAppStartService;
            this.sitesService = sitesService;
            this.globalParamService = globalParamService;
            this.userService = userService;
            this.optOutReasonService = optOutReasonService;
        }


      // 
      public async Task<StandingDataSet> Get()
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isRenault = dealerGroup == Model.DealerGroupName.RRGUK;

         ClaimsPrincipal cp = Request.HttpContext.User;
         int userId = int.Parse(cp.Claims.First(x => x.Type == "LinkedPersonId").Value);

         IEnumerable<string> franchiseCodes = await clientAppStartService.GetFranchiseCodes();
         IEnumerable<VehicleType> vehicleTypes = await clientAppStartService.GetVehicleTypes();
         IEnumerable<OrderType> orderTypes = await clientAppStartService.GetOrderTypes();
         IEnumerable<DepartmentClientApp> departments = await clientAppStartService.GetDepartments();
         IEnumerable<TagDTO> tags = await clientAppStartService.GetTags(dealerGroup);

            IEnumerable<SiteVM> sites = await sitesService.GetSites(userId,dealerGroup, true);
            IEnumerable<OptOutReasonVM> optOutReasons = await optOutReasonService.GetAll(dealerGroup);
            IEnumerable<GlobalParam> globalParams = await globalParamService.GetAll(dealerGroup);
            LastUpdatedDates lastUpdateDates = ConstantsCache.GetLastUpdateDates(this.userService.GetUserDealerGroupName());
            IEnumerable<RetailerSite> retailerSites = await userService.GetUserRetailSites(dealerGroup);
            List<UserPreference> userPreferences = await userService.GetUserPreferences(userId, dealerGroup);
            IEnumerable<ClaimVM> userClaims = await userService.GetUserAllClaimsFromDB(dealerGroup, userId);
            StandingDataSet standingData = new StandingDataSet()
            {
                Sites = sites,
                FranchiseCodes = franchiseCodes,
                VehicleTypes = vehicleTypes,
                OrderTypes = orderTypes,
                Departments = departments,
                OptOutReasons = optOutReasons,
                GlobalParams = globalParams,
                LastUpdatedDates = lastUpdateDates,
                DealerGroup = dealerGroup.ToString(),
                Blobname = DealerGroupBlobname.Get(dealerGroup),
                RetailerSites = retailerSites,
                UserPreferences = userPreferences,
                UserClaims = userClaims,
                Tags = tags
            };


         return standingData;
      }



      [HttpGet]
      [Route("GetMobileAppConfiguration")]
      public async Task<string> GetMobileAppConfiguration()
      {
         return await clientAppStartService.GetMobileAppConfig();
      }






   }
}