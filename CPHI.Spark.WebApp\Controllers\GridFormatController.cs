using CPHI.Model.ViewModels;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Services;
using CPHI.Spark.WebApp.Service;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace CPHI.Spark.WebApp.Controllers
{
   [Route("api/[controller]")]
   [ApiController]
   [Authorize]
   public class GridFormatController : ControllerBase
   {
      private readonly IGridFormatService _gridFormatService;
      private readonly IUserService _userService;

      public GridFormatController(IGridFormatService gridFormatService, IUserService userService)
      {
         _gridFormatService = gridFormatService;
         _userService = userService;
      }

      [HttpGet]
      // For performance reasons tell the browser to cache column definitions for 60s (we could increase this?)
      // VaryByQueryKeys tells the browser to include these query properties in identifying a unique request
      
      /*
      [ResponseCache(Duration = 60, VaryByQueryKeys = new[] { 
         nameof(showDMSSellingPrice_Col),
         nameof(showVsDMSSellingPrice_Col),
         nameof(allowTestStrategy),
         nameof(showPhysicalLocation_Col)
      })]
      */
      [Route("GetColumnDefinitions/{gridType}")]
      public async Task<IActionResult> GetColumnDefinitions(
         string gridType, 
         [FromQuery] bool? showDMSSellingPrice_Col,
         [FromQuery] bool? showVsDMSSellingPrice_Col,
         [FromQuery] bool? allowTestStrategy,
         [FromQuery] bool? showPhysicalLocation_Col,
         [FromQuery] bool? defaultToDaysInStock,
         [FromQuery] bool optionShowBigMotoringCols

         )
      {
         var options = new GetColumnDefinitionOptions()
         {
            optionAllowTestStrategy = allowTestStrategy,
            optionShowDMSSellingPrice_Col = showDMSSellingPrice_Col,
            optionShowPhysicalLocation_Col = showPhysicalLocation_Col,
            optionShowVsDMSSellingPrice_Col = showVsDMSSellingPrice_Col,
            defaultToDaysInStock = defaultToDaysInStock,
            optionShowBigMotoringCols = optionShowBigMotoringCols
         };

         var dealerGroup = _userService.GetUserDealerGroupName();
         var columnDefs = await _gridFormatService.GetColumnDefinitions(gridType, dealerGroup, options);

         var serializerOptions = new JsonSerializerOptions
         {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
         };

         var json = JsonSerializer.Serialize(columnDefs, serializerOptions);

         return Content(json, "application/json");
      }
   }
}
