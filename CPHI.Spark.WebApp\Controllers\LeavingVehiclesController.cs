﻿using Microsoft.AspNetCore.Mvc;
using System;
using Microsoft.AspNetCore.Authorization;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using CPHI.Spark.WebApp.Service;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.Services;
using CPHI.Spark.WebApp.Service.AutoPrice;
using CPHI.Spark.WebApp.Service.Autoprice;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;using CPHI.Spark.Model;
using CPHI.Spark.Model;

namespace CPHI.Spark.WebApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class LeavingVehiclesController : ControllerBase
    {
        private readonly ILeavingService leavingService;
        private readonly int userId;

        public LeavingVehiclesController(
                                   IUserService userService,
                                   ILeavingService leavingService
                                   )
        {
            this.userId = userService.GetUserId();
            this.leavingService = leavingService;
        }


        //[HttpPost]
        //[Route("GetLeavingPriceItems")]
        //public async Task<IEnumerable<LeavingPriceSummaryItem>> GetLeavingPriceItems(GetLeavingPriceItemsParams parms)
        //{
        //    return await leavingService.GetLeavingPriceItems(parms);
        //}

        [HttpPost]
        [Route("GetLeavingVehicleItems")]
        public async Task<IEnumerable<LeavingVehicleItem>> GetLeavingVehicleItems(GetLeavingVehicleItemsParams parms)
        {
            return await leavingService.GetLeavingVehicleItems(parms);
        }

      [HttpPost]
      [Route("GetLeavingVehicleExplorerItems")]
      public async Task<List<LVExplorerItem>> GetLeavingVehicleExplorerItems(GetLeavingVehicleExplorerItemsParams parms)
      {
          return await leavingService.GetLeavingVehicleExplorerItems(parms);
      }
        
    }
}
