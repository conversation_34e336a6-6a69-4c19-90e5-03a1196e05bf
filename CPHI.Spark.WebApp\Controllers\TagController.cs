using CPHI.Spark.BusinessLogic.Services;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.WebApp.Service;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CPHI.Spark.WebApp.Controllers
{
   [Route("api/[controller]")]
   [ApiController]
   [Authorize]
   public class TagsController : ControllerBase
   {
      private readonly ITagService _tagService;
      private readonly IUserService _userService;
      private readonly DealerGroupName _userDealerGroupName;
      private readonly int _userId;

      public TagsController(ITagService tagService, IUserService userService)
      {
         _tagService = tagService;
         _userService = userService;
         _userDealerGroupName = userService.GetUserDealerGroupName();
         _userId = userService.GetUserId();
      }

      [HttpGet]
      [Route("")]
      public async Task<IEnumerable<TagDTO>> SearchTags(TagSearchDTO? dto)
      {
         if (dto == null)
         {
            dto = new TagSearchDTO();
         }
         dto.DealerGroupName = _userDealerGroupName;
         return await _tagService.SearchTags(dto);
      }

      [HttpGet]
      [Route("{id}")]
      public async Task<TagDTO> GetTag(int id)
      {
         return await _tagService.GetTag(id, _userDealerGroupName);
      }

      [HttpPost]
      [Route("")]
      public async Task<TagDTO> CreateTag([FromBody] CreateTagDTO tagDTO)
      {
         tagDTO.DealerGroupId = (int)_userDealerGroupName;
         tagDTO.CreatedById = (int)_userId;
         tagDTO.UpdatedById = (int)_userId;

         tagDTO.CreatedDate = DateTime.Now;
         tagDTO.UpdatedDate = DateTime.Now;

         return await _tagService.CreateTag(tagDTO);
      }

      [HttpPatch]
      [Route("{id}")]
      public async Task<TagDTO> PatchTag(int id, [FromBody] JsonPatchDocument<Tag> patchDocument)
      {
         patchDocument.Add(x => x.UpdatedById, _userId);
         patchDocument.Add(x => x.UpdatedDate, DateTime.Now);

         return await _tagService.PatchTag(id, patchDocument, _userDealerGroupName);
      }
   }

   public class CamelCaseJsonResult : ContentResult
   {
      public CamelCaseJsonResult(object data)
      {
         var settings = new JsonSerializerSettings
         {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            Formatting = Formatting.None // or Formatting.Indented if you prefer
         };

         Content = JsonConvert.SerializeObject(data, settings);
         ContentType = "application/json";
         StatusCode = 200;
      }
   }
}
