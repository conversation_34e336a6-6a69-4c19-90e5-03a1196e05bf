using CPHI.Spark.BusinessLogic.Services;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository.Migrations;
using CPHI.Spark.WebApp.Service;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CPHI.Spark.WebApp.Controllers
{
   [Route("api/[controller]")]
   [ApiController]
   [Authorize]
   public class VehicleAdvertTagsController : ControllerBase
   {
      private readonly IVehicleAdvertTagService _vehicleAdvertTagService;
      private readonly IUserService _userService;
      private readonly IAutoPriceCache _autoPriceCache;
      private readonly DealerGroupName _userDealerGroupName;
      private readonly int _userId;

      public VehicleAdvertTagsController(
         IVehicleAdvertTagService vehicleAdvertTagService,
         IUserService userService,
         IAutoPriceCache autoPriceCache
         )
      {
         _vehicleAdvertTagService = vehicleAdvertTagService;
         _userService = userService;
         _userDealerGroupName = userService.GetUserDealerGroupName();
         _userId = userService.GetUserId();
         _autoPriceCache = autoPriceCache;
      }

      [HttpGet]
      [Route("")]
      public async Task<IEnumerable<VehicleAdvertTagDTO>> SearchVehicleAdvertTags([FromQuery] VehicleAdvertTagSearchDTO? dto)
      {
         if (dto == null)
         {
            dto = new VehicleAdvertTagSearchDTO();
         }

         dto.DealerGroupName = _userDealerGroupName;
         return await _vehicleAdvertTagService.SearchVehicleAdvertTags(dto);
      }

      [HttpGet]
      [Route("{id}")]
      public async Task<VehicleAdvertTagDTO> GetVehicleAdvertTag(int id)
      {
         return await _vehicleAdvertTagService.GetVehicleAdvertTag(id, _userDealerGroupName);
      }

      [HttpDelete]
      [Route("{id}")]
      public async Task<int?> DeleteVehicleAdvertTag(int id)
      {
         var vehicleId = await _vehicleAdvertTagService.DeleteVehicleAdvertTag(id, _userDealerGroupName);

         if (vehicleId != null)
         {
            var ok2 = await _autoPriceCache.UpdateTagsVehicleAdvertCache(new ApplyTagToVehiclesDTO() { VehicleAdvertIds = new List<int>() { vehicleId.Value } }, _userDealerGroupName);
         }

         return vehicleId;
      }

      [HttpPost]
      [Route("")]
      public async Task<VehicleAdvertTagDTO> CreateVehicleAdvertTag([FromBody] CreateVehicleAdvertTagDTO vehicleAdvertTagDTO)
      {
         vehicleAdvertTagDTO.CreatedById = _userId;
         vehicleAdvertTagDTO.UpdatedById = _userId;
         vehicleAdvertTagDTO.CreatedDate = DateTime.Now;
         vehicleAdvertTagDTO.UpdatedDate = DateTime.Now;
         vehicleAdvertTagDTO.DealerGroupName = _userDealerGroupName;

         var ok = await _vehicleAdvertTagService.CreateVehicleAdvertTag(vehicleAdvertTagDTO);


         if (ok != null)
         {
            var updateTag = new ApplyTagToVehiclesDTO
            {
               TagId = ok.TagId,
               VehicleAdvertIds = new List<int> { ok.VehicleAdvertId }
            };

            var ok2 = await _autoPriceCache.UpdateTagsVehicleAdvertCache(updateTag, _userDealerGroupName);
         }

         return ok;
      }

      [HttpPut]
      [Route("{id}/ApplyToVehicles")]
      public async Task<bool> ApplyToVehicles(int id, [FromBody] ApplyTagToVehiclesDTO vehicleAdvertTagDTO)
      {
         vehicleAdvertTagDTO.UpdateCache = true;
         vehicleAdvertTagDTO.TagId = id;

         var ok = await _vehicleAdvertTagService.ApplyTagToVehicles(id, vehicleAdvertTagDTO, _userDealerGroupName, _userId);

         if (vehicleAdvertTagDTO.UpdateCache == true)
         {
            var ok2 = await _autoPriceCache.UpdateTagsVehicleAdvertCache(vehicleAdvertTagDTO, _userDealerGroupName);
         }

         return true;
      }

      [HttpPut]
      [Route("{id}/RemoveFromVehicles")]
      public async Task<bool> RemoveFromVehicles(int id, [FromBody] ApplyTagToVehiclesDTO vehicleAdvertTagDTO)
      {
         vehicleAdvertTagDTO.TagId = id;
         var ok = await _vehicleAdvertTagService.RemoveTagFromVehicles(id, vehicleAdvertTagDTO, _userDealerGroupName, _userId);

         if (ok != null)
         {
            var ok2 = await _autoPriceCache.UpdateTagsVehicleAdvertCache(new ApplyTagToVehiclesDTO() { VehicleAdvertIds = vehicleAdvertTagDTO.VehicleAdvertIds }, _userDealerGroupName);
         }

         return ok;
      }

      [HttpPatch]
      [Route("{id}")]
      public async Task<VehicleAdvertTagDTO> PatchVehicleAdvertTag(int id, [FromBody] JsonPatchDocument<VehicleAdvertTag> patchDocument)
      {
         patchDocument.Add(x => x.UpdatedById, _userId);
         patchDocument.Add(x => x.UpdatedDate, DateTime.Now);

         return await _vehicleAdvertTagService.PatchVehicleAdvertTag(id, patchDocument, _userId, _userDealerGroupName);
      }
   }
}
