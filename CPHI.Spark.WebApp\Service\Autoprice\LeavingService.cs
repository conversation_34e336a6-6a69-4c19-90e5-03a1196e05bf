﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using Microsoft.Exchange.WebServices.Data;
using CPHI.Spark.WebApp.DataAccess;
using OfficeOpenXml.FormulaParsing.Excel.Functions.RefAndLookup;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{
  public interface ILeavingService
  {
      Task<List<LVExplorerItem>> GetLeavingVehicleExplorerItems(GetLeavingVehicleExplorerItemsParams parms);

      //Task<IEnumerable<LeavingPriceSummaryItem>> GetLeavingPriceItems(GetLeavingPriceItemsParams parms);
      Task<IEnumerable<LeavingVehicleItem>> GetLeavingVehicleItems(GetLeavingVehicleItemsParams parms);
  }
  public class LeavingService : ILeavingService
  {
    private readonly IUserService userService;
    private readonly IConfiguration configuration;
      private readonly IOrderbookService orderbookService;
      private readonly string _connectionString;

      public LeavingService(
          IUserService userService, IConfiguration configuration
  , IOrderbookService orderbookService)
      {
         this.userService = userService;
         this.configuration = configuration;
         DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         _connectionString = configuration.GetConnectionString(dgName);
         this.orderbookService = orderbookService;
      }

      //Leaving
      //public async Task<IEnumerable<LeavingPriceSummaryItem>> GetLeavingPriceItems(GetLeavingPriceItemsParams parms)
      //{
      //    IEnumerable<int> userRetailerSiteIds = userService.GetUserRetailerSiteIds();

      //    var parmsForSP = new GetLeavingPriceItemsParams()
      //    {
      //        EligibleSiteIds = userService.GetUserSiteIds().ToList(),
      //        ChosenRetailerSiteIds = parms.ChosenRetailerSiteIds.Intersect(userRetailerSiteIds).ToList(),
      //        StartDate = parms.StartDate,
      //        EndDate = parms.EndDate,
      //        includeNewVehicles = parms.includeNewVehicles,
      //    };



      //    var leavingPricesDataAccess = new LeavingPricesDataAccess(_connectionString);
      //    return (await leavingPricesDataAccess.GetLeavingPriceItemsNew(parmsForSP)).ToList();
      //}


      public async Task<IEnumerable<LeavingVehicleItem>> GetLeavingVehicleItems(GetLeavingVehicleItemsParams parms)
    {
      IEnumerable<int> userRetailerSiteIds = userService.GetUserRetailerSiteIds();
      var leavingPricesDataAccess = new LeavingPricesDataAccess(_connectionString);
      var res = await leavingPricesDataAccess.GetLeavingVehicleItems(parms, userRetailerSiteIds);
         var tst = res.ToList();
      foreach (var item in res)
      {
        item.BodyType = ConstantMethodsService.ToTitleCase(item.BodyType);
      }

      // Filter out any leaving items where the first price position % or last price position % is > 130% or < 80%
      var toReturn = res.Where(x => x.FirstPP <= 1.3M  && x.LastPP <= 1.3M && x.LastPP >= 0.8M).ToList(); //&& x.FirstPP >= 0.8M
         return toReturn;
    }

      public async Task<List<LVExplorerItem>> GetLeavingVehicleExplorerItems(GetLeavingVehicleExplorerItemsParams parms)
      {
          IEnumerable<int> userRetailerSiteIds = userService.GetUserRetailerSiteIds();
          var leavingPricesDataAccess = new LeavingPricesDataAccess(_connectionString);
         
         //Firstly get regular leaving vehicle items
         GetLeavingVehicleItemsParams getLeavingItemsParms = new GetLeavingVehicleItemsParams(parms);
         IEnumerable<LeavingVehicleItem> leavingItems = await GetLeavingVehicleItems(getLeavingItemsParms);

         //Now get orderbook detailed items
         OrderbookParams orderbookParams = new OrderbookParams(parms);
         List<OrderbookRow> orderbookRows = await orderbookService.GetOrderbookRows(orderbookParams);
         var dealIds = orderbookRows.Select(x=>x.DealId).Distinct().ToList();
         int userId = userService.GetUserId();
         IEnumerable<OrderbookDetailedRow> orderBookRows = await orderbookService.GetOrderbookDetailedRows(dealIds, userId);

         //blend together and return.   

         // Build lookup from orderbookRows
         var orderLookup = new Dictionary<(int, string), OrderbookDetailedRow>();

         foreach (var o in orderBookRows)
         {
            if (string.IsNullOrEmpty(o.Reg))
               continue;

            var key = (o.SiteId, o.Reg.Trim().ToUpperInvariant());

            // Add to dictionary if not already there
            if (!orderLookup.ContainsKey(key))
            {
               orderLookup.Add(key, o);
            }
         }


         var results = new List<LVExplorerItem>();

         //iterate over leaving, if find orderbook, add to results
         foreach (var leaving in leavingItems)
         {
            if (string.IsNullOrEmpty(leaving.VehicleReg))
            {
               continue;
            }

            var key = (leaving.SiteId, Reg: leaving.VehicleReg.Trim().ToUpperInvariant());

            if (orderLookup.TryGetValue(key, out var order))
            {
               results.Add(new LVExplorerItem(leaving, order));
            }
         }

         return results;

      }
  }
}