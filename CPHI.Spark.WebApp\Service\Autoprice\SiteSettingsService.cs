﻿using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using CPHI.Spark.Model.Services;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using CPHI.Spark.DataAccess.AutoPrice;
using System.Linq;
using System;
using CPHI.Spark.Model.ViewModels;

namespace CPHI.Spark.WebApp.Service.Autoprice
{
   public interface ISiteSettingsService
   {
      Task<IEnumerable<SiteSettings>> GetSitesSettings(DealerGroupName userDealerGroupName);
      Task<SiteSettings> SaveSiteSettings(SaveSiteSettingsParams parms, DealerGroupName userDealerGroupName, List<int> userRetailerSiteIds, int userId);
      //Task<RetailerSiteStrategyVersion> SaveSiteStrategy(RetailerSiteStrategyVersion retailerSiteStrategyVersion);
   }

   public class SiteSettingsService : ISiteSettingsService
   {
      private readonly IUserService userService;
      private readonly IConfiguration configuration;
      private readonly string _connectionString;
      private readonly DealerGroupName userDealerGroup;
      private readonly string dgName;

      public SiteSettingsService(IUserService userService, IConfiguration configuration)
      {
         this.userService = userService;
         this.configuration = configuration;
         userDealerGroup = userService.GetUserDealerGroupName();
         dgName = DealerGroupConnectionNameService.GetConnectionName(userDealerGroup);
         _connectionString = configuration.GetConnectionString(dgName);
      }

      public async Task<IEnumerable<SiteSettings>> GetSitesSettings(DealerGroupName userDealerGroupName)
      {
         var siteSettingsDataAccess = new SiteSettingsDataAccess(_connectionString);
         int userId = this.userService.GetUserId();
         return (await siteSettingsDataAccess.GetSitesSettings(userDealerGroupName, userId));
      }

      public async Task<SiteSettings> SaveSiteSettings(SaveSiteSettingsParams parms, DealerGroupName userDealerGroupName, List<int> userRetailerSiteIds, int userId)
      {
         var siteSettingsDataAccess = new SiteSettingsDataAccess(_connectionString);

         if (!userRetailerSiteIds.Contains(parms.siteSettings.RetailerSiteId))
         {
            //user does not have access
            throw new Exception("User does not have access to this site");
         }

         var toReturn = await siteSettingsDataAccess.SaveSiteSettings(parms, userId);

         //now update teh constants cache
         var retailerSitesDA = new RetailerSitesDataAccess(_connectionString);
         if(dgName == "RRGUKConnection")
         {
            ConstantsCache.bandingDefinitionsRRG = await retailerSitesDA.GetRetailerStrategyBandings();
         }
         else if (dgName == "DefaultConnection")  
         {
            ConstantsCache.bandingDefinitionsAutoprice = await retailerSitesDA.GetRetailerStrategyBandings();
         }
         //VindisConnection
         else if (dgName == "VindisConnection")
         {
            ConstantsCache.bandingDefinitionsVindis = await retailerSitesDA.GetRetailerStrategyBandings();
         }
         //SytnerConnection
         else if (dgName == "SytnerConnection")
         {
            ConstantsCache.bandingDefinitionsSytner = await retailerSitesDA.GetRetailerStrategyBandings();
         }

            return toReturn;
      }

      //public async Task<RetailerSiteStrategyVersion> SaveSiteStrategy(RetailerSiteStrategyVersion retailerSiteStrategyVersion)
      //{
      //    var siteSettingsDataAccess = new SiteSettingsDataAccess(_connectionString);
      //    return (await siteSettingsDataAccess.SaveSiteStrategy(retailerSiteStrategyVersion));
      //}
   }
}
