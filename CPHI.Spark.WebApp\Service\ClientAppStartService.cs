﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.WebApp.DataAccess;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System;
using CPHI.Spark.BusinessLogic.Services;

namespace CPHI.Spark.WebApp.Service
{
   public interface IClientAppStartService
   {
      Task<IEnumerable<DepartmentClientApp>> GetDepartments();
      Task<IEnumerable<string>> GetFranchiseCodes();
      Task<string> GetMobileAppConfig();
      Task<IEnumerable<OrderType>> GetOrderTypes();
      Task<IEnumerable<VehicleType>> GetVehicleTypes();
      Task<IEnumerable<TagDTO>> GetTags(DealerGroupName dg);
   }

   public class ClientAppStartService : IClientAppStartService
   {
      //properties of the service
      private readonly IClientAppStartDataAccess clientAppStartDataAccess;
      private readonly IUserService userService;
      private readonly ITagService tagService;

      //constructor
      public ClientAppStartService(
         IClientAppStartDataAccess clientAppStartDataAccess,
         IUserService userService,
         ITagService tagService)
      {
         this.clientAppStartDataAccess = clientAppStartDataAccess;
         this.userService = userService;
         this.tagService = tagService;
      }

      public async Task<IEnumerable<string>> GetFranchiseCodes()
      {
         return await clientAppStartDataAccess.GetFranchiseCodes(userService.GetUserDealerGroupName());
      }

      public async Task<IEnumerable<TagDTO>> GetTags(DealerGroupName dg)
      {
         return await tagService.SearchTags(new TagSearchDTO() { DealerGroupName = dg });
      }

      public async Task<IEnumerable<VehicleType>> GetVehicleTypes()
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         var result = await clientAppStartDataAccess.GetVehicleTypes(dealerGroup);

         if (dealerGroup == Model.DealerGroupName.RRGSpain)
         {
            var demoRow = result.Where(r => r.Description == "VD Type").FirstOrDefault();
            if (demoRow != null)
            {
               demoRow.Type = "Demo";
               demoRow.SuperType = "Demo";
            }
         }

         return result;
      }

      public async Task<IEnumerable<OrderType>> GetOrderTypes()
      {
         return await clientAppStartDataAccess.GetOrderTypes(userService.GetUserDealerGroupName());
      }

      public async Task<IEnumerable<DepartmentClientApp>> GetDepartments()
      {
         IEnumerable<DepartmentVM> departments = await clientAppStartDataAccess.GetDepartments(userService.GetUserDealerGroupName());
         //group up
         List<DepartmentClientApp> results = new List<DepartmentClientApp>();
         foreach (var item in departments.ToLookup(x => x.Name))
         {
            results.Add(new DepartmentClientApp()
            {
               Name = item.Key,
               ShortName = item.First().ShortName,
               DepartmentCode = item.First().DepartmentCode,
               VehicleTypeTypes = item.Select(x => x.VehicleTypeType).Distinct(),
               OrderTypeTypes = item.Select(x => x.OrderTypeType).Distinct(),
            });
         }

         results = results.OrderBy(x => x.DepartmentCode).ToList();

         //add a 'All' dept
         results.Add(new DepartmentClientApp()
         {
            Name = "All",
            ShortName = "All",
            VehicleTypeTypes = departments.Select(x => x.VehicleTypeType).Distinct(),
            OrderTypeTypes = departments.Select(x => x.OrderTypeType).Distinct()
         });

         // Define your custom order
         var customOrder = new List<string> { "New Retail", "Fleet", "Used", "All" };

         // Assuming 'departments' is your list of DepartmentClientApp objects
         results = results
             .OrderBy(d => customOrder.IndexOf(d.Name) >= 0 ? customOrder.IndexOf(d.Name) : int.MaxValue)
             .ToList();





         return results;
      }


      public async Task<string> GetMobileAppConfig()
      {
         int dealerGroupId = userService.GetIdFromAccessToken("DealerGroupId");
         return await clientAppStartDataAccess.GetMobileAppConfig(dealerGroupId, userService.GetUserDealerGroupName());
      }

   }
}
