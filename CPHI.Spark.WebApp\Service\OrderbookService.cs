﻿using CPHI.Spark.DataAccess;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.WebApp.DataAccess;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CPHI.Spark.WebApp.Service
{
   public interface IOrderbookService
   {
      Task<IEnumerable<OrderbookDetailedRow>> GetOrderbookDetailedRows(List<int> dealIds, int userId);
      Task<IEnumerable<string>> GetOrderbookRows(OrderbookParams parms, int userId);
      Task<IEnumerable<string>> GetOrderbookRowsMobile(OrderbookParams parms, int userId);
      Task<IEnumerable<OrderbookExcelRowSpain>> GetOrderbookExcelRowsSpain(List<int> dealIds, int userId);
      Task<List<OrderbookRow>> GetOrderbookRows(OrderbookParams parms);
   }

   public class OrderbookService : IOrderbookService
   {
      private readonly IOrderbookDataAccess orderbookDataAccess;
      private readonly IUserService userService;


      public OrderbookService(IUserService userService, IOrderbookDataAccess orderbookDataAccess)
      {
         this.userService = userService;
         this.orderbookDataAccess = orderbookDataAccess;
      }

      public async Task<IEnumerable<string>> GetOrderbookRows(OrderbookParams parms, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isSpain = dealerGroup == Model.DealerGroupName.RRGSpain;

         IEnumerable<OrderbookRow> rows;

         if (dealerGroup == DealerGroupName.JJPremiumCars)
         {
            rows = await orderbookDataAccess.GetOrderbookRowsJJPremiumCars(parms, userId, dealerGroup);
         }
         else if (isSpain)
         {
            rows = await orderbookDataAccess.GetOrderbookRowsSpain(parms, userId, dealerGroup);
         }
         else
         {
            rows = await orderbookDataAccess.GetOrderbookRows(parms, userId, dealerGroup);
         }

         var res = rows.ToList().ConvertAll(x => convertRowToString(x));

         return res;
      }

      public async Task<List<OrderbookRow>> GetOrderbookRows(OrderbookParams parms)
      {
         int userId =  userService.GetUserId();
         var userDG = userService.GetUserDealerGroupName();
         var res = await orderbookDataAccess.GetOrderbookRows(parms, userId,userDG);
         return res.ToList();
      }

      public async Task<IEnumerable<string>> GetOrderbookRowsMobile(OrderbookParams parms, int userId)
      {
         IEnumerable<OrderbookRowMobile> rows = await orderbookDataAccess.GetOrderbookRowsMobile(parms, userId, userService.GetUserDealerGroupName());
         return rows.ToList().ConvertAll(x => convertMobileRowToString(x));
      }


      private string convertRowToString(OrderbookRow row)
      {

         row.Customer = RemovePipeCharacter(row.Customer);
         row.Description = RemovePipeCharacter(row.Description);
         row.Reg = RemovePipeCharacter(row.Reg);

         return
            $"{row.DealId}|" + //DealId int
             $"{row.SiteDescription}|" + //SiteDescription string
             $"{row.SiteId}|" + //SiteId int
             $"{row.DeliverySiteDescription}|" +   //DeliverySiteDescription string
             $"{row.Customer}|" + //Customer string
             $"{row.StockNumber}|" + //StockNumber string
             $"{row.OEMReference}|" +  //OEMReference string
             $"{row.Franchise}|" + //Franchise string
             $"{row.VehicleClass}|" +   //VehicleClass string
             $"{row.VehicleTypeCode}|" + //VehicleTypeCode string
             $"{row.OrderTypeCode}|" + //OrderTypeCode string
             $"{row.Model}|" +  //Model string
             $"{row.ModelYear}|" +  //ModelYear int
             $"{row.Source}|" +  //Source string
             $"{row.DIS}|" + //DIS int
             $"{row.Reg}|" + //Reg string
             $"{row.Description}|" + //Description string
             $"{row.SalesmanName}|" + //SalesmanName string
             $"{row.SalesmanId}|" + //SalesmanId int
             $"{StaticHelpersService.dateToString(row.OrderDate)}|" + //OrderDate DateTime
             $"{StaticHelpersService.nullableDateToString(row.InvoiceDate)}|" + //InvoiceDate DateTime
             $"{StaticHelpersService.dateToString(row.AccountingDate)}|" + //AccountingDate DateTime
             $"{StaticHelpersService.nullableDateToString(row.DeliveryDate)}|" + //DeliveryDate DateTime
             $"{StaticHelpersService.nullableDateToString(row.RegisteredDate)}|" + //RegisteredDate DateTime
             $"{row.IsInvoiced}|" + //IsInvoiced bool
             $"{row.DaysToDeliver}|" +  //DaysToDeliver int
             $"{row.Location}|" +  //Location string
             $"{row.IsConfirmed}|" +   //IsConfirmed bool
             $"{row.IsDelivered}|" + //IsDelivered bool
             $"{row.IsClosed}|" +  //IsClosed bool
             $"{row.Units}|" +  //Units int
             $"{row.Sale}|" + //Sale decimal
             $"{row.Discount}|" +  //Discount decimal
             $"{row.MetalProfit}|" + //MetalProfit decimal
             $"{row.OtherProfit}|" + //OtherProfit decimal
             $"{row.AddOnProfit}|" + //AddOnProfit decimal
             $"{row.FinanceProfit}|" + //FinanceProfit decimal
             $"{row.TotalProfit} | " + //TotalProfit decimal
             $"{row.FinanceType}|" +  //FinanceType string
             $"{row.IsFinanced}|" + //IsFinanced bool
             $"{row.IsCosmetic}|" + //IsCosmetic bool
             $"{row.IsPaint}|" + //IsPaint bool
             $"{row.IsGap}|" + //IsGap bool
             $"{row.IsTyre}|" + //IsTyre bool
             $"{row.IsAlloy}|" + //IsAlloy bool
             $"{row.IsTyreAlloy}|" + //IsTyreAlloy bool
             $"{row.IsWheelGuard}|" + //IsWheelGuard bool
             $"{row.IsServicePlan}|" + //IsServicePlan bool
             $"{row.IsWarranty}|" + //IsWarranty bool
             $"{row.IsLateCost}|" +   //IsLateCost bool
             $"{row.TotalProductCount}|" + //TotalProductCount int
             $"{StaticHelpersService.nullableDateToString(row.OrderAllocationDate)}|" +  //OrderAllocationDate DateTime
             $"{row.Channel}|" +  //Channel string
             $"{row.AuditPass}|" +  //Audit Pass bool
             $"{row.SoldBySalesExec}|" + //SoldBySalesExec bool
             $"{row.DaysToSale}|" +//DaysToSale int
             $"{row.ManagerName}|" + //ManagerName string
             $"{row.ManagerId}|" + //ManagerId int
             $"{row.ChannelVoDescription}|" +//ChannelVoDescription string
             $"{row.TypeVoDescription}|" + //TypeVoDescription string
             $"{StaticHelpersService.nullableDateToString(row.DateFactoryTransportation)}|" + //DateFactoryTransportation DateTime
             $"{StaticHelpersService.nullableDateToString(row.DateVehicleRecondition)}|" + //DateVehicleRecondition DateTime
             $"{StaticHelpersService.nullableDateToString(row.DateSiteTransportation)}|" + //DateSiteTransportation DateTime
             $"{StaticHelpersService.nullableDateToString(row.DateSiteArrival)}|" + //DateSiteArrival DateTime
             $"{StaticHelpersService.nullableDateToString(row.ReservedDate)}|" +  //ReservedDate DateTime
             $"{row.IsVatQualifying}|" +
             $"{row.LastAdvertisedPrice}|"

             ;
      }

      private string convertMobileRowToString(OrderbookRowMobile row)
      {
         row.Customer = RemovePipeCharacter(row.Customer);
         row.Description = RemovePipeCharacter(row.Description);

         return
             $"{row.DealId}|" +  //DealId
             $"{row.SiteDescription}|" +  //SiteDescription
             $"{row.SiteId}|" +  //SiteId
             $"{row.Customer}|" +  //Customer
             $"{row.StockNumber}|" +  //StockNumber
             $"{row.Franchise}|" +  //Franchise
             $"{row.VehicleTypeCode}|" +  //VehicleTypeCode
             $"{row.OrderTypeCode}|" +  //OrderTypeCode
             $"{row.Reg}|" +  //Reg
             $"{row.Description}|" +  //Description
             $"{row.SalesmanName}|" +  //SalesmanName
             $"{row.SalesmanId}|" +  //SalesmanId
             $"{StaticHelpersService.dateToString(row.OrderDate)}|" +  //OrderDate
             $"{StaticHelpersService.nullableDateToString(row.InvoiceDate)}|" +  //InvoiceDate
             $"{StaticHelpersService.dateToString(row.AccountingDate)}|" +  //AccountingDate
             $"{StaticHelpersService.dateToString(row.DeliveryDate)}|" +  //DeliveryDate
             $"{row.IsInvoiced}|" +  //IsInvoiced
             $"{row.Units}|" +   //Units
             $"{row.MetalProfit}|" +  //MetalProfit
             $"{row.OtherProfit}|" +  //OtherProfit
             $"{row.AddOnProfit}|" +  //AddOnProfit
             $"{row.FinanceProfit}|" +  //FinanceProfit
             $"{row.TotalProfit}|" +  //TotalProfit
             $"{row.IsFinanced}|" +  //IsFinanced
             $"{row.IsCosmetic}|" +  //IsCosmetic
             $"{row.IsPaint}|" +  //IsPaint
             $"{row.IsGap}|" +  //IsGap
             $"{row.IsTyre}|" +  //IsTyre
             $"{row.IsAlloy}|" +  //IsAlloy
             $"{row.IsTyreAlloy}|" +  //IsTyreAlloy
             $"{row.IsWheelGuard}|" +  //IsWheelGuard
             $"{row.IsServicePlan}|" +  //IsServicePlan
             $"{row.IsWarranty}|" +  //IsWarranty
             $"{row.IsLateCost}|" +  //IsLateCost
             $"{row.TotalProductCount}|"   //TotalProductCount
             ;
      }


      public static string RemovePipeCharacter(string input)
      {
         if (input == null)
         { return string.Empty; }
         if (input.Contains("|"))
         {
            input = input.Replace("|", string.Empty);
         }

         return input;
      }


      public async Task<IEnumerable<OrderbookDetailedRow>> GetOrderbookDetailedRows(List<int> dealIds, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool byStockNumber = dealerGroup != Model.DealerGroupName.Vindis;

         return await orderbookDataAccess.GetOrderbookDetailedRows(dealIds, byStockNumber, dealerGroup);
      }

      public async Task<IEnumerable<OrderbookExcelRowSpain>> GetOrderbookExcelRowsSpain(List<int> dealIds, int userId)
      {
         return await orderbookDataAccess.GetOrderbookExcelRowsSpain(dealIds, userService.GetUserDealerGroupName());
      }


   }



}
